// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDBasicInfo.proto

#ifndef PROTOBUF_MDBasicInfo_2eproto__INCLUDED
#define PROTOBUF_MDBasicInfo_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDBasicInfo_2eproto();
void protobuf_InitDefaults_MDBasicInfo_2eproto();
void protobuf_AssignDesc_MDBasicInfo_2eproto();
void protobuf_ShutdownFile_MDBasicInfo_2eproto();

class MDBasicInfo;
class MDBasicInfo_ConstantParam;

// ===================================================================

class MDBasicInfo_ConstantParam : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam) */ {
 public:
  MDBasicInfo_ConstantParam();
  virtual ~MDBasicInfo_ConstantParam();

  MDBasicInfo_ConstantParam(const MDBasicInfo_ConstantParam& from);

  inline MDBasicInfo_ConstantParam& operator=(const MDBasicInfo_ConstantParam& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDBasicInfo_ConstantParam& default_instance();

  static const MDBasicInfo_ConstantParam* internal_default_instance();

  void Swap(MDBasicInfo_ConstantParam* other);

  // implements Message ----------------------------------------------

  inline MDBasicInfo_ConstantParam* New() const { return New(NULL); }

  MDBasicInfo_ConstantParam* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDBasicInfo_ConstantParam& from);
  void MergeFrom(const MDBasicInfo_ConstantParam& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDBasicInfo_ConstantParam* other);
  void UnsafeMergeFrom(const MDBasicInfo_ConstantParam& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ParamName = 1;
  void clear_paramname();
  static const int kParamNameFieldNumber = 1;
  const ::std::string& paramname() const;
  void set_paramname(const ::std::string& value);
  void set_paramname(const char* value);
  void set_paramname(const char* value, size_t size);
  ::std::string* mutable_paramname();
  ::std::string* release_paramname();
  void set_allocated_paramname(::std::string* paramname);

  // optional string ParamValue = 2;
  void clear_paramvalue();
  static const int kParamValueFieldNumber = 2;
  const ::std::string& paramvalue() const;
  void set_paramvalue(const ::std::string& value);
  void set_paramvalue(const char* value);
  void set_paramvalue(const char* value, size_t size);
  ::std::string* mutable_paramvalue();
  ::std::string* release_paramvalue();
  void set_allocated_paramvalue(::std::string* paramvalue);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr paramname_;
  ::google::protobuf::internal::ArenaStringPtr paramvalue_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDBasicInfo_2eproto_impl();
  friend void  protobuf_AddDesc_MDBasicInfo_2eproto_impl();
  friend void protobuf_AssignDesc_MDBasicInfo_2eproto();
  friend void protobuf_ShutdownFile_MDBasicInfo_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDBasicInfo_ConstantParam> MDBasicInfo_ConstantParam_default_instance_;

// -------------------------------------------------------------------

class MDBasicInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDBasicInfo) */ {
 public:
  MDBasicInfo();
  virtual ~MDBasicInfo();

  MDBasicInfo(const MDBasicInfo& from);

  inline MDBasicInfo& operator=(const MDBasicInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDBasicInfo& default_instance();

  static const MDBasicInfo* internal_default_instance();

  void Swap(MDBasicInfo* other);

  // implements Message ----------------------------------------------

  inline MDBasicInfo* New() const { return New(NULL); }

  MDBasicInfo* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDBasicInfo& from);
  void MergeFrom(const MDBasicInfo& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDBasicInfo* other);
  void UnsafeMergeFrom(const MDBasicInfo& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef MDBasicInfo_ConstantParam ConstantParam;

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional string SecurityID = 2;
  void clear_securityid();
  static const int kSecurityIDFieldNumber = 2;
  const ::std::string& securityid() const;
  void set_securityid(const ::std::string& value);
  void set_securityid(const char* value);
  void set_securityid(const char* value, size_t size);
  ::std::string* mutable_securityid();
  ::std::string* release_securityid();
  void set_allocated_securityid(::std::string* securityid);

  // optional string Symbol = 3;
  void clear_symbol();
  static const int kSymbolFieldNumber = 3;
  const ::std::string& symbol() const;
  void set_symbol(const ::std::string& value);
  void set_symbol(const char* value);
  void set_symbol(const char* value, size_t size);
  ::std::string* mutable_symbol();
  ::std::string* release_symbol();
  void set_allocated_symbol(::std::string* symbol);

  // optional string ChiSpelling = 4;
  void clear_chispelling();
  static const int kChiSpellingFieldNumber = 4;
  const ::std::string& chispelling() const;
  void set_chispelling(const ::std::string& value);
  void set_chispelling(const char* value);
  void set_chispelling(const char* value, size_t size);
  ::std::string* mutable_chispelling();
  ::std::string* release_chispelling();
  void set_allocated_chispelling(::std::string* chispelling);

  // optional string EnglishName = 5;
  void clear_englishname();
  static const int kEnglishNameFieldNumber = 5;
  const ::std::string& englishname() const;
  void set_englishname(const ::std::string& value);
  void set_englishname(const char* value);
  void set_englishname(const char* value, size_t size);
  ::std::string* mutable_englishname();
  ::std::string* release_englishname();
  void set_allocated_englishname(::std::string* englishname);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string SecuritySubType = 8;
  void clear_securitysubtype();
  static const int kSecuritySubTypeFieldNumber = 8;
  const ::std::string& securitysubtype() const;
  void set_securitysubtype(const ::std::string& value);
  void set_securitysubtype(const char* value);
  void set_securitysubtype(const char* value, size_t size);
  ::std::string* mutable_securitysubtype();
  ::std::string* release_securitysubtype();
  void set_allocated_securitysubtype(::std::string* securitysubtype);

  // optional string ListDate = 9;
  void clear_listdate();
  static const int kListDateFieldNumber = 9;
  const ::std::string& listdate() const;
  void set_listdate(const ::std::string& value);
  void set_listdate(const char* value);
  void set_listdate(const char* value, size_t size);
  ::std::string* mutable_listdate();
  ::std::string* release_listdate();
  void set_allocated_listdate(::std::string* listdate);

  // optional string Currency = 10;
  void clear_currency();
  static const int kCurrencyFieldNumber = 10;
  const ::std::string& currency() const;
  void set_currency(const ::std::string& value);
  void set_currency(const char* value);
  void set_currency(const char* value, size_t size);
  ::std::string* mutable_currency();
  ::std::string* release_currency();
  void set_allocated_currency(::std::string* currency);

  // optional int64 OutstandingShare = 11;
  void clear_outstandingshare();
  static const int kOutstandingShareFieldNumber = 11;
  ::google::protobuf::int64 outstandingshare() const;
  void set_outstandingshare(::google::protobuf::int64 value);

  // optional int64 PublicFloatShareQuantity = 12;
  void clear_publicfloatsharequantity();
  static const int kPublicFloatShareQuantityFieldNumber = 12;
  ::google::protobuf::int64 publicfloatsharequantity() const;
  void set_publicfloatsharequantity(::google::protobuf::int64 value);

  // optional int32 MDDate = 13;
  void clear_mddate();
  static const int kMDDateFieldNumber = 13;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional string TradingPhaseCode = 14;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 14;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional int64 PreClosePx = 15;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 15;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 MaxPx = 16;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 16;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 17;
  void clear_minpx();
  static const int kMinPxFieldNumber = 17;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 LotSize = 18;
  void clear_lotsize();
  static const int kLotSizeFieldNumber = 18;
  ::google::protobuf::int64 lotsize() const;
  void set_lotsize(::google::protobuf::int64 value);

  // optional bool ShortSellFlag = 19;
  void clear_shortsellflag();
  static const int kShortSellFlagFieldNumber = 19;
  bool shortsellflag() const;
  void set_shortsellflag(bool value);

  // optional string ExchangeDate = 20;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 20;
  const ::std::string& exchangedate() const;
  void set_exchangedate(const ::std::string& value);
  void set_exchangedate(const char* value);
  void set_exchangedate(const char* value, size_t size);
  ::std::string* mutable_exchangedate();
  ::std::string* release_exchangedate();
  void set_allocated_exchangedate(::std::string* exchangedate);

  // optional string ExchangeSymbol = 21;
  void clear_exchangesymbol();
  static const int kExchangeSymbolFieldNumber = 21;
  const ::std::string& exchangesymbol() const;
  void set_exchangesymbol(const ::std::string& value);
  void set_exchangesymbol(const char* value);
  void set_exchangesymbol(const char* value, size_t size);
  ::std::string* mutable_exchangesymbol();
  ::std::string* release_exchangesymbol();
  void set_allocated_exchangesymbol(::std::string* exchangesymbol);

  // optional double TickSize = 22;
  void clear_ticksize();
  static const int kTickSizeFieldNumber = 22;
  double ticksize() const;
  void set_ticksize(double value);

  // optional int32 LoanMarginIndicator = 23;
  void clear_loanmarginindicator();
  static const int kLoanMarginIndicatorFieldNumber = 23;
  ::google::protobuf::int32 loanmarginindicator() const;
  void set_loanmarginindicator(::google::protobuf::int32 value);

  // optional int32 PxAccuracy = 24;
  void clear_pxaccuracy();
  static const int kPxAccuracyFieldNumber = 24;
  ::google::protobuf::int32 pxaccuracy() const;
  void set_pxaccuracy(::google::protobuf::int32 value);

  // optional int32 IPOProfitable = 25;
  void clear_ipoprofitable();
  static const int kIPOProfitableFieldNumber = 25;
  ::google::protobuf::int32 ipoprofitable() const;
  void set_ipoprofitable(::google::protobuf::int32 value);

  // optional int32 DiffRightsIndicator = 26;
  void clear_diffrightsindicator();
  static const int kDiffRightsIndicatorFieldNumber = 26;
  ::google::protobuf::int32 diffrightsindicator() const;
  void set_diffrightsindicator(::google::protobuf::int32 value);

  // optional string HKSpreadTableCode = 27;
  void clear_hkspreadtablecode();
  static const int kHKSpreadTableCodeFieldNumber = 27;
  const ::std::string& hkspreadtablecode() const;
  void set_hkspreadtablecode(const ::std::string& value);
  void set_hkspreadtablecode(const char* value);
  void set_hkspreadtablecode(const char* value, size_t size);
  ::std::string* mutable_hkspreadtablecode();
  ::std::string* release_hkspreadtablecode();
  void set_allocated_hkspreadtablecode(::std::string* hkspreadtablecode);

  // optional double PreSettlePx = 28;
  void clear_presettlepx();
  static const int kPreSettlePxFieldNumber = 28;
  double presettlepx() const;
  void set_presettlepx(double value);

  // optional double PreIOPV = 29;
  void clear_preiopv();
  static const int kPreIOPVFieldNumber = 29;
  double preiopv() const;
  void set_preiopv(double value);

  // optional int32 ShHkConnect = 30;
  void clear_shhkconnect();
  static const int kShHkConnectFieldNumber = 30;
  ::google::protobuf::int32 shhkconnect() const;
  void set_shhkconnect(::google::protobuf::int32 value);

  // optional int32 SzHkConnect = 31;
  void clear_szhkconnect();
  static const int kSzHkConnectFieldNumber = 31;
  ::google::protobuf::int32 szhkconnect() const;
  void set_szhkconnect(::google::protobuf::int32 value);

  // optional string OptionContractID = 40;
  void clear_optioncontractid();
  static const int kOptionContractIDFieldNumber = 40;
  const ::std::string& optioncontractid() const;
  void set_optioncontractid(const ::std::string& value);
  void set_optioncontractid(const char* value);
  void set_optioncontractid(const char* value, size_t size);
  ::std::string* mutable_optioncontractid();
  ::std::string* release_optioncontractid();
  void set_allocated_optioncontractid(::std::string* optioncontractid);

  // optional string OptionContractSymbol = 41;
  void clear_optioncontractsymbol();
  static const int kOptionContractSymbolFieldNumber = 41;
  const ::std::string& optioncontractsymbol() const;
  void set_optioncontractsymbol(const ::std::string& value);
  void set_optioncontractsymbol(const char* value);
  void set_optioncontractsymbol(const char* value, size_t size);
  ::std::string* mutable_optioncontractsymbol();
  ::std::string* release_optioncontractsymbol();
  void set_allocated_optioncontractsymbol(::std::string* optioncontractsymbol);

  // optional string OptionUnderlyingSecurityID = 42;
  void clear_optionunderlyingsecurityid();
  static const int kOptionUnderlyingSecurityIDFieldNumber = 42;
  const ::std::string& optionunderlyingsecurityid() const;
  void set_optionunderlyingsecurityid(const ::std::string& value);
  void set_optionunderlyingsecurityid(const char* value);
  void set_optionunderlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_optionunderlyingsecurityid();
  ::std::string* release_optionunderlyingsecurityid();
  void set_allocated_optionunderlyingsecurityid(::std::string* optionunderlyingsecurityid);

  // optional string OptionUnderlyingSymbol = 43;
  void clear_optionunderlyingsymbol();
  static const int kOptionUnderlyingSymbolFieldNumber = 43;
  const ::std::string& optionunderlyingsymbol() const;
  void set_optionunderlyingsymbol(const ::std::string& value);
  void set_optionunderlyingsymbol(const char* value);
  void set_optionunderlyingsymbol(const char* value, size_t size);
  ::std::string* mutable_optionunderlyingsymbol();
  ::std::string* release_optionunderlyingsymbol();
  void set_allocated_optionunderlyingsymbol(::std::string* optionunderlyingsymbol);

  // optional string OptionUnderlyingType = 44;
  void clear_optionunderlyingtype();
  static const int kOptionUnderlyingTypeFieldNumber = 44;
  const ::std::string& optionunderlyingtype() const;
  void set_optionunderlyingtype(const ::std::string& value);
  void set_optionunderlyingtype(const char* value);
  void set_optionunderlyingtype(const char* value, size_t size);
  ::std::string* mutable_optionunderlyingtype();
  ::std::string* release_optionunderlyingtype();
  void set_allocated_optionunderlyingtype(::std::string* optionunderlyingtype);

  // optional string OptionOptionType = 45;
  void clear_optionoptiontype();
  static const int kOptionOptionTypeFieldNumber = 45;
  const ::std::string& optionoptiontype() const;
  void set_optionoptiontype(const ::std::string& value);
  void set_optionoptiontype(const char* value);
  void set_optionoptiontype(const char* value, size_t size);
  ::std::string* mutable_optionoptiontype();
  ::std::string* release_optionoptiontype();
  void set_allocated_optionoptiontype(::std::string* optionoptiontype);

  // optional string OptionCallOrPut = 46;
  void clear_optioncallorput();
  static const int kOptionCallOrPutFieldNumber = 46;
  const ::std::string& optioncallorput() const;
  void set_optioncallorput(const ::std::string& value);
  void set_optioncallorput(const char* value);
  void set_optioncallorput(const char* value, size_t size);
  ::std::string* mutable_optioncallorput();
  ::std::string* release_optioncallorput();
  void set_allocated_optioncallorput(::std::string* optioncallorput);

  // optional int64 OptionContractMultiplierUnit = 47;
  void clear_optioncontractmultiplierunit();
  static const int kOptionContractMultiplierUnitFieldNumber = 47;
  ::google::protobuf::int64 optioncontractmultiplierunit() const;
  void set_optioncontractmultiplierunit(::google::protobuf::int64 value);

  // optional double OptionExercisePrice = 48;
  void clear_optionexerciseprice();
  static const int kOptionExercisePriceFieldNumber = 48;
  double optionexerciseprice() const;
  void set_optionexerciseprice(double value);

  // optional string OptionStartDate = 49;
  void clear_optionstartdate();
  static const int kOptionStartDateFieldNumber = 49;
  const ::std::string& optionstartdate() const;
  void set_optionstartdate(const ::std::string& value);
  void set_optionstartdate(const char* value);
  void set_optionstartdate(const char* value, size_t size);
  ::std::string* mutable_optionstartdate();
  ::std::string* release_optionstartdate();
  void set_allocated_optionstartdate(::std::string* optionstartdate);

  // optional string OptionEndDate = 50;
  void clear_optionenddate();
  static const int kOptionEndDateFieldNumber = 50;
  const ::std::string& optionenddate() const;
  void set_optionenddate(const ::std::string& value);
  void set_optionenddate(const char* value);
  void set_optionenddate(const char* value, size_t size);
  ::std::string* mutable_optionenddate();
  ::std::string* release_optionenddate();
  void set_allocated_optionenddate(::std::string* optionenddate);

  // optional string OptionExerciseDate = 51;
  void clear_optionexercisedate();
  static const int kOptionExerciseDateFieldNumber = 51;
  const ::std::string& optionexercisedate() const;
  void set_optionexercisedate(const ::std::string& value);
  void set_optionexercisedate(const char* value);
  void set_optionexercisedate(const char* value, size_t size);
  ::std::string* mutable_optionexercisedate();
  ::std::string* release_optionexercisedate();
  void set_allocated_optionexercisedate(::std::string* optionexercisedate);

  // optional string OptionDeliveryDate = 52;
  void clear_optiondeliverydate();
  static const int kOptionDeliveryDateFieldNumber = 52;
  const ::std::string& optiondeliverydate() const;
  void set_optiondeliverydate(const ::std::string& value);
  void set_optiondeliverydate(const char* value);
  void set_optiondeliverydate(const char* value, size_t size);
  ::std::string* mutable_optiondeliverydate();
  ::std::string* release_optiondeliverydate();
  void set_allocated_optiondeliverydate(::std::string* optiondeliverydate);

  // optional string OptionExpireDate = 53;
  void clear_optionexpiredate();
  static const int kOptionExpireDateFieldNumber = 53;
  const ::std::string& optionexpiredate() const;
  void set_optionexpiredate(const ::std::string& value);
  void set_optionexpiredate(const char* value);
  void set_optionexpiredate(const char* value, size_t size);
  ::std::string* mutable_optionexpiredate();
  ::std::string* release_optionexpiredate();
  void set_allocated_optionexpiredate(::std::string* optionexpiredate);

  // optional string OptionUpdateVersion = 54;
  void clear_optionupdateversion();
  static const int kOptionUpdateVersionFieldNumber = 54;
  const ::std::string& optionupdateversion() const;
  void set_optionupdateversion(const ::std::string& value);
  void set_optionupdateversion(const char* value);
  void set_optionupdateversion(const char* value, size_t size);
  ::std::string* mutable_optionupdateversion();
  ::std::string* release_optionupdateversion();
  void set_allocated_optionupdateversion(::std::string* optionupdateversion);

  // optional int64 OptionTotalLongPosition = 55;
  void clear_optiontotallongposition();
  static const int kOptionTotalLongPositionFieldNumber = 55;
  ::google::protobuf::int64 optiontotallongposition() const;
  void set_optiontotallongposition(::google::protobuf::int64 value);

  // optional double OptionSecurityClosePx = 56;
  void clear_optionsecurityclosepx();
  static const int kOptionSecurityClosePxFieldNumber = 56;
  double optionsecurityclosepx() const;
  void set_optionsecurityclosepx(double value);

  // optional double OptionSettlPrice = 57;
  void clear_optionsettlprice();
  static const int kOptionSettlPriceFieldNumber = 57;
  double optionsettlprice() const;
  void set_optionsettlprice(double value);

  // optional double OptionUnderlyingClosePx = 58;
  void clear_optionunderlyingclosepx();
  static const int kOptionUnderlyingClosePxFieldNumber = 58;
  double optionunderlyingclosepx() const;
  void set_optionunderlyingclosepx(double value);

  // optional string OptionPriceLimitType = 59;
  void clear_optionpricelimittype();
  static const int kOptionPriceLimitTypeFieldNumber = 59;
  const ::std::string& optionpricelimittype() const;
  void set_optionpricelimittype(const ::std::string& value);
  void set_optionpricelimittype(const char* value);
  void set_optionpricelimittype(const char* value, size_t size);
  ::std::string* mutable_optionpricelimittype();
  ::std::string* release_optionpricelimittype();
  void set_allocated_optionpricelimittype(::std::string* optionpricelimittype);

  // optional double OptionDailyPriceUpLimit = 60;
  void clear_optiondailypriceuplimit();
  static const int kOptionDailyPriceUpLimitFieldNumber = 60;
  double optiondailypriceuplimit() const;
  void set_optiondailypriceuplimit(double value);

  // optional double OptionDailyPriceDownLimit = 61;
  void clear_optiondailypricedownlimit();
  static const int kOptionDailyPriceDownLimitFieldNumber = 61;
  double optiondailypricedownlimit() const;
  void set_optiondailypricedownlimit(double value);

  // optional double OptionMarginUnit = 62;
  void clear_optionmarginunit();
  static const int kOptionMarginUnitFieldNumber = 62;
  double optionmarginunit() const;
  void set_optionmarginunit(double value);

  // optional double OptionMarginRatioParam1 = 63;
  void clear_optionmarginratioparam1();
  static const int kOptionMarginRatioParam1FieldNumber = 63;
  double optionmarginratioparam1() const;
  void set_optionmarginratioparam1(double value);

  // optional double OptionMarginRatioParam2 = 64;
  void clear_optionmarginratioparam2();
  static const int kOptionMarginRatioParam2FieldNumber = 64;
  double optionmarginratioparam2() const;
  void set_optionmarginratioparam2(double value);

  // optional int64 OptionRoundLot = 65;
  void clear_optionroundlot();
  static const int kOptionRoundLotFieldNumber = 65;
  ::google::protobuf::int64 optionroundlot() const;
  void set_optionroundlot(::google::protobuf::int64 value);

  // optional int64 OptionLmtOrdMinFloor = 66;
  void clear_optionlmtordminfloor();
  static const int kOptionLmtOrdMinFloorFieldNumber = 66;
  ::google::protobuf::int64 optionlmtordminfloor() const;
  void set_optionlmtordminfloor(::google::protobuf::int64 value);

  // optional int64 OptionLmtOrdMaxFloor = 67;
  void clear_optionlmtordmaxfloor();
  static const int kOptionLmtOrdMaxFloorFieldNumber = 67;
  ::google::protobuf::int64 optionlmtordmaxfloor() const;
  void set_optionlmtordmaxfloor(::google::protobuf::int64 value);

  // optional int64 OptionMktOrdMinFloor = 68;
  void clear_optionmktordminfloor();
  static const int kOptionMktOrdMinFloorFieldNumber = 68;
  ::google::protobuf::int64 optionmktordminfloor() const;
  void set_optionmktordminfloor(::google::protobuf::int64 value);

  // optional int64 OptionMktOrdMaxFloor = 69;
  void clear_optionmktordmaxfloor();
  static const int kOptionMktOrdMaxFloorFieldNumber = 69;
  ::google::protobuf::int64 optionmktordmaxfloor() const;
  void set_optionmktordmaxfloor(::google::protobuf::int64 value);

  // optional double OptionTickSize = 70;
  void clear_optionticksize();
  static const int kOptionTickSizeFieldNumber = 70;
  double optionticksize() const;
  void set_optionticksize(double value);

  // optional string OptionSecurityStatusFlag = 71;
  void clear_optionsecuritystatusflag();
  static const int kOptionSecurityStatusFlagFieldNumber = 71;
  const ::std::string& optionsecuritystatusflag() const;
  void set_optionsecuritystatusflag(const ::std::string& value);
  void set_optionsecuritystatusflag(const char* value);
  void set_optionsecuritystatusflag(const char* value, size_t size);
  ::std::string* mutable_optionsecuritystatusflag();
  ::std::string* release_optionsecuritystatusflag();
  void set_allocated_optionsecuritystatusflag(::std::string* optionsecuritystatusflag);

  // optional string OptionCarryInterestDate = 72;
  void clear_optioncarryinterestdate();
  static const int kOptionCarryInterestDateFieldNumber = 72;
  const ::std::string& optioncarryinterestdate() const;
  void set_optioncarryinterestdate(const ::std::string& value);
  void set_optioncarryinterestdate(const char* value);
  void set_optioncarryinterestdate(const char* value, size_t size);
  ::std::string* mutable_optioncarryinterestdate();
  ::std::string* release_optioncarryinterestdate();
  void set_allocated_optioncarryinterestdate(::std::string* optioncarryinterestdate);

  // optional string OptionEarlyExpireDate = 73;
  void clear_optionearlyexpiredate();
  static const int kOptionEarlyExpireDateFieldNumber = 73;
  const ::std::string& optionearlyexpiredate() const;
  void set_optionearlyexpiredate(const ::std::string& value);
  void set_optionearlyexpiredate(const char* value);
  void set_optionearlyexpiredate(const char* value, size_t size);
  ::std::string* mutable_optionearlyexpiredate();
  ::std::string* release_optionearlyexpiredate();
  void set_allocated_optionearlyexpiredate(::std::string* optionearlyexpiredate);

  // optional string OptionStrategySecurityID = 74;
  void clear_optionstrategysecurityid();
  static const int kOptionStrategySecurityIDFieldNumber = 74;
  const ::std::string& optionstrategysecurityid() const;
  void set_optionstrategysecurityid(const ::std::string& value);
  void set_optionstrategysecurityid(const char* value);
  void set_optionstrategysecurityid(const char* value, size_t size);
  ::std::string* mutable_optionstrategysecurityid();
  ::std::string* release_optionstrategysecurityid();
  void set_allocated_optionstrategysecurityid(::std::string* optionstrategysecurityid);

  // optional string FITradeProductType = 80;
  void clear_fitradeproducttype();
  static const int kFITradeProductTypeFieldNumber = 80;
  const ::std::string& fitradeproducttype() const;
  void set_fitradeproducttype(const ::std::string& value);
  void set_fitradeproducttype(const char* value);
  void set_fitradeproducttype(const char* value, size_t size);
  ::std::string* mutable_fitradeproducttype();
  ::std::string* release_fitradeproducttype();
  void set_allocated_fitradeproducttype(::std::string* fitradeproducttype);

  // optional string FISecurityProperty = 81;
  void clear_fisecurityproperty();
  static const int kFISecurityPropertyFieldNumber = 81;
  const ::std::string& fisecurityproperty() const;
  void set_fisecurityproperty(const ::std::string& value);
  void set_fisecurityproperty(const char* value);
  void set_fisecurityproperty(const char* value, size_t size);
  ::std::string* mutable_fisecurityproperty();
  ::std::string* release_fisecurityproperty();
  void set_allocated_fisecurityproperty(::std::string* fisecurityproperty);

  // optional string FISecurityStatus = 82;
  void clear_fisecuritystatus();
  static const int kFISecurityStatusFieldNumber = 82;
  const ::std::string& fisecuritystatus() const;
  void set_fisecuritystatus(const ::std::string& value);
  void set_fisecuritystatus(const char* value);
  void set_fisecuritystatus(const char* value, size_t size);
  ::std::string* mutable_fisecuritystatus();
  ::std::string* release_fisecuritystatus();
  void set_allocated_fisecuritystatus(::std::string* fisecuritystatus);

  // optional string FIPledgedSecurityID = 83;
  void clear_fipledgedsecurityid();
  static const int kFIPledgedSecurityIDFieldNumber = 83;
  const ::std::string& fipledgedsecurityid() const;
  void set_fipledgedsecurityid(const ::std::string& value);
  void set_fipledgedsecurityid(const char* value);
  void set_fipledgedsecurityid(const char* value, size_t size);
  ::std::string* mutable_fipledgedsecurityid();
  ::std::string* release_fipledgedsecurityid();
  void set_allocated_fipledgedsecurityid(::std::string* fipledgedsecurityid);

  // optional string FIOpenTime = 84;
  void clear_fiopentime();
  static const int kFIOpenTimeFieldNumber = 84;
  const ::std::string& fiopentime() const;
  void set_fiopentime(const ::std::string& value);
  void set_fiopentime(const char* value);
  void set_fiopentime(const char* value, size_t size);
  ::std::string* mutable_fiopentime();
  ::std::string* release_fiopentime();
  void set_allocated_fiopentime(::std::string* fiopentime);

  // optional string FICloseTime = 85;
  void clear_ficlosetime();
  static const int kFICloseTimeFieldNumber = 85;
  const ::std::string& ficlosetime() const;
  void set_ficlosetime(const ::std::string& value);
  void set_ficlosetime(const char* value);
  void set_ficlosetime(const char* value, size_t size);
  ::std::string* mutable_ficlosetime();
  ::std::string* release_ficlosetime();
  void set_allocated_ficlosetime(::std::string* ficlosetime);

  // optional string FIIssueMode = 86;
  void clear_fiissuemode();
  static const int kFIIssueModeFieldNumber = 86;
  const ::std::string& fiissuemode() const;
  void set_fiissuemode(const ::std::string& value);
  void set_fiissuemode(const char* value);
  void set_fiissuemode(const char* value, size_t size);
  ::std::string* mutable_fiissuemode();
  ::std::string* release_fiissuemode();
  void set_allocated_fiissuemode(::std::string* fiissuemode);

  // optional double FIFaceAmount = 87;
  void clear_fifaceamount();
  static const int kFIFaceAmountFieldNumber = 87;
  double fifaceamount() const;
  void set_fifaceamount(double value);

  // optional double FIIssuePrice = 88;
  void clear_fiissueprice();
  static const int kFIIssuePriceFieldNumber = 88;
  double fiissueprice() const;
  void set_fiissueprice(double value);

  // optional string FIInterestType = 89;
  void clear_fiinteresttype();
  static const int kFIInterestTypeFieldNumber = 89;
  const ::std::string& fiinteresttype() const;
  void set_fiinteresttype(const ::std::string& value);
  void set_fiinteresttype(const char* value);
  void set_fiinteresttype(const char* value, size_t size);
  ::std::string* mutable_fiinteresttype();
  ::std::string* release_fiinteresttype();
  void set_allocated_fiinteresttype(::std::string* fiinteresttype);

  // optional string FIInterestFrequency = 90;
  void clear_fiinterestfrequency();
  static const int kFIInterestFrequencyFieldNumber = 90;
  const ::std::string& fiinterestfrequency() const;
  void set_fiinterestfrequency(const ::std::string& value);
  void set_fiinterestfrequency(const char* value);
  void set_fiinterestfrequency(const char* value, size_t size);
  ::std::string* mutable_fiinterestfrequency();
  ::std::string* release_fiinterestfrequency();
  void set_allocated_fiinterestfrequency(::std::string* fiinterestfrequency);

  // optional double FIGuaranteedInterestRate = 91;
  void clear_figuaranteedinterestrate();
  static const int kFIGuaranteedInterestRateFieldNumber = 91;
  double figuaranteedinterestrate() const;
  void set_figuaranteedinterestrate(double value);

  // optional double FIBaseInterestRate = 92;
  void clear_fibaseinterestrate();
  static const int kFIBaseInterestRateFieldNumber = 92;
  double fibaseinterestrate() const;
  void set_fibaseinterestrate(double value);

  // optional double FIQuotedMargin = 93;
  void clear_fiquotedmargin();
  static const int kFIQuotedMarginFieldNumber = 93;
  double fiquotedmargin() const;
  void set_fiquotedmargin(double value);

  // optional int32 FITimeLimit = 94;
  void clear_fitimelimit();
  static const int kFITimeLimitFieldNumber = 94;
  ::google::protobuf::int32 fitimelimit() const;
  void set_fitimelimit(::google::protobuf::int32 value);

  // optional double FITotalIssuance = 95;
  void clear_fitotalissuance();
  static const int kFITotalIssuanceFieldNumber = 95;
  double fitotalissuance() const;
  void set_fitotalissuance(double value);

  // optional string FIIssueStartDate = 96;
  void clear_fiissuestartdate();
  static const int kFIIssueStartDateFieldNumber = 96;
  const ::std::string& fiissuestartdate() const;
  void set_fiissuestartdate(const ::std::string& value);
  void set_fiissuestartdate(const char* value);
  void set_fiissuestartdate(const char* value, size_t size);
  ::std::string* mutable_fiissuestartdate();
  ::std::string* release_fiissuestartdate();
  void set_allocated_fiissuestartdate(::std::string* fiissuestartdate);

  // optional string FIIssueEndDate = 97;
  void clear_fiissueenddate();
  static const int kFIIssueEndDateFieldNumber = 97;
  const ::std::string& fiissueenddate() const;
  void set_fiissueenddate(const ::std::string& value);
  void set_fiissueenddate(const char* value);
  void set_fiissueenddate(const char* value, size_t size);
  ::std::string* mutable_fiissueenddate();
  ::std::string* release_fiissueenddate();
  void set_allocated_fiissueenddate(::std::string* fiissueenddate);

  // optional string FIListDate = 98;
  void clear_filistdate();
  static const int kFIListDateFieldNumber = 98;
  const ::std::string& filistdate() const;
  void set_filistdate(const ::std::string& value);
  void set_filistdate(const char* value);
  void set_filistdate(const char* value, size_t size);
  ::std::string* mutable_filistdate();
  ::std::string* release_filistdate();
  void set_allocated_filistdate(::std::string* filistdate);

  // optional string FIExpireDate = 99;
  void clear_fiexpiredate();
  static const int kFIExpireDateFieldNumber = 99;
  const ::std::string& fiexpiredate() const;
  void set_fiexpiredate(const ::std::string& value);
  void set_fiexpiredate(const char* value);
  void set_fiexpiredate(const char* value, size_t size);
  ::std::string* mutable_fiexpiredate();
  ::std::string* release_fiexpiredate();
  void set_allocated_fiexpiredate(::std::string* fiexpiredate);

  // optional string FINationalDebtType = 100;
  void clear_finationaldebttype();
  static const int kFINationalDebtTypeFieldNumber = 100;
  const ::std::string& finationaldebttype() const;
  void set_finationaldebttype(const ::std::string& value);
  void set_finationaldebttype(const char* value);
  void set_finationaldebttype(const char* value, size_t size);
  ::std::string* mutable_finationaldebttype();
  ::std::string* release_finationaldebttype();
  void set_allocated_finationaldebttype(::std::string* finationaldebttype);

  // optional string FIIssueMethod = 101;
  void clear_fiissuemethod();
  static const int kFIIssueMethodFieldNumber = 101;
  const ::std::string& fiissuemethod() const;
  void set_fiissuemethod(const ::std::string& value);
  void set_fiissuemethod(const char* value);
  void set_fiissuemethod(const char* value, size_t size);
  ::std::string* mutable_fiissuemethod();
  ::std::string* release_fiissuemethod();
  void set_allocated_fiissuemethod(::std::string* fiissuemethod);

  // optional bool FICrossMarket = 102;
  void clear_ficrossmarket();
  static const int kFICrossMarketFieldNumber = 102;
  bool ficrossmarket() const;
  void set_ficrossmarket(bool value);

  // optional bool FIShortSellFlag = 103;
  void clear_fishortsellflag();
  static const int kFIShortSellFlagFieldNumber = 103;
  bool fishortsellflag() const;
  void set_fishortsellflag(bool value);

  // optional double FITotalShortSellQuota = 104;
  void clear_fitotalshortsellquota();
  static const int kFITotalShortSellQuotaFieldNumber = 104;
  double fitotalshortsellquota() const;
  void set_fitotalshortsellquota(double value);

  // optional double FIDealerShortSellQuota = 105;
  void clear_fidealershortsellquota();
  static const int kFIDealerShortSellQuotaFieldNumber = 105;
  double fidealershortsellquota() const;
  void set_fidealershortsellquota(double value);

  // optional double FIPreClosePx = 106;
  void clear_fipreclosepx();
  static const int kFIPreClosePxFieldNumber = 106;
  double fipreclosepx() const;
  void set_fipreclosepx(double value);

  // optional double FIPreWeightedPx = 107;
  void clear_fipreweightedpx();
  static const int kFIPreWeightedPxFieldNumber = 107;
  double fipreweightedpx() const;
  void set_fipreweightedpx(double value);

  // optional string OptionListType = 110;
  void clear_optionlisttype();
  static const int kOptionListTypeFieldNumber = 110;
  const ::std::string& optionlisttype() const;
  void set_optionlisttype(const ::std::string& value);
  void set_optionlisttype(const char* value);
  void set_optionlisttype(const char* value, size_t size);
  ::std::string* mutable_optionlisttype();
  ::std::string* release_optionlisttype();
  void set_allocated_optionlisttype(::std::string* optionlisttype);

  // optional string OptionDeliveryType = 111;
  void clear_optiondeliverytype();
  static const int kOptionDeliveryTypeFieldNumber = 111;
  const ::std::string& optiondeliverytype() const;
  void set_optiondeliverytype(const ::std::string& value);
  void set_optiondeliverytype(const char* value);
  void set_optiondeliverytype(const char* value, size_t size);
  ::std::string* mutable_optiondeliverytype();
  ::std::string* release_optiondeliverytype();
  void set_allocated_optiondeliverytype(::std::string* optiondeliverytype);

  // optional int32 OptionAdjustTimes = 112;
  void clear_optionadjusttimes();
  static const int kOptionAdjustTimesFieldNumber = 112;
  ::google::protobuf::int32 optionadjusttimes() const;
  void set_optionadjusttimes(::google::protobuf::int32 value);

  // optional int64 OptionContractPosition = 113;
  void clear_optioncontractposition();
  static const int kOptionContractPositionFieldNumber = 113;
  ::google::protobuf::int64 optioncontractposition() const;
  void set_optioncontractposition(::google::protobuf::int64 value);

  // optional int64 OptionBuyQtyUpperLimit = 114;
  void clear_optionbuyqtyupperlimit();
  static const int kOptionBuyQtyUpperLimitFieldNumber = 114;
  ::google::protobuf::int64 optionbuyqtyupperlimit() const;
  void set_optionbuyqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionSellQtyUpperLimit = 115;
  void clear_optionsellqtyupperlimit();
  static const int kOptionSellQtyUpperLimitFieldNumber = 115;
  ::google::protobuf::int64 optionsellqtyupperlimit() const;
  void set_optionsellqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
  void clear_optionmarketorderbuyqtyupperlimit();
  static const int kOptionMarketOrderBuyQtyUpperLimitFieldNumber = 116;
  ::google::protobuf::int64 optionmarketorderbuyqtyupperlimit() const;
  void set_optionmarketorderbuyqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
  void clear_optionmarketordersellqtyupperlimit();
  static const int kOptionMarketOrderSellQtyUpperLimitFieldNumber = 117;
  ::google::protobuf::int64 optionmarketordersellqtyupperlimit() const;
  void set_optionmarketordersellqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
  void clear_optionquoteorderbuyqtyupperlimit();
  static const int kOptionQuoteOrderBuyQtyUpperLimitFieldNumber = 118;
  ::google::protobuf::int64 optionquoteorderbuyqtyupperlimit() const;
  void set_optionquoteorderbuyqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
  void clear_optionquoteordersellqtyupperlimit();
  static const int kOptionQuoteOrderSellQtyUpperLimitFieldNumber = 119;
  ::google::protobuf::int64 optionquoteordersellqtyupperlimit() const;
  void set_optionquoteordersellqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 OptionBuyQtyUnit = 120;
  void clear_optionbuyqtyunit();
  static const int kOptionBuyQtyUnitFieldNumber = 120;
  ::google::protobuf::int64 optionbuyqtyunit() const;
  void set_optionbuyqtyunit(::google::protobuf::int64 value);

  // optional int64 OptionSellQtyUnit = 121;
  void clear_optionsellqtyunit();
  static const int kOptionSellQtyUnitFieldNumber = 121;
  ::google::protobuf::int64 optionsellqtyunit() const;
  void set_optionsellqtyunit(::google::protobuf::int64 value);

  // optional double OptionLastSellMargin = 122;
  void clear_optionlastsellmargin();
  static const int kOptionLastSellMarginFieldNumber = 122;
  double optionlastsellmargin() const;
  void set_optionlastsellmargin(double value);

  // optional double OptionSellMargin = 123;
  void clear_optionsellmargin();
  static const int kOptionSellMarginFieldNumber = 123;
  double optionsellmargin() const;
  void set_optionsellmargin(double value);

  // optional string OptionMarketMakerFlag = 124;
  void clear_optionmarketmakerflag();
  static const int kOptionMarketMakerFlagFieldNumber = 124;
  const ::std::string& optionmarketmakerflag() const;
  void set_optionmarketmakerflag(const ::std::string& value);
  void set_optionmarketmakerflag(const char* value);
  void set_optionmarketmakerflag(const char* value, size_t size);
  ::std::string* mutable_optionmarketmakerflag();
  ::std::string* release_optionmarketmakerflag();
  void set_allocated_optionmarketmakerflag(::std::string* optionmarketmakerflag);

  // optional string OptionCombinationStrategy = 125;
  void clear_optioncombinationstrategy();
  static const int kOptionCombinationStrategyFieldNumber = 125;
  const ::std::string& optioncombinationstrategy() const;
  void set_optioncombinationstrategy(const ::std::string& value);
  void set_optioncombinationstrategy(const char* value);
  void set_optioncombinationstrategy(const char* value, size_t size);
  ::std::string* mutable_optioncombinationstrategy();
  ::std::string* release_optioncombinationstrategy();
  void set_allocated_optioncombinationstrategy(::std::string* optioncombinationstrategy);

  // optional string DeliveryYear = 126;
  void clear_deliveryyear();
  static const int kDeliveryYearFieldNumber = 126;
  const ::std::string& deliveryyear() const;
  void set_deliveryyear(const ::std::string& value);
  void set_deliveryyear(const char* value);
  void set_deliveryyear(const char* value, size_t size);
  ::std::string* mutable_deliveryyear();
  ::std::string* release_deliveryyear();
  void set_allocated_deliveryyear(::std::string* deliveryyear);

  // optional string DeliveryMonth = 127;
  void clear_deliverymonth();
  static const int kDeliveryMonthFieldNumber = 127;
  const ::std::string& deliverymonth() const;
  void set_deliverymonth(const ::std::string& value);
  void set_deliverymonth(const char* value);
  void set_deliverymonth(const char* value, size_t size);
  ::std::string* mutable_deliverymonth();
  ::std::string* release_deliverymonth();
  void set_allocated_deliverymonth(::std::string* deliverymonth);

  // optional string InstrumentID = 128;
  void clear_instrumentid();
  static const int kInstrumentIDFieldNumber = 128;
  const ::std::string& instrumentid() const;
  void set_instrumentid(const ::std::string& value);
  void set_instrumentid(const char* value);
  void set_instrumentid(const char* value, size_t size);
  ::std::string* mutable_instrumentid();
  ::std::string* release_instrumentid();
  void set_allocated_instrumentid(::std::string* instrumentid);

  // optional string InstrumentName = 129;
  void clear_instrumentname();
  static const int kInstrumentNameFieldNumber = 129;
  const ::std::string& instrumentname() const;
  void set_instrumentname(const ::std::string& value);
  void set_instrumentname(const char* value);
  void set_instrumentname(const char* value, size_t size);
  ::std::string* mutable_instrumentname();
  ::std::string* release_instrumentname();
  void set_allocated_instrumentname(::std::string* instrumentname);

  // optional string ExchangeInstID = 130;
  void clear_exchangeinstid();
  static const int kExchangeInstIDFieldNumber = 130;
  const ::std::string& exchangeinstid() const;
  void set_exchangeinstid(const ::std::string& value);
  void set_exchangeinstid(const char* value);
  void set_exchangeinstid(const char* value, size_t size);
  ::std::string* mutable_exchangeinstid();
  ::std::string* release_exchangeinstid();
  void set_allocated_exchangeinstid(::std::string* exchangeinstid);

  // optional string ProductID = 131;
  void clear_productid();
  static const int kProductIDFieldNumber = 131;
  const ::std::string& productid() const;
  void set_productid(const ::std::string& value);
  void set_productid(const char* value);
  void set_productid(const char* value, size_t size);
  ::std::string* mutable_productid();
  ::std::string* release_productid();
  void set_allocated_productid(::std::string* productid);

  // optional int64 MaxMarketOrderVolume = 132;
  void clear_maxmarketordervolume();
  static const int kMaxMarketOrderVolumeFieldNumber = 132;
  ::google::protobuf::int64 maxmarketordervolume() const;
  void set_maxmarketordervolume(::google::protobuf::int64 value);

  // optional int64 MinMarketOrderVolume = 133;
  void clear_minmarketordervolume();
  static const int kMinMarketOrderVolumeFieldNumber = 133;
  ::google::protobuf::int64 minmarketordervolume() const;
  void set_minmarketordervolume(::google::protobuf::int64 value);

  // optional int64 MaxLimitOrderVolume = 134;
  void clear_maxlimitordervolume();
  static const int kMaxLimitOrderVolumeFieldNumber = 134;
  ::google::protobuf::int64 maxlimitordervolume() const;
  void set_maxlimitordervolume(::google::protobuf::int64 value);

  // optional int64 MinLimitOrderVolume = 135;
  void clear_minlimitordervolume();
  static const int kMinLimitOrderVolumeFieldNumber = 135;
  ::google::protobuf::int64 minlimitordervolume() const;
  void set_minlimitordervolume(::google::protobuf::int64 value);

  // optional int64 VolumeMultiple = 136;
  void clear_volumemultiple();
  static const int kVolumeMultipleFieldNumber = 136;
  ::google::protobuf::int64 volumemultiple() const;
  void set_volumemultiple(::google::protobuf::int64 value);

  // optional string CreateDate = 137;
  void clear_createdate();
  static const int kCreateDateFieldNumber = 137;
  const ::std::string& createdate() const;
  void set_createdate(const ::std::string& value);
  void set_createdate(const char* value);
  void set_createdate(const char* value, size_t size);
  ::std::string* mutable_createdate();
  ::std::string* release_createdate();
  void set_allocated_createdate(::std::string* createdate);

  // optional string ExpireDate = 138;
  void clear_expiredate();
  static const int kExpireDateFieldNumber = 138;
  const ::std::string& expiredate() const;
  void set_expiredate(const ::std::string& value);
  void set_expiredate(const char* value);
  void set_expiredate(const char* value, size_t size);
  ::std::string* mutable_expiredate();
  ::std::string* release_expiredate();
  void set_allocated_expiredate(::std::string* expiredate);

  // optional string StartDelivDate = 139;
  void clear_startdelivdate();
  static const int kStartDelivDateFieldNumber = 139;
  const ::std::string& startdelivdate() const;
  void set_startdelivdate(const ::std::string& value);
  void set_startdelivdate(const char* value);
  void set_startdelivdate(const char* value, size_t size);
  ::std::string* mutable_startdelivdate();
  ::std::string* release_startdelivdate();
  void set_allocated_startdelivdate(::std::string* startdelivdate);

  // optional string EndDelivDate = 140;
  void clear_enddelivdate();
  static const int kEndDelivDateFieldNumber = 140;
  const ::std::string& enddelivdate() const;
  void set_enddelivdate(const ::std::string& value);
  void set_enddelivdate(const char* value);
  void set_enddelivdate(const char* value, size_t size);
  ::std::string* mutable_enddelivdate();
  ::std::string* release_enddelivdate();
  void set_allocated_enddelivdate(::std::string* enddelivdate);

  // optional string PositionType = 141;
  void clear_positiontype();
  static const int kPositionTypeFieldNumber = 141;
  const ::std::string& positiontype() const;
  void set_positiontype(const ::std::string& value);
  void set_positiontype(const char* value);
  void set_positiontype(const char* value, size_t size);
  ::std::string* mutable_positiontype();
  ::std::string* release_positiontype();
  void set_allocated_positiontype(::std::string* positiontype);

  // optional double LongMarginRatio = 142;
  void clear_longmarginratio();
  static const int kLongMarginRatioFieldNumber = 142;
  double longmarginratio() const;
  void set_longmarginratio(double value);

  // optional double ShortMarginRatio = 143;
  void clear_shortmarginratio();
  static const int kShortMarginRatioFieldNumber = 143;
  double shortmarginratio() const;
  void set_shortmarginratio(double value);

  // optional string MaxMarginSideAlgorithm = 144;
  void clear_maxmarginsidealgorithm();
  static const int kMaxMarginSideAlgorithmFieldNumber = 144;
  const ::std::string& maxmarginsidealgorithm() const;
  void set_maxmarginsidealgorithm(const ::std::string& value);
  void set_maxmarginsidealgorithm(const char* value);
  void set_maxmarginsidealgorithm(const char* value, size_t size);
  ::std::string* mutable_maxmarginsidealgorithm();
  ::std::string* release_maxmarginsidealgorithm();
  void set_allocated_maxmarginsidealgorithm(::std::string* maxmarginsidealgorithm);

  // optional double StrikePrice = 145;
  void clear_strikeprice();
  static const int kStrikePriceFieldNumber = 145;
  double strikeprice() const;
  void set_strikeprice(double value);

  // optional double PreOpenInterest = 146;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 146;
  double preopeninterest() const;
  void set_preopeninterest(double value);

  // optional string FormerSymbol = 147;
  void clear_formersymbol();
  static const int kFormerSymbolFieldNumber = 147;
  const ::std::string& formersymbol() const;
  void set_formersymbol(const ::std::string& value);
  void set_formersymbol(const char* value);
  void set_formersymbol(const char* value, size_t size);
  ::std::string* mutable_formersymbol();
  ::std::string* release_formersymbol();
  void set_allocated_formersymbol(::std::string* formersymbol);

  // optional string DelistDate = 148;
  void clear_delistdate();
  static const int kDelistDateFieldNumber = 148;
  const ::std::string& delistdate() const;
  void set_delistdate(const ::std::string& value);
  void set_delistdate(const char* value);
  void set_delistdate(const char* value, size_t size);
  ::std::string* mutable_delistdate();
  ::std::string* release_delistdate();
  void set_allocated_delistdate(::std::string* delistdate);

  // optional int64 BuyQtyUnit = 149;
  void clear_buyqtyunit();
  static const int kBuyQtyUnitFieldNumber = 149;
  ::google::protobuf::int64 buyqtyunit() const;
  void set_buyqtyunit(::google::protobuf::int64 value);

  // optional int64 SellQtyUnit = 150;
  void clear_sellqtyunit();
  static const int kSellQtyUnitFieldNumber = 150;
  ::google::protobuf::int64 sellqtyunit() const;
  void set_sellqtyunit(::google::protobuf::int64 value);

  // optional int64 BuyQtyUpperLimit = 161;
  void clear_buyqtyupperlimit();
  static const int kBuyQtyUpperLimitFieldNumber = 161;
  ::google::protobuf::int64 buyqtyupperlimit() const;
  void set_buyqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 SellQtyUpperLimit = 162;
  void clear_sellqtyupperlimit();
  static const int kSellQtyUpperLimitFieldNumber = 162;
  ::google::protobuf::int64 sellqtyupperlimit() const;
  void set_sellqtyupperlimit(::google::protobuf::int64 value);

  // optional int64 BuyQtyLowerLimit = 163;
  void clear_buyqtylowerlimit();
  static const int kBuyQtyLowerLimitFieldNumber = 163;
  ::google::protobuf::int64 buyqtylowerlimit() const;
  void set_buyqtylowerlimit(::google::protobuf::int64 value);

  // optional int64 SellQtyLowerLimit = 164;
  void clear_sellqtylowerlimit();
  static const int kSellQtyLowerLimitFieldNumber = 164;
  ::google::protobuf::int64 sellqtylowerlimit() const;
  void set_sellqtylowerlimit(::google::protobuf::int64 value);

  // optional int32 VCMFlag = 165;
  void clear_vcmflag();
  static const int kVCMFlagFieldNumber = 165;
  ::google::protobuf::int32 vcmflag() const;
  void set_vcmflag(::google::protobuf::int32 value);

  // optional int32 CASFlag = 166;
  void clear_casflag();
  static const int kCASFlagFieldNumber = 166;
  ::google::protobuf::int32 casflag() const;
  void set_casflag(::google::protobuf::int32 value);

  // optional int32 POSFlag = 167;
  void clear_posflag();
  static const int kPOSFlagFieldNumber = 167;
  ::google::protobuf::int32 posflag() const;
  void set_posflag(::google::protobuf::int32 value);

  // optional double POSUpperLimitPx = 168;
  void clear_posupperlimitpx();
  static const int kPOSUpperLimitPxFieldNumber = 168;
  double posupperlimitpx() const;
  void set_posupperlimitpx(double value);

  // optional double POSLowerLimitPx = 169;
  void clear_poslowerlimitpx();
  static const int kPOSLowerLimitPxFieldNumber = 169;
  double poslowerlimitpx() const;
  void set_poslowerlimitpx(double value);

  // optional string BaseContractID = 170;
  void clear_basecontractid();
  static const int kBaseContractIDFieldNumber = 170;
  const ::std::string& basecontractid() const;
  void set_basecontractid(const ::std::string& value);
  void set_basecontractid(const char* value);
  void set_basecontractid(const char* value, size_t size);
  ::std::string* mutable_basecontractid();
  ::std::string* release_basecontractid();
  void set_allocated_basecontractid(::std::string* basecontractid);

  // repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
  int constantparams_size() const;
  void clear_constantparams();
  static const int kConstantParamsFieldNumber = 171;
  const ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam& constantparams(int index) const;
  ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* mutable_constantparams(int index);
  ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* add_constantparams();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >*
      mutable_constantparams();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >&
      constantparams() const;

  // optional int32 DataMultiplePowerOf10 = 172;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 172;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string InterestAccrualDate = 173;
  void clear_interestaccrualdate();
  static const int kInterestAccrualDateFieldNumber = 173;
  const ::std::string& interestaccrualdate() const;
  void set_interestaccrualdate(const ::std::string& value);
  void set_interestaccrualdate(const char* value);
  void set_interestaccrualdate(const char* value, size_t size);
  ::std::string* mutable_interestaccrualdate();
  ::std::string* release_interestaccrualdate();
  void set_allocated_interestaccrualdate(::std::string* interestaccrualdate);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDBasicInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam > constantparams_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securityid_;
  ::google::protobuf::internal::ArenaStringPtr symbol_;
  ::google::protobuf::internal::ArenaStringPtr chispelling_;
  ::google::protobuf::internal::ArenaStringPtr englishname_;
  ::google::protobuf::internal::ArenaStringPtr securitysubtype_;
  ::google::protobuf::internal::ArenaStringPtr listdate_;
  ::google::protobuf::internal::ArenaStringPtr currency_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr exchangedate_;
  ::google::protobuf::internal::ArenaStringPtr exchangesymbol_;
  ::google::protobuf::internal::ArenaStringPtr hkspreadtablecode_;
  ::google::protobuf::internal::ArenaStringPtr optioncontractid_;
  ::google::protobuf::internal::ArenaStringPtr optioncontractsymbol_;
  ::google::protobuf::internal::ArenaStringPtr optionunderlyingsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr optionunderlyingsymbol_;
  ::google::protobuf::internal::ArenaStringPtr optionunderlyingtype_;
  ::google::protobuf::internal::ArenaStringPtr optionoptiontype_;
  ::google::protobuf::internal::ArenaStringPtr optioncallorput_;
  ::google::protobuf::internal::ArenaStringPtr optionstartdate_;
  ::google::protobuf::internal::ArenaStringPtr optionenddate_;
  ::google::protobuf::internal::ArenaStringPtr optionexercisedate_;
  ::google::protobuf::internal::ArenaStringPtr optiondeliverydate_;
  ::google::protobuf::internal::ArenaStringPtr optionexpiredate_;
  ::google::protobuf::internal::ArenaStringPtr optionupdateversion_;
  ::google::protobuf::internal::ArenaStringPtr optionpricelimittype_;
  ::google::protobuf::internal::ArenaStringPtr optionsecuritystatusflag_;
  ::google::protobuf::internal::ArenaStringPtr optioncarryinterestdate_;
  ::google::protobuf::internal::ArenaStringPtr optionearlyexpiredate_;
  ::google::protobuf::internal::ArenaStringPtr optionstrategysecurityid_;
  ::google::protobuf::internal::ArenaStringPtr fitradeproducttype_;
  ::google::protobuf::internal::ArenaStringPtr fisecurityproperty_;
  ::google::protobuf::internal::ArenaStringPtr fisecuritystatus_;
  ::google::protobuf::internal::ArenaStringPtr fipledgedsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr fiopentime_;
  ::google::protobuf::internal::ArenaStringPtr ficlosetime_;
  ::google::protobuf::internal::ArenaStringPtr fiissuemode_;
  ::google::protobuf::internal::ArenaStringPtr fiinteresttype_;
  ::google::protobuf::internal::ArenaStringPtr fiinterestfrequency_;
  ::google::protobuf::internal::ArenaStringPtr fiissuestartdate_;
  ::google::protobuf::internal::ArenaStringPtr fiissueenddate_;
  ::google::protobuf::internal::ArenaStringPtr filistdate_;
  ::google::protobuf::internal::ArenaStringPtr fiexpiredate_;
  ::google::protobuf::internal::ArenaStringPtr finationaldebttype_;
  ::google::protobuf::internal::ArenaStringPtr fiissuemethod_;
  ::google::protobuf::internal::ArenaStringPtr optionlisttype_;
  ::google::protobuf::internal::ArenaStringPtr optiondeliverytype_;
  ::google::protobuf::internal::ArenaStringPtr optionmarketmakerflag_;
  ::google::protobuf::internal::ArenaStringPtr optioncombinationstrategy_;
  ::google::protobuf::internal::ArenaStringPtr deliveryyear_;
  ::google::protobuf::internal::ArenaStringPtr deliverymonth_;
  ::google::protobuf::internal::ArenaStringPtr instrumentid_;
  ::google::protobuf::internal::ArenaStringPtr instrumentname_;
  ::google::protobuf::internal::ArenaStringPtr exchangeinstid_;
  ::google::protobuf::internal::ArenaStringPtr productid_;
  ::google::protobuf::internal::ArenaStringPtr createdate_;
  ::google::protobuf::internal::ArenaStringPtr expiredate_;
  ::google::protobuf::internal::ArenaStringPtr startdelivdate_;
  ::google::protobuf::internal::ArenaStringPtr enddelivdate_;
  ::google::protobuf::internal::ArenaStringPtr positiontype_;
  ::google::protobuf::internal::ArenaStringPtr maxmarginsidealgorithm_;
  ::google::protobuf::internal::ArenaStringPtr formersymbol_;
  ::google::protobuf::internal::ArenaStringPtr delistdate_;
  ::google::protobuf::internal::ArenaStringPtr basecontractid_;
  ::google::protobuf::internal::ArenaStringPtr interestaccrualdate_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 outstandingshare_;
  ::google::protobuf::int64 publicfloatsharequantity_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 lotsize_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 loanmarginindicator_;
  double ticksize_;
  ::google::protobuf::int32 pxaccuracy_;
  ::google::protobuf::int32 ipoprofitable_;
  double presettlepx_;
  ::google::protobuf::int32 diffrightsindicator_;
  ::google::protobuf::int32 shhkconnect_;
  double preiopv_;
  ::google::protobuf::int64 optioncontractmultiplierunit_;
  double optionexerciseprice_;
  ::google::protobuf::int64 optiontotallongposition_;
  double optionsecurityclosepx_;
  double optionsettlprice_;
  double optionunderlyingclosepx_;
  double optiondailypriceuplimit_;
  double optiondailypricedownlimit_;
  double optionmarginunit_;
  double optionmarginratioparam1_;
  double optionmarginratioparam2_;
  ::google::protobuf::int64 optionroundlot_;
  ::google::protobuf::int32 szhkconnect_;
  bool shortsellflag_;
  bool ficrossmarket_;
  bool fishortsellflag_;
  ::google::protobuf::int64 optionlmtordminfloor_;
  ::google::protobuf::int64 optionlmtordmaxfloor_;
  ::google::protobuf::int64 optionmktordminfloor_;
  ::google::protobuf::int64 optionmktordmaxfloor_;
  double optionticksize_;
  double fifaceamount_;
  double fiissueprice_;
  double figuaranteedinterestrate_;
  double fibaseinterestrate_;
  double fiquotedmargin_;
  double fitotalissuance_;
  ::google::protobuf::int32 fitimelimit_;
  ::google::protobuf::int32 optionadjusttimes_;
  double fitotalshortsellquota_;
  double fidealershortsellquota_;
  double fipreclosepx_;
  double fipreweightedpx_;
  ::google::protobuf::int64 optioncontractposition_;
  ::google::protobuf::int64 optionbuyqtyupperlimit_;
  ::google::protobuf::int64 optionsellqtyupperlimit_;
  ::google::protobuf::int64 optionmarketorderbuyqtyupperlimit_;
  ::google::protobuf::int64 optionmarketordersellqtyupperlimit_;
  ::google::protobuf::int64 optionquoteorderbuyqtyupperlimit_;
  ::google::protobuf::int64 optionquoteordersellqtyupperlimit_;
  ::google::protobuf::int64 optionbuyqtyunit_;
  ::google::protobuf::int64 optionsellqtyunit_;
  double optionlastsellmargin_;
  double optionsellmargin_;
  ::google::protobuf::int64 maxmarketordervolume_;
  ::google::protobuf::int64 minmarketordervolume_;
  ::google::protobuf::int64 maxlimitordervolume_;
  ::google::protobuf::int64 minlimitordervolume_;
  ::google::protobuf::int64 volumemultiple_;
  double longmarginratio_;
  double shortmarginratio_;
  double strikeprice_;
  double preopeninterest_;
  ::google::protobuf::int64 buyqtyunit_;
  ::google::protobuf::int64 sellqtyunit_;
  ::google::protobuf::int64 buyqtyupperlimit_;
  ::google::protobuf::int64 sellqtyupperlimit_;
  ::google::protobuf::int64 buyqtylowerlimit_;
  ::google::protobuf::int64 sellqtylowerlimit_;
  ::google::protobuf::int32 vcmflag_;
  ::google::protobuf::int32 casflag_;
  double posupperlimitpx_;
  double poslowerlimitpx_;
  ::google::protobuf::int32 posflag_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDBasicInfo_2eproto_impl();
  friend void  protobuf_AddDesc_MDBasicInfo_2eproto_impl();
  friend void protobuf_AssignDesc_MDBasicInfo_2eproto();
  friend void protobuf_ShutdownFile_MDBasicInfo_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDBasicInfo> MDBasicInfo_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDBasicInfo_ConstantParam

// optional string ParamName = 1;
inline void MDBasicInfo_ConstantParam::clear_paramname() {
  paramname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo_ConstantParam::paramname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  return paramname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo_ConstantParam::set_paramname(const ::std::string& value) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
inline void MDBasicInfo_ConstantParam::set_paramname(const char* value) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
inline void MDBasicInfo_ConstantParam::set_paramname(const char* value, size_t size) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
inline ::std::string* MDBasicInfo_ConstantParam::mutable_paramname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  return paramname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo_ConstantParam::release_paramname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  
  return paramname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo_ConstantParam::set_allocated_paramname(::std::string* paramname) {
  if (paramname != NULL) {
    
  } else {
    
  }
  paramname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}

// optional string ParamValue = 2;
inline void MDBasicInfo_ConstantParam::clear_paramvalue() {
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo_ConstantParam::paramvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  return paramvalue_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo_ConstantParam::set_paramvalue(const ::std::string& value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
inline void MDBasicInfo_ConstantParam::set_paramvalue(const char* value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
inline void MDBasicInfo_ConstantParam::set_paramvalue(const char* value, size_t size) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
inline ::std::string* MDBasicInfo_ConstantParam::mutable_paramvalue() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  return paramvalue_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo_ConstantParam::release_paramvalue() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  
  return paramvalue_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo_ConstantParam::set_allocated_paramvalue(::std::string* paramvalue) {
  if (paramvalue != NULL) {
    
  } else {
    
  }
  paramvalue_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramvalue);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}

inline const MDBasicInfo_ConstantParam* MDBasicInfo_ConstantParam::internal_default_instance() {
  return &MDBasicInfo_ConstantParam_default_instance_.get();
}
// -------------------------------------------------------------------

// MDBasicInfo

// optional string HTSCSecurityID = 1;
inline void MDBasicInfo::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
inline void MDBasicInfo::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
inline void MDBasicInfo::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
inline ::std::string* MDBasicInfo::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}

// optional string SecurityID = 2;
inline void MDBasicInfo::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
inline void MDBasicInfo::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
inline void MDBasicInfo::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
inline ::std::string* MDBasicInfo::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}

// optional string Symbol = 3;
inline void MDBasicInfo::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
inline void MDBasicInfo::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
inline void MDBasicInfo::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
inline ::std::string* MDBasicInfo::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}

// optional string ChiSpelling = 4;
inline void MDBasicInfo::clear_chispelling() {
  chispelling_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::chispelling() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  return chispelling_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_chispelling(const ::std::string& value) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
inline void MDBasicInfo::set_chispelling(const char* value) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
inline void MDBasicInfo::set_chispelling(const char* value, size_t size) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
inline ::std::string* MDBasicInfo::mutable_chispelling() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  return chispelling_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_chispelling() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  
  return chispelling_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_chispelling(::std::string* chispelling) {
  if (chispelling != NULL) {
    
  } else {
    
  }
  chispelling_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), chispelling);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}

// optional string EnglishName = 5;
inline void MDBasicInfo::clear_englishname() {
  englishname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::englishname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  return englishname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_englishname(const ::std::string& value) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
inline void MDBasicInfo::set_englishname(const char* value) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
inline void MDBasicInfo::set_englishname(const char* value, size_t size) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
inline ::std::string* MDBasicInfo::mutable_englishname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  return englishname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_englishname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  
  return englishname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_englishname(::std::string* englishname) {
  if (englishname != NULL) {
    
  } else {
    
  }
  englishname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), englishname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDBasicInfo::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDBasicInfo::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDBasicInfo::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDBasicInfo::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDBasicInfo::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDBasicInfo::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.securityType)
}

// optional string SecuritySubType = 8;
inline void MDBasicInfo::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
inline void MDBasicInfo::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
inline void MDBasicInfo::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
inline ::std::string* MDBasicInfo::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}

// optional string ListDate = 9;
inline void MDBasicInfo::clear_listdate() {
  listdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::listdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  return listdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_listdate(const ::std::string& value) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
inline void MDBasicInfo::set_listdate(const char* value) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
inline void MDBasicInfo::set_listdate(const char* value, size_t size) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
inline ::std::string* MDBasicInfo::mutable_listdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  return listdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_listdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  
  return listdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_listdate(::std::string* listdate) {
  if (listdate != NULL) {
    
  } else {
    
  }
  listdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), listdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}

// optional string Currency = 10;
inline void MDBasicInfo::clear_currency() {
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::currency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  return currency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_currency(const ::std::string& value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
inline void MDBasicInfo::set_currency(const char* value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
inline void MDBasicInfo::set_currency(const char* value, size_t size) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
inline ::std::string* MDBasicInfo::mutable_currency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  return currency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_currency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  
  return currency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_currency(::std::string* currency) {
  if (currency != NULL) {
    
  } else {
    
  }
  currency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}

// optional int64 OutstandingShare = 11;
inline void MDBasicInfo::clear_outstandingshare() {
  outstandingshare_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::outstandingshare() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OutstandingShare)
  return outstandingshare_;
}
inline void MDBasicInfo::set_outstandingshare(::google::protobuf::int64 value) {
  
  outstandingshare_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OutstandingShare)
}

// optional int64 PublicFloatShareQuantity = 12;
inline void MDBasicInfo::clear_publicfloatsharequantity() {
  publicfloatsharequantity_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::publicfloatsharequantity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PublicFloatShareQuantity)
  return publicfloatsharequantity_;
}
inline void MDBasicInfo::set_publicfloatsharequantity(::google::protobuf::int64 value) {
  
  publicfloatsharequantity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PublicFloatShareQuantity)
}

// optional int32 MDDate = 13;
inline void MDBasicInfo::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MDDate)
  return mddate_;
}
inline void MDBasicInfo::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MDDate)
}

// optional string TradingPhaseCode = 14;
inline void MDBasicInfo::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
inline void MDBasicInfo::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
inline void MDBasicInfo::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
inline ::std::string* MDBasicInfo::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}

// optional int64 PreClosePx = 15;
inline void MDBasicInfo::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreClosePx)
  return preclosepx_;
}
inline void MDBasicInfo::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreClosePx)
}

// optional int64 MaxPx = 16;
inline void MDBasicInfo::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxPx)
  return maxpx_;
}
inline void MDBasicInfo::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxPx)
}

// optional int64 MinPx = 17;
inline void MDBasicInfo::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinPx)
  return minpx_;
}
inline void MDBasicInfo::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinPx)
}

// optional int64 LotSize = 18;
inline void MDBasicInfo::clear_lotsize() {
  lotsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::lotsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LotSize)
  return lotsize_;
}
inline void MDBasicInfo::set_lotsize(::google::protobuf::int64 value) {
  
  lotsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LotSize)
}

// optional bool ShortSellFlag = 19;
inline void MDBasicInfo::clear_shortsellflag() {
  shortsellflag_ = false;
}
inline bool MDBasicInfo::shortsellflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShortSellFlag)
  return shortsellflag_;
}
inline void MDBasicInfo::set_shortsellflag(bool value) {
  
  shortsellflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShortSellFlag)
}

// optional string ExchangeDate = 20;
inline void MDBasicInfo::clear_exchangedate() {
  exchangedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  return exchangedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_exchangedate(const ::std::string& value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
inline void MDBasicInfo::set_exchangedate(const char* value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
inline void MDBasicInfo::set_exchangedate(const char* value, size_t size) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
inline ::std::string* MDBasicInfo::mutable_exchangedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  return exchangedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_exchangedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  
  return exchangedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_exchangedate(::std::string* exchangedate) {
  if (exchangedate != NULL) {
    
  } else {
    
  }
  exchangedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}

// optional string ExchangeSymbol = 21;
inline void MDBasicInfo::clear_exchangesymbol() {
  exchangesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::exchangesymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  return exchangesymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_exchangesymbol(const ::std::string& value) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
inline void MDBasicInfo::set_exchangesymbol(const char* value) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
inline void MDBasicInfo::set_exchangesymbol(const char* value, size_t size) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
inline ::std::string* MDBasicInfo::mutable_exchangesymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  return exchangesymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_exchangesymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  
  return exchangesymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_exchangesymbol(::std::string* exchangesymbol) {
  if (exchangesymbol != NULL) {
    
  } else {
    
  }
  exchangesymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangesymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}

// optional double TickSize = 22;
inline void MDBasicInfo::clear_ticksize() {
  ticksize_ = 0;
}
inline double MDBasicInfo::ticksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.TickSize)
  return ticksize_;
}
inline void MDBasicInfo::set_ticksize(double value) {
  
  ticksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.TickSize)
}

// optional int32 LoanMarginIndicator = 23;
inline void MDBasicInfo::clear_loanmarginindicator() {
  loanmarginindicator_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::loanmarginindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LoanMarginIndicator)
  return loanmarginindicator_;
}
inline void MDBasicInfo::set_loanmarginindicator(::google::protobuf::int32 value) {
  
  loanmarginindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LoanMarginIndicator)
}

// optional int32 PxAccuracy = 24;
inline void MDBasicInfo::clear_pxaccuracy() {
  pxaccuracy_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::pxaccuracy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PxAccuracy)
  return pxaccuracy_;
}
inline void MDBasicInfo::set_pxaccuracy(::google::protobuf::int32 value) {
  
  pxaccuracy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PxAccuracy)
}

// optional int32 IPOProfitable = 25;
inline void MDBasicInfo::clear_ipoprofitable() {
  ipoprofitable_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::ipoprofitable() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.IPOProfitable)
  return ipoprofitable_;
}
inline void MDBasicInfo::set_ipoprofitable(::google::protobuf::int32 value) {
  
  ipoprofitable_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.IPOProfitable)
}

// optional int32 DiffRightsIndicator = 26;
inline void MDBasicInfo::clear_diffrightsindicator() {
  diffrightsindicator_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::diffrightsindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DiffRightsIndicator)
  return diffrightsindicator_;
}
inline void MDBasicInfo::set_diffrightsindicator(::google::protobuf::int32 value) {
  
  diffrightsindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DiffRightsIndicator)
}

// optional string HKSpreadTableCode = 27;
inline void MDBasicInfo::clear_hkspreadtablecode() {
  hkspreadtablecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::hkspreadtablecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  return hkspreadtablecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_hkspreadtablecode(const ::std::string& value) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
inline void MDBasicInfo::set_hkspreadtablecode(const char* value) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
inline void MDBasicInfo::set_hkspreadtablecode(const char* value, size_t size) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
inline ::std::string* MDBasicInfo::mutable_hkspreadtablecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  return hkspreadtablecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_hkspreadtablecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  
  return hkspreadtablecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_hkspreadtablecode(::std::string* hkspreadtablecode) {
  if (hkspreadtablecode != NULL) {
    
  } else {
    
  }
  hkspreadtablecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hkspreadtablecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}

// optional double PreSettlePx = 28;
inline void MDBasicInfo::clear_presettlepx() {
  presettlepx_ = 0;
}
inline double MDBasicInfo::presettlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreSettlePx)
  return presettlepx_;
}
inline void MDBasicInfo::set_presettlepx(double value) {
  
  presettlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreSettlePx)
}

// optional double PreIOPV = 29;
inline void MDBasicInfo::clear_preiopv() {
  preiopv_ = 0;
}
inline double MDBasicInfo::preiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreIOPV)
  return preiopv_;
}
inline void MDBasicInfo::set_preiopv(double value) {
  
  preiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreIOPV)
}

// optional int32 ShHkConnect = 30;
inline void MDBasicInfo::clear_shhkconnect() {
  shhkconnect_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::shhkconnect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShHkConnect)
  return shhkconnect_;
}
inline void MDBasicInfo::set_shhkconnect(::google::protobuf::int32 value) {
  
  shhkconnect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShHkConnect)
}

// optional int32 SzHkConnect = 31;
inline void MDBasicInfo::clear_szhkconnect() {
  szhkconnect_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::szhkconnect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SzHkConnect)
  return szhkconnect_;
}
inline void MDBasicInfo::set_szhkconnect(::google::protobuf::int32 value) {
  
  szhkconnect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SzHkConnect)
}

// optional string OptionContractID = 40;
inline void MDBasicInfo::clear_optioncontractid() {
  optioncontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optioncontractid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  return optioncontractid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optioncontractid(const ::std::string& value) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
inline void MDBasicInfo::set_optioncontractid(const char* value) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
inline void MDBasicInfo::set_optioncontractid(const char* value, size_t size) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
inline ::std::string* MDBasicInfo::mutable_optioncontractid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  return optioncontractid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optioncontractid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  
  return optioncontractid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optioncontractid(::std::string* optioncontractid) {
  if (optioncontractid != NULL) {
    
  } else {
    
  }
  optioncontractid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncontractid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}

// optional string OptionContractSymbol = 41;
inline void MDBasicInfo::clear_optioncontractsymbol() {
  optioncontractsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optioncontractsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  return optioncontractsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optioncontractsymbol(const ::std::string& value) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
inline void MDBasicInfo::set_optioncontractsymbol(const char* value) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
inline void MDBasicInfo::set_optioncontractsymbol(const char* value, size_t size) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
inline ::std::string* MDBasicInfo::mutable_optioncontractsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  return optioncontractsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optioncontractsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  
  return optioncontractsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optioncontractsymbol(::std::string* optioncontractsymbol) {
  if (optioncontractsymbol != NULL) {
    
  } else {
    
  }
  optioncontractsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncontractsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}

// optional string OptionUnderlyingSecurityID = 42;
inline void MDBasicInfo::clear_optionunderlyingsecurityid() {
  optionunderlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionunderlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  return optionunderlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionunderlyingsecurityid(const ::std::string& value) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
inline void MDBasicInfo::set_optionunderlyingsecurityid(const char* value) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
inline void MDBasicInfo::set_optionunderlyingsecurityid(const char* value, size_t size) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
inline ::std::string* MDBasicInfo::mutable_optionunderlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  return optionunderlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionunderlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  
  return optionunderlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionunderlyingsecurityid(::std::string* optionunderlyingsecurityid) {
  if (optionunderlyingsecurityid != NULL) {
    
  } else {
    
  }
  optionunderlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}

// optional string OptionUnderlyingSymbol = 43;
inline void MDBasicInfo::clear_optionunderlyingsymbol() {
  optionunderlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionunderlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  return optionunderlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionunderlyingsymbol(const ::std::string& value) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
inline void MDBasicInfo::set_optionunderlyingsymbol(const char* value) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
inline void MDBasicInfo::set_optionunderlyingsymbol(const char* value, size_t size) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
inline ::std::string* MDBasicInfo::mutable_optionunderlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  return optionunderlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionunderlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  
  return optionunderlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionunderlyingsymbol(::std::string* optionunderlyingsymbol) {
  if (optionunderlyingsymbol != NULL) {
    
  } else {
    
  }
  optionunderlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}

// optional string OptionUnderlyingType = 44;
inline void MDBasicInfo::clear_optionunderlyingtype() {
  optionunderlyingtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionunderlyingtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  return optionunderlyingtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionunderlyingtype(const ::std::string& value) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
inline void MDBasicInfo::set_optionunderlyingtype(const char* value) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
inline void MDBasicInfo::set_optionunderlyingtype(const char* value, size_t size) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
inline ::std::string* MDBasicInfo::mutable_optionunderlyingtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  return optionunderlyingtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionunderlyingtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  
  return optionunderlyingtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionunderlyingtype(::std::string* optionunderlyingtype) {
  if (optionunderlyingtype != NULL) {
    
  } else {
    
  }
  optionunderlyingtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}

// optional string OptionOptionType = 45;
inline void MDBasicInfo::clear_optionoptiontype() {
  optionoptiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionoptiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  return optionoptiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionoptiontype(const ::std::string& value) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
inline void MDBasicInfo::set_optionoptiontype(const char* value) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
inline void MDBasicInfo::set_optionoptiontype(const char* value, size_t size) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
inline ::std::string* MDBasicInfo::mutable_optionoptiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  return optionoptiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionoptiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  
  return optionoptiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionoptiontype(::std::string* optionoptiontype) {
  if (optionoptiontype != NULL) {
    
  } else {
    
  }
  optionoptiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionoptiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}

// optional string OptionCallOrPut = 46;
inline void MDBasicInfo::clear_optioncallorput() {
  optioncallorput_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optioncallorput() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  return optioncallorput_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optioncallorput(const ::std::string& value) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
inline void MDBasicInfo::set_optioncallorput(const char* value) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
inline void MDBasicInfo::set_optioncallorput(const char* value, size_t size) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
inline ::std::string* MDBasicInfo::mutable_optioncallorput() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  return optioncallorput_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optioncallorput() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  
  return optioncallorput_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optioncallorput(::std::string* optioncallorput) {
  if (optioncallorput != NULL) {
    
  } else {
    
  }
  optioncallorput_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncallorput);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}

// optional int64 OptionContractMultiplierUnit = 47;
inline void MDBasicInfo::clear_optioncontractmultiplierunit() {
  optioncontractmultiplierunit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optioncontractmultiplierunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractMultiplierUnit)
  return optioncontractmultiplierunit_;
}
inline void MDBasicInfo::set_optioncontractmultiplierunit(::google::protobuf::int64 value) {
  
  optioncontractmultiplierunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractMultiplierUnit)
}

// optional double OptionExercisePrice = 48;
inline void MDBasicInfo::clear_optionexerciseprice() {
  optionexerciseprice_ = 0;
}
inline double MDBasicInfo::optionexerciseprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExercisePrice)
  return optionexerciseprice_;
}
inline void MDBasicInfo::set_optionexerciseprice(double value) {
  
  optionexerciseprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExercisePrice)
}

// optional string OptionStartDate = 49;
inline void MDBasicInfo::clear_optionstartdate() {
  optionstartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionstartdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  return optionstartdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionstartdate(const ::std::string& value) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
inline void MDBasicInfo::set_optionstartdate(const char* value) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
inline void MDBasicInfo::set_optionstartdate(const char* value, size_t size) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
inline ::std::string* MDBasicInfo::mutable_optionstartdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  return optionstartdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionstartdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  
  return optionstartdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionstartdate(::std::string* optionstartdate) {
  if (optionstartdate != NULL) {
    
  } else {
    
  }
  optionstartdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionstartdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}

// optional string OptionEndDate = 50;
inline void MDBasicInfo::clear_optionenddate() {
  optionenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionenddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  return optionenddate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionenddate(const ::std::string& value) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
inline void MDBasicInfo::set_optionenddate(const char* value) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
inline void MDBasicInfo::set_optionenddate(const char* value, size_t size) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
inline ::std::string* MDBasicInfo::mutable_optionenddate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  return optionenddate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionenddate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  
  return optionenddate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionenddate(::std::string* optionenddate) {
  if (optionenddate != NULL) {
    
  } else {
    
  }
  optionenddate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionenddate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}

// optional string OptionExerciseDate = 51;
inline void MDBasicInfo::clear_optionexercisedate() {
  optionexercisedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionexercisedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  return optionexercisedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionexercisedate(const ::std::string& value) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
inline void MDBasicInfo::set_optionexercisedate(const char* value) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
inline void MDBasicInfo::set_optionexercisedate(const char* value, size_t size) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
inline ::std::string* MDBasicInfo::mutable_optionexercisedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  return optionexercisedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionexercisedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  
  return optionexercisedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionexercisedate(::std::string* optionexercisedate) {
  if (optionexercisedate != NULL) {
    
  } else {
    
  }
  optionexercisedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionexercisedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}

// optional string OptionDeliveryDate = 52;
inline void MDBasicInfo::clear_optiondeliverydate() {
  optiondeliverydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optiondeliverydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  return optiondeliverydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optiondeliverydate(const ::std::string& value) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
inline void MDBasicInfo::set_optiondeliverydate(const char* value) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
inline void MDBasicInfo::set_optiondeliverydate(const char* value, size_t size) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
inline ::std::string* MDBasicInfo::mutable_optiondeliverydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  return optiondeliverydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optiondeliverydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  
  return optiondeliverydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optiondeliverydate(::std::string* optiondeliverydate) {
  if (optiondeliverydate != NULL) {
    
  } else {
    
  }
  optiondeliverydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiondeliverydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}

// optional string OptionExpireDate = 53;
inline void MDBasicInfo::clear_optionexpiredate() {
  optionexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  return optionexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionexpiredate(const ::std::string& value) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
inline void MDBasicInfo::set_optionexpiredate(const char* value) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
inline void MDBasicInfo::set_optionexpiredate(const char* value, size_t size) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
inline ::std::string* MDBasicInfo::mutable_optionexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  return optionexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  
  return optionexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionexpiredate(::std::string* optionexpiredate) {
  if (optionexpiredate != NULL) {
    
  } else {
    
  }
  optionexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}

// optional string OptionUpdateVersion = 54;
inline void MDBasicInfo::clear_optionupdateversion() {
  optionupdateversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionupdateversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  return optionupdateversion_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionupdateversion(const ::std::string& value) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
inline void MDBasicInfo::set_optionupdateversion(const char* value) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
inline void MDBasicInfo::set_optionupdateversion(const char* value, size_t size) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
inline ::std::string* MDBasicInfo::mutable_optionupdateversion() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  return optionupdateversion_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionupdateversion() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  
  return optionupdateversion_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionupdateversion(::std::string* optionupdateversion) {
  if (optionupdateversion != NULL) {
    
  } else {
    
  }
  optionupdateversion_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionupdateversion);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}

// optional int64 OptionTotalLongPosition = 55;
inline void MDBasicInfo::clear_optiontotallongposition() {
  optiontotallongposition_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optiontotallongposition() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionTotalLongPosition)
  return optiontotallongposition_;
}
inline void MDBasicInfo::set_optiontotallongposition(::google::protobuf::int64 value) {
  
  optiontotallongposition_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionTotalLongPosition)
}

// optional double OptionSecurityClosePx = 56;
inline void MDBasicInfo::clear_optionsecurityclosepx() {
  optionsecurityclosepx_ = 0;
}
inline double MDBasicInfo::optionsecurityclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityClosePx)
  return optionsecurityclosepx_;
}
inline void MDBasicInfo::set_optionsecurityclosepx(double value) {
  
  optionsecurityclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityClosePx)
}

// optional double OptionSettlPrice = 57;
inline void MDBasicInfo::clear_optionsettlprice() {
  optionsettlprice_ = 0;
}
inline double MDBasicInfo::optionsettlprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSettlPrice)
  return optionsettlprice_;
}
inline void MDBasicInfo::set_optionsettlprice(double value) {
  
  optionsettlprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSettlPrice)
}

// optional double OptionUnderlyingClosePx = 58;
inline void MDBasicInfo::clear_optionunderlyingclosepx() {
  optionunderlyingclosepx_ = 0;
}
inline double MDBasicInfo::optionunderlyingclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingClosePx)
  return optionunderlyingclosepx_;
}
inline void MDBasicInfo::set_optionunderlyingclosepx(double value) {
  
  optionunderlyingclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingClosePx)
}

// optional string OptionPriceLimitType = 59;
inline void MDBasicInfo::clear_optionpricelimittype() {
  optionpricelimittype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionpricelimittype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  return optionpricelimittype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionpricelimittype(const ::std::string& value) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
inline void MDBasicInfo::set_optionpricelimittype(const char* value) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
inline void MDBasicInfo::set_optionpricelimittype(const char* value, size_t size) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
inline ::std::string* MDBasicInfo::mutable_optionpricelimittype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  return optionpricelimittype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionpricelimittype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  
  return optionpricelimittype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionpricelimittype(::std::string* optionpricelimittype) {
  if (optionpricelimittype != NULL) {
    
  } else {
    
  }
  optionpricelimittype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionpricelimittype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}

// optional double OptionDailyPriceUpLimit = 60;
inline void MDBasicInfo::clear_optiondailypriceuplimit() {
  optiondailypriceuplimit_ = 0;
}
inline double MDBasicInfo::optiondailypriceuplimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceUpLimit)
  return optiondailypriceuplimit_;
}
inline void MDBasicInfo::set_optiondailypriceuplimit(double value) {
  
  optiondailypriceuplimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceUpLimit)
}

// optional double OptionDailyPriceDownLimit = 61;
inline void MDBasicInfo::clear_optiondailypricedownlimit() {
  optiondailypricedownlimit_ = 0;
}
inline double MDBasicInfo::optiondailypricedownlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceDownLimit)
  return optiondailypricedownlimit_;
}
inline void MDBasicInfo::set_optiondailypricedownlimit(double value) {
  
  optiondailypricedownlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceDownLimit)
}

// optional double OptionMarginUnit = 62;
inline void MDBasicInfo::clear_optionmarginunit() {
  optionmarginunit_ = 0;
}
inline double MDBasicInfo::optionmarginunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginUnit)
  return optionmarginunit_;
}
inline void MDBasicInfo::set_optionmarginunit(double value) {
  
  optionmarginunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginUnit)
}

// optional double OptionMarginRatioParam1 = 63;
inline void MDBasicInfo::clear_optionmarginratioparam1() {
  optionmarginratioparam1_ = 0;
}
inline double MDBasicInfo::optionmarginratioparam1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam1)
  return optionmarginratioparam1_;
}
inline void MDBasicInfo::set_optionmarginratioparam1(double value) {
  
  optionmarginratioparam1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam1)
}

// optional double OptionMarginRatioParam2 = 64;
inline void MDBasicInfo::clear_optionmarginratioparam2() {
  optionmarginratioparam2_ = 0;
}
inline double MDBasicInfo::optionmarginratioparam2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam2)
  return optionmarginratioparam2_;
}
inline void MDBasicInfo::set_optionmarginratioparam2(double value) {
  
  optionmarginratioparam2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam2)
}

// optional int64 OptionRoundLot = 65;
inline void MDBasicInfo::clear_optionroundlot() {
  optionroundlot_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionroundlot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionRoundLot)
  return optionroundlot_;
}
inline void MDBasicInfo::set_optionroundlot(::google::protobuf::int64 value) {
  
  optionroundlot_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionRoundLot)
}

// optional int64 OptionLmtOrdMinFloor = 66;
inline void MDBasicInfo::clear_optionlmtordminfloor() {
  optionlmtordminfloor_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionlmtordminfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMinFloor)
  return optionlmtordminfloor_;
}
inline void MDBasicInfo::set_optionlmtordminfloor(::google::protobuf::int64 value) {
  
  optionlmtordminfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMinFloor)
}

// optional int64 OptionLmtOrdMaxFloor = 67;
inline void MDBasicInfo::clear_optionlmtordmaxfloor() {
  optionlmtordmaxfloor_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionlmtordmaxfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMaxFloor)
  return optionlmtordmaxfloor_;
}
inline void MDBasicInfo::set_optionlmtordmaxfloor(::google::protobuf::int64 value) {
  
  optionlmtordmaxfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMaxFloor)
}

// optional int64 OptionMktOrdMinFloor = 68;
inline void MDBasicInfo::clear_optionmktordminfloor() {
  optionmktordminfloor_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionmktordminfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMinFloor)
  return optionmktordminfloor_;
}
inline void MDBasicInfo::set_optionmktordminfloor(::google::protobuf::int64 value) {
  
  optionmktordminfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMinFloor)
}

// optional int64 OptionMktOrdMaxFloor = 69;
inline void MDBasicInfo::clear_optionmktordmaxfloor() {
  optionmktordmaxfloor_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionmktordmaxfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMaxFloor)
  return optionmktordmaxfloor_;
}
inline void MDBasicInfo::set_optionmktordmaxfloor(::google::protobuf::int64 value) {
  
  optionmktordmaxfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMaxFloor)
}

// optional double OptionTickSize = 70;
inline void MDBasicInfo::clear_optionticksize() {
  optionticksize_ = 0;
}
inline double MDBasicInfo::optionticksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionTickSize)
  return optionticksize_;
}
inline void MDBasicInfo::set_optionticksize(double value) {
  
  optionticksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionTickSize)
}

// optional string OptionSecurityStatusFlag = 71;
inline void MDBasicInfo::clear_optionsecuritystatusflag() {
  optionsecuritystatusflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionsecuritystatusflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  return optionsecuritystatusflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionsecuritystatusflag(const ::std::string& value) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
inline void MDBasicInfo::set_optionsecuritystatusflag(const char* value) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
inline void MDBasicInfo::set_optionsecuritystatusflag(const char* value, size_t size) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
inline ::std::string* MDBasicInfo::mutable_optionsecuritystatusflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  return optionsecuritystatusflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionsecuritystatusflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  
  return optionsecuritystatusflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionsecuritystatusflag(::std::string* optionsecuritystatusflag) {
  if (optionsecuritystatusflag != NULL) {
    
  } else {
    
  }
  optionsecuritystatusflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionsecuritystatusflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}

// optional string OptionCarryInterestDate = 72;
inline void MDBasicInfo::clear_optioncarryinterestdate() {
  optioncarryinterestdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optioncarryinterestdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  return optioncarryinterestdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optioncarryinterestdate(const ::std::string& value) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
inline void MDBasicInfo::set_optioncarryinterestdate(const char* value) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
inline void MDBasicInfo::set_optioncarryinterestdate(const char* value, size_t size) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
inline ::std::string* MDBasicInfo::mutable_optioncarryinterestdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  return optioncarryinterestdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optioncarryinterestdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  
  return optioncarryinterestdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optioncarryinterestdate(::std::string* optioncarryinterestdate) {
  if (optioncarryinterestdate != NULL) {
    
  } else {
    
  }
  optioncarryinterestdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncarryinterestdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}

// optional string OptionEarlyExpireDate = 73;
inline void MDBasicInfo::clear_optionearlyexpiredate() {
  optionearlyexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionearlyexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  return optionearlyexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionearlyexpiredate(const ::std::string& value) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
inline void MDBasicInfo::set_optionearlyexpiredate(const char* value) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
inline void MDBasicInfo::set_optionearlyexpiredate(const char* value, size_t size) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
inline ::std::string* MDBasicInfo::mutable_optionearlyexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  return optionearlyexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionearlyexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  
  return optionearlyexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionearlyexpiredate(::std::string* optionearlyexpiredate) {
  if (optionearlyexpiredate != NULL) {
    
  } else {
    
  }
  optionearlyexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionearlyexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}

// optional string OptionStrategySecurityID = 74;
inline void MDBasicInfo::clear_optionstrategysecurityid() {
  optionstrategysecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionstrategysecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  return optionstrategysecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionstrategysecurityid(const ::std::string& value) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
inline void MDBasicInfo::set_optionstrategysecurityid(const char* value) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
inline void MDBasicInfo::set_optionstrategysecurityid(const char* value, size_t size) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
inline ::std::string* MDBasicInfo::mutable_optionstrategysecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  return optionstrategysecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionstrategysecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  
  return optionstrategysecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionstrategysecurityid(::std::string* optionstrategysecurityid) {
  if (optionstrategysecurityid != NULL) {
    
  } else {
    
  }
  optionstrategysecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionstrategysecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}

// optional string FITradeProductType = 80;
inline void MDBasicInfo::clear_fitradeproducttype() {
  fitradeproducttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fitradeproducttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  return fitradeproducttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fitradeproducttype(const ::std::string& value) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
inline void MDBasicInfo::set_fitradeproducttype(const char* value) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
inline void MDBasicInfo::set_fitradeproducttype(const char* value, size_t size) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
inline ::std::string* MDBasicInfo::mutable_fitradeproducttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  return fitradeproducttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fitradeproducttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  
  return fitradeproducttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fitradeproducttype(::std::string* fitradeproducttype) {
  if (fitradeproducttype != NULL) {
    
  } else {
    
  }
  fitradeproducttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fitradeproducttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}

// optional string FISecurityProperty = 81;
inline void MDBasicInfo::clear_fisecurityproperty() {
  fisecurityproperty_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fisecurityproperty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  return fisecurityproperty_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fisecurityproperty(const ::std::string& value) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
inline void MDBasicInfo::set_fisecurityproperty(const char* value) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
inline void MDBasicInfo::set_fisecurityproperty(const char* value, size_t size) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
inline ::std::string* MDBasicInfo::mutable_fisecurityproperty() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  return fisecurityproperty_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fisecurityproperty() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  
  return fisecurityproperty_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fisecurityproperty(::std::string* fisecurityproperty) {
  if (fisecurityproperty != NULL) {
    
  } else {
    
  }
  fisecurityproperty_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fisecurityproperty);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}

// optional string FISecurityStatus = 82;
inline void MDBasicInfo::clear_fisecuritystatus() {
  fisecuritystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fisecuritystatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  return fisecuritystatus_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fisecuritystatus(const ::std::string& value) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
inline void MDBasicInfo::set_fisecuritystatus(const char* value) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
inline void MDBasicInfo::set_fisecuritystatus(const char* value, size_t size) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
inline ::std::string* MDBasicInfo::mutable_fisecuritystatus() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  return fisecuritystatus_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fisecuritystatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  
  return fisecuritystatus_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fisecuritystatus(::std::string* fisecuritystatus) {
  if (fisecuritystatus != NULL) {
    
  } else {
    
  }
  fisecuritystatus_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fisecuritystatus);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}

// optional string FIPledgedSecurityID = 83;
inline void MDBasicInfo::clear_fipledgedsecurityid() {
  fipledgedsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fipledgedsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  return fipledgedsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fipledgedsecurityid(const ::std::string& value) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
inline void MDBasicInfo::set_fipledgedsecurityid(const char* value) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
inline void MDBasicInfo::set_fipledgedsecurityid(const char* value, size_t size) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
inline ::std::string* MDBasicInfo::mutable_fipledgedsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  return fipledgedsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fipledgedsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  
  return fipledgedsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fipledgedsecurityid(::std::string* fipledgedsecurityid) {
  if (fipledgedsecurityid != NULL) {
    
  } else {
    
  }
  fipledgedsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fipledgedsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}

// optional string FIOpenTime = 84;
inline void MDBasicInfo::clear_fiopentime() {
  fiopentime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiopentime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  return fiopentime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiopentime(const ::std::string& value) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
inline void MDBasicInfo::set_fiopentime(const char* value) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
inline void MDBasicInfo::set_fiopentime(const char* value, size_t size) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
inline ::std::string* MDBasicInfo::mutable_fiopentime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  return fiopentime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiopentime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  
  return fiopentime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiopentime(::std::string* fiopentime) {
  if (fiopentime != NULL) {
    
  } else {
    
  }
  fiopentime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiopentime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}

// optional string FICloseTime = 85;
inline void MDBasicInfo::clear_ficlosetime() {
  ficlosetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::ficlosetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  return ficlosetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_ficlosetime(const ::std::string& value) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
inline void MDBasicInfo::set_ficlosetime(const char* value) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
inline void MDBasicInfo::set_ficlosetime(const char* value, size_t size) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
inline ::std::string* MDBasicInfo::mutable_ficlosetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  return ficlosetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_ficlosetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  
  return ficlosetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_ficlosetime(::std::string* ficlosetime) {
  if (ficlosetime != NULL) {
    
  } else {
    
  }
  ficlosetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ficlosetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}

// optional string FIIssueMode = 86;
inline void MDBasicInfo::clear_fiissuemode() {
  fiissuemode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiissuemode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  return fiissuemode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiissuemode(const ::std::string& value) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
inline void MDBasicInfo::set_fiissuemode(const char* value) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
inline void MDBasicInfo::set_fiissuemode(const char* value, size_t size) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
inline ::std::string* MDBasicInfo::mutable_fiissuemode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  return fiissuemode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiissuemode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  
  return fiissuemode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiissuemode(::std::string* fiissuemode) {
  if (fiissuemode != NULL) {
    
  } else {
    
  }
  fiissuemode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuemode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}

// optional double FIFaceAmount = 87;
inline void MDBasicInfo::clear_fifaceamount() {
  fifaceamount_ = 0;
}
inline double MDBasicInfo::fifaceamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIFaceAmount)
  return fifaceamount_;
}
inline void MDBasicInfo::set_fifaceamount(double value) {
  
  fifaceamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIFaceAmount)
}

// optional double FIIssuePrice = 88;
inline void MDBasicInfo::clear_fiissueprice() {
  fiissueprice_ = 0;
}
inline double MDBasicInfo::fiissueprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssuePrice)
  return fiissueprice_;
}
inline void MDBasicInfo::set_fiissueprice(double value) {
  
  fiissueprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssuePrice)
}

// optional string FIInterestType = 89;
inline void MDBasicInfo::clear_fiinteresttype() {
  fiinteresttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiinteresttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  return fiinteresttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiinteresttype(const ::std::string& value) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
inline void MDBasicInfo::set_fiinteresttype(const char* value) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
inline void MDBasicInfo::set_fiinteresttype(const char* value, size_t size) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
inline ::std::string* MDBasicInfo::mutable_fiinteresttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  return fiinteresttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiinteresttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  
  return fiinteresttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiinteresttype(::std::string* fiinteresttype) {
  if (fiinteresttype != NULL) {
    
  } else {
    
  }
  fiinteresttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiinteresttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}

// optional string FIInterestFrequency = 90;
inline void MDBasicInfo::clear_fiinterestfrequency() {
  fiinterestfrequency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiinterestfrequency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  return fiinterestfrequency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiinterestfrequency(const ::std::string& value) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
inline void MDBasicInfo::set_fiinterestfrequency(const char* value) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
inline void MDBasicInfo::set_fiinterestfrequency(const char* value, size_t size) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
inline ::std::string* MDBasicInfo::mutable_fiinterestfrequency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  return fiinterestfrequency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiinterestfrequency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  
  return fiinterestfrequency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiinterestfrequency(::std::string* fiinterestfrequency) {
  if (fiinterestfrequency != NULL) {
    
  } else {
    
  }
  fiinterestfrequency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiinterestfrequency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}

// optional double FIGuaranteedInterestRate = 91;
inline void MDBasicInfo::clear_figuaranteedinterestrate() {
  figuaranteedinterestrate_ = 0;
}
inline double MDBasicInfo::figuaranteedinterestrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIGuaranteedInterestRate)
  return figuaranteedinterestrate_;
}
inline void MDBasicInfo::set_figuaranteedinterestrate(double value) {
  
  figuaranteedinterestrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIGuaranteedInterestRate)
}

// optional double FIBaseInterestRate = 92;
inline void MDBasicInfo::clear_fibaseinterestrate() {
  fibaseinterestrate_ = 0;
}
inline double MDBasicInfo::fibaseinterestrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIBaseInterestRate)
  return fibaseinterestrate_;
}
inline void MDBasicInfo::set_fibaseinterestrate(double value) {
  
  fibaseinterestrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIBaseInterestRate)
}

// optional double FIQuotedMargin = 93;
inline void MDBasicInfo::clear_fiquotedmargin() {
  fiquotedmargin_ = 0;
}
inline double MDBasicInfo::fiquotedmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIQuotedMargin)
  return fiquotedmargin_;
}
inline void MDBasicInfo::set_fiquotedmargin(double value) {
  
  fiquotedmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIQuotedMargin)
}

// optional int32 FITimeLimit = 94;
inline void MDBasicInfo::clear_fitimelimit() {
  fitimelimit_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::fitimelimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITimeLimit)
  return fitimelimit_;
}
inline void MDBasicInfo::set_fitimelimit(::google::protobuf::int32 value) {
  
  fitimelimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITimeLimit)
}

// optional double FITotalIssuance = 95;
inline void MDBasicInfo::clear_fitotalissuance() {
  fitotalissuance_ = 0;
}
inline double MDBasicInfo::fitotalissuance() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITotalIssuance)
  return fitotalissuance_;
}
inline void MDBasicInfo::set_fitotalissuance(double value) {
  
  fitotalissuance_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITotalIssuance)
}

// optional string FIIssueStartDate = 96;
inline void MDBasicInfo::clear_fiissuestartdate() {
  fiissuestartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiissuestartdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  return fiissuestartdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiissuestartdate(const ::std::string& value) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
inline void MDBasicInfo::set_fiissuestartdate(const char* value) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
inline void MDBasicInfo::set_fiissuestartdate(const char* value, size_t size) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
inline ::std::string* MDBasicInfo::mutable_fiissuestartdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  return fiissuestartdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiissuestartdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  
  return fiissuestartdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiissuestartdate(::std::string* fiissuestartdate) {
  if (fiissuestartdate != NULL) {
    
  } else {
    
  }
  fiissuestartdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuestartdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}

// optional string FIIssueEndDate = 97;
inline void MDBasicInfo::clear_fiissueenddate() {
  fiissueenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiissueenddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  return fiissueenddate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiissueenddate(const ::std::string& value) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
inline void MDBasicInfo::set_fiissueenddate(const char* value) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
inline void MDBasicInfo::set_fiissueenddate(const char* value, size_t size) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
inline ::std::string* MDBasicInfo::mutable_fiissueenddate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  return fiissueenddate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiissueenddate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  
  return fiissueenddate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiissueenddate(::std::string* fiissueenddate) {
  if (fiissueenddate != NULL) {
    
  } else {
    
  }
  fiissueenddate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissueenddate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}

// optional string FIListDate = 98;
inline void MDBasicInfo::clear_filistdate() {
  filistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::filistdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  return filistdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_filistdate(const ::std::string& value) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
inline void MDBasicInfo::set_filistdate(const char* value) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
inline void MDBasicInfo::set_filistdate(const char* value, size_t size) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
inline ::std::string* MDBasicInfo::mutable_filistdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  return filistdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_filistdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  
  return filistdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_filistdate(::std::string* filistdate) {
  if (filistdate != NULL) {
    
  } else {
    
  }
  filistdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), filistdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}

// optional string FIExpireDate = 99;
inline void MDBasicInfo::clear_fiexpiredate() {
  fiexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  return fiexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiexpiredate(const ::std::string& value) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
inline void MDBasicInfo::set_fiexpiredate(const char* value) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
inline void MDBasicInfo::set_fiexpiredate(const char* value, size_t size) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
inline ::std::string* MDBasicInfo::mutable_fiexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  return fiexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  
  return fiexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiexpiredate(::std::string* fiexpiredate) {
  if (fiexpiredate != NULL) {
    
  } else {
    
  }
  fiexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}

// optional string FINationalDebtType = 100;
inline void MDBasicInfo::clear_finationaldebttype() {
  finationaldebttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::finationaldebttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  return finationaldebttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_finationaldebttype(const ::std::string& value) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
inline void MDBasicInfo::set_finationaldebttype(const char* value) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
inline void MDBasicInfo::set_finationaldebttype(const char* value, size_t size) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
inline ::std::string* MDBasicInfo::mutable_finationaldebttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  return finationaldebttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_finationaldebttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  
  return finationaldebttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_finationaldebttype(::std::string* finationaldebttype) {
  if (finationaldebttype != NULL) {
    
  } else {
    
  }
  finationaldebttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), finationaldebttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}

// optional string FIIssueMethod = 101;
inline void MDBasicInfo::clear_fiissuemethod() {
  fiissuemethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::fiissuemethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  return fiissuemethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_fiissuemethod(const ::std::string& value) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
inline void MDBasicInfo::set_fiissuemethod(const char* value) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
inline void MDBasicInfo::set_fiissuemethod(const char* value, size_t size) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
inline ::std::string* MDBasicInfo::mutable_fiissuemethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  return fiissuemethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_fiissuemethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  
  return fiissuemethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_fiissuemethod(::std::string* fiissuemethod) {
  if (fiissuemethod != NULL) {
    
  } else {
    
  }
  fiissuemethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuemethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}

// optional bool FICrossMarket = 102;
inline void MDBasicInfo::clear_ficrossmarket() {
  ficrossmarket_ = false;
}
inline bool MDBasicInfo::ficrossmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FICrossMarket)
  return ficrossmarket_;
}
inline void MDBasicInfo::set_ficrossmarket(bool value) {
  
  ficrossmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FICrossMarket)
}

// optional bool FIShortSellFlag = 103;
inline void MDBasicInfo::clear_fishortsellflag() {
  fishortsellflag_ = false;
}
inline bool MDBasicInfo::fishortsellflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIShortSellFlag)
  return fishortsellflag_;
}
inline void MDBasicInfo::set_fishortsellflag(bool value) {
  
  fishortsellflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIShortSellFlag)
}

// optional double FITotalShortSellQuota = 104;
inline void MDBasicInfo::clear_fitotalshortsellquota() {
  fitotalshortsellquota_ = 0;
}
inline double MDBasicInfo::fitotalshortsellquota() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITotalShortSellQuota)
  return fitotalshortsellquota_;
}
inline void MDBasicInfo::set_fitotalshortsellquota(double value) {
  
  fitotalshortsellquota_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITotalShortSellQuota)
}

// optional double FIDealerShortSellQuota = 105;
inline void MDBasicInfo::clear_fidealershortsellquota() {
  fidealershortsellquota_ = 0;
}
inline double MDBasicInfo::fidealershortsellquota() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIDealerShortSellQuota)
  return fidealershortsellquota_;
}
inline void MDBasicInfo::set_fidealershortsellquota(double value) {
  
  fidealershortsellquota_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIDealerShortSellQuota)
}

// optional double FIPreClosePx = 106;
inline void MDBasicInfo::clear_fipreclosepx() {
  fipreclosepx_ = 0;
}
inline double MDBasicInfo::fipreclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPreClosePx)
  return fipreclosepx_;
}
inline void MDBasicInfo::set_fipreclosepx(double value) {
  
  fipreclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPreClosePx)
}

// optional double FIPreWeightedPx = 107;
inline void MDBasicInfo::clear_fipreweightedpx() {
  fipreweightedpx_ = 0;
}
inline double MDBasicInfo::fipreweightedpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPreWeightedPx)
  return fipreweightedpx_;
}
inline void MDBasicInfo::set_fipreweightedpx(double value) {
  
  fipreweightedpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPreWeightedPx)
}

// optional string OptionListType = 110;
inline void MDBasicInfo::clear_optionlisttype() {
  optionlisttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionlisttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  return optionlisttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionlisttype(const ::std::string& value) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
inline void MDBasicInfo::set_optionlisttype(const char* value) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
inline void MDBasicInfo::set_optionlisttype(const char* value, size_t size) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
inline ::std::string* MDBasicInfo::mutable_optionlisttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  return optionlisttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionlisttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  
  return optionlisttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionlisttype(::std::string* optionlisttype) {
  if (optionlisttype != NULL) {
    
  } else {
    
  }
  optionlisttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionlisttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}

// optional string OptionDeliveryType = 111;
inline void MDBasicInfo::clear_optiondeliverytype() {
  optiondeliverytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optiondeliverytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  return optiondeliverytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optiondeliverytype(const ::std::string& value) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
inline void MDBasicInfo::set_optiondeliverytype(const char* value) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
inline void MDBasicInfo::set_optiondeliverytype(const char* value, size_t size) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
inline ::std::string* MDBasicInfo::mutable_optiondeliverytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  return optiondeliverytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optiondeliverytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  
  return optiondeliverytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optiondeliverytype(::std::string* optiondeliverytype) {
  if (optiondeliverytype != NULL) {
    
  } else {
    
  }
  optiondeliverytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiondeliverytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}

// optional int32 OptionAdjustTimes = 112;
inline void MDBasicInfo::clear_optionadjusttimes() {
  optionadjusttimes_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::optionadjusttimes() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionAdjustTimes)
  return optionadjusttimes_;
}
inline void MDBasicInfo::set_optionadjusttimes(::google::protobuf::int32 value) {
  
  optionadjusttimes_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionAdjustTimes)
}

// optional int64 OptionContractPosition = 113;
inline void MDBasicInfo::clear_optioncontractposition() {
  optioncontractposition_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optioncontractposition() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractPosition)
  return optioncontractposition_;
}
inline void MDBasicInfo::set_optioncontractposition(::google::protobuf::int64 value) {
  
  optioncontractposition_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractPosition)
}

// optional int64 OptionBuyQtyUpperLimit = 114;
inline void MDBasicInfo::clear_optionbuyqtyupperlimit() {
  optionbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUpperLimit)
  return optionbuyqtyupperlimit_;
}
inline void MDBasicInfo::set_optionbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUpperLimit)
}

// optional int64 OptionSellQtyUpperLimit = 115;
inline void MDBasicInfo::clear_optionsellqtyupperlimit() {
  optionsellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionsellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUpperLimit)
  return optionsellqtyupperlimit_;
}
inline void MDBasicInfo::set_optionsellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionsellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUpperLimit)
}

// optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
inline void MDBasicInfo::clear_optionmarketorderbuyqtyupperlimit() {
  optionmarketorderbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionmarketorderbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderBuyQtyUpperLimit)
  return optionmarketorderbuyqtyupperlimit_;
}
inline void MDBasicInfo::set_optionmarketorderbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionmarketorderbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderBuyQtyUpperLimit)
}

// optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
inline void MDBasicInfo::clear_optionmarketordersellqtyupperlimit() {
  optionmarketordersellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionmarketordersellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderSellQtyUpperLimit)
  return optionmarketordersellqtyupperlimit_;
}
inline void MDBasicInfo::set_optionmarketordersellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionmarketordersellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderSellQtyUpperLimit)
}

// optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
inline void MDBasicInfo::clear_optionquoteorderbuyqtyupperlimit() {
  optionquoteorderbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionquoteorderbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderBuyQtyUpperLimit)
  return optionquoteorderbuyqtyupperlimit_;
}
inline void MDBasicInfo::set_optionquoteorderbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionquoteorderbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderBuyQtyUpperLimit)
}

// optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
inline void MDBasicInfo::clear_optionquoteordersellqtyupperlimit() {
  optionquoteordersellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionquoteordersellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderSellQtyUpperLimit)
  return optionquoteordersellqtyupperlimit_;
}
inline void MDBasicInfo::set_optionquoteordersellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionquoteordersellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderSellQtyUpperLimit)
}

// optional int64 OptionBuyQtyUnit = 120;
inline void MDBasicInfo::clear_optionbuyqtyunit() {
  optionbuyqtyunit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionbuyqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUnit)
  return optionbuyqtyunit_;
}
inline void MDBasicInfo::set_optionbuyqtyunit(::google::protobuf::int64 value) {
  
  optionbuyqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUnit)
}

// optional int64 OptionSellQtyUnit = 121;
inline void MDBasicInfo::clear_optionsellqtyunit() {
  optionsellqtyunit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::optionsellqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUnit)
  return optionsellqtyunit_;
}
inline void MDBasicInfo::set_optionsellqtyunit(::google::protobuf::int64 value) {
  
  optionsellqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUnit)
}

// optional double OptionLastSellMargin = 122;
inline void MDBasicInfo::clear_optionlastsellmargin() {
  optionlastsellmargin_ = 0;
}
inline double MDBasicInfo::optionlastsellmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLastSellMargin)
  return optionlastsellmargin_;
}
inline void MDBasicInfo::set_optionlastsellmargin(double value) {
  
  optionlastsellmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLastSellMargin)
}

// optional double OptionSellMargin = 123;
inline void MDBasicInfo::clear_optionsellmargin() {
  optionsellmargin_ = 0;
}
inline double MDBasicInfo::optionsellmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellMargin)
  return optionsellmargin_;
}
inline void MDBasicInfo::set_optionsellmargin(double value) {
  
  optionsellmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellMargin)
}

// optional string OptionMarketMakerFlag = 124;
inline void MDBasicInfo::clear_optionmarketmakerflag() {
  optionmarketmakerflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optionmarketmakerflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  return optionmarketmakerflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optionmarketmakerflag(const ::std::string& value) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
inline void MDBasicInfo::set_optionmarketmakerflag(const char* value) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
inline void MDBasicInfo::set_optionmarketmakerflag(const char* value, size_t size) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
inline ::std::string* MDBasicInfo::mutable_optionmarketmakerflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  return optionmarketmakerflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optionmarketmakerflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  
  return optionmarketmakerflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optionmarketmakerflag(::std::string* optionmarketmakerflag) {
  if (optionmarketmakerflag != NULL) {
    
  } else {
    
  }
  optionmarketmakerflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionmarketmakerflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}

// optional string OptionCombinationStrategy = 125;
inline void MDBasicInfo::clear_optioncombinationstrategy() {
  optioncombinationstrategy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::optioncombinationstrategy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  return optioncombinationstrategy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_optioncombinationstrategy(const ::std::string& value) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
inline void MDBasicInfo::set_optioncombinationstrategy(const char* value) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
inline void MDBasicInfo::set_optioncombinationstrategy(const char* value, size_t size) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
inline ::std::string* MDBasicInfo::mutable_optioncombinationstrategy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  return optioncombinationstrategy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_optioncombinationstrategy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  
  return optioncombinationstrategy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_optioncombinationstrategy(::std::string* optioncombinationstrategy) {
  if (optioncombinationstrategy != NULL) {
    
  } else {
    
  }
  optioncombinationstrategy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncombinationstrategy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}

// optional string DeliveryYear = 126;
inline void MDBasicInfo::clear_deliveryyear() {
  deliveryyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::deliveryyear() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  return deliveryyear_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_deliveryyear(const ::std::string& value) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
inline void MDBasicInfo::set_deliveryyear(const char* value) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
inline void MDBasicInfo::set_deliveryyear(const char* value, size_t size) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
inline ::std::string* MDBasicInfo::mutable_deliveryyear() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  return deliveryyear_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_deliveryyear() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  
  return deliveryyear_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_deliveryyear(::std::string* deliveryyear) {
  if (deliveryyear != NULL) {
    
  } else {
    
  }
  deliveryyear_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deliveryyear);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}

// optional string DeliveryMonth = 127;
inline void MDBasicInfo::clear_deliverymonth() {
  deliverymonth_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::deliverymonth() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  return deliverymonth_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_deliverymonth(const ::std::string& value) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
inline void MDBasicInfo::set_deliverymonth(const char* value) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
inline void MDBasicInfo::set_deliverymonth(const char* value, size_t size) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
inline ::std::string* MDBasicInfo::mutable_deliverymonth() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  return deliverymonth_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_deliverymonth() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  
  return deliverymonth_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_deliverymonth(::std::string* deliverymonth) {
  if (deliverymonth != NULL) {
    
  } else {
    
  }
  deliverymonth_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deliverymonth);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}

// optional string InstrumentID = 128;
inline void MDBasicInfo::clear_instrumentid() {
  instrumentid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::instrumentid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  return instrumentid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_instrumentid(const ::std::string& value) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
inline void MDBasicInfo::set_instrumentid(const char* value) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
inline void MDBasicInfo::set_instrumentid(const char* value, size_t size) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
inline ::std::string* MDBasicInfo::mutable_instrumentid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  return instrumentid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_instrumentid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  
  return instrumentid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_instrumentid(::std::string* instrumentid) {
  if (instrumentid != NULL) {
    
  } else {
    
  }
  instrumentid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}

// optional string InstrumentName = 129;
inline void MDBasicInfo::clear_instrumentname() {
  instrumentname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::instrumentname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  return instrumentname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_instrumentname(const ::std::string& value) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
inline void MDBasicInfo::set_instrumentname(const char* value) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
inline void MDBasicInfo::set_instrumentname(const char* value, size_t size) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
inline ::std::string* MDBasicInfo::mutable_instrumentname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  return instrumentname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_instrumentname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  
  return instrumentname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_instrumentname(::std::string* instrumentname) {
  if (instrumentname != NULL) {
    
  } else {
    
  }
  instrumentname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}

// optional string ExchangeInstID = 130;
inline void MDBasicInfo::clear_exchangeinstid() {
  exchangeinstid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::exchangeinstid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  return exchangeinstid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_exchangeinstid(const ::std::string& value) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
inline void MDBasicInfo::set_exchangeinstid(const char* value) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
inline void MDBasicInfo::set_exchangeinstid(const char* value, size_t size) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
inline ::std::string* MDBasicInfo::mutable_exchangeinstid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  return exchangeinstid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_exchangeinstid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  
  return exchangeinstid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_exchangeinstid(::std::string* exchangeinstid) {
  if (exchangeinstid != NULL) {
    
  } else {
    
  }
  exchangeinstid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangeinstid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}

// optional string ProductID = 131;
inline void MDBasicInfo::clear_productid() {
  productid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::productid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  return productid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_productid(const ::std::string& value) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
inline void MDBasicInfo::set_productid(const char* value) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
inline void MDBasicInfo::set_productid(const char* value, size_t size) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
inline ::std::string* MDBasicInfo::mutable_productid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  return productid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_productid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  
  return productid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_productid(::std::string* productid) {
  if (productid != NULL) {
    
  } else {
    
  }
  productid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), productid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}

// optional int64 MaxMarketOrderVolume = 132;
inline void MDBasicInfo::clear_maxmarketordervolume() {
  maxmarketordervolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::maxmarketordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarketOrderVolume)
  return maxmarketordervolume_;
}
inline void MDBasicInfo::set_maxmarketordervolume(::google::protobuf::int64 value) {
  
  maxmarketordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarketOrderVolume)
}

// optional int64 MinMarketOrderVolume = 133;
inline void MDBasicInfo::clear_minmarketordervolume() {
  minmarketordervolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::minmarketordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinMarketOrderVolume)
  return minmarketordervolume_;
}
inline void MDBasicInfo::set_minmarketordervolume(::google::protobuf::int64 value) {
  
  minmarketordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinMarketOrderVolume)
}

// optional int64 MaxLimitOrderVolume = 134;
inline void MDBasicInfo::clear_maxlimitordervolume() {
  maxlimitordervolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::maxlimitordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxLimitOrderVolume)
  return maxlimitordervolume_;
}
inline void MDBasicInfo::set_maxlimitordervolume(::google::protobuf::int64 value) {
  
  maxlimitordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxLimitOrderVolume)
}

// optional int64 MinLimitOrderVolume = 135;
inline void MDBasicInfo::clear_minlimitordervolume() {
  minlimitordervolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::minlimitordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinLimitOrderVolume)
  return minlimitordervolume_;
}
inline void MDBasicInfo::set_minlimitordervolume(::google::protobuf::int64 value) {
  
  minlimitordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinLimitOrderVolume)
}

// optional int64 VolumeMultiple = 136;
inline void MDBasicInfo::clear_volumemultiple() {
  volumemultiple_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::volumemultiple() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.VolumeMultiple)
  return volumemultiple_;
}
inline void MDBasicInfo::set_volumemultiple(::google::protobuf::int64 value) {
  
  volumemultiple_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.VolumeMultiple)
}

// optional string CreateDate = 137;
inline void MDBasicInfo::clear_createdate() {
  createdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::createdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  return createdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_createdate(const ::std::string& value) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
inline void MDBasicInfo::set_createdate(const char* value) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
inline void MDBasicInfo::set_createdate(const char* value, size_t size) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
inline ::std::string* MDBasicInfo::mutable_createdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  return createdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_createdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  
  return createdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_createdate(::std::string* createdate) {
  if (createdate != NULL) {
    
  } else {
    
  }
  createdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), createdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}

// optional string ExpireDate = 138;
inline void MDBasicInfo::clear_expiredate() {
  expiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::expiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  return expiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_expiredate(const ::std::string& value) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
inline void MDBasicInfo::set_expiredate(const char* value) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
inline void MDBasicInfo::set_expiredate(const char* value, size_t size) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
inline ::std::string* MDBasicInfo::mutable_expiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  return expiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_expiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  
  return expiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_expiredate(::std::string* expiredate) {
  if (expiredate != NULL) {
    
  } else {
    
  }
  expiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), expiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}

// optional string StartDelivDate = 139;
inline void MDBasicInfo::clear_startdelivdate() {
  startdelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::startdelivdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  return startdelivdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_startdelivdate(const ::std::string& value) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
inline void MDBasicInfo::set_startdelivdate(const char* value) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
inline void MDBasicInfo::set_startdelivdate(const char* value, size_t size) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
inline ::std::string* MDBasicInfo::mutable_startdelivdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  return startdelivdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_startdelivdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  
  return startdelivdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_startdelivdate(::std::string* startdelivdate) {
  if (startdelivdate != NULL) {
    
  } else {
    
  }
  startdelivdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), startdelivdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}

// optional string EndDelivDate = 140;
inline void MDBasicInfo::clear_enddelivdate() {
  enddelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::enddelivdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  return enddelivdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_enddelivdate(const ::std::string& value) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
inline void MDBasicInfo::set_enddelivdate(const char* value) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
inline void MDBasicInfo::set_enddelivdate(const char* value, size_t size) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
inline ::std::string* MDBasicInfo::mutable_enddelivdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  return enddelivdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_enddelivdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  
  return enddelivdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_enddelivdate(::std::string* enddelivdate) {
  if (enddelivdate != NULL) {
    
  } else {
    
  }
  enddelivdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), enddelivdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}

// optional string PositionType = 141;
inline void MDBasicInfo::clear_positiontype() {
  positiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::positiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  return positiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_positiontype(const ::std::string& value) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
inline void MDBasicInfo::set_positiontype(const char* value) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
inline void MDBasicInfo::set_positiontype(const char* value, size_t size) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
inline ::std::string* MDBasicInfo::mutable_positiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  return positiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_positiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  
  return positiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_positiontype(::std::string* positiontype) {
  if (positiontype != NULL) {
    
  } else {
    
  }
  positiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), positiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}

// optional double LongMarginRatio = 142;
inline void MDBasicInfo::clear_longmarginratio() {
  longmarginratio_ = 0;
}
inline double MDBasicInfo::longmarginratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LongMarginRatio)
  return longmarginratio_;
}
inline void MDBasicInfo::set_longmarginratio(double value) {
  
  longmarginratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LongMarginRatio)
}

// optional double ShortMarginRatio = 143;
inline void MDBasicInfo::clear_shortmarginratio() {
  shortmarginratio_ = 0;
}
inline double MDBasicInfo::shortmarginratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShortMarginRatio)
  return shortmarginratio_;
}
inline void MDBasicInfo::set_shortmarginratio(double value) {
  
  shortmarginratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShortMarginRatio)
}

// optional string MaxMarginSideAlgorithm = 144;
inline void MDBasicInfo::clear_maxmarginsidealgorithm() {
  maxmarginsidealgorithm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::maxmarginsidealgorithm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  return maxmarginsidealgorithm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_maxmarginsidealgorithm(const ::std::string& value) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
inline void MDBasicInfo::set_maxmarginsidealgorithm(const char* value) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
inline void MDBasicInfo::set_maxmarginsidealgorithm(const char* value, size_t size) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
inline ::std::string* MDBasicInfo::mutable_maxmarginsidealgorithm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  return maxmarginsidealgorithm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_maxmarginsidealgorithm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  
  return maxmarginsidealgorithm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_maxmarginsidealgorithm(::std::string* maxmarginsidealgorithm) {
  if (maxmarginsidealgorithm != NULL) {
    
  } else {
    
  }
  maxmarginsidealgorithm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), maxmarginsidealgorithm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}

// optional double StrikePrice = 145;
inline void MDBasicInfo::clear_strikeprice() {
  strikeprice_ = 0;
}
inline double MDBasicInfo::strikeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.StrikePrice)
  return strikeprice_;
}
inline void MDBasicInfo::set_strikeprice(double value) {
  
  strikeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.StrikePrice)
}

// optional double PreOpenInterest = 146;
inline void MDBasicInfo::clear_preopeninterest() {
  preopeninterest_ = 0;
}
inline double MDBasicInfo::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreOpenInterest)
  return preopeninterest_;
}
inline void MDBasicInfo::set_preopeninterest(double value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreOpenInterest)
}

// optional string FormerSymbol = 147;
inline void MDBasicInfo::clear_formersymbol() {
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::formersymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  return formersymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_formersymbol(const ::std::string& value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
inline void MDBasicInfo::set_formersymbol(const char* value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
inline void MDBasicInfo::set_formersymbol(const char* value, size_t size) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
inline ::std::string* MDBasicInfo::mutable_formersymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  return formersymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_formersymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  
  return formersymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_formersymbol(::std::string* formersymbol) {
  if (formersymbol != NULL) {
    
  } else {
    
  }
  formersymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), formersymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}

// optional string DelistDate = 148;
inline void MDBasicInfo::clear_delistdate() {
  delistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::delistdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  return delistdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_delistdate(const ::std::string& value) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
inline void MDBasicInfo::set_delistdate(const char* value) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
inline void MDBasicInfo::set_delistdate(const char* value, size_t size) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
inline ::std::string* MDBasicInfo::mutable_delistdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  return delistdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_delistdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  
  return delistdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_delistdate(::std::string* delistdate) {
  if (delistdate != NULL) {
    
  } else {
    
  }
  delistdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), delistdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}

// optional int64 BuyQtyUnit = 149;
inline void MDBasicInfo::clear_buyqtyunit() {
  buyqtyunit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::buyqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUnit)
  return buyqtyunit_;
}
inline void MDBasicInfo::set_buyqtyunit(::google::protobuf::int64 value) {
  
  buyqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUnit)
}

// optional int64 SellQtyUnit = 150;
inline void MDBasicInfo::clear_sellqtyunit() {
  sellqtyunit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::sellqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUnit)
  return sellqtyunit_;
}
inline void MDBasicInfo::set_sellqtyunit(::google::protobuf::int64 value) {
  
  sellqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUnit)
}

// optional int64 BuyQtyUpperLimit = 161;
inline void MDBasicInfo::clear_buyqtyupperlimit() {
  buyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::buyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUpperLimit)
  return buyqtyupperlimit_;
}
inline void MDBasicInfo::set_buyqtyupperlimit(::google::protobuf::int64 value) {
  
  buyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUpperLimit)
}

// optional int64 SellQtyUpperLimit = 162;
inline void MDBasicInfo::clear_sellqtyupperlimit() {
  sellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::sellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUpperLimit)
  return sellqtyupperlimit_;
}
inline void MDBasicInfo::set_sellqtyupperlimit(::google::protobuf::int64 value) {
  
  sellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUpperLimit)
}

// optional int64 BuyQtyLowerLimit = 163;
inline void MDBasicInfo::clear_buyqtylowerlimit() {
  buyqtylowerlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::buyqtylowerlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyLowerLimit)
  return buyqtylowerlimit_;
}
inline void MDBasicInfo::set_buyqtylowerlimit(::google::protobuf::int64 value) {
  
  buyqtylowerlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyLowerLimit)
}

// optional int64 SellQtyLowerLimit = 164;
inline void MDBasicInfo::clear_sellqtylowerlimit() {
  sellqtylowerlimit_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBasicInfo::sellqtylowerlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyLowerLimit)
  return sellqtylowerlimit_;
}
inline void MDBasicInfo::set_sellqtylowerlimit(::google::protobuf::int64 value) {
  
  sellqtylowerlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyLowerLimit)
}

// optional int32 VCMFlag = 165;
inline void MDBasicInfo::clear_vcmflag() {
  vcmflag_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::vcmflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.VCMFlag)
  return vcmflag_;
}
inline void MDBasicInfo::set_vcmflag(::google::protobuf::int32 value) {
  
  vcmflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.VCMFlag)
}

// optional int32 CASFlag = 166;
inline void MDBasicInfo::clear_casflag() {
  casflag_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::casflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.CASFlag)
  return casflag_;
}
inline void MDBasicInfo::set_casflag(::google::protobuf::int32 value) {
  
  casflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.CASFlag)
}

// optional int32 POSFlag = 167;
inline void MDBasicInfo::clear_posflag() {
  posflag_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::posflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSFlag)
  return posflag_;
}
inline void MDBasicInfo::set_posflag(::google::protobuf::int32 value) {
  
  posflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSFlag)
}

// optional double POSUpperLimitPx = 168;
inline void MDBasicInfo::clear_posupperlimitpx() {
  posupperlimitpx_ = 0;
}
inline double MDBasicInfo::posupperlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSUpperLimitPx)
  return posupperlimitpx_;
}
inline void MDBasicInfo::set_posupperlimitpx(double value) {
  
  posupperlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSUpperLimitPx)
}

// optional double POSLowerLimitPx = 169;
inline void MDBasicInfo::clear_poslowerlimitpx() {
  poslowerlimitpx_ = 0;
}
inline double MDBasicInfo::poslowerlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSLowerLimitPx)
  return poslowerlimitpx_;
}
inline void MDBasicInfo::set_poslowerlimitpx(double value) {
  
  poslowerlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSLowerLimitPx)
}

// optional string BaseContractID = 170;
inline void MDBasicInfo::clear_basecontractid() {
  basecontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::basecontractid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  return basecontractid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_basecontractid(const ::std::string& value) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
inline void MDBasicInfo::set_basecontractid(const char* value) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
inline void MDBasicInfo::set_basecontractid(const char* value, size_t size) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
inline ::std::string* MDBasicInfo::mutable_basecontractid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  return basecontractid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_basecontractid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  
  return basecontractid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_basecontractid(::std::string* basecontractid) {
  if (basecontractid != NULL) {
    
  } else {
    
  }
  basecontractid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), basecontractid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}

// repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
inline int MDBasicInfo::constantparams_size() const {
  return constantparams_.size();
}
inline void MDBasicInfo::clear_constantparams() {
  constantparams_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam& MDBasicInfo::constantparams(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* MDBasicInfo::mutable_constantparams(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* MDBasicInfo::add_constantparams() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >*
MDBasicInfo::mutable_constantparams() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return &constantparams_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >&
MDBasicInfo::constantparams() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_;
}

// optional int32 DataMultiplePowerOf10 = 172;
inline void MDBasicInfo::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDBasicInfo::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDBasicInfo::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DataMultiplePowerOf10)
}

// optional string InterestAccrualDate = 173;
inline void MDBasicInfo::clear_interestaccrualdate() {
  interestaccrualdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBasicInfo::interestaccrualdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  return interestaccrualdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_interestaccrualdate(const ::std::string& value) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
inline void MDBasicInfo::set_interestaccrualdate(const char* value) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
inline void MDBasicInfo::set_interestaccrualdate(const char* value, size_t size) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
inline ::std::string* MDBasicInfo::mutable_interestaccrualdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  return interestaccrualdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBasicInfo::release_interestaccrualdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  
  return interestaccrualdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBasicInfo::set_allocated_interestaccrualdate(::std::string* interestaccrualdate) {
  if (interestaccrualdate != NULL) {
    
  } else {
    
  }
  interestaccrualdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), interestaccrualdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}

inline const MDBasicInfo* MDBasicInfo::internal_default_instance() {
  return &MDBasicInfo_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDBasicInfo_2eproto__INCLUDED
