// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsCurrencySnapshot.proto

#ifndef PROTOBUF_MDCfetsCurrencySnapshot_2eproto__INCLUDED
#define PROTOBUF_MDCfetsCurrencySnapshot_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto();
void protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto();
void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto();

class CollateralRepoSnapshot;
class InterBankOfferingSnapshot;
class MDCfetsCurrencySnapshot;
class OutrightRepoSnapshot;

// ===================================================================

class MDCfetsCurrencySnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot) */ {
 public:
  MDCfetsCurrencySnapshot();
  virtual ~MDCfetsCurrencySnapshot();

  MDCfetsCurrencySnapshot(const MDCfetsCurrencySnapshot& from);

  inline MDCfetsCurrencySnapshot& operator=(const MDCfetsCurrencySnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsCurrencySnapshot& default_instance();

  static const MDCfetsCurrencySnapshot* internal_default_instance();

  void Swap(MDCfetsCurrencySnapshot* other);

  // implements Message ----------------------------------------------

  inline MDCfetsCurrencySnapshot* New() const { return New(NULL); }

  MDCfetsCurrencySnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsCurrencySnapshot& from);
  void MergeFrom(const MDCfetsCurrencySnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsCurrencySnapshot* other);
  void UnsafeMergeFrom(const MDCfetsCurrencySnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 CurrencySnapshotType = 9;
  void clear_currencysnapshottype();
  static const int kCurrencySnapshotTypeFieldNumber = 9;
  ::google::protobuf::int32 currencysnapshottype() const;
  void set_currencysnapshottype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
  bool has_interbankofferingsnapshot() const;
  void clear_interbankofferingsnapshot();
  static const int kInterBankOfferingSnapshotFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot& interbankofferingsnapshot() const;
  ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* mutable_interbankofferingsnapshot();
  ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* release_interbankofferingsnapshot();
  void set_allocated_interbankofferingsnapshot(::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* interbankofferingsnapshot);

  // optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
  bool has_collateralreposnapshot() const;
  void clear_collateralreposnapshot();
  static const int kCollateralRepoSnapshotFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::CollateralRepoSnapshot& collateralreposnapshot() const;
  ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* mutable_collateralreposnapshot();
  ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* release_collateralreposnapshot();
  void set_allocated_collateralreposnapshot(::com::htsc::mdc::insight::model::CollateralRepoSnapshot* collateralreposnapshot);

  // optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
  bool has_outrightreposnapshot() const;
  void clear_outrightreposnapshot();
  static const int kOutrightRepoSnapshotFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::OutrightRepoSnapshot& outrightreposnapshot() const;
  ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* mutable_outrightreposnapshot();
  ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* release_outrightreposnapshot();
  void set_allocated_outrightreposnapshot(::com::htsc::mdc::insight::model::OutrightRepoSnapshot* outrightreposnapshot);

  // optional int32 DataMultiplePowerOf10 = 13;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 13;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* interbankofferingsnapshot_;
  ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* collateralreposnapshot_;
  ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* outrightreposnapshot_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int32 currencysnapshottype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsCurrencySnapshot> MDCfetsCurrencySnapshot_default_instance_;

// -------------------------------------------------------------------

class InterBankOfferingSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.InterBankOfferingSnapshot) */ {
 public:
  InterBankOfferingSnapshot();
  virtual ~InterBankOfferingSnapshot();

  InterBankOfferingSnapshot(const InterBankOfferingSnapshot& from);

  inline InterBankOfferingSnapshot& operator=(const InterBankOfferingSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InterBankOfferingSnapshot& default_instance();

  static const InterBankOfferingSnapshot* internal_default_instance();

  void Swap(InterBankOfferingSnapshot* other);

  // implements Message ----------------------------------------------

  inline InterBankOfferingSnapshot* New() const { return New(NULL); }

  InterBankOfferingSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InterBankOfferingSnapshot& from);
  void MergeFrom(const InterBankOfferingSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(InterBankOfferingSnapshot* other);
  void UnsafeMergeFrom(const InterBankOfferingSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 PreCloseRate = 1;
  void clear_precloserate();
  static const int kPreCloseRateFieldNumber = 1;
  ::google::protobuf::int64 precloserate() const;
  void set_precloserate(::google::protobuf::int64 value);

  // optional int64 PreWeightedRate = 2;
  void clear_preweightedrate();
  static const int kPreWeightedRateFieldNumber = 2;
  ::google::protobuf::int64 preweightedrate() const;
  void set_preweightedrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 3;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 3;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 LastRate = 4;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 4;
  ::google::protobuf::int64 lastrate() const;
  void set_lastrate(::google::protobuf::int64 value);

  // optional int64 HighRate = 5;
  void clear_highrate();
  static const int kHighRateFieldNumber = 5;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 6;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 6;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 7;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 7;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int64 WeightedRate = 8;
  void clear_weightedrate();
  static const int kWeightedRateFieldNumber = 8;
  ::google::protobuf::int64 weightedrate() const;
  void set_weightedrate(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 9;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 9;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int32 NumTrades = 10;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 10;
  ::google::protobuf::int32 numtrades() const;
  void set_numtrades(::google::protobuf::int32 value);

  // optional int64 AverageTerm = 11;
  void clear_averageterm();
  static const int kAverageTermFieldNumber = 11;
  ::google::protobuf::int64 averageterm() const;
  void set_averageterm(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 precloserate_;
  ::google::protobuf::int64 preweightedrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 lastrate_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int64 weightedrate_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 averageterm_;
  ::google::protobuf::int32 numtrades_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<InterBankOfferingSnapshot> InterBankOfferingSnapshot_default_instance_;

// -------------------------------------------------------------------

class CollateralRepoSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.CollateralRepoSnapshot) */ {
 public:
  CollateralRepoSnapshot();
  virtual ~CollateralRepoSnapshot();

  CollateralRepoSnapshot(const CollateralRepoSnapshot& from);

  inline CollateralRepoSnapshot& operator=(const CollateralRepoSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CollateralRepoSnapshot& default_instance();

  static const CollateralRepoSnapshot* internal_default_instance();

  void Swap(CollateralRepoSnapshot* other);

  // implements Message ----------------------------------------------

  inline CollateralRepoSnapshot* New() const { return New(NULL); }

  CollateralRepoSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CollateralRepoSnapshot& from);
  void MergeFrom(const CollateralRepoSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CollateralRepoSnapshot* other);
  void UnsafeMergeFrom(const CollateralRepoSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeMethod = 1;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 1;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional int64 PreCloseRate = 2;
  void clear_precloserate();
  static const int kPreCloseRateFieldNumber = 2;
  ::google::protobuf::int64 precloserate() const;
  void set_precloserate(::google::protobuf::int64 value);

  // optional int64 PreWeightedRate = 3;
  void clear_preweightedrate();
  static const int kPreWeightedRateFieldNumber = 3;
  ::google::protobuf::int64 preweightedrate() const;
  void set_preweightedrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 4;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 4;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 LastRate = 5;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 5;
  ::google::protobuf::int64 lastrate() const;
  void set_lastrate(::google::protobuf::int64 value);

  // optional int64 HighRate = 6;
  void clear_highrate();
  static const int kHighRateFieldNumber = 6;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 7;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 7;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 8;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 8;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int64 WeightedRate = 9;
  void clear_weightedrate();
  static const int kWeightedRateFieldNumber = 9;
  ::google::protobuf::int64 weightedrate() const;
  void set_weightedrate(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 10;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 10;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int32 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int32 numtrades() const;
  void set_numtrades(::google::protobuf::int32 value);

  // optional int64 AverageTerm = 12;
  void clear_averageterm();
  static const int kAverageTermFieldNumber = 12;
  ::google::protobuf::int64 averageterm() const;
  void set_averageterm(::google::protobuf::int64 value);

  // optional int64 IRBondWeightedRate = 13;
  void clear_irbondweightedrate();
  static const int kIRBondWeightedRateFieldNumber = 13;
  ::google::protobuf::int64 irbondweightedrate() const;
  void set_irbondweightedrate(::google::protobuf::int64 value);

  // optional string RepoMethod = 14;
  void clear_repomethod();
  static const int kRepoMethodFieldNumber = 14;
  const ::std::string& repomethod() const;
  void set_repomethod(const ::std::string& value);
  void set_repomethod(const char* value);
  void set_repomethod(const char* value, size_t size);
  ::std::string* mutable_repomethod();
  ::std::string* release_repomethod();
  void set_allocated_repomethod(::std::string* repomethod);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr repomethod_;
  ::google::protobuf::int64 precloserate_;
  ::google::protobuf::int64 preweightedrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 lastrate_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int32 trademethod_;
  ::google::protobuf::int32 numtrades_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int64 weightedrate_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 averageterm_;
  ::google::protobuf::int64 irbondweightedrate_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CollateralRepoSnapshot> CollateralRepoSnapshot_default_instance_;

// -------------------------------------------------------------------

class OutrightRepoSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.OutrightRepoSnapshot) */ {
 public:
  OutrightRepoSnapshot();
  virtual ~OutrightRepoSnapshot();

  OutrightRepoSnapshot(const OutrightRepoSnapshot& from);

  inline OutrightRepoSnapshot& operator=(const OutrightRepoSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OutrightRepoSnapshot& default_instance();

  static const OutrightRepoSnapshot* internal_default_instance();

  void Swap(OutrightRepoSnapshot* other);

  // implements Message ----------------------------------------------

  inline OutrightRepoSnapshot* New() const { return New(NULL); }

  OutrightRepoSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OutrightRepoSnapshot& from);
  void MergeFrom(const OutrightRepoSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OutrightRepoSnapshot* other);
  void UnsafeMergeFrom(const OutrightRepoSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 PreCloseRate = 1;
  void clear_precloserate();
  static const int kPreCloseRateFieldNumber = 1;
  ::google::protobuf::int64 precloserate() const;
  void set_precloserate(::google::protobuf::int64 value);

  // optional int64 PreWeightedRate = 2;
  void clear_preweightedrate();
  static const int kPreWeightedRateFieldNumber = 2;
  ::google::protobuf::int64 preweightedrate() const;
  void set_preweightedrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 3;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 3;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 LastRate = 4;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 4;
  ::google::protobuf::int64 lastrate() const;
  void set_lastrate(::google::protobuf::int64 value);

  // optional int64 HighRate = 5;
  void clear_highrate();
  static const int kHighRateFieldNumber = 5;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 6;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 6;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 7;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 7;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int64 WeightedRate = 8;
  void clear_weightedrate();
  static const int kWeightedRateFieldNumber = 8;
  ::google::protobuf::int64 weightedrate() const;
  void set_weightedrate(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 9;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 9;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 precloserate_;
  ::google::protobuf::int64 preweightedrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 lastrate_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int64 weightedrate_;
  ::google::protobuf::int64 totalvaluetrade_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OutrightRepoSnapshot> OutrightRepoSnapshot_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsCurrencySnapshot

// optional string HTSCSecurityID = 1;
inline void MDCfetsCurrencySnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencySnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
inline void MDCfetsCurrencySnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
inline void MDCfetsCurrencySnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
inline ::std::string* MDCfetsCurrencySnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencySnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsCurrencySnapshot::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsCurrencySnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsCurrencySnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsCurrencySnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsCurrencySnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsCurrencySnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsCurrencySnapshot::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencySnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDDate)
  return mddate_;
}
inline void MDCfetsCurrencySnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsCurrencySnapshot::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencySnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDTime)
  return mdtime_;
}
inline void MDCfetsCurrencySnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsCurrencySnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsCurrencySnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsCurrencySnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsCurrencySnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencySnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
inline void MDCfetsCurrencySnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
inline void MDCfetsCurrencySnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
inline ::std::string* MDCfetsCurrencySnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencySnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsCurrencySnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencySnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
inline void MDCfetsCurrencySnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
inline void MDCfetsCurrencySnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
inline ::std::string* MDCfetsCurrencySnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencySnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencySnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}

// optional int32 CurrencySnapshotType = 9;
inline void MDCfetsCurrencySnapshot::clear_currencysnapshottype() {
  currencysnapshottype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencySnapshot::currencysnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.CurrencySnapshotType)
  return currencysnapshottype_;
}
inline void MDCfetsCurrencySnapshot::set_currencysnapshottype(::google::protobuf::int32 value) {
  
  currencysnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.CurrencySnapshotType)
}

// optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
inline bool MDCfetsCurrencySnapshot::has_interbankofferingsnapshot() const {
  return this != internal_default_instance() && interbankofferingsnapshot_ != NULL;
}
inline void MDCfetsCurrencySnapshot::clear_interbankofferingsnapshot() {
  if (GetArenaNoVirtual() == NULL && interbankofferingsnapshot_ != NULL) delete interbankofferingsnapshot_;
  interbankofferingsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot& MDCfetsCurrencySnapshot::interbankofferingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  return interbankofferingsnapshot_ != NULL ? *interbankofferingsnapshot_
                         : *::com::htsc::mdc::insight::model::InterBankOfferingSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* MDCfetsCurrencySnapshot::mutable_interbankofferingsnapshot() {
  
  if (interbankofferingsnapshot_ == NULL) {
    interbankofferingsnapshot_ = new ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  return interbankofferingsnapshot_;
}
inline ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* MDCfetsCurrencySnapshot::release_interbankofferingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  
  ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* temp = interbankofferingsnapshot_;
  interbankofferingsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsCurrencySnapshot::set_allocated_interbankofferingsnapshot(::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* interbankofferingsnapshot) {
  delete interbankofferingsnapshot_;
  interbankofferingsnapshot_ = interbankofferingsnapshot;
  if (interbankofferingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
}

// optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
inline bool MDCfetsCurrencySnapshot::has_collateralreposnapshot() const {
  return this != internal_default_instance() && collateralreposnapshot_ != NULL;
}
inline void MDCfetsCurrencySnapshot::clear_collateralreposnapshot() {
  if (GetArenaNoVirtual() == NULL && collateralreposnapshot_ != NULL) delete collateralreposnapshot_;
  collateralreposnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::CollateralRepoSnapshot& MDCfetsCurrencySnapshot::collateralreposnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  return collateralreposnapshot_ != NULL ? *collateralreposnapshot_
                         : *::com::htsc::mdc::insight::model::CollateralRepoSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* MDCfetsCurrencySnapshot::mutable_collateralreposnapshot() {
  
  if (collateralreposnapshot_ == NULL) {
    collateralreposnapshot_ = new ::com::htsc::mdc::insight::model::CollateralRepoSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  return collateralreposnapshot_;
}
inline ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* MDCfetsCurrencySnapshot::release_collateralreposnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  
  ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* temp = collateralreposnapshot_;
  collateralreposnapshot_ = NULL;
  return temp;
}
inline void MDCfetsCurrencySnapshot::set_allocated_collateralreposnapshot(::com::htsc::mdc::insight::model::CollateralRepoSnapshot* collateralreposnapshot) {
  delete collateralreposnapshot_;
  collateralreposnapshot_ = collateralreposnapshot;
  if (collateralreposnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
}

// optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
inline bool MDCfetsCurrencySnapshot::has_outrightreposnapshot() const {
  return this != internal_default_instance() && outrightreposnapshot_ != NULL;
}
inline void MDCfetsCurrencySnapshot::clear_outrightreposnapshot() {
  if (GetArenaNoVirtual() == NULL && outrightreposnapshot_ != NULL) delete outrightreposnapshot_;
  outrightreposnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::OutrightRepoSnapshot& MDCfetsCurrencySnapshot::outrightreposnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  return outrightreposnapshot_ != NULL ? *outrightreposnapshot_
                         : *::com::htsc::mdc::insight::model::OutrightRepoSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* MDCfetsCurrencySnapshot::mutable_outrightreposnapshot() {
  
  if (outrightreposnapshot_ == NULL) {
    outrightreposnapshot_ = new ::com::htsc::mdc::insight::model::OutrightRepoSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  return outrightreposnapshot_;
}
inline ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* MDCfetsCurrencySnapshot::release_outrightreposnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  
  ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* temp = outrightreposnapshot_;
  outrightreposnapshot_ = NULL;
  return temp;
}
inline void MDCfetsCurrencySnapshot::set_allocated_outrightreposnapshot(::com::htsc::mdc::insight::model::OutrightRepoSnapshot* outrightreposnapshot) {
  delete outrightreposnapshot_;
  outrightreposnapshot_ = outrightreposnapshot;
  if (outrightreposnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
}

// optional int32 DataMultiplePowerOf10 = 13;
inline void MDCfetsCurrencySnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencySnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsCurrencySnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataMultiplePowerOf10)
}

inline const MDCfetsCurrencySnapshot* MDCfetsCurrencySnapshot::internal_default_instance() {
  return &MDCfetsCurrencySnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// InterBankOfferingSnapshot

// optional int64 PreCloseRate = 1;
inline void InterBankOfferingSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreCloseRate)
  return precloserate_;
}
inline void InterBankOfferingSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 2;
inline void InterBankOfferingSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreWeightedRate)
  return preweightedrate_;
}
inline void InterBankOfferingSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 3;
inline void InterBankOfferingSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.OpenRate)
  return openrate_;
}
inline void InterBankOfferingSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.OpenRate)
}

// optional int64 LastRate = 4;
inline void InterBankOfferingSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LastRate)
  return lastrate_;
}
inline void InterBankOfferingSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LastRate)
}

// optional int64 HighRate = 5;
inline void InterBankOfferingSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.HighRate)
  return highrate_;
}
inline void InterBankOfferingSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.HighRate)
}

// optional int64 LowRate = 6;
inline void InterBankOfferingSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LowRate)
  return lowrate_;
}
inline void InterBankOfferingSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LowRate)
}

// optional int64 CloseRate = 7;
inline void InterBankOfferingSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.CloseRate)
  return closerate_;
}
inline void InterBankOfferingSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.CloseRate)
}

// optional int64 WeightedRate = 8;
inline void InterBankOfferingSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.WeightedRate)
  return weightedrate_;
}
inline void InterBankOfferingSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 9;
inline void InterBankOfferingSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
inline void InterBankOfferingSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.TotalValueTrade)
}

// optional int32 NumTrades = 10;
inline void InterBankOfferingSnapshot::clear_numtrades() {
  numtrades_ = 0;
}
inline ::google::protobuf::int32 InterBankOfferingSnapshot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.NumTrades)
  return numtrades_;
}
inline void InterBankOfferingSnapshot::set_numtrades(::google::protobuf::int32 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.NumTrades)
}

// optional int64 AverageTerm = 11;
inline void InterBankOfferingSnapshot::clear_averageterm() {
  averageterm_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 InterBankOfferingSnapshot::averageterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.AverageTerm)
  return averageterm_;
}
inline void InterBankOfferingSnapshot::set_averageterm(::google::protobuf::int64 value) {
  
  averageterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.AverageTerm)
}

inline const InterBankOfferingSnapshot* InterBankOfferingSnapshot::internal_default_instance() {
  return &InterBankOfferingSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// CollateralRepoSnapshot

// optional int32 TradeMethod = 1;
inline void CollateralRepoSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 CollateralRepoSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TradeMethod)
  return trademethod_;
}
inline void CollateralRepoSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TradeMethod)
}

// optional int64 PreCloseRate = 2;
inline void CollateralRepoSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreCloseRate)
  return precloserate_;
}
inline void CollateralRepoSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 3;
inline void CollateralRepoSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreWeightedRate)
  return preweightedrate_;
}
inline void CollateralRepoSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 4;
inline void CollateralRepoSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.OpenRate)
  return openrate_;
}
inline void CollateralRepoSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.OpenRate)
}

// optional int64 LastRate = 5;
inline void CollateralRepoSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LastRate)
  return lastrate_;
}
inline void CollateralRepoSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LastRate)
}

// optional int64 HighRate = 6;
inline void CollateralRepoSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.HighRate)
  return highrate_;
}
inline void CollateralRepoSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.HighRate)
}

// optional int64 LowRate = 7;
inline void CollateralRepoSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LowRate)
  return lowrate_;
}
inline void CollateralRepoSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LowRate)
}

// optional int64 CloseRate = 8;
inline void CollateralRepoSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.CloseRate)
  return closerate_;
}
inline void CollateralRepoSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.CloseRate)
}

// optional int64 WeightedRate = 9;
inline void CollateralRepoSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.WeightedRate)
  return weightedrate_;
}
inline void CollateralRepoSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 10;
inline void CollateralRepoSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
inline void CollateralRepoSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TotalValueTrade)
}

// optional int32 NumTrades = 11;
inline void CollateralRepoSnapshot::clear_numtrades() {
  numtrades_ = 0;
}
inline ::google::protobuf::int32 CollateralRepoSnapshot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.NumTrades)
  return numtrades_;
}
inline void CollateralRepoSnapshot::set_numtrades(::google::protobuf::int32 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.NumTrades)
}

// optional int64 AverageTerm = 12;
inline void CollateralRepoSnapshot::clear_averageterm() {
  averageterm_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::averageterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.AverageTerm)
  return averageterm_;
}
inline void CollateralRepoSnapshot::set_averageterm(::google::protobuf::int64 value) {
  
  averageterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.AverageTerm)
}

// optional int64 IRBondWeightedRate = 13;
inline void CollateralRepoSnapshot::clear_irbondweightedrate() {
  irbondweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CollateralRepoSnapshot::irbondweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.IRBondWeightedRate)
  return irbondweightedrate_;
}
inline void CollateralRepoSnapshot::set_irbondweightedrate(::google::protobuf::int64 value) {
  
  irbondweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.IRBondWeightedRate)
}

// optional string RepoMethod = 14;
inline void CollateralRepoSnapshot::clear_repomethod() {
  repomethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CollateralRepoSnapshot::repomethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  return repomethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoSnapshot::set_repomethod(const ::std::string& value) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
inline void CollateralRepoSnapshot::set_repomethod(const char* value) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
inline void CollateralRepoSnapshot::set_repomethod(const char* value, size_t size) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
inline ::std::string* CollateralRepoSnapshot::mutable_repomethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  return repomethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CollateralRepoSnapshot::release_repomethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  
  return repomethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoSnapshot::set_allocated_repomethod(::std::string* repomethod) {
  if (repomethod != NULL) {
    
  } else {
    
  }
  repomethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), repomethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}

inline const CollateralRepoSnapshot* CollateralRepoSnapshot::internal_default_instance() {
  return &CollateralRepoSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// OutrightRepoSnapshot

// optional int64 PreCloseRate = 1;
inline void OutrightRepoSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreCloseRate)
  return precloserate_;
}
inline void OutrightRepoSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 2;
inline void OutrightRepoSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreWeightedRate)
  return preweightedrate_;
}
inline void OutrightRepoSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 3;
inline void OutrightRepoSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.OpenRate)
  return openrate_;
}
inline void OutrightRepoSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.OpenRate)
}

// optional int64 LastRate = 4;
inline void OutrightRepoSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LastRate)
  return lastrate_;
}
inline void OutrightRepoSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LastRate)
}

// optional int64 HighRate = 5;
inline void OutrightRepoSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.HighRate)
  return highrate_;
}
inline void OutrightRepoSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.HighRate)
}

// optional int64 LowRate = 6;
inline void OutrightRepoSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LowRate)
  return lowrate_;
}
inline void OutrightRepoSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LowRate)
}

// optional int64 CloseRate = 7;
inline void OutrightRepoSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.CloseRate)
  return closerate_;
}
inline void OutrightRepoSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.CloseRate)
}

// optional int64 WeightedRate = 8;
inline void OutrightRepoSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.WeightedRate)
  return weightedrate_;
}
inline void OutrightRepoSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 9;
inline void OutrightRepoSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OutrightRepoSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
inline void OutrightRepoSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.TotalValueTrade)
}

inline const OutrightRepoSnapshot* OutrightRepoSnapshot::internal_default_instance() {
  return &OutrightRepoSnapshot_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsCurrencySnapshot_2eproto__INCLUDED
