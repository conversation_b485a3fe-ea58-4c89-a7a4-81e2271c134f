// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSATransaction.proto

#ifndef PROTOBUF_MDUSATransaction_2eproto__INCLUDED
#define PROTOBUF_MDUSATransaction_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDUSATransaction_2eproto();
void protobuf_InitDefaults_MDUSATransaction_2eproto();
void protobuf_AssignDesc_MDUSATransaction_2eproto();
void protobuf_ShutdownFile_MDUSATransaction_2eproto();

class MDUSATransaction;

// ===================================================================

class MDUSATransaction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDUSATransaction) */ {
 public:
  MDUSATransaction();
  virtual ~MDUSATransaction();

  MDUSATransaction(const MDUSATransaction& from);

  inline MDUSATransaction& operator=(const MDUSATransaction& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDUSATransaction& default_instance();

  static const MDUSATransaction* internal_default_instance();

  void Swap(MDUSATransaction* other);

  // implements Message ----------------------------------------------

  inline MDUSATransaction* New() const { return New(NULL); }

  MDUSATransaction* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDUSATransaction& from);
  void MergeFrom(const MDUSATransaction& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDUSATransaction* other);
  void UnsafeMergeFrom(const MDUSATransaction& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 7;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 7;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 8;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 8;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 Nanosecond = 9;
  void clear_nanosecond();
  static const int kNanosecondFieldNumber = 9;
  ::google::protobuf::int32 nanosecond() const;
  void set_nanosecond(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 10;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 10;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional string TradeNum = 11;
  void clear_tradenum();
  static const int kTradeNumFieldNumber = 11;
  const ::std::string& tradenum() const;
  void set_tradenum(const ::std::string& value);
  void set_tradenum(const char* value);
  void set_tradenum(const char* value, size_t size);
  ::std::string* mutable_tradenum();
  ::std::string* release_tradenum();
  void set_allocated_tradenum(::std::string* tradenum);

  // optional string OriginalTradeNum = 12;
  void clear_originaltradenum();
  static const int kOriginalTradeNumFieldNumber = 12;
  const ::std::string& originaltradenum() const;
  void set_originaltradenum(const ::std::string& value);
  void set_originaltradenum(const char* value);
  void set_originaltradenum(const char* value, size_t size);
  ::std::string* mutable_originaltradenum();
  ::std::string* release_originaltradenum();
  void set_allocated_originaltradenum(::std::string* originaltradenum);

  // optional int64 TradeBuyNo = 13;
  void clear_tradebuyno();
  static const int kTradeBuyNoFieldNumber = 13;
  ::google::protobuf::int64 tradebuyno() const;
  void set_tradebuyno(::google::protobuf::int64 value);

  // optional int64 TradeSellNo = 14;
  void clear_tradesellno();
  static const int kTradeSellNoFieldNumber = 14;
  ::google::protobuf::int64 tradesellno() const;
  void set_tradesellno(::google::protobuf::int64 value);

  // optional int32 TradeType = 15;
  void clear_tradetype();
  static const int kTradeTypeFieldNumber = 15;
  ::google::protobuf::int32 tradetype() const;
  void set_tradetype(::google::protobuf::int32 value);

  // optional int32 TradeBSFlag = 16;
  void clear_tradebsflag();
  static const int kTradeBSFlagFieldNumber = 16;
  ::google::protobuf::int32 tradebsflag() const;
  void set_tradebsflag(::google::protobuf::int32 value);

  // optional int64 TradePrice = 17;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 17;
  ::google::protobuf::int64 tradeprice() const;
  void set_tradeprice(::google::protobuf::int64 value);

  // optional int64 TradeQty = 18;
  void clear_tradeqty();
  static const int kTradeQtyFieldNumber = 18;
  ::google::protobuf::int64 tradeqty() const;
  void set_tradeqty(::google::protobuf::int64 value);

  // optional int64 TradeMoney = 19;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 19;
  ::google::protobuf::int64 trademoney() const;
  void set_trademoney(::google::protobuf::int64 value);

  // optional int64 NAVOffsetAmount = 20;
  void clear_navoffsetamount();
  static const int kNAVOffsetAmountFieldNumber = 20;
  ::google::protobuf::int64 navoffsetamount() const;
  void set_navoffsetamount(::google::protobuf::int64 value);

  // optional int64 TotalConsolidateVolume = 21;
  void clear_totalconsolidatevolume();
  static const int kTotalConsolidateVolumeFieldNumber = 21;
  ::google::protobuf::int64 totalconsolidatevolume() const;
  void set_totalconsolidatevolume(::google::protobuf::int64 value);

  // optional string SaleConditionLV1 = 22;
  void clear_saleconditionlv1();
  static const int kSaleConditionLV1FieldNumber = 22;
  const ::std::string& saleconditionlv1() const;
  void set_saleconditionlv1(const ::std::string& value);
  void set_saleconditionlv1(const char* value);
  void set_saleconditionlv1(const char* value, size_t size);
  ::std::string* mutable_saleconditionlv1();
  ::std::string* release_saleconditionlv1();
  void set_allocated_saleconditionlv1(::std::string* saleconditionlv1);

  // optional string SaleConditionLV2 = 23;
  void clear_saleconditionlv2();
  static const int kSaleConditionLV2FieldNumber = 23;
  const ::std::string& saleconditionlv2() const;
  void set_saleconditionlv2(const ::std::string& value);
  void set_saleconditionlv2(const char* value);
  void set_saleconditionlv2(const char* value, size_t size);
  ::std::string* mutable_saleconditionlv2();
  ::std::string* release_saleconditionlv2();
  void set_allocated_saleconditionlv2(::std::string* saleconditionlv2);

  // optional string SaleConditionLV3 = 24;
  void clear_saleconditionlv3();
  static const int kSaleConditionLV3FieldNumber = 24;
  const ::std::string& saleconditionlv3() const;
  void set_saleconditionlv3(const ::std::string& value);
  void set_saleconditionlv3(const char* value);
  void set_saleconditionlv3(const char* value, size_t size);
  ::std::string* mutable_saleconditionlv3();
  ::std::string* release_saleconditionlv3();
  void set_allocated_saleconditionlv3(::std::string* saleconditionlv3);

  // optional string SaleConditionLV4 = 25;
  void clear_saleconditionlv4();
  static const int kSaleConditionLV4FieldNumber = 25;
  const ::std::string& saleconditionlv4() const;
  void set_saleconditionlv4(const ::std::string& value);
  void set_saleconditionlv4(const char* value);
  void set_saleconditionlv4(const char* value, size_t size);
  ::std::string* mutable_saleconditionlv4();
  ::std::string* release_saleconditionlv4();
  void set_allocated_saleconditionlv4(::std::string* saleconditionlv4);

  // optional int32 TrackingNum = 26;
  void clear_trackingnum();
  static const int kTrackingNumFieldNumber = 26;
  ::google::protobuf::int32 trackingnum() const;
  void set_trackingnum(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 27;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 27;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 TimeIndex = 28;
  void clear_timeindex();
  static const int kTimeIndexFieldNumber = 28;
  ::google::protobuf::int32 timeindex() const;
  void set_timeindex(::google::protobuf::int32 value);

  // optional int64 DataIndex = 29;
  void clear_dataindex();
  static const int kDataIndexFieldNumber = 29;
  ::google::protobuf::int64 dataindex() const;
  void set_dataindex(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDUSATransaction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradenum_;
  ::google::protobuf::internal::ArenaStringPtr originaltradenum_;
  ::google::protobuf::internal::ArenaStringPtr saleconditionlv1_;
  ::google::protobuf::internal::ArenaStringPtr saleconditionlv2_;
  ::google::protobuf::internal::ArenaStringPtr saleconditionlv3_;
  ::google::protobuf::internal::ArenaStringPtr saleconditionlv4_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 nanosecond_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int64 tradebuyno_;
  ::google::protobuf::int64 tradesellno_;
  ::google::protobuf::int32 tradetype_;
  ::google::protobuf::int32 tradebsflag_;
  ::google::protobuf::int64 tradeprice_;
  ::google::protobuf::int64 tradeqty_;
  ::google::protobuf::int64 trademoney_;
  ::google::protobuf::int64 navoffsetamount_;
  ::google::protobuf::int64 totalconsolidatevolume_;
  ::google::protobuf::int32 trackingnum_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int64 dataindex_;
  ::google::protobuf::int32 timeindex_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDUSATransaction_2eproto_impl();
  friend void  protobuf_AddDesc_MDUSATransaction_2eproto_impl();
  friend void protobuf_AssignDesc_MDUSATransaction_2eproto();
  friend void protobuf_ShutdownFile_MDUSATransaction_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDUSATransaction> MDUSATransaction_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSATransaction

// optional string HTSCSecurityID = 1;
inline void MDUSATransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
inline void MDUSATransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
inline void MDUSATransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
inline ::std::string* MDUSATransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDUSATransaction::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.MDDate)
  return mddate_;
}
inline void MDUSATransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.MDDate)
}

// optional int32 MDTime = 3;
inline void MDUSATransaction::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.MDTime)
  return mdtime_;
}
inline void MDUSATransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDUSATransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataTimestamp)
  return datatimestamp_;
}
inline void MDUSATransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDUSATransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDUSATransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDUSATransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDUSATransaction::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDUSATransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDUSATransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.securityType)
}

// optional int32 ExchangeDate = 7;
inline void MDUSATransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeDate)
  return exchangedate_;
}
inline void MDUSATransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
inline void MDUSATransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeTime)
  return exchangetime_;
}
inline void MDUSATransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeTime)
}

// optional int32 Nanosecond = 9;
inline void MDUSATransaction::clear_nanosecond() {
  nanosecond_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.Nanosecond)
  return nanosecond_;
}
inline void MDUSATransaction::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.Nanosecond)
}

// optional int32 ChannelNo = 10;
inline void MDUSATransaction::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ChannelNo)
  return channelno_;
}
inline void MDUSATransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ChannelNo)
}

// optional string TradeNum = 11;
inline void MDUSATransaction::clear_tradenum() {
  tradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::tradenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  return tradenum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_tradenum(const ::std::string& value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
inline void MDUSATransaction::set_tradenum(const char* value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
inline void MDUSATransaction::set_tradenum(const char* value, size_t size) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
inline ::std::string* MDUSATransaction::mutable_tradenum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  return tradenum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_tradenum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  
  return tradenum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_tradenum(::std::string* tradenum) {
  if (tradenum != NULL) {
    
  } else {
    
  }
  tradenum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradenum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}

// optional string OriginalTradeNum = 12;
inline void MDUSATransaction::clear_originaltradenum() {
  originaltradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::originaltradenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  return originaltradenum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_originaltradenum(const ::std::string& value) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
inline void MDUSATransaction::set_originaltradenum(const char* value) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
inline void MDUSATransaction::set_originaltradenum(const char* value, size_t size) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
inline ::std::string* MDUSATransaction::mutable_originaltradenum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  return originaltradenum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_originaltradenum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  
  return originaltradenum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_originaltradenum(::std::string* originaltradenum) {
  if (originaltradenum != NULL) {
    
  } else {
    
  }
  originaltradenum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), originaltradenum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}

// optional int64 TradeBuyNo = 13;
inline void MDUSATransaction::clear_tradebuyno() {
  tradebuyno_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::tradebuyno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeBuyNo)
  return tradebuyno_;
}
inline void MDUSATransaction::set_tradebuyno(::google::protobuf::int64 value) {
  
  tradebuyno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeBuyNo)
}

// optional int64 TradeSellNo = 14;
inline void MDUSATransaction::clear_tradesellno() {
  tradesellno_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::tradesellno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeSellNo)
  return tradesellno_;
}
inline void MDUSATransaction::set_tradesellno(::google::protobuf::int64 value) {
  
  tradesellno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeSellNo)
}

// optional int32 TradeType = 15;
inline void MDUSATransaction::clear_tradetype() {
  tradetype_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeType)
  return tradetype_;
}
inline void MDUSATransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeType)
}

// optional int32 TradeBSFlag = 16;
inline void MDUSATransaction::clear_tradebsflag() {
  tradebsflag_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::tradebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeBSFlag)
  return tradebsflag_;
}
inline void MDUSATransaction::set_tradebsflag(::google::protobuf::int32 value) {
  
  tradebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeBSFlag)
}

// optional int64 TradePrice = 17;
inline void MDUSATransaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradePrice)
  return tradeprice_;
}
inline void MDUSATransaction::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradePrice)
}

// optional int64 TradeQty = 18;
inline void MDUSATransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeQty)
  return tradeqty_;
}
inline void MDUSATransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeQty)
}

// optional int64 TradeMoney = 19;
inline void MDUSATransaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeMoney)
  return trademoney_;
}
inline void MDUSATransaction::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeMoney)
}

// optional int64 NAVOffsetAmount = 20;
inline void MDUSATransaction::clear_navoffsetamount() {
  navoffsetamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::navoffsetamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.NAVOffsetAmount)
  return navoffsetamount_;
}
inline void MDUSATransaction::set_navoffsetamount(::google::protobuf::int64 value) {
  
  navoffsetamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.NAVOffsetAmount)
}

// optional int64 TotalConsolidateVolume = 21;
inline void MDUSATransaction::clear_totalconsolidatevolume() {
  totalconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::totalconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TotalConsolidateVolume)
  return totalconsolidatevolume_;
}
inline void MDUSATransaction::set_totalconsolidatevolume(::google::protobuf::int64 value) {
  
  totalconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TotalConsolidateVolume)
}

// optional string SaleConditionLV1 = 22;
inline void MDUSATransaction::clear_saleconditionlv1() {
  saleconditionlv1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::saleconditionlv1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  return saleconditionlv1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_saleconditionlv1(const ::std::string& value) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
inline void MDUSATransaction::set_saleconditionlv1(const char* value) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
inline void MDUSATransaction::set_saleconditionlv1(const char* value, size_t size) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
inline ::std::string* MDUSATransaction::mutable_saleconditionlv1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  return saleconditionlv1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_saleconditionlv1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  
  return saleconditionlv1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_saleconditionlv1(::std::string* saleconditionlv1) {
  if (saleconditionlv1 != NULL) {
    
  } else {
    
  }
  saleconditionlv1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}

// optional string SaleConditionLV2 = 23;
inline void MDUSATransaction::clear_saleconditionlv2() {
  saleconditionlv2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::saleconditionlv2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  return saleconditionlv2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_saleconditionlv2(const ::std::string& value) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
inline void MDUSATransaction::set_saleconditionlv2(const char* value) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
inline void MDUSATransaction::set_saleconditionlv2(const char* value, size_t size) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
inline ::std::string* MDUSATransaction::mutable_saleconditionlv2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  return saleconditionlv2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_saleconditionlv2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  
  return saleconditionlv2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_saleconditionlv2(::std::string* saleconditionlv2) {
  if (saleconditionlv2 != NULL) {
    
  } else {
    
  }
  saleconditionlv2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}

// optional string SaleConditionLV3 = 24;
inline void MDUSATransaction::clear_saleconditionlv3() {
  saleconditionlv3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::saleconditionlv3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  return saleconditionlv3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_saleconditionlv3(const ::std::string& value) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
inline void MDUSATransaction::set_saleconditionlv3(const char* value) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
inline void MDUSATransaction::set_saleconditionlv3(const char* value, size_t size) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
inline ::std::string* MDUSATransaction::mutable_saleconditionlv3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  return saleconditionlv3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_saleconditionlv3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  
  return saleconditionlv3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_saleconditionlv3(::std::string* saleconditionlv3) {
  if (saleconditionlv3 != NULL) {
    
  } else {
    
  }
  saleconditionlv3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}

// optional string SaleConditionLV4 = 25;
inline void MDUSATransaction::clear_saleconditionlv4() {
  saleconditionlv4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSATransaction::saleconditionlv4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  return saleconditionlv4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_saleconditionlv4(const ::std::string& value) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
inline void MDUSATransaction::set_saleconditionlv4(const char* value) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
inline void MDUSATransaction::set_saleconditionlv4(const char* value, size_t size) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
inline ::std::string* MDUSATransaction::mutable_saleconditionlv4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  return saleconditionlv4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSATransaction::release_saleconditionlv4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  
  return saleconditionlv4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSATransaction::set_allocated_saleconditionlv4(::std::string* saleconditionlv4) {
  if (saleconditionlv4 != NULL) {
    
  } else {
    
  }
  saleconditionlv4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}

// optional int32 TrackingNum = 26;
inline void MDUSATransaction::clear_trackingnum() {
  trackingnum_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::trackingnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TrackingNum)
  return trackingnum_;
}
inline void MDUSATransaction::set_trackingnum(::google::protobuf::int32 value) {
  
  trackingnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TrackingNum)
}

// optional int32 DataMultiplePowerOf10 = 27;
inline void MDUSATransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDUSATransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 28;
inline void MDUSATransaction::clear_timeindex() {
  timeindex_ = 0;
}
inline ::google::protobuf::int32 MDUSATransaction::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TimeIndex)
  return timeindex_;
}
inline void MDUSATransaction::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TimeIndex)
}

// optional int64 DataIndex = 29;
inline void MDUSATransaction::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSATransaction::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataIndex)
  return dataindex_;
}
inline void MDUSATransaction::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataIndex)
}

inline const MDUSATransaction* MDUSATransaction::internal_default_instance() {
  return &MDUSATransaction_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDUSATransaction_2eproto__INCLUDED
