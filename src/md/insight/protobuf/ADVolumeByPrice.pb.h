// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADVolumeByPrice.proto

#ifndef PROTOBUF_ADVolumeByPrice_2eproto__INCLUDED
#define PROTOBUF_ADVolumeByPrice_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_ADVolumeByPrice_2eproto();
void protobuf_InitDefaults_ADVolumeByPrice_2eproto();
void protobuf_AssignDesc_ADVolumeByPrice_2eproto();
void protobuf_ShutdownFile_ADVolumeByPrice_2eproto();

class ADVolumeByPrice;
class ADVolumeByPriceDetail;

// ===================================================================

class ADVolumeByPrice : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADVolumeByPrice) */ {
 public:
  ADVolumeByPrice();
  virtual ~ADVolumeByPrice();

  ADVolumeByPrice(const ADVolumeByPrice& from);

  inline ADVolumeByPrice& operator=(const ADVolumeByPrice& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADVolumeByPrice& default_instance();

  static const ADVolumeByPrice* internal_default_instance();

  void Swap(ADVolumeByPrice* other);

  // implements Message ----------------------------------------------

  inline ADVolumeByPrice* New() const { return New(NULL); }

  ADVolumeByPrice* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADVolumeByPrice& from);
  void MergeFrom(const ADVolumeByPrice& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADVolumeByPrice* other);
  void UnsafeMergeFrom(const ADVolumeByPrice& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 TotalVolumeTrade = 7;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 7;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
  int details_size() const;
  void clear_details();
  static const int kDetailsFieldNumber = 8;
  const ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail& details(int index) const;
  ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* mutable_details(int index);
  ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* add_details();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >*
      mutable_details();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >&
      details() const;

  // optional int32 ExchangeDate = 9;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 9;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 10;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 10;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 11;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 11;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADVolumeByPrice)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail > details_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADVolumeByPrice_2eproto_impl();
  friend void  protobuf_AddDesc_ADVolumeByPrice_2eproto_impl();
  friend void protobuf_AssignDesc_ADVolumeByPrice_2eproto();
  friend void protobuf_ShutdownFile_ADVolumeByPrice_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADVolumeByPrice> ADVolumeByPrice_default_instance_;

// -------------------------------------------------------------------

class ADVolumeByPriceDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADVolumeByPriceDetail) */ {
 public:
  ADVolumeByPriceDetail();
  virtual ~ADVolumeByPriceDetail();

  ADVolumeByPriceDetail(const ADVolumeByPriceDetail& from);

  inline ADVolumeByPriceDetail& operator=(const ADVolumeByPriceDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADVolumeByPriceDetail& default_instance();

  static const ADVolumeByPriceDetail* internal_default_instance();

  void Swap(ADVolumeByPriceDetail* other);

  // implements Message ----------------------------------------------

  inline ADVolumeByPriceDetail* New() const { return New(NULL); }

  ADVolumeByPriceDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADVolumeByPriceDetail& from);
  void MergeFrom(const ADVolumeByPriceDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADVolumeByPriceDetail* other);
  void UnsafeMergeFrom(const ADVolumeByPriceDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 TradePrice = 1;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 1;
  ::google::protobuf::int64 tradeprice() const;
  void set_tradeprice(::google::protobuf::int64 value);

  // optional int64 TotalQty = 2;
  void clear_totalqty();
  static const int kTotalQtyFieldNumber = 2;
  ::google::protobuf::int64 totalqty() const;
  void set_totalqty(::google::protobuf::int64 value);

  // optional int64 BuyQty = 3;
  void clear_buyqty();
  static const int kBuyQtyFieldNumber = 3;
  ::google::protobuf::int64 buyqty() const;
  void set_buyqty(::google::protobuf::int64 value);

  // optional int64 SellQty = 4;
  void clear_sellqty();
  static const int kSellQtyFieldNumber = 4;
  ::google::protobuf::int64 sellqty() const;
  void set_sellqty(::google::protobuf::int64 value);

  // optional int64 TotalNumbers = 5;
  void clear_totalnumbers();
  static const int kTotalNumbersFieldNumber = 5;
  ::google::protobuf::int64 totalnumbers() const;
  void set_totalnumbers(::google::protobuf::int64 value);

  // optional int64 BuyNumbers = 6;
  void clear_buynumbers();
  static const int kBuyNumbersFieldNumber = 6;
  ::google::protobuf::int64 buynumbers() const;
  void set_buynumbers(::google::protobuf::int64 value);

  // optional int64 SellNumbers = 7;
  void clear_sellnumbers();
  static const int kSellNumbersFieldNumber = 7;
  ::google::protobuf::int64 sellnumbers() const;
  void set_sellnumbers(::google::protobuf::int64 value);

  // optional int64 VolumePerNumber = 8;
  void clear_volumepernumber();
  static const int kVolumePerNumberFieldNumber = 8;
  ::google::protobuf::int64 volumepernumber() const;
  void set_volumepernumber(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 tradeprice_;
  ::google::protobuf::int64 totalqty_;
  ::google::protobuf::int64 buyqty_;
  ::google::protobuf::int64 sellqty_;
  ::google::protobuf::int64 totalnumbers_;
  ::google::protobuf::int64 buynumbers_;
  ::google::protobuf::int64 sellnumbers_;
  ::google::protobuf::int64 volumepernumber_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADVolumeByPrice_2eproto_impl();
  friend void  protobuf_AddDesc_ADVolumeByPrice_2eproto_impl();
  friend void protobuf_AssignDesc_ADVolumeByPrice_2eproto();
  friend void protobuf_ShutdownFile_ADVolumeByPrice_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADVolumeByPriceDetail> ADVolumeByPriceDetail_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// ADVolumeByPrice

// optional string HTSCSecurityID = 1;
inline void ADVolumeByPrice::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADVolumeByPrice::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADVolumeByPrice::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
inline void ADVolumeByPrice::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
inline void ADVolumeByPrice::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
inline ::std::string* ADVolumeByPrice::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADVolumeByPrice::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADVolumeByPrice::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void ADVolumeByPrice::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 ADVolumeByPrice::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.MDDate)
  return mddate_;
}
inline void ADVolumeByPrice::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.MDDate)
}

// optional int32 MDTime = 3;
inline void ADVolumeByPrice::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 ADVolumeByPrice::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.MDTime)
  return mdtime_;
}
inline void ADVolumeByPrice::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void ADVolumeByPrice::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPrice::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.DataTimestamp)
  return datatimestamp_;
}
inline void ADVolumeByPrice::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void ADVolumeByPrice::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource ADVolumeByPrice::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void ADVolumeByPrice::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void ADVolumeByPrice::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType ADVolumeByPrice::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void ADVolumeByPrice::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.securityType)
}

// optional int64 TotalVolumeTrade = 7;
inline void ADVolumeByPrice::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPrice::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void ADVolumeByPrice::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.TotalVolumeTrade)
}

// repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
inline int ADVolumeByPrice::details_size() const {
  return details_.size();
}
inline void ADVolumeByPrice::clear_details() {
  details_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail& ADVolumeByPrice::details(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* ADVolumeByPrice::mutable_details(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* ADVolumeByPrice::add_details() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >*
ADVolumeByPrice::mutable_details() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return &details_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >&
ADVolumeByPrice::details() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_;
}

// optional int32 ExchangeDate = 9;
inline void ADVolumeByPrice::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 ADVolumeByPrice::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeDate)
  return exchangedate_;
}
inline void ADVolumeByPrice::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeDate)
}

// optional int32 ExchangeTime = 10;
inline void ADVolumeByPrice::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 ADVolumeByPrice::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeTime)
  return exchangetime_;
}
inline void ADVolumeByPrice::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 11;
inline void ADVolumeByPrice::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 ADVolumeByPrice::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void ADVolumeByPrice::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.DataMultiplePowerOf10)
}

inline const ADVolumeByPrice* ADVolumeByPrice::internal_default_instance() {
  return &ADVolumeByPrice_default_instance_.get();
}
// -------------------------------------------------------------------

// ADVolumeByPriceDetail

// optional int64 TradePrice = 1;
inline void ADVolumeByPriceDetail::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TradePrice)
  return tradeprice_;
}
inline void ADVolumeByPriceDetail::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TradePrice)
}

// optional int64 TotalQty = 2;
inline void ADVolumeByPriceDetail::clear_totalqty() {
  totalqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::totalqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalQty)
  return totalqty_;
}
inline void ADVolumeByPriceDetail::set_totalqty(::google::protobuf::int64 value) {
  
  totalqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalQty)
}

// optional int64 BuyQty = 3;
inline void ADVolumeByPriceDetail::clear_buyqty() {
  buyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::buyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyQty)
  return buyqty_;
}
inline void ADVolumeByPriceDetail::set_buyqty(::google::protobuf::int64 value) {
  
  buyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyQty)
}

// optional int64 SellQty = 4;
inline void ADVolumeByPriceDetail::clear_sellqty() {
  sellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::sellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellQty)
  return sellqty_;
}
inline void ADVolumeByPriceDetail::set_sellqty(::google::protobuf::int64 value) {
  
  sellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellQty)
}

// optional int64 TotalNumbers = 5;
inline void ADVolumeByPriceDetail::clear_totalnumbers() {
  totalnumbers_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::totalnumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalNumbers)
  return totalnumbers_;
}
inline void ADVolumeByPriceDetail::set_totalnumbers(::google::protobuf::int64 value) {
  
  totalnumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalNumbers)
}

// optional int64 BuyNumbers = 6;
inline void ADVolumeByPriceDetail::clear_buynumbers() {
  buynumbers_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::buynumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyNumbers)
  return buynumbers_;
}
inline void ADVolumeByPriceDetail::set_buynumbers(::google::protobuf::int64 value) {
  
  buynumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyNumbers)
}

// optional int64 SellNumbers = 7;
inline void ADVolumeByPriceDetail::clear_sellnumbers() {
  sellnumbers_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::sellnumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellNumbers)
  return sellnumbers_;
}
inline void ADVolumeByPriceDetail::set_sellnumbers(::google::protobuf::int64 value) {
  
  sellnumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellNumbers)
}

// optional int64 VolumePerNumber = 8;
inline void ADVolumeByPriceDetail::clear_volumepernumber() {
  volumepernumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADVolumeByPriceDetail::volumepernumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.VolumePerNumber)
  return volumepernumber_;
}
inline void ADVolumeByPriceDetail::set_volumepernumber(::google::protobuf::int64 value) {
  
  volumepernumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.VolumePerNumber)
}

inline const ADVolumeByPriceDetail* ADVolumeByPriceDetail::internal_default_instance() {
  return &ADVolumeByPriceDetail_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_ADVolumeByPrice_2eproto__INCLUDED
