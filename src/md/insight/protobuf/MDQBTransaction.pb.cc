// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQBTransaction.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDQBTransaction.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDQBTransaction_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDQBTransaction_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDQBTransaction_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDQBTransaction_2eproto() {
  protobuf_AddDesc_MDQBTransaction_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDQBTransaction.proto");
  GOOGLE_CHECK(file != NULL);
  MDQBTransaction_descriptor_ = file->message_type(0);
  static const int MDQBTransaction_offsets_[31] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradeid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, side_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, settldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, strikeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, yield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, originalprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, fullprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, instrmtassignmentmethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradestatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, pricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, clearspeed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, brokerdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, ssdetect_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, tradereqid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, settltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, dealtranstype_),
  };
  MDQBTransaction_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDQBTransaction_descriptor_,
      MDQBTransaction::internal_default_instance(),
      MDQBTransaction_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDQBTransaction),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBTransaction, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDQBTransaction_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDQBTransaction_descriptor_, MDQBTransaction::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDQBTransaction_2eproto() {
  MDQBTransaction_default_instance_.Shutdown();
  delete MDQBTransaction_reflection_;
}

void protobuf_InitDefaults_MDQBTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDQBTransaction_default_instance_.DefaultConstruct();
  MDQBTransaction_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDQBTransaction_2eproto_once_);
void protobuf_InitDefaults_MDQBTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDQBTransaction_2eproto_once_,
                 &protobuf_InitDefaults_MDQBTransaction_2eproto_impl);
}
void protobuf_AddDesc_MDQBTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDQBTransaction_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025MDQBTransaction.proto\022\032com.htsc.mdc.in"
    "sight.model\032\027ESecurityIDSource.proto\032\023ES"
    "ecurityType.proto\"\346\005\n\017MDQBTransaction\022\026\n"
    "\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n"
    "\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020"
    "TradingPhaseCode\030\005 \001(\t\022\?\n\020securityIDSour"
    "ce\030\006 \001(\0162%.com.htsc.mdc.model.ESecurityI"
    "DSource\0227\n\014securityType\030\007 \001(\0162!.com.htsc"
    ".mdc.model.ESecurityType\022\024\n\014ExchangeDate"
    "\030\010 \001(\005\022\024\n\014ExchangeTime\030\t \001(\005\022\021\n\tChannelN"
    "o\030\n \001(\005\022\022\n\nApplSeqNum\030\013 \001(\003\022\017\n\007TradeID\030\014"
    " \001(\t\022\014\n\004Side\030\r \001(\005\022\020\n\010TradeQty\030\016 \001(\003\022\021\n\t"
    "SettlDate\030\017 \001(\005\022\023\n\013StrikePrice\030\020 \001(\003\022\r\n\005"
    "Yield\030\021 \001(\003\022\025\n\rOriginalPrice\030\022 \001(\003\022\021\n\tFu"
    "llPrice\030\023 \001(\003\022\037\n\027InstrmtAssignmentMethod"
    "\030\024 \001(\005\022\023\n\013TradeStatus\030\025 \001(\005\022\021\n\tTradeType"
    "\030\026 \001(\005\022\021\n\tPriceType\030\027 \001(\005\022\022\n\nClearSpeed\030"
    "\030 \001(\005\022\026\n\016BrokerDataType\030\031 \001(\005\022\020\n\010SSDetec"
    "t\030\032 \001(\005\022\022\n\nTradeReqID\030\033 \001(\t\022\024\n\014TransactT"
    "ime\030\034 \001(\003\022\035\n\025DataMultiplePowerOf10\030\035 \001(\005"
    "\022\021\n\tSettlType\030\036 \001(\t\022\025\n\rDealTransType\030\037 \001"
    "(\005B8\n\032com.htsc.mdc.insight.modelB\025MDQBTr"
    "ansactionProtosH\001\240\001\001b\006proto3", 908);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDQBTransaction.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDQBTransaction_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDQBTransaction_2eproto_once_);
void protobuf_AddDesc_MDQBTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDQBTransaction_2eproto_once_,
                 &protobuf_AddDesc_MDQBTransaction_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDQBTransaction_2eproto {
  StaticDescriptorInitializer_MDQBTransaction_2eproto() {
    protobuf_AddDesc_MDQBTransaction_2eproto();
  }
} static_descriptor_initializer_MDQBTransaction_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDQBTransaction::kHTSCSecurityIDFieldNumber;
const int MDQBTransaction::kMDDateFieldNumber;
const int MDQBTransaction::kMDTimeFieldNumber;
const int MDQBTransaction::kDataTimestampFieldNumber;
const int MDQBTransaction::kTradingPhaseCodeFieldNumber;
const int MDQBTransaction::kSecurityIDSourceFieldNumber;
const int MDQBTransaction::kSecurityTypeFieldNumber;
const int MDQBTransaction::kExchangeDateFieldNumber;
const int MDQBTransaction::kExchangeTimeFieldNumber;
const int MDQBTransaction::kChannelNoFieldNumber;
const int MDQBTransaction::kApplSeqNumFieldNumber;
const int MDQBTransaction::kTradeIDFieldNumber;
const int MDQBTransaction::kSideFieldNumber;
const int MDQBTransaction::kTradeQtyFieldNumber;
const int MDQBTransaction::kSettlDateFieldNumber;
const int MDQBTransaction::kStrikePriceFieldNumber;
const int MDQBTransaction::kYieldFieldNumber;
const int MDQBTransaction::kOriginalPriceFieldNumber;
const int MDQBTransaction::kFullPriceFieldNumber;
const int MDQBTransaction::kInstrmtAssignmentMethodFieldNumber;
const int MDQBTransaction::kTradeStatusFieldNumber;
const int MDQBTransaction::kTradeTypeFieldNumber;
const int MDQBTransaction::kPriceTypeFieldNumber;
const int MDQBTransaction::kClearSpeedFieldNumber;
const int MDQBTransaction::kBrokerDataTypeFieldNumber;
const int MDQBTransaction::kSSDetectFieldNumber;
const int MDQBTransaction::kTradeReqIDFieldNumber;
const int MDQBTransaction::kTransactTimeFieldNumber;
const int MDQBTransaction::kDataMultiplePowerOf10FieldNumber;
const int MDQBTransaction::kSettlTypeFieldNumber;
const int MDQBTransaction::kDealTransTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDQBTransaction::MDQBTransaction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQBTransaction_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDQBTransaction)
}

void MDQBTransaction::InitAsDefaultInstance() {
}

MDQBTransaction::MDQBTransaction(const MDQBTransaction& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDQBTransaction)
}

void MDQBTransaction::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradeid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradereqid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settltype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&dealtranstype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(dealtranstype_));
  _cached_size_ = 0;
}

MDQBTransaction::~MDQBTransaction() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDQBTransaction)
  SharedDtor();
}

void MDQBTransaction::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradeid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradereqid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settltype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDQBTransaction::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDQBTransaction::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDQBTransaction_descriptor_;
}

const MDQBTransaction& MDQBTransaction::default_instance() {
  protobuf_InitDefaults_MDQBTransaction_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDQBTransaction> MDQBTransaction_default_instance_;

MDQBTransaction* MDQBTransaction::New(::google::protobuf::Arena* arena) const {
  MDQBTransaction* n = new MDQBTransaction;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDQBTransaction::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDQBTransaction)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDQBTransaction, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDQBTransaction*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangedate_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(exchangetime_, strikeprice_);
  tradeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settldate_ = 0;
  ZR_(instrmtassignmentmethod_, clearspeed_);
  yield_ = GOOGLE_LONGLONG(0);
  ZR_(brokerdatatype_, dealtranstype_);
  tradereqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDQBTransaction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDQBTransaction)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 9;
      case 9: {
        if (tag == 72) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 10;
      case 10: {
        if (tag == 80) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 11;
      case 11: {
        if (tag == 88) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_TradeID;
        break;
      }

      // optional string TradeID = 12;
      case 12: {
        if (tag == 98) {
         parse_TradeID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradeid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradeid().data(), this->tradeid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBTransaction.TradeID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_Side;
        break;
      }

      // optional int32 Side = 13;
      case 13: {
        if (tag == 104) {
         parse_Side:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &side_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_TradeQty;
        break;
      }

      // optional int64 TradeQty = 14;
      case 14: {
        if (tag == 112) {
         parse_TradeQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_SettlDate;
        break;
      }

      // optional int32 SettlDate = 15;
      case 15: {
        if (tag == 120) {
         parse_SettlDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settldate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_StrikePrice;
        break;
      }

      // optional int64 StrikePrice = 16;
      case 16: {
        if (tag == 128) {
         parse_StrikePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &strikeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_Yield;
        break;
      }

      // optional int64 Yield = 17;
      case 17: {
        if (tag == 136) {
         parse_Yield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &yield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_OriginalPrice;
        break;
      }

      // optional int64 OriginalPrice = 18;
      case 18: {
        if (tag == 144) {
         parse_OriginalPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &originalprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_FullPrice;
        break;
      }

      // optional int64 FullPrice = 19;
      case 19: {
        if (tag == 152) {
         parse_FullPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &fullprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_InstrmtAssignmentMethod;
        break;
      }

      // optional int32 InstrmtAssignmentMethod = 20;
      case 20: {
        if (tag == 160) {
         parse_InstrmtAssignmentMethod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &instrmtassignmentmethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TradeStatus;
        break;
      }

      // optional int32 TradeStatus = 21;
      case 21: {
        if (tag == 168) {
         parse_TradeStatus:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradestatus_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TradeType;
        break;
      }

      // optional int32 TradeType = 22;
      case 22: {
        if (tag == 176) {
         parse_TradeType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_PriceType;
        break;
      }

      // optional int32 PriceType = 23;
      case 23: {
        if (tag == 184) {
         parse_PriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &pricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_ClearSpeed;
        break;
      }

      // optional int32 ClearSpeed = 24;
      case 24: {
        if (tag == 192) {
         parse_ClearSpeed:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &clearspeed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_BrokerDataType;
        break;
      }

      // optional int32 BrokerDataType = 25;
      case 25: {
        if (tag == 200) {
         parse_BrokerDataType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &brokerdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_SSDetect;
        break;
      }

      // optional int32 SSDetect = 26;
      case 26: {
        if (tag == 208) {
         parse_SSDetect:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ssdetect_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_TradeReqID;
        break;
      }

      // optional string TradeReqID = 27;
      case 27: {
        if (tag == 218) {
         parse_TradeReqID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradereqid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradereqid().data(), this->tradereqid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_TransactTime;
        break;
      }

      // optional int64 TransactTime = 28;
      case 28: {
        if (tag == 224) {
         parse_TransactTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &transacttime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 29;
      case 29: {
        if (tag == 232) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_SettlType;
        break;
      }

      // optional string SettlType = 30;
      case 30: {
        if (tag == 242) {
         parse_SettlType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settltype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settltype().data(), this->settltype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBTransaction.SettlType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_DealTransType;
        break;
      }

      // optional int32 DealTransType = 31;
      case 31: {
        if (tag == 248) {
         parse_DealTransType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dealtranstype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDQBTransaction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDQBTransaction)
  return false;
#undef DO_
}

void MDQBTransaction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDQBTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->channelno(), output);
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->applseqnum(), output);
  }

  // optional string TradeID = 12;
  if (this->tradeid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradeid().data(), this->tradeid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradeID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->tradeid(), output);
  }

  // optional int32 Side = 13;
  if (this->side() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->side(), output);
  }

  // optional int64 TradeQty = 14;
  if (this->tradeqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->tradeqty(), output);
  }

  // optional int32 SettlDate = 15;
  if (this->settldate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->settldate(), output);
  }

  // optional int64 StrikePrice = 16;
  if (this->strikeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->strikeprice(), output);
  }

  // optional int64 Yield = 17;
  if (this->yield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->yield(), output);
  }

  // optional int64 OriginalPrice = 18;
  if (this->originalprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->originalprice(), output);
  }

  // optional int64 FullPrice = 19;
  if (this->fullprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->fullprice(), output);
  }

  // optional int32 InstrmtAssignmentMethod = 20;
  if (this->instrmtassignmentmethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->instrmtassignmentmethod(), output);
  }

  // optional int32 TradeStatus = 21;
  if (this->tradestatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->tradestatus(), output);
  }

  // optional int32 TradeType = 22;
  if (this->tradetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(22, this->tradetype(), output);
  }

  // optional int32 PriceType = 23;
  if (this->pricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(23, this->pricetype(), output);
  }

  // optional int32 ClearSpeed = 24;
  if (this->clearspeed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(24, this->clearspeed(), output);
  }

  // optional int32 BrokerDataType = 25;
  if (this->brokerdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->brokerdatatype(), output);
  }

  // optional int32 SSDetect = 26;
  if (this->ssdetect() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->ssdetect(), output);
  }

  // optional string TradeReqID = 27;
  if (this->tradereqid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradereqid().data(), this->tradereqid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      27, this->tradereqid(), output);
  }

  // optional int64 TransactTime = 28;
  if (this->transacttime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->transacttime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 29;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(29, this->datamultiplepowerof10(), output);
  }

  // optional string SettlType = 30;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.SettlType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      30, this->settltype(), output);
  }

  // optional int32 DealTransType = 31;
  if (this->dealtranstype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(31, this->dealtranstype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDQBTransaction)
}

::google::protobuf::uint8* MDQBTransaction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDQBTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->channelno(), target);
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->applseqnum(), target);
  }

  // optional string TradeID = 12;
  if (this->tradeid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradeid().data(), this->tradeid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradeID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->tradeid(), target);
  }

  // optional int32 Side = 13;
  if (this->side() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->side(), target);
  }

  // optional int64 TradeQty = 14;
  if (this->tradeqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->tradeqty(), target);
  }

  // optional int32 SettlDate = 15;
  if (this->settldate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->settldate(), target);
  }

  // optional int64 StrikePrice = 16;
  if (this->strikeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->strikeprice(), target);
  }

  // optional int64 Yield = 17;
  if (this->yield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->yield(), target);
  }

  // optional int64 OriginalPrice = 18;
  if (this->originalprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->originalprice(), target);
  }

  // optional int64 FullPrice = 19;
  if (this->fullprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->fullprice(), target);
  }

  // optional int32 InstrmtAssignmentMethod = 20;
  if (this->instrmtassignmentmethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->instrmtassignmentmethod(), target);
  }

  // optional int32 TradeStatus = 21;
  if (this->tradestatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->tradestatus(), target);
  }

  // optional int32 TradeType = 22;
  if (this->tradetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(22, this->tradetype(), target);
  }

  // optional int32 PriceType = 23;
  if (this->pricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(23, this->pricetype(), target);
  }

  // optional int32 ClearSpeed = 24;
  if (this->clearspeed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(24, this->clearspeed(), target);
  }

  // optional int32 BrokerDataType = 25;
  if (this->brokerdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->brokerdatatype(), target);
  }

  // optional int32 SSDetect = 26;
  if (this->ssdetect() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->ssdetect(), target);
  }

  // optional string TradeReqID = 27;
  if (this->tradereqid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradereqid().data(), this->tradereqid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        27, this->tradereqid(), target);
  }

  // optional int64 TransactTime = 28;
  if (this->transacttime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->transacttime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 29;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(29, this->datamultiplepowerof10(), target);
  }

  // optional string SettlType = 30;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBTransaction.SettlType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        30, this->settltype(), target);
  }

  // optional int32 DealTransType = 31;
  if (this->dealtranstype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(31, this->dealtranstype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDQBTransaction)
  return target;
}

size_t MDQBTransaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDQBTransaction)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional string TradeID = 12;
  if (this->tradeid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradeid());
  }

  // optional int32 Side = 13;
  if (this->side() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->side());
  }

  // optional int64 TradeQty = 14;
  if (this->tradeqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeqty());
  }

  // optional int32 SettlDate = 15;
  if (this->settldate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settldate());
  }

  // optional int64 StrikePrice = 16;
  if (this->strikeprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->strikeprice());
  }

  // optional int64 Yield = 17;
  if (this->yield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->yield());
  }

  // optional int64 OriginalPrice = 18;
  if (this->originalprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->originalprice());
  }

  // optional int64 FullPrice = 19;
  if (this->fullprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->fullprice());
  }

  // optional int32 InstrmtAssignmentMethod = 20;
  if (this->instrmtassignmentmethod() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->instrmtassignmentmethod());
  }

  // optional int32 TradeStatus = 21;
  if (this->tradestatus() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradestatus());
  }

  // optional int32 TradeType = 22;
  if (this->tradetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradetype());
  }

  // optional int32 PriceType = 23;
  if (this->pricetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->pricetype());
  }

  // optional int32 ClearSpeed = 24;
  if (this->clearspeed() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->clearspeed());
  }

  // optional int32 BrokerDataType = 25;
  if (this->brokerdatatype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->brokerdatatype());
  }

  // optional int32 SSDetect = 26;
  if (this->ssdetect() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ssdetect());
  }

  // optional string TradeReqID = 27;
  if (this->tradereqid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradereqid());
  }

  // optional int64 TransactTime = 28;
  if (this->transacttime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->transacttime());
  }

  // optional int32 DataMultiplePowerOf10 = 29;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string SettlType = 30;
  if (this->settltype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settltype());
  }

  // optional int32 DealTransType = 31;
  if (this->dealtranstype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dealtranstype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDQBTransaction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDQBTransaction)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDQBTransaction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDQBTransaction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDQBTransaction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDQBTransaction)
    UnsafeMergeFrom(*source);
  }
}

void MDQBTransaction::MergeFrom(const MDQBTransaction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDQBTransaction)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDQBTransaction::UnsafeMergeFrom(const MDQBTransaction& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.tradeid().size() > 0) {

    tradeid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradeid_);
  }
  if (from.side() != 0) {
    set_side(from.side());
  }
  if (from.tradeqty() != 0) {
    set_tradeqty(from.tradeqty());
  }
  if (from.settldate() != 0) {
    set_settldate(from.settldate());
  }
  if (from.strikeprice() != 0) {
    set_strikeprice(from.strikeprice());
  }
  if (from.yield() != 0) {
    set_yield(from.yield());
  }
  if (from.originalprice() != 0) {
    set_originalprice(from.originalprice());
  }
  if (from.fullprice() != 0) {
    set_fullprice(from.fullprice());
  }
  if (from.instrmtassignmentmethod() != 0) {
    set_instrmtassignmentmethod(from.instrmtassignmentmethod());
  }
  if (from.tradestatus() != 0) {
    set_tradestatus(from.tradestatus());
  }
  if (from.tradetype() != 0) {
    set_tradetype(from.tradetype());
  }
  if (from.pricetype() != 0) {
    set_pricetype(from.pricetype());
  }
  if (from.clearspeed() != 0) {
    set_clearspeed(from.clearspeed());
  }
  if (from.brokerdatatype() != 0) {
    set_brokerdatatype(from.brokerdatatype());
  }
  if (from.ssdetect() != 0) {
    set_ssdetect(from.ssdetect());
  }
  if (from.tradereqid().size() > 0) {

    tradereqid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradereqid_);
  }
  if (from.transacttime() != 0) {
    set_transacttime(from.transacttime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.settltype().size() > 0) {

    settltype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settltype_);
  }
  if (from.dealtranstype() != 0) {
    set_dealtranstype(from.dealtranstype());
  }
}

void MDQBTransaction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDQBTransaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDQBTransaction::CopyFrom(const MDQBTransaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDQBTransaction)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDQBTransaction::IsInitialized() const {

  return true;
}

void MDQBTransaction::Swap(MDQBTransaction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDQBTransaction::InternalSwap(MDQBTransaction* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  std::swap(applseqnum_, other->applseqnum_);
  tradeid_.Swap(&other->tradeid_);
  std::swap(side_, other->side_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(settldate_, other->settldate_);
  std::swap(strikeprice_, other->strikeprice_);
  std::swap(yield_, other->yield_);
  std::swap(originalprice_, other->originalprice_);
  std::swap(fullprice_, other->fullprice_);
  std::swap(instrmtassignmentmethod_, other->instrmtassignmentmethod_);
  std::swap(tradestatus_, other->tradestatus_);
  std::swap(tradetype_, other->tradetype_);
  std::swap(pricetype_, other->pricetype_);
  std::swap(clearspeed_, other->clearspeed_);
  std::swap(brokerdatatype_, other->brokerdatatype_);
  std::swap(ssdetect_, other->ssdetect_);
  tradereqid_.Swap(&other->tradereqid_);
  std::swap(transacttime_, other->transacttime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  settltype_.Swap(&other->settltype_);
  std::swap(dealtranstype_, other->dealtranstype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDQBTransaction::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDQBTransaction_descriptor_;
  metadata.reflection = MDQBTransaction_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQBTransaction

// optional string HTSCSecurityID = 1;
void MDQBTransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBTransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
void MDQBTransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
void MDQBTransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
::std::string* MDQBTransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBTransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDQBTransaction::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDQBTransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.MDDate)
  return mddate_;
}
void MDQBTransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.MDDate)
}

// optional int32 MDTime = 3;
void MDQBTransaction::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDQBTransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.MDTime)
  return mdtime_;
}
void MDQBTransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDQBTransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DataTimestamp)
  return datatimestamp_;
}
void MDQBTransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDQBTransaction::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBTransaction::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
void MDQBTransaction::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
void MDQBTransaction::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
::std::string* MDQBTransaction::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBTransaction::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDQBTransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDQBTransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDQBTransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDQBTransaction::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDQBTransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDQBTransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.securityType)
}

// optional int32 ExchangeDate = 8;
void MDQBTransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDQBTransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeDate)
  return exchangedate_;
}
void MDQBTransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
void MDQBTransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDQBTransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeTime)
  return exchangetime_;
}
void MDQBTransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeTime)
}

// optional int32 ChannelNo = 10;
void MDQBTransaction::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDQBTransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ChannelNo)
  return channelno_;
}
void MDQBTransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ChannelNo)
}

// optional int64 ApplSeqNum = 11;
void MDQBTransaction::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ApplSeqNum)
  return applseqnum_;
}
void MDQBTransaction::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ApplSeqNum)
}

// optional string TradeID = 12;
void MDQBTransaction::clear_tradeid() {
  tradeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBTransaction::tradeid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  return tradeid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_tradeid(const ::std::string& value) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
void MDQBTransaction::set_tradeid(const char* value) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
void MDQBTransaction::set_tradeid(const char* value, size_t size) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
::std::string* MDQBTransaction::mutable_tradeid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  return tradeid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBTransaction::release_tradeid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  
  return tradeid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_allocated_tradeid(::std::string* tradeid) {
  if (tradeid != NULL) {
    
  } else {
    
  }
  tradeid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}

// optional int32 Side = 13;
void MDQBTransaction::clear_side() {
  side_ = 0;
}
::google::protobuf::int32 MDQBTransaction::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.Side)
  return side_;
}
void MDQBTransaction::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.Side)
}

// optional int64 TradeQty = 14;
void MDQBTransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeQty)
  return tradeqty_;
}
void MDQBTransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeQty)
}

// optional int32 SettlDate = 15;
void MDQBTransaction::clear_settldate() {
  settldate_ = 0;
}
::google::protobuf::int32 MDQBTransaction::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SettlDate)
  return settldate_;
}
void MDQBTransaction::set_settldate(::google::protobuf::int32 value) {
  
  settldate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SettlDate)
}

// optional int64 StrikePrice = 16;
void MDQBTransaction::clear_strikeprice() {
  strikeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::strikeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.StrikePrice)
  return strikeprice_;
}
void MDQBTransaction::set_strikeprice(::google::protobuf::int64 value) {
  
  strikeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.StrikePrice)
}

// optional int64 Yield = 17;
void MDQBTransaction::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.Yield)
  return yield_;
}
void MDQBTransaction::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.Yield)
}

// optional int64 OriginalPrice = 18;
void MDQBTransaction::clear_originalprice() {
  originalprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::originalprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.OriginalPrice)
  return originalprice_;
}
void MDQBTransaction::set_originalprice(::google::protobuf::int64 value) {
  
  originalprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.OriginalPrice)
}

// optional int64 FullPrice = 19;
void MDQBTransaction::clear_fullprice() {
  fullprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::fullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.FullPrice)
  return fullprice_;
}
void MDQBTransaction::set_fullprice(::google::protobuf::int64 value) {
  
  fullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.FullPrice)
}

// optional int32 InstrmtAssignmentMethod = 20;
void MDQBTransaction::clear_instrmtassignmentmethod() {
  instrmtassignmentmethod_ = 0;
}
::google::protobuf::int32 MDQBTransaction::instrmtassignmentmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.InstrmtAssignmentMethod)
  return instrmtassignmentmethod_;
}
void MDQBTransaction::set_instrmtassignmentmethod(::google::protobuf::int32 value) {
  
  instrmtassignmentmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.InstrmtAssignmentMethod)
}

// optional int32 TradeStatus = 21;
void MDQBTransaction::clear_tradestatus() {
  tradestatus_ = 0;
}
::google::protobuf::int32 MDQBTransaction::tradestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeStatus)
  return tradestatus_;
}
void MDQBTransaction::set_tradestatus(::google::protobuf::int32 value) {
  
  tradestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeStatus)
}

// optional int32 TradeType = 22;
void MDQBTransaction::clear_tradetype() {
  tradetype_ = 0;
}
::google::protobuf::int32 MDQBTransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeType)
  return tradetype_;
}
void MDQBTransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeType)
}

// optional int32 PriceType = 23;
void MDQBTransaction::clear_pricetype() {
  pricetype_ = 0;
}
::google::protobuf::int32 MDQBTransaction::pricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.PriceType)
  return pricetype_;
}
void MDQBTransaction::set_pricetype(::google::protobuf::int32 value) {
  
  pricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.PriceType)
}

// optional int32 ClearSpeed = 24;
void MDQBTransaction::clear_clearspeed() {
  clearspeed_ = 0;
}
::google::protobuf::int32 MDQBTransaction::clearspeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ClearSpeed)
  return clearspeed_;
}
void MDQBTransaction::set_clearspeed(::google::protobuf::int32 value) {
  
  clearspeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ClearSpeed)
}

// optional int32 BrokerDataType = 25;
void MDQBTransaction::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
::google::protobuf::int32 MDQBTransaction::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.BrokerDataType)
  return brokerdatatype_;
}
void MDQBTransaction::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.BrokerDataType)
}

// optional int32 SSDetect = 26;
void MDQBTransaction::clear_ssdetect() {
  ssdetect_ = 0;
}
::google::protobuf::int32 MDQBTransaction::ssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SSDetect)
  return ssdetect_;
}
void MDQBTransaction::set_ssdetect(::google::protobuf::int32 value) {
  
  ssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SSDetect)
}

// optional string TradeReqID = 27;
void MDQBTransaction::clear_tradereqid() {
  tradereqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBTransaction::tradereqid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  return tradereqid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_tradereqid(const ::std::string& value) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
void MDQBTransaction::set_tradereqid(const char* value) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
void MDQBTransaction::set_tradereqid(const char* value, size_t size) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
::std::string* MDQBTransaction::mutable_tradereqid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  return tradereqid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBTransaction::release_tradereqid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  
  return tradereqid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_allocated_tradereqid(::std::string* tradereqid) {
  if (tradereqid != NULL) {
    
  } else {
    
  }
  tradereqid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradereqid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}

// optional int64 TransactTime = 28;
void MDQBTransaction::clear_transacttime() {
  transacttime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBTransaction::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TransactTime)
  return transacttime_;
}
void MDQBTransaction::set_transacttime(::google::protobuf::int64 value) {
  
  transacttime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TransactTime)
}

// optional int32 DataMultiplePowerOf10 = 29;
void MDQBTransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDQBTransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDQBTransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DataMultiplePowerOf10)
}

// optional string SettlType = 30;
void MDQBTransaction::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBTransaction::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
void MDQBTransaction::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
void MDQBTransaction::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
::std::string* MDQBTransaction::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBTransaction::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBTransaction::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}

// optional int32 DealTransType = 31;
void MDQBTransaction::clear_dealtranstype() {
  dealtranstype_ = 0;
}
::google::protobuf::int32 MDQBTransaction::dealtranstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DealTransType)
  return dealtranstype_;
}
void MDQBTransaction::set_dealtranstype(::google::protobuf::int32 value) {
  
  dealtranstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DealTransType)
}

inline const MDQBTransaction* MDQBTransaction::internal_default_instance() {
  return &MDQBTransaction_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
