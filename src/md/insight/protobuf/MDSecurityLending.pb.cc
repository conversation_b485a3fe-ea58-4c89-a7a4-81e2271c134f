// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSecurityLending.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSecurityLending.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSecurityLending_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSecurityLending_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADValidSecurityLendingEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADValidSecurityLendingEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADSecurityLendingEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADSecurityLendingEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADEstimatedSecurityLendingEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADEstimatedSecurityLendingEntry_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSecurityLending_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSecurityLending_2eproto() {
  protobuf_AddDesc_MDSecurityLending_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSecurityLending.proto");
  GOOGLE_CHECK(file != NULL);
  MDSecurityLending_descriptor_ = file->message_type(0);
  static const int MDSecurityLending_offsets_[57] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, preweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, prehighrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, prelowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, prehtscvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, premarketvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, weightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, marketvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, bestborrowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, bestlendrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validalends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validblends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validclends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, alends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, blends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, clends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validreservationborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validreservationlends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, reservationborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, reservationlends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validotclends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, bestreservationborrowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, bestreservationlendrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validlendamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validalendamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validblendamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrowamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, bestloanrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrowtradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrowweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, prehtscborrowtradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, prehtscborrowweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, loans_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, externallends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborrowterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, htscborroworderamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validlendterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validlendorderamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, marketloans_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, marketlends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, validborrowamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, loanamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, marketborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, borrowamount_),
  };
  MDSecurityLending_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSecurityLending_descriptor_,
      MDSecurityLending::internal_default_instance(),
      MDSecurityLending_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSecurityLending),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSecurityLending, _internal_metadata_));
  ADValidSecurityLendingEntry_descriptor_ = file->message_type(1);
  static const int ADValidSecurityLendingEntry_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, level_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, rate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, term_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, amount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, htscprovided_),
  };
  ADValidSecurityLendingEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADValidSecurityLendingEntry_descriptor_,
      ADValidSecurityLendingEntry::internal_default_instance(),
      ADValidSecurityLendingEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADValidSecurityLendingEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADValidSecurityLendingEntry, _internal_metadata_));
  ADSecurityLendingEntry_descriptor_ = file->message_type(2);
  static const int ADSecurityLendingEntry_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, level_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, rate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, term_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, totalamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, matchedamount_),
  };
  ADSecurityLendingEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADSecurityLendingEntry_descriptor_,
      ADSecurityLendingEntry::internal_default_instance(),
      ADSecurityLendingEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADSecurityLendingEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSecurityLendingEntry, _internal_metadata_));
  ADEstimatedSecurityLendingEntry_descriptor_ = file->message_type(3);
  static const int ADEstimatedSecurityLendingEntry_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, level_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, rate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, term_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, amount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, postponeprobability_),
  };
  ADEstimatedSecurityLendingEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADEstimatedSecurityLendingEntry_descriptor_,
      ADEstimatedSecurityLendingEntry::internal_default_instance(),
      ADEstimatedSecurityLendingEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADEstimatedSecurityLendingEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADEstimatedSecurityLendingEntry, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSecurityLending_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSecurityLending_descriptor_, MDSecurityLending::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADValidSecurityLendingEntry_descriptor_, ADValidSecurityLendingEntry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADSecurityLendingEntry_descriptor_, ADSecurityLendingEntry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADEstimatedSecurityLendingEntry_descriptor_, ADEstimatedSecurityLendingEntry::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSecurityLending_2eproto() {
  MDSecurityLending_default_instance_.Shutdown();
  delete MDSecurityLending_reflection_;
  ADValidSecurityLendingEntry_default_instance_.Shutdown();
  delete ADValidSecurityLendingEntry_reflection_;
  ADSecurityLendingEntry_default_instance_.Shutdown();
  delete ADSecurityLendingEntry_reflection_;
  ADEstimatedSecurityLendingEntry_default_instance_.Shutdown();
  delete ADEstimatedSecurityLendingEntry_reflection_;
}

void protobuf_InitDefaults_MDSecurityLending_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSecurityLending_default_instance_.DefaultConstruct();
  ADValidSecurityLendingEntry_default_instance_.DefaultConstruct();
  ADSecurityLendingEntry_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADEstimatedSecurityLendingEntry_default_instance_.DefaultConstruct();
  MDSecurityLending_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADValidSecurityLendingEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADSecurityLendingEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADEstimatedSecurityLendingEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSecurityLending_2eproto_once_);
void protobuf_InitDefaults_MDSecurityLending_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSecurityLending_2eproto_once_,
                 &protobuf_InitDefaults_MDSecurityLending_2eproto_impl);
}
void protobuf_AddDesc_MDSecurityLending_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSecurityLending_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\027MDSecurityLending.proto\022\032com.htsc.mdc."
    "insight.model\032\027ESecurityIDSource.proto\032\023"
    "ESecurityType.proto\"\241\023\n\021MDSecurityLendin"
    "g\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001("
    "\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003"
    "\022\030\n\020TradingPhaseCode\030\005 \001(\t\022\?\n\020securityID"
    "Source\030\006 \001(\0162%.com.htsc.mdc.model.ESecur"
    "ityIDSource\0227\n\014securityType\030\007 \001(\0162!.com."
    "htsc.mdc.model.ESecurityType\022\027\n\017PreWeigh"
    "tedRate\030\n \001(\003\022\023\n\013PreHighRate\030\013 \001(\003\022\022\n\nPr"
    "eLowRate\030\014 \001(\003\022\025\n\rPreHtscVolume\030\r \001(\003\022\027\n"
    "\017PreMarketVolume\030\016 \001(\003\022\024\n\014WeightedRate\030\017"
    " \001(\003\022\020\n\010HighRate\030\020 \001(\003\022\017\n\007LowRate\030\021 \001(\003\022"
    "\022\n\nHtscVolume\030\022 \001(\003\022\024\n\014MarketVolume\030\023 \001("
    "\003\022\026\n\016BestBorrowRate\030\024 \001(\003\022\024\n\014BestLendRat"
    "e\030\025 \001(\003\022M\n\014ValidBorrows\030\033 \003(\01327.com.htsc"
    ".mdc.insight.model.ADValidSecurityLendin"
    "gEntry\022L\n\013ValidALends\030\034 \003(\01327.com.htsc.m"
    "dc.insight.model.ADValidSecurityLendingE"
    "ntry\022L\n\013ValidBLends\030\035 \003(\01327.com.htsc.mdc"
    ".insight.model.ADValidSecurityLendingEnt"
    "ry\022L\n\013ValidCLends\030\036 \003(\01327.com.htsc.mdc.i"
    "nsight.model.ADValidSecurityLendingEntry"
    "\022B\n\006ALends\030\037 \003(\01322.com.htsc.mdc.insight."
    "model.ADSecurityLendingEntry\022B\n\006BLends\030 "
    " \003(\01322.com.htsc.mdc.insight.model.ADSecu"
    "rityLendingEntry\022B\n\006CLends\030! \003(\01322.com.h"
    "tsc.mdc.insight.model.ADSecurityLendingE"
    "ntry\022X\n\027ValidReservationBorrows\030\" \003(\01327."
    "com.htsc.mdc.insight.model.ADValidSecuri"
    "tyLendingEntry\022V\n\025ValidReservationLends\030"
    "# \003(\01327.com.htsc.mdc.insight.model.ADVal"
    "idSecurityLendingEntry\022N\n\022ReservationBor"
    "rows\030$ \003(\01322.com.htsc.mdc.insight.model."
    "ADSecurityLendingEntry\022L\n\020ReservationLen"
    "ds\030% \003(\01322.com.htsc.mdc.insight.model.AD"
    "SecurityLendingEntry\022N\n\rValidOtcLends\030& "
    "\003(\01327.com.htsc.mdc.insight.model.ADValid"
    "SecurityLendingEntry\022!\n\031BestReservationB"
    "orrowRate\030\' \001(\003\022\037\n\027BestReservationLendRa"
    "te\030( \001(\003\022\027\n\017ValidLendAmount\030) \001(\003\022\030\n\020Val"
    "idALendAmount\030* \001(\003\022\030\n\020ValidBLendAmount\030"
    "+ \001(\003\022\030\n\020HtscBorrowAmount\030, \001(\003\022\026\n\016HtscB"
    "orrowRate\030- \001(\003\022\024\n\014BestLoanRate\030. \001(\003\022\035\n"
    "\025HtscBorrowTradeVolume\030/ \001(\003\022\036\n\026HtscBorr"
    "owWeightedRate\0300 \001(\003\022 \n\030PreHtscBorrowTra"
    "deVolume\0301 \001(\003\022!\n\031PreHtscBorrowWeightedR"
    "ate\0302 \001(\003\022L\n\013HtscBorrows\0303 \003(\01327.com.hts"
    "c.mdc.insight.model.ADValidSecurityLendi"
    "ngEntry\022F\n\005Loans\0304 \003(\01327.com.htsc.mdc.in"
    "sight.model.ADValidSecurityLendingEntry\022"
    "\035\n\025DataMultiplePowerOf10\0305 \001(\005\022R\n\rExtern"
    "alLends\0306 \003(\0132;.com.htsc.mdc.insight.mod"
    "el.ADEstimatedSecurityLendingEntry\022\026\n\016Ht"
    "scBorrowTerm\0309 \001(\t\022\035\n\025HtscBorrowOrderAmo"
    "unt\030: \001(\003\022\025\n\rValidLendTerm\030; \001(\t\022\034\n\024Vali"
    "dLendOrderAmount\030< \001(\003\022L\n\013MarketLoans\030= "
    "\003(\01327.com.htsc.mdc.insight.model.ADValid"
    "SecurityLendingEntry\022L\n\013MarketLends\030> \003("
    "\01327.com.htsc.mdc.insight.model.ADValidSe"
    "curityLendingEntry\022\031\n\021ValidBorrowAmount\030"
    "\? \001(\003\022\022\n\nLoanAmount\030@ \001(\003\022N\n\rMarketBorro"
    "ws\030A \003(\01327.com.htsc.mdc.insight.model.AD"
    "ValidSecurityLendingEntry\022\024\n\014BorrowAmoun"
    "t\030B \001(\003\"n\n\033ADValidSecurityLendingEntry\022\r"
    "\n\005Level\030\001 \001(\005\022\014\n\004Rate\030\002 \001(\003\022\014\n\004Term\030\003 \001("
    "\005\022\016\n\006Amount\030\004 \001(\003\022\024\n\014HtscProvided\030\005 \001(\010\""
    "o\n\026ADSecurityLendingEntry\022\r\n\005Level\030\001 \001(\005"
    "\022\014\n\004Rate\030\002 \001(\003\022\014\n\004Term\030\003 \001(\005\022\023\n\013TotalAmo"
    "unt\030\004 \001(\003\022\025\n\rMatchedAmount\030\005 \001(\003\"y\n\037ADEs"
    "timatedSecurityLendingEntry\022\r\n\005Level\030\001 \001"
    "(\005\022\014\n\004Rate\030\002 \001(\003\022\014\n\004Term\030\003 \001(\t\022\016\n\006Amount"
    "\030\004 \001(\003\022\033\n\023PostponeProbability\030\005 \001(\005B:\n\032c"
    "om.htsc.mdc.insight.modelB\027MDSecurityLen"
    "dingProtosH\001\240\001\001b\006proto3", 2983);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSecurityLending.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSecurityLending_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSecurityLending_2eproto_once_);
void protobuf_AddDesc_MDSecurityLending_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSecurityLending_2eproto_once_,
                 &protobuf_AddDesc_MDSecurityLending_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSecurityLending_2eproto {
  StaticDescriptorInitializer_MDSecurityLending_2eproto() {
    protobuf_AddDesc_MDSecurityLending_2eproto();
  }
} static_descriptor_initializer_MDSecurityLending_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSecurityLending::kHTSCSecurityIDFieldNumber;
const int MDSecurityLending::kMDDateFieldNumber;
const int MDSecurityLending::kMDTimeFieldNumber;
const int MDSecurityLending::kDataTimestampFieldNumber;
const int MDSecurityLending::kTradingPhaseCodeFieldNumber;
const int MDSecurityLending::kSecurityIDSourceFieldNumber;
const int MDSecurityLending::kSecurityTypeFieldNumber;
const int MDSecurityLending::kPreWeightedRateFieldNumber;
const int MDSecurityLending::kPreHighRateFieldNumber;
const int MDSecurityLending::kPreLowRateFieldNumber;
const int MDSecurityLending::kPreHtscVolumeFieldNumber;
const int MDSecurityLending::kPreMarketVolumeFieldNumber;
const int MDSecurityLending::kWeightedRateFieldNumber;
const int MDSecurityLending::kHighRateFieldNumber;
const int MDSecurityLending::kLowRateFieldNumber;
const int MDSecurityLending::kHtscVolumeFieldNumber;
const int MDSecurityLending::kMarketVolumeFieldNumber;
const int MDSecurityLending::kBestBorrowRateFieldNumber;
const int MDSecurityLending::kBestLendRateFieldNumber;
const int MDSecurityLending::kValidBorrowsFieldNumber;
const int MDSecurityLending::kValidALendsFieldNumber;
const int MDSecurityLending::kValidBLendsFieldNumber;
const int MDSecurityLending::kValidCLendsFieldNumber;
const int MDSecurityLending::kALendsFieldNumber;
const int MDSecurityLending::kBLendsFieldNumber;
const int MDSecurityLending::kCLendsFieldNumber;
const int MDSecurityLending::kValidReservationBorrowsFieldNumber;
const int MDSecurityLending::kValidReservationLendsFieldNumber;
const int MDSecurityLending::kReservationBorrowsFieldNumber;
const int MDSecurityLending::kReservationLendsFieldNumber;
const int MDSecurityLending::kValidOtcLendsFieldNumber;
const int MDSecurityLending::kBestReservationBorrowRateFieldNumber;
const int MDSecurityLending::kBestReservationLendRateFieldNumber;
const int MDSecurityLending::kValidLendAmountFieldNumber;
const int MDSecurityLending::kValidALendAmountFieldNumber;
const int MDSecurityLending::kValidBLendAmountFieldNumber;
const int MDSecurityLending::kHtscBorrowAmountFieldNumber;
const int MDSecurityLending::kHtscBorrowRateFieldNumber;
const int MDSecurityLending::kBestLoanRateFieldNumber;
const int MDSecurityLending::kHtscBorrowTradeVolumeFieldNumber;
const int MDSecurityLending::kHtscBorrowWeightedRateFieldNumber;
const int MDSecurityLending::kPreHtscBorrowTradeVolumeFieldNumber;
const int MDSecurityLending::kPreHtscBorrowWeightedRateFieldNumber;
const int MDSecurityLending::kHtscBorrowsFieldNumber;
const int MDSecurityLending::kLoansFieldNumber;
const int MDSecurityLending::kDataMultiplePowerOf10FieldNumber;
const int MDSecurityLending::kExternalLendsFieldNumber;
const int MDSecurityLending::kHtscBorrowTermFieldNumber;
const int MDSecurityLending::kHtscBorrowOrderAmountFieldNumber;
const int MDSecurityLending::kValidLendTermFieldNumber;
const int MDSecurityLending::kValidLendOrderAmountFieldNumber;
const int MDSecurityLending::kMarketLoansFieldNumber;
const int MDSecurityLending::kMarketLendsFieldNumber;
const int MDSecurityLending::kValidBorrowAmountFieldNumber;
const int MDSecurityLending::kLoanAmountFieldNumber;
const int MDSecurityLending::kMarketBorrowsFieldNumber;
const int MDSecurityLending::kBorrowAmountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSecurityLending::MDSecurityLending()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSecurityLending_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSecurityLending)
}

void MDSecurityLending::InitAsDefaultInstance() {
}

MDSecurityLending::MDSecurityLending(const MDSecurityLending& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSecurityLending)
}

void MDSecurityLending::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscborrowterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  validlendterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSecurityLending::~MDSecurityLending() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSecurityLending)
  SharedDtor();
}

void MDSecurityLending::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscborrowterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  validlendterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSecurityLending::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSecurityLending::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSecurityLending_descriptor_;
}

const MDSecurityLending& MDSecurityLending::default_instance() {
  protobuf_InitDefaults_MDSecurityLending_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSecurityLending> MDSecurityLending_default_instance_;

MDSecurityLending* MDSecurityLending::New(::google::protobuf::Arena* arena) const {
  MDSecurityLending* n = new MDSecurityLending;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSecurityLending::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSecurityLending)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSecurityLending, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSecurityLending*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, preweightedrate_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(prehighrate_, htscvolume_);
  ZR_(marketvolume_, bestlendrate_);
  bestreservationborrowrate_ = GOOGLE_LONGLONG(0);
  ZR_(bestreservationlendrate_, htscborrowtradevolume_);
  ZR_(htscborrowweightedrate_, prehtscborrowweightedrate_);
  datamultiplepowerof10_ = 0;
  htscborrowterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(htscborroworderamount_, loanamount_);
  validlendterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  borrowamount_ = GOOGLE_LONGLONG(0);

#undef ZR_HELPER_
#undef ZR_

  validborrows_.Clear();
  validalends_.Clear();
  validblends_.Clear();
  validclends_.Clear();
  alends_.Clear();
  blends_.Clear();
  clends_.Clear();
  validreservationborrows_.Clear();
  validreservationlends_.Clear();
  reservationborrows_.Clear();
  reservationlends_.Clear();
  validotclends_.Clear();
  htscborrows_.Clear();
  loans_.Clear();
  externallends_.Clear();
  marketloans_.Clear();
  marketlends_.Clear();
  marketborrows_.Clear();
}

bool MDSecurityLending::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSecurityLending)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreWeightedRate;
        break;
      }

      // optional int64 PreWeightedRate = 10;
      case 10: {
        if (tag == 80) {
         parse_PreWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_PreHighRate;
        break;
      }

      // optional int64 PreHighRate = 11;
      case 11: {
        if (tag == 88) {
         parse_PreHighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prehighrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_PreLowRate;
        break;
      }

      // optional int64 PreLowRate = 12;
      case 12: {
        if (tag == 96) {
         parse_PreLowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prelowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_PreHtscVolume;
        break;
      }

      // optional int64 PreHtscVolume = 13;
      case 13: {
        if (tag == 104) {
         parse_PreHtscVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prehtscvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_PreMarketVolume;
        break;
      }

      // optional int64 PreMarketVolume = 14;
      case 14: {
        if (tag == 112) {
         parse_PreMarketVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarketvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_WeightedRate;
        break;
      }

      // optional int64 WeightedRate = 15;
      case 15: {
        if (tag == 120) {
         parse_WeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 16;
      case 16: {
        if (tag == 128) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 17;
      case 17: {
        if (tag == 136) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_HtscVolume;
        break;
      }

      // optional int64 HtscVolume = 18;
      case 18: {
        if (tag == 144) {
         parse_HtscVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_MarketVolume;
        break;
      }

      // optional int64 MarketVolume = 19;
      case 19: {
        if (tag == 152) {
         parse_MarketVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &marketvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_BestBorrowRate;
        break;
      }

      // optional int64 BestBorrowRate = 20;
      case 20: {
        if (tag == 160) {
         parse_BestBorrowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestborrowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_BestLendRate;
        break;
      }

      // optional int64 BestLendRate = 21;
      case 21: {
        if (tag == 168) {
         parse_BestLendRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestlendrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_ValidBorrows;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
      case 27: {
        if (tag == 218) {
         parse_ValidBorrows:
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_loop_ValidBorrows;
        if (input->ExpectTag(226)) goto parse_loop_ValidALends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
      case 28: {
        if (tag == 226) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidALends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validalends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_loop_ValidALends;
        if (input->ExpectTag(234)) goto parse_loop_ValidBLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
      case 29: {
        if (tag == 234) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidBLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validblends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_loop_ValidBLends;
        if (input->ExpectTag(242)) goto parse_loop_ValidCLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
      case 30: {
        if (tag == 242) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidCLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validclends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_loop_ValidCLends;
        if (input->ExpectTag(250)) goto parse_loop_ALends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
      case 31: {
        if (tag == 250) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ALends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_alends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_loop_ALends;
        if (input->ExpectTag(258)) goto parse_loop_BLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
      case 32: {
        if (tag == 258) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_BLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_blends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_loop_BLends;
        if (input->ExpectTag(266)) goto parse_loop_CLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
      case 33: {
        if (tag == 266) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_CLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_clends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_loop_CLends;
        if (input->ExpectTag(274)) goto parse_loop_ValidReservationBorrows;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
      case 34: {
        if (tag == 274) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidReservationBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validreservationborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_loop_ValidReservationBorrows;
        if (input->ExpectTag(282)) goto parse_loop_ValidReservationLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
      case 35: {
        if (tag == 282) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidReservationLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validreservationlends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_loop_ValidReservationLends;
        if (input->ExpectTag(290)) goto parse_loop_ReservationBorrows;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
      case 36: {
        if (tag == 290) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ReservationBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_reservationborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_loop_ReservationBorrows;
        if (input->ExpectTag(298)) goto parse_loop_ReservationLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
      case 37: {
        if (tag == 298) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ReservationLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_reservationlends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_loop_ReservationLends;
        if (input->ExpectTag(306)) goto parse_loop_ValidOtcLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
      case 38: {
        if (tag == 306) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidOtcLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validotclends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_loop_ValidOtcLends;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(312)) goto parse_BestReservationBorrowRate;
        break;
      }

      // optional int64 BestReservationBorrowRate = 39;
      case 39: {
        if (tag == 312) {
         parse_BestReservationBorrowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestreservationborrowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_BestReservationLendRate;
        break;
      }

      // optional int64 BestReservationLendRate = 40;
      case 40: {
        if (tag == 320) {
         parse_BestReservationLendRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestreservationlendrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_ValidLendAmount;
        break;
      }

      // optional int64 ValidLendAmount = 41;
      case 41: {
        if (tag == 328) {
         parse_ValidLendAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &validlendamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(336)) goto parse_ValidALendAmount;
        break;
      }

      // optional int64 ValidALendAmount = 42;
      case 42: {
        if (tag == 336) {
         parse_ValidALendAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &validalendamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_ValidBLendAmount;
        break;
      }

      // optional int64 ValidBLendAmount = 43;
      case 43: {
        if (tag == 344) {
         parse_ValidBLendAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &validblendamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_HtscBorrowAmount;
        break;
      }

      // optional int64 HtscBorrowAmount = 44;
      case 44: {
        if (tag == 352) {
         parse_HtscBorrowAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_HtscBorrowRate;
        break;
      }

      // optional int64 HtscBorrowRate = 45;
      case 45: {
        if (tag == 360) {
         parse_HtscBorrowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_BestLoanRate;
        break;
      }

      // optional int64 BestLoanRate = 46;
      case 46: {
        if (tag == 368) {
         parse_BestLoanRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestloanrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(376)) goto parse_HtscBorrowTradeVolume;
        break;
      }

      // optional int64 HtscBorrowTradeVolume = 47;
      case 47: {
        if (tag == 376) {
         parse_HtscBorrowTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowtradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(384)) goto parse_HtscBorrowWeightedRate;
        break;
      }

      // optional int64 HtscBorrowWeightedRate = 48;
      case 48: {
        if (tag == 384) {
         parse_HtscBorrowWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(392)) goto parse_PreHtscBorrowTradeVolume;
        break;
      }

      // optional int64 PreHtscBorrowTradeVolume = 49;
      case 49: {
        if (tag == 392) {
         parse_PreHtscBorrowTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prehtscborrowtradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_PreHtscBorrowWeightedRate;
        break;
      }

      // optional int64 PreHtscBorrowWeightedRate = 50;
      case 50: {
        if (tag == 400) {
         parse_PreHtscBorrowWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prehtscborrowweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_HtscBorrows;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
      case 51: {
        if (tag == 410) {
         parse_HtscBorrows:
          DO_(input->IncrementRecursionDepth());
         parse_loop_HtscBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_htscborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_loop_HtscBorrows;
        if (input->ExpectTag(418)) goto parse_loop_Loans;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
      case 52: {
        if (tag == 418) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_Loans:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_loans()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_loop_Loans;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(424)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 53;
      case 53: {
        if (tag == 424) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_ExternalLends;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
      case 54: {
        if (tag == 434) {
         parse_ExternalLends:
          DO_(input->IncrementRecursionDepth());
         parse_loop_ExternalLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_externallends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_loop_ExternalLends;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(458)) goto parse_HtscBorrowTerm;
        break;
      }

      // optional string HtscBorrowTerm = 57;
      case 57: {
        if (tag == 458) {
         parse_HtscBorrowTerm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscborrowterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscborrowterm().data(), this->htscborrowterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(464)) goto parse_HtscBorrowOrderAmount;
        break;
      }

      // optional int64 HtscBorrowOrderAmount = 58;
      case 58: {
        if (tag == 464) {
         parse_HtscBorrowOrderAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborroworderamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(474)) goto parse_ValidLendTerm;
        break;
      }

      // optional string ValidLendTerm = 59;
      case 59: {
        if (tag == 474) {
         parse_ValidLendTerm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_validlendterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->validlendterm().data(), this->validlendterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_ValidLendOrderAmount;
        break;
      }

      // optional int64 ValidLendOrderAmount = 60;
      case 60: {
        if (tag == 480) {
         parse_ValidLendOrderAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &validlendorderamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(490)) goto parse_MarketLoans;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
      case 61: {
        if (tag == 490) {
         parse_MarketLoans:
          DO_(input->IncrementRecursionDepth());
         parse_loop_MarketLoans:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_marketloans()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(490)) goto parse_loop_MarketLoans;
        if (input->ExpectTag(498)) goto parse_loop_MarketLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
      case 62: {
        if (tag == 498) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_MarketLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_marketlends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(498)) goto parse_loop_MarketLends;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(504)) goto parse_ValidBorrowAmount;
        break;
      }

      // optional int64 ValidBorrowAmount = 63;
      case 63: {
        if (tag == 504) {
         parse_ValidBorrowAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &validborrowamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(512)) goto parse_LoanAmount;
        break;
      }

      // optional int64 LoanAmount = 64;
      case 64: {
        if (tag == 512) {
         parse_LoanAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &loanamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(522)) goto parse_MarketBorrows;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
      case 65: {
        if (tag == 522) {
         parse_MarketBorrows:
          DO_(input->IncrementRecursionDepth());
         parse_loop_MarketBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_marketborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(522)) goto parse_loop_MarketBorrows;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(528)) goto parse_BorrowAmount;
        break;
      }

      // optional int64 BorrowAmount = 66;
      case 66: {
        if (tag == 528) {
         parse_BorrowAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &borrowamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSecurityLending)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSecurityLending)
  return false;
#undef DO_
}

void MDSecurityLending::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSecurityLending)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 PreWeightedRate = 10;
  if (this->preweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preweightedrate(), output);
  }

  // optional int64 PreHighRate = 11;
  if (this->prehighrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->prehighrate(), output);
  }

  // optional int64 PreLowRate = 12;
  if (this->prelowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->prelowrate(), output);
  }

  // optional int64 PreHtscVolume = 13;
  if (this->prehtscvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->prehtscvolume(), output);
  }

  // optional int64 PreMarketVolume = 14;
  if (this->premarketvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->premarketvolume(), output);
  }

  // optional int64 WeightedRate = 15;
  if (this->weightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->weightedrate(), output);
  }

  // optional int64 HighRate = 16;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->highrate(), output);
  }

  // optional int64 LowRate = 17;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->lowrate(), output);
  }

  // optional int64 HtscVolume = 18;
  if (this->htscvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->htscvolume(), output);
  }

  // optional int64 MarketVolume = 19;
  if (this->marketvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->marketvolume(), output);
  }

  // optional int64 BestBorrowRate = 20;
  if (this->bestborrowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->bestborrowrate(), output);
  }

  // optional int64 BestLendRate = 21;
  if (this->bestlendrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->bestlendrate(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
  for (unsigned int i = 0, n = this->validborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      27, this->validborrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
  for (unsigned int i = 0, n = this->validalends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      28, this->validalends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
  for (unsigned int i = 0, n = this->validblends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      29, this->validblends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
  for (unsigned int i = 0, n = this->validclends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      30, this->validclends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
  for (unsigned int i = 0, n = this->alends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, this->alends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
  for (unsigned int i = 0, n = this->blends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      32, this->blends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
  for (unsigned int i = 0, n = this->clends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      33, this->clends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
  for (unsigned int i = 0, n = this->validreservationborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      34, this->validreservationborrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
  for (unsigned int i = 0, n = this->validreservationlends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      35, this->validreservationlends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
  for (unsigned int i = 0, n = this->reservationborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      36, this->reservationborrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
  for (unsigned int i = 0, n = this->reservationlends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      37, this->reservationlends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
  for (unsigned int i = 0, n = this->validotclends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      38, this->validotclends(i), output);
  }

  // optional int64 BestReservationBorrowRate = 39;
  if (this->bestreservationborrowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(39, this->bestreservationborrowrate(), output);
  }

  // optional int64 BestReservationLendRate = 40;
  if (this->bestreservationlendrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(40, this->bestreservationlendrate(), output);
  }

  // optional int64 ValidLendAmount = 41;
  if (this->validlendamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(41, this->validlendamount(), output);
  }

  // optional int64 ValidALendAmount = 42;
  if (this->validalendamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(42, this->validalendamount(), output);
  }

  // optional int64 ValidBLendAmount = 43;
  if (this->validblendamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(43, this->validblendamount(), output);
  }

  // optional int64 HtscBorrowAmount = 44;
  if (this->htscborrowamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(44, this->htscborrowamount(), output);
  }

  // optional int64 HtscBorrowRate = 45;
  if (this->htscborrowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(45, this->htscborrowrate(), output);
  }

  // optional int64 BestLoanRate = 46;
  if (this->bestloanrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(46, this->bestloanrate(), output);
  }

  // optional int64 HtscBorrowTradeVolume = 47;
  if (this->htscborrowtradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(47, this->htscborrowtradevolume(), output);
  }

  // optional int64 HtscBorrowWeightedRate = 48;
  if (this->htscborrowweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(48, this->htscborrowweightedrate(), output);
  }

  // optional int64 PreHtscBorrowTradeVolume = 49;
  if (this->prehtscborrowtradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(49, this->prehtscborrowtradevolume(), output);
  }

  // optional int64 PreHtscBorrowWeightedRate = 50;
  if (this->prehtscborrowweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(50, this->prehtscborrowweightedrate(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
  for (unsigned int i = 0, n = this->htscborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      51, this->htscborrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
  for (unsigned int i = 0, n = this->loans_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      52, this->loans(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 53;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(53, this->datamultiplepowerof10(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
  for (unsigned int i = 0, n = this->externallends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      54, this->externallends(i), output);
  }

  // optional string HtscBorrowTerm = 57;
  if (this->htscborrowterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscborrowterm().data(), this->htscborrowterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      57, this->htscborrowterm(), output);
  }

  // optional int64 HtscBorrowOrderAmount = 58;
  if (this->htscborroworderamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(58, this->htscborroworderamount(), output);
  }

  // optional string ValidLendTerm = 59;
  if (this->validlendterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->validlendterm().data(), this->validlendterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      59, this->validlendterm(), output);
  }

  // optional int64 ValidLendOrderAmount = 60;
  if (this->validlendorderamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(60, this->validlendorderamount(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
  for (unsigned int i = 0, n = this->marketloans_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      61, this->marketloans(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
  for (unsigned int i = 0, n = this->marketlends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      62, this->marketlends(i), output);
  }

  // optional int64 ValidBorrowAmount = 63;
  if (this->validborrowamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(63, this->validborrowamount(), output);
  }

  // optional int64 LoanAmount = 64;
  if (this->loanamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(64, this->loanamount(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
  for (unsigned int i = 0, n = this->marketborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      65, this->marketborrows(i), output);
  }

  // optional int64 BorrowAmount = 66;
  if (this->borrowamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(66, this->borrowamount(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSecurityLending)
}

::google::protobuf::uint8* MDSecurityLending::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSecurityLending)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 PreWeightedRate = 10;
  if (this->preweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preweightedrate(), target);
  }

  // optional int64 PreHighRate = 11;
  if (this->prehighrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->prehighrate(), target);
  }

  // optional int64 PreLowRate = 12;
  if (this->prelowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->prelowrate(), target);
  }

  // optional int64 PreHtscVolume = 13;
  if (this->prehtscvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->prehtscvolume(), target);
  }

  // optional int64 PreMarketVolume = 14;
  if (this->premarketvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->premarketvolume(), target);
  }

  // optional int64 WeightedRate = 15;
  if (this->weightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->weightedrate(), target);
  }

  // optional int64 HighRate = 16;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->highrate(), target);
  }

  // optional int64 LowRate = 17;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->lowrate(), target);
  }

  // optional int64 HtscVolume = 18;
  if (this->htscvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->htscvolume(), target);
  }

  // optional int64 MarketVolume = 19;
  if (this->marketvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->marketvolume(), target);
  }

  // optional int64 BestBorrowRate = 20;
  if (this->bestborrowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->bestborrowrate(), target);
  }

  // optional int64 BestLendRate = 21;
  if (this->bestlendrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->bestlendrate(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
  for (unsigned int i = 0, n = this->validborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        27, this->validborrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
  for (unsigned int i = 0, n = this->validalends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        28, this->validalends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
  for (unsigned int i = 0, n = this->validblends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        29, this->validblends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
  for (unsigned int i = 0, n = this->validclends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        30, this->validclends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
  for (unsigned int i = 0, n = this->alends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        31, this->alends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
  for (unsigned int i = 0, n = this->blends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        32, this->blends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
  for (unsigned int i = 0, n = this->clends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        33, this->clends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
  for (unsigned int i = 0, n = this->validreservationborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        34, this->validreservationborrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
  for (unsigned int i = 0, n = this->validreservationlends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        35, this->validreservationlends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
  for (unsigned int i = 0, n = this->reservationborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        36, this->reservationborrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
  for (unsigned int i = 0, n = this->reservationlends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        37, this->reservationlends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
  for (unsigned int i = 0, n = this->validotclends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        38, this->validotclends(i), false, target);
  }

  // optional int64 BestReservationBorrowRate = 39;
  if (this->bestreservationborrowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(39, this->bestreservationborrowrate(), target);
  }

  // optional int64 BestReservationLendRate = 40;
  if (this->bestreservationlendrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(40, this->bestreservationlendrate(), target);
  }

  // optional int64 ValidLendAmount = 41;
  if (this->validlendamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(41, this->validlendamount(), target);
  }

  // optional int64 ValidALendAmount = 42;
  if (this->validalendamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(42, this->validalendamount(), target);
  }

  // optional int64 ValidBLendAmount = 43;
  if (this->validblendamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(43, this->validblendamount(), target);
  }

  // optional int64 HtscBorrowAmount = 44;
  if (this->htscborrowamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(44, this->htscborrowamount(), target);
  }

  // optional int64 HtscBorrowRate = 45;
  if (this->htscborrowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(45, this->htscborrowrate(), target);
  }

  // optional int64 BestLoanRate = 46;
  if (this->bestloanrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(46, this->bestloanrate(), target);
  }

  // optional int64 HtscBorrowTradeVolume = 47;
  if (this->htscborrowtradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(47, this->htscborrowtradevolume(), target);
  }

  // optional int64 HtscBorrowWeightedRate = 48;
  if (this->htscborrowweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(48, this->htscborrowweightedrate(), target);
  }

  // optional int64 PreHtscBorrowTradeVolume = 49;
  if (this->prehtscborrowtradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(49, this->prehtscborrowtradevolume(), target);
  }

  // optional int64 PreHtscBorrowWeightedRate = 50;
  if (this->prehtscborrowweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(50, this->prehtscborrowweightedrate(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
  for (unsigned int i = 0, n = this->htscborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        51, this->htscborrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
  for (unsigned int i = 0, n = this->loans_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        52, this->loans(i), false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 53;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(53, this->datamultiplepowerof10(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
  for (unsigned int i = 0, n = this->externallends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        54, this->externallends(i), false, target);
  }

  // optional string HtscBorrowTerm = 57;
  if (this->htscborrowterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscborrowterm().data(), this->htscborrowterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        57, this->htscborrowterm(), target);
  }

  // optional int64 HtscBorrowOrderAmount = 58;
  if (this->htscborroworderamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(58, this->htscborroworderamount(), target);
  }

  // optional string ValidLendTerm = 59;
  if (this->validlendterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->validlendterm().data(), this->validlendterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        59, this->validlendterm(), target);
  }

  // optional int64 ValidLendOrderAmount = 60;
  if (this->validlendorderamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(60, this->validlendorderamount(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
  for (unsigned int i = 0, n = this->marketloans_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        61, this->marketloans(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
  for (unsigned int i = 0, n = this->marketlends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        62, this->marketlends(i), false, target);
  }

  // optional int64 ValidBorrowAmount = 63;
  if (this->validborrowamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(63, this->validborrowamount(), target);
  }

  // optional int64 LoanAmount = 64;
  if (this->loanamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(64, this->loanamount(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
  for (unsigned int i = 0, n = this->marketborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        65, this->marketborrows(i), false, target);
  }

  // optional int64 BorrowAmount = 66;
  if (this->borrowamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(66, this->borrowamount(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSecurityLending)
  return target;
}

size_t MDSecurityLending::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSecurityLending)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 PreWeightedRate = 10;
  if (this->preweightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedrate());
  }

  // optional int64 PreHighRate = 11;
  if (this->prehighrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prehighrate());
  }

  // optional int64 PreLowRate = 12;
  if (this->prelowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prelowrate());
  }

  // optional int64 PreHtscVolume = 13;
  if (this->prehtscvolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prehtscvolume());
  }

  // optional int64 PreMarketVolume = 14;
  if (this->premarketvolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarketvolume());
  }

  // optional int64 WeightedRate = 15;
  if (this->weightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedrate());
  }

  // optional int64 HighRate = 16;
  if (this->highrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 17;
  if (this->lowrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 HtscVolume = 18;
  if (this->htscvolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscvolume());
  }

  // optional int64 MarketVolume = 19;
  if (this->marketvolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->marketvolume());
  }

  // optional int64 BestBorrowRate = 20;
  if (this->bestborrowrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestborrowrate());
  }

  // optional int64 BestLendRate = 21;
  if (this->bestlendrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestlendrate());
  }

  // optional int64 BestReservationBorrowRate = 39;
  if (this->bestreservationborrowrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestreservationborrowrate());
  }

  // optional int64 BestReservationLendRate = 40;
  if (this->bestreservationlendrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestreservationlendrate());
  }

  // optional int64 ValidLendAmount = 41;
  if (this->validlendamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->validlendamount());
  }

  // optional int64 ValidALendAmount = 42;
  if (this->validalendamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->validalendamount());
  }

  // optional int64 ValidBLendAmount = 43;
  if (this->validblendamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->validblendamount());
  }

  // optional int64 HtscBorrowAmount = 44;
  if (this->htscborrowamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowamount());
  }

  // optional int64 HtscBorrowRate = 45;
  if (this->htscborrowrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowrate());
  }

  // optional int64 BestLoanRate = 46;
  if (this->bestloanrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestloanrate());
  }

  // optional int64 HtscBorrowTradeVolume = 47;
  if (this->htscborrowtradevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowtradevolume());
  }

  // optional int64 HtscBorrowWeightedRate = 48;
  if (this->htscborrowweightedrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowweightedrate());
  }

  // optional int64 PreHtscBorrowTradeVolume = 49;
  if (this->prehtscborrowtradevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prehtscborrowtradevolume());
  }

  // optional int64 PreHtscBorrowWeightedRate = 50;
  if (this->prehtscborrowweightedrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prehtscborrowweightedrate());
  }

  // optional int32 DataMultiplePowerOf10 = 53;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string HtscBorrowTerm = 57;
  if (this->htscborrowterm().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscborrowterm());
  }

  // optional int64 HtscBorrowOrderAmount = 58;
  if (this->htscborroworderamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborroworderamount());
  }

  // optional string ValidLendTerm = 59;
  if (this->validlendterm().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->validlendterm());
  }

  // optional int64 ValidLendOrderAmount = 60;
  if (this->validlendorderamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->validlendorderamount());
  }

  // optional int64 ValidBorrowAmount = 63;
  if (this->validborrowamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->validborrowamount());
  }

  // optional int64 LoanAmount = 64;
  if (this->loanamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->loanamount());
  }

  // optional int64 BorrowAmount = 66;
  if (this->borrowamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->borrowamount());
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
  {
    unsigned int count = this->validborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validborrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
  {
    unsigned int count = this->validalends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validalends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
  {
    unsigned int count = this->validblends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validblends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
  {
    unsigned int count = this->validclends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validclends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
  {
    unsigned int count = this->alends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->alends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
  {
    unsigned int count = this->blends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->blends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
  {
    unsigned int count = this->clends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->clends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
  {
    unsigned int count = this->validreservationborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validreservationborrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
  {
    unsigned int count = this->validreservationlends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validreservationlends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
  {
    unsigned int count = this->reservationborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reservationborrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
  {
    unsigned int count = this->reservationlends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->reservationlends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
  {
    unsigned int count = this->validotclends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validotclends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
  {
    unsigned int count = this->htscborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->htscborrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
  {
    unsigned int count = this->loans_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->loans(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
  {
    unsigned int count = this->externallends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->externallends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
  {
    unsigned int count = this->marketloans_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->marketloans(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
  {
    unsigned int count = this->marketlends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->marketlends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
  {
    unsigned int count = this->marketborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->marketborrows(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSecurityLending::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSecurityLending)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSecurityLending* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSecurityLending>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSecurityLending)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSecurityLending)
    UnsafeMergeFrom(*source);
  }
}

void MDSecurityLending::MergeFrom(const MDSecurityLending& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSecurityLending)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSecurityLending::UnsafeMergeFrom(const MDSecurityLending& from) {
  GOOGLE_DCHECK(&from != this);
  validborrows_.MergeFrom(from.validborrows_);
  validalends_.MergeFrom(from.validalends_);
  validblends_.MergeFrom(from.validblends_);
  validclends_.MergeFrom(from.validclends_);
  alends_.MergeFrom(from.alends_);
  blends_.MergeFrom(from.blends_);
  clends_.MergeFrom(from.clends_);
  validreservationborrows_.MergeFrom(from.validreservationborrows_);
  validreservationlends_.MergeFrom(from.validreservationlends_);
  reservationborrows_.MergeFrom(from.reservationborrows_);
  reservationlends_.MergeFrom(from.reservationlends_);
  validotclends_.MergeFrom(from.validotclends_);
  htscborrows_.MergeFrom(from.htscborrows_);
  loans_.MergeFrom(from.loans_);
  externallends_.MergeFrom(from.externallends_);
  marketloans_.MergeFrom(from.marketloans_);
  marketlends_.MergeFrom(from.marketlends_);
  marketborrows_.MergeFrom(from.marketborrows_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.preweightedrate() != 0) {
    set_preweightedrate(from.preweightedrate());
  }
  if (from.prehighrate() != 0) {
    set_prehighrate(from.prehighrate());
  }
  if (from.prelowrate() != 0) {
    set_prelowrate(from.prelowrate());
  }
  if (from.prehtscvolume() != 0) {
    set_prehtscvolume(from.prehtscvolume());
  }
  if (from.premarketvolume() != 0) {
    set_premarketvolume(from.premarketvolume());
  }
  if (from.weightedrate() != 0) {
    set_weightedrate(from.weightedrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.htscvolume() != 0) {
    set_htscvolume(from.htscvolume());
  }
  if (from.marketvolume() != 0) {
    set_marketvolume(from.marketvolume());
  }
  if (from.bestborrowrate() != 0) {
    set_bestborrowrate(from.bestborrowrate());
  }
  if (from.bestlendrate() != 0) {
    set_bestlendrate(from.bestlendrate());
  }
  if (from.bestreservationborrowrate() != 0) {
    set_bestreservationborrowrate(from.bestreservationborrowrate());
  }
  if (from.bestreservationlendrate() != 0) {
    set_bestreservationlendrate(from.bestreservationlendrate());
  }
  if (from.validlendamount() != 0) {
    set_validlendamount(from.validlendamount());
  }
  if (from.validalendamount() != 0) {
    set_validalendamount(from.validalendamount());
  }
  if (from.validblendamount() != 0) {
    set_validblendamount(from.validblendamount());
  }
  if (from.htscborrowamount() != 0) {
    set_htscborrowamount(from.htscborrowamount());
  }
  if (from.htscborrowrate() != 0) {
    set_htscborrowrate(from.htscborrowrate());
  }
  if (from.bestloanrate() != 0) {
    set_bestloanrate(from.bestloanrate());
  }
  if (from.htscborrowtradevolume() != 0) {
    set_htscborrowtradevolume(from.htscborrowtradevolume());
  }
  if (from.htscborrowweightedrate() != 0) {
    set_htscborrowweightedrate(from.htscborrowweightedrate());
  }
  if (from.prehtscborrowtradevolume() != 0) {
    set_prehtscborrowtradevolume(from.prehtscborrowtradevolume());
  }
  if (from.prehtscborrowweightedrate() != 0) {
    set_prehtscborrowweightedrate(from.prehtscborrowweightedrate());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.htscborrowterm().size() > 0) {

    htscborrowterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscborrowterm_);
  }
  if (from.htscborroworderamount() != 0) {
    set_htscborroworderamount(from.htscborroworderamount());
  }
  if (from.validlendterm().size() > 0) {

    validlendterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.validlendterm_);
  }
  if (from.validlendorderamount() != 0) {
    set_validlendorderamount(from.validlendorderamount());
  }
  if (from.validborrowamount() != 0) {
    set_validborrowamount(from.validborrowamount());
  }
  if (from.loanamount() != 0) {
    set_loanamount(from.loanamount());
  }
  if (from.borrowamount() != 0) {
    set_borrowamount(from.borrowamount());
  }
}

void MDSecurityLending::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSecurityLending)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSecurityLending::CopyFrom(const MDSecurityLending& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSecurityLending)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSecurityLending::IsInitialized() const {

  return true;
}

void MDSecurityLending::Swap(MDSecurityLending* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSecurityLending::InternalSwap(MDSecurityLending* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(preweightedrate_, other->preweightedrate_);
  std::swap(prehighrate_, other->prehighrate_);
  std::swap(prelowrate_, other->prelowrate_);
  std::swap(prehtscvolume_, other->prehtscvolume_);
  std::swap(premarketvolume_, other->premarketvolume_);
  std::swap(weightedrate_, other->weightedrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(htscvolume_, other->htscvolume_);
  std::swap(marketvolume_, other->marketvolume_);
  std::swap(bestborrowrate_, other->bestborrowrate_);
  std::swap(bestlendrate_, other->bestlendrate_);
  validborrows_.UnsafeArenaSwap(&other->validborrows_);
  validalends_.UnsafeArenaSwap(&other->validalends_);
  validblends_.UnsafeArenaSwap(&other->validblends_);
  validclends_.UnsafeArenaSwap(&other->validclends_);
  alends_.UnsafeArenaSwap(&other->alends_);
  blends_.UnsafeArenaSwap(&other->blends_);
  clends_.UnsafeArenaSwap(&other->clends_);
  validreservationborrows_.UnsafeArenaSwap(&other->validreservationborrows_);
  validreservationlends_.UnsafeArenaSwap(&other->validreservationlends_);
  reservationborrows_.UnsafeArenaSwap(&other->reservationborrows_);
  reservationlends_.UnsafeArenaSwap(&other->reservationlends_);
  validotclends_.UnsafeArenaSwap(&other->validotclends_);
  std::swap(bestreservationborrowrate_, other->bestreservationborrowrate_);
  std::swap(bestreservationlendrate_, other->bestreservationlendrate_);
  std::swap(validlendamount_, other->validlendamount_);
  std::swap(validalendamount_, other->validalendamount_);
  std::swap(validblendamount_, other->validblendamount_);
  std::swap(htscborrowamount_, other->htscborrowamount_);
  std::swap(htscborrowrate_, other->htscborrowrate_);
  std::swap(bestloanrate_, other->bestloanrate_);
  std::swap(htscborrowtradevolume_, other->htscborrowtradevolume_);
  std::swap(htscborrowweightedrate_, other->htscborrowweightedrate_);
  std::swap(prehtscborrowtradevolume_, other->prehtscborrowtradevolume_);
  std::swap(prehtscborrowweightedrate_, other->prehtscborrowweightedrate_);
  htscborrows_.UnsafeArenaSwap(&other->htscborrows_);
  loans_.UnsafeArenaSwap(&other->loans_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  externallends_.UnsafeArenaSwap(&other->externallends_);
  htscborrowterm_.Swap(&other->htscborrowterm_);
  std::swap(htscborroworderamount_, other->htscborroworderamount_);
  validlendterm_.Swap(&other->validlendterm_);
  std::swap(validlendorderamount_, other->validlendorderamount_);
  marketloans_.UnsafeArenaSwap(&other->marketloans_);
  marketlends_.UnsafeArenaSwap(&other->marketlends_);
  std::swap(validborrowamount_, other->validborrowamount_);
  std::swap(loanamount_, other->loanamount_);
  marketborrows_.UnsafeArenaSwap(&other->marketborrows_);
  std::swap(borrowamount_, other->borrowamount_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSecurityLending::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSecurityLending_descriptor_;
  metadata.reflection = MDSecurityLending_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSecurityLending

// optional string HTSCSecurityID = 1;
void MDSecurityLending::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSecurityLending::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
void MDSecurityLending::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
void MDSecurityLending::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
::std::string* MDSecurityLending::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSecurityLending::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSecurityLending::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSecurityLending::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MDDate)
  return mddate_;
}
void MDSecurityLending::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MDDate)
}

// optional int32 MDTime = 3;
void MDSecurityLending::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSecurityLending::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MDTime)
  return mdtime_;
}
void MDSecurityLending::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSecurityLending::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.DataTimestamp)
  return datatimestamp_;
}
void MDSecurityLending::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSecurityLending::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSecurityLending::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
void MDSecurityLending::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
void MDSecurityLending::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
::std::string* MDSecurityLending::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSecurityLending::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSecurityLending::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSecurityLending::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSecurityLending::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSecurityLending::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSecurityLending::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSecurityLending::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.securityType)
}

// optional int64 PreWeightedRate = 10;
void MDSecurityLending::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreWeightedRate)
  return preweightedrate_;
}
void MDSecurityLending::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreWeightedRate)
}

// optional int64 PreHighRate = 11;
void MDSecurityLending::clear_prehighrate() {
  prehighrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::prehighrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHighRate)
  return prehighrate_;
}
void MDSecurityLending::set_prehighrate(::google::protobuf::int64 value) {
  
  prehighrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHighRate)
}

// optional int64 PreLowRate = 12;
void MDSecurityLending::clear_prelowrate() {
  prelowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::prelowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreLowRate)
  return prelowrate_;
}
void MDSecurityLending::set_prelowrate(::google::protobuf::int64 value) {
  
  prelowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreLowRate)
}

// optional int64 PreHtscVolume = 13;
void MDSecurityLending::clear_prehtscvolume() {
  prehtscvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::prehtscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscVolume)
  return prehtscvolume_;
}
void MDSecurityLending::set_prehtscvolume(::google::protobuf::int64 value) {
  
  prehtscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscVolume)
}

// optional int64 PreMarketVolume = 14;
void MDSecurityLending::clear_premarketvolume() {
  premarketvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::premarketvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreMarketVolume)
  return premarketvolume_;
}
void MDSecurityLending::set_premarketvolume(::google::protobuf::int64 value) {
  
  premarketvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreMarketVolume)
}

// optional int64 WeightedRate = 15;
void MDSecurityLending::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.WeightedRate)
  return weightedrate_;
}
void MDSecurityLending::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.WeightedRate)
}

// optional int64 HighRate = 16;
void MDSecurityLending::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HighRate)
  return highrate_;
}
void MDSecurityLending::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HighRate)
}

// optional int64 LowRate = 17;
void MDSecurityLending::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.LowRate)
  return lowrate_;
}
void MDSecurityLending::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.LowRate)
}

// optional int64 HtscVolume = 18;
void MDSecurityLending::clear_htscvolume() {
  htscvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscVolume)
  return htscvolume_;
}
void MDSecurityLending::set_htscvolume(::google::protobuf::int64 value) {
  
  htscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscVolume)
}

// optional int64 MarketVolume = 19;
void MDSecurityLending::clear_marketvolume() {
  marketvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::marketvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketVolume)
  return marketvolume_;
}
void MDSecurityLending::set_marketvolume(::google::protobuf::int64 value) {
  
  marketvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MarketVolume)
}

// optional int64 BestBorrowRate = 20;
void MDSecurityLending::clear_bestborrowrate() {
  bestborrowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::bestborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestBorrowRate)
  return bestborrowrate_;
}
void MDSecurityLending::set_bestborrowrate(::google::protobuf::int64 value) {
  
  bestborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestBorrowRate)
}

// optional int64 BestLendRate = 21;
void MDSecurityLending::clear_bestlendrate() {
  bestlendrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::bestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestLendRate)
  return bestlendrate_;
}
void MDSecurityLending::set_bestlendrate(::google::protobuf::int64 value) {
  
  bestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestLendRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
int MDSecurityLending::validborrows_size() const {
  return validborrows_.size();
}
void MDSecurityLending::clear_validborrows() {
  validborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return &validborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
int MDSecurityLending::validalends_size() const {
  return validalends_.size();
}
void MDSecurityLending::clear_validalends() {
  validalends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validalends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validalends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validalends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validalends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return &validalends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validalends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
int MDSecurityLending::validblends_size() const {
  return validblends_.size();
}
void MDSecurityLending::clear_validblends() {
  validblends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validblends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validblends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validblends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validblends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return &validblends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validblends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
int MDSecurityLending::validclends_size() const {
  return validclends_.size();
}
void MDSecurityLending::clear_validclends() {
  validclends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validclends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validclends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validclends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validclends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return &validclends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validclends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
int MDSecurityLending::alends_size() const {
  return alends_.size();
}
void MDSecurityLending::clear_alends() {
  alends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::alends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_alends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_alends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_alends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return &alends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::alends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
int MDSecurityLending::blends_size() const {
  return blends_.size();
}
void MDSecurityLending::clear_blends() {
  blends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::blends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_blends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_blends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_blends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return &blends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::blends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
int MDSecurityLending::clends_size() const {
  return clends_.size();
}
void MDSecurityLending::clear_clends() {
  clends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::clends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_clends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_clends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_clends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return &clends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::clends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
int MDSecurityLending::validreservationborrows_size() const {
  return validreservationborrows_.size();
}
void MDSecurityLending::clear_validreservationborrows() {
  validreservationborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validreservationborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validreservationborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validreservationborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validreservationborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return &validreservationborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validreservationborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
int MDSecurityLending::validreservationlends_size() const {
  return validreservationlends_.size();
}
void MDSecurityLending::clear_validreservationlends() {
  validreservationlends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validreservationlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validreservationlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validreservationlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validreservationlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return &validreservationlends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validreservationlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
int MDSecurityLending::reservationborrows_size() const {
  return reservationborrows_.size();
}
void MDSecurityLending::clear_reservationborrows() {
  reservationborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::reservationborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_reservationborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_reservationborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_reservationborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return &reservationborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::reservationborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
int MDSecurityLending::reservationlends_size() const {
  return reservationlends_.size();
}
void MDSecurityLending::clear_reservationlends() {
  reservationlends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::reservationlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_reservationlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_reservationlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_reservationlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return &reservationlends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::reservationlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
int MDSecurityLending::validotclends_size() const {
  return validotclends_.size();
}
void MDSecurityLending::clear_validotclends() {
  validotclends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validotclends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validotclends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validotclends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validotclends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return &validotclends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validotclends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_;
}

// optional int64 BestReservationBorrowRate = 39;
void MDSecurityLending::clear_bestreservationborrowrate() {
  bestreservationborrowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::bestreservationborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationBorrowRate)
  return bestreservationborrowrate_;
}
void MDSecurityLending::set_bestreservationborrowrate(::google::protobuf::int64 value) {
  
  bestreservationborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationBorrowRate)
}

// optional int64 BestReservationLendRate = 40;
void MDSecurityLending::clear_bestreservationlendrate() {
  bestreservationlendrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::bestreservationlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationLendRate)
  return bestreservationlendrate_;
}
void MDSecurityLending::set_bestreservationlendrate(::google::protobuf::int64 value) {
  
  bestreservationlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationLendRate)
}

// optional int64 ValidLendAmount = 41;
void MDSecurityLending::clear_validlendamount() {
  validlendamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::validlendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendAmount)
  return validlendamount_;
}
void MDSecurityLending::set_validlendamount(::google::protobuf::int64 value) {
  
  validlendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendAmount)
}

// optional int64 ValidALendAmount = 42;
void MDSecurityLending::clear_validalendamount() {
  validalendamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::validalendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidALendAmount)
  return validalendamount_;
}
void MDSecurityLending::set_validalendamount(::google::protobuf::int64 value) {
  
  validalendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidALendAmount)
}

// optional int64 ValidBLendAmount = 43;
void MDSecurityLending::clear_validblendamount() {
  validblendamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::validblendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLendAmount)
  return validblendamount_;
}
void MDSecurityLending::set_validblendamount(::google::protobuf::int64 value) {
  
  validblendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLendAmount)
}

// optional int64 HtscBorrowAmount = 44;
void MDSecurityLending::clear_htscborrowamount() {
  htscborrowamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowAmount)
  return htscborrowamount_;
}
void MDSecurityLending::set_htscborrowamount(::google::protobuf::int64 value) {
  
  htscborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowAmount)
}

// optional int64 HtscBorrowRate = 45;
void MDSecurityLending::clear_htscborrowrate() {
  htscborrowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowRate)
  return htscborrowrate_;
}
void MDSecurityLending::set_htscborrowrate(::google::protobuf::int64 value) {
  
  htscborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowRate)
}

// optional int64 BestLoanRate = 46;
void MDSecurityLending::clear_bestloanrate() {
  bestloanrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::bestloanrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestLoanRate)
  return bestloanrate_;
}
void MDSecurityLending::set_bestloanrate(::google::protobuf::int64 value) {
  
  bestloanrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestLoanRate)
}

// optional int64 HtscBorrowTradeVolume = 47;
void MDSecurityLending::clear_htscborrowtradevolume() {
  htscborrowtradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscborrowtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTradeVolume)
  return htscborrowtradevolume_;
}
void MDSecurityLending::set_htscborrowtradevolume(::google::protobuf::int64 value) {
  
  htscborrowtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTradeVolume)
}

// optional int64 HtscBorrowWeightedRate = 48;
void MDSecurityLending::clear_htscborrowweightedrate() {
  htscborrowweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscborrowweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowWeightedRate)
  return htscborrowweightedrate_;
}
void MDSecurityLending::set_htscborrowweightedrate(::google::protobuf::int64 value) {
  
  htscborrowweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowWeightedRate)
}

// optional int64 PreHtscBorrowTradeVolume = 49;
void MDSecurityLending::clear_prehtscborrowtradevolume() {
  prehtscborrowtradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::prehtscborrowtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowTradeVolume)
  return prehtscborrowtradevolume_;
}
void MDSecurityLending::set_prehtscborrowtradevolume(::google::protobuf::int64 value) {
  
  prehtscborrowtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowTradeVolume)
}

// optional int64 PreHtscBorrowWeightedRate = 50;
void MDSecurityLending::clear_prehtscborrowweightedrate() {
  prehtscborrowweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::prehtscborrowweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowWeightedRate)
  return prehtscborrowweightedrate_;
}
void MDSecurityLending::set_prehtscborrowweightedrate(::google::protobuf::int64 value) {
  
  prehtscborrowweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowWeightedRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
int MDSecurityLending::htscborrows_size() const {
  return htscborrows_.size();
}
void MDSecurityLending::clear_htscborrows() {
  htscborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::htscborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_htscborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_htscborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_htscborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return &htscborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::htscborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
int MDSecurityLending::loans_size() const {
  return loans_.size();
}
void MDSecurityLending::clear_loans() {
  loans_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::loans(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_loans(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_loans() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_loans() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return &loans_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::loans() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_;
}

// optional int32 DataMultiplePowerOf10 = 53;
void MDSecurityLending::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSecurityLending::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSecurityLending::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
int MDSecurityLending::externallends_size() const {
  return externallends_.size();
}
void MDSecurityLending::clear_externallends() {
  externallends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry& MDSecurityLending::externallends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Get(index);
}
::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* MDSecurityLending::mutable_externallends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* MDSecurityLending::add_externallends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >*
MDSecurityLending::mutable_externallends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return &externallends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >&
MDSecurityLending::externallends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_;
}

// optional string HtscBorrowTerm = 57;
void MDSecurityLending::clear_htscborrowterm() {
  htscborrowterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSecurityLending::htscborrowterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  return htscborrowterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_htscborrowterm(const ::std::string& value) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
void MDSecurityLending::set_htscborrowterm(const char* value) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
void MDSecurityLending::set_htscborrowterm(const char* value, size_t size) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
::std::string* MDSecurityLending::mutable_htscborrowterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  return htscborrowterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSecurityLending::release_htscborrowterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  
  return htscborrowterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_allocated_htscborrowterm(::std::string* htscborrowterm) {
  if (htscborrowterm != NULL) {
    
  } else {
    
  }
  htscborrowterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscborrowterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}

// optional int64 HtscBorrowOrderAmount = 58;
void MDSecurityLending::clear_htscborroworderamount() {
  htscborroworderamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::htscborroworderamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowOrderAmount)
  return htscborroworderamount_;
}
void MDSecurityLending::set_htscborroworderamount(::google::protobuf::int64 value) {
  
  htscborroworderamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowOrderAmount)
}

// optional string ValidLendTerm = 59;
void MDSecurityLending::clear_validlendterm() {
  validlendterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSecurityLending::validlendterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  return validlendterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_validlendterm(const ::std::string& value) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
void MDSecurityLending::set_validlendterm(const char* value) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
void MDSecurityLending::set_validlendterm(const char* value, size_t size) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
::std::string* MDSecurityLending::mutable_validlendterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  return validlendterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSecurityLending::release_validlendterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  
  return validlendterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSecurityLending::set_allocated_validlendterm(::std::string* validlendterm) {
  if (validlendterm != NULL) {
    
  } else {
    
  }
  validlendterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), validlendterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}

// optional int64 ValidLendOrderAmount = 60;
void MDSecurityLending::clear_validlendorderamount() {
  validlendorderamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::validlendorderamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendOrderAmount)
  return validlendorderamount_;
}
void MDSecurityLending::set_validlendorderamount(::google::protobuf::int64 value) {
  
  validlendorderamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendOrderAmount)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
int MDSecurityLending::marketloans_size() const {
  return marketloans_.size();
}
void MDSecurityLending::clear_marketloans() {
  marketloans_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketloans(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketloans(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketloans() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketloans() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return &marketloans_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketloans() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
int MDSecurityLending::marketlends_size() const {
  return marketlends_.size();
}
void MDSecurityLending::clear_marketlends() {
  marketlends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return &marketlends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_;
}

// optional int64 ValidBorrowAmount = 63;
void MDSecurityLending::clear_validborrowamount() {
  validborrowamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::validborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrowAmount)
  return validborrowamount_;
}
void MDSecurityLending::set_validborrowamount(::google::protobuf::int64 value) {
  
  validborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrowAmount)
}

// optional int64 LoanAmount = 64;
void MDSecurityLending::clear_loanamount() {
  loanamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::loanamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.LoanAmount)
  return loanamount_;
}
void MDSecurityLending::set_loanamount(::google::protobuf::int64 value) {
  
  loanamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.LoanAmount)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
int MDSecurityLending::marketborrows_size() const {
  return marketborrows_.size();
}
void MDSecurityLending::clear_marketborrows() {
  marketborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return &marketborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_;
}

// optional int64 BorrowAmount = 66;
void MDSecurityLending::clear_borrowamount() {
  borrowamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSecurityLending::borrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BorrowAmount)
  return borrowamount_;
}
void MDSecurityLending::set_borrowamount(::google::protobuf::int64 value) {
  
  borrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BorrowAmount)
}

inline const MDSecurityLending* MDSecurityLending::internal_default_instance() {
  return &MDSecurityLending_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADValidSecurityLendingEntry::kLevelFieldNumber;
const int ADValidSecurityLendingEntry::kRateFieldNumber;
const int ADValidSecurityLendingEntry::kTermFieldNumber;
const int ADValidSecurityLendingEntry::kAmountFieldNumber;
const int ADValidSecurityLendingEntry::kHtscProvidedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADValidSecurityLendingEntry::ADValidSecurityLendingEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSecurityLending_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
}

void ADValidSecurityLendingEntry::InitAsDefaultInstance() {
}

ADValidSecurityLendingEntry::ADValidSecurityLendingEntry(const ADValidSecurityLendingEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
}

void ADValidSecurityLendingEntry::SharedCtor() {
  ::memset(&rate_, 0, reinterpret_cast<char*>(&htscprovided_) -
    reinterpret_cast<char*>(&rate_) + sizeof(htscprovided_));
  _cached_size_ = 0;
}

ADValidSecurityLendingEntry::~ADValidSecurityLendingEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  SharedDtor();
}

void ADValidSecurityLendingEntry::SharedDtor() {
}

void ADValidSecurityLendingEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADValidSecurityLendingEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADValidSecurityLendingEntry_descriptor_;
}

const ADValidSecurityLendingEntry& ADValidSecurityLendingEntry::default_instance() {
  protobuf_InitDefaults_MDSecurityLending_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADValidSecurityLendingEntry> ADValidSecurityLendingEntry_default_instance_;

ADValidSecurityLendingEntry* ADValidSecurityLendingEntry::New(::google::protobuf::Arena* arena) const {
  ADValidSecurityLendingEntry* n = new ADValidSecurityLendingEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADValidSecurityLendingEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADValidSecurityLendingEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADValidSecurityLendingEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(rate_, htscprovided_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADValidSecurityLendingEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Level = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &level_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Rate;
        break;
      }

      // optional int64 Rate = 2;
      case 2: {
        if (tag == 16) {
         parse_Rate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &rate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Term;
        break;
      }

      // optional int32 Term = 3;
      case 3: {
        if (tag == 24) {
         parse_Term:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &term_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Amount;
        break;
      }

      // optional int64 Amount = 4;
      case 4: {
        if (tag == 32) {
         parse_Amount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &amount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_HtscProvided;
        break;
      }

      // optional bool HtscProvided = 5;
      case 5: {
        if (tag == 40) {
         parse_HtscProvided:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &htscprovided_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  return false;
#undef DO_
}

void ADValidSecurityLendingEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->level(), output);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->rate(), output);
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->term(), output);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->amount(), output);
  }

  // optional bool HtscProvided = 5;
  if (this->htscprovided() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->htscprovided(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
}

::google::protobuf::uint8* ADValidSecurityLendingEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->level(), target);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->rate(), target);
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->term(), target);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->amount(), target);
  }

  // optional bool HtscProvided = 5;
  if (this->htscprovided() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->htscprovided(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  return target;
}

size_t ADValidSecurityLendingEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  size_t total_size = 0;

  // optional int32 Level = 1;
  if (this->level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->level());
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->rate());
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->term());
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->amount());
  }

  // optional bool HtscProvided = 5;
  if (this->htscprovided() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADValidSecurityLendingEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADValidSecurityLendingEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADValidSecurityLendingEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
    UnsafeMergeFrom(*source);
  }
}

void ADValidSecurityLendingEntry::MergeFrom(const ADValidSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADValidSecurityLendingEntry::UnsafeMergeFrom(const ADValidSecurityLendingEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.level() != 0) {
    set_level(from.level());
  }
  if (from.rate() != 0) {
    set_rate(from.rate());
  }
  if (from.term() != 0) {
    set_term(from.term());
  }
  if (from.amount() != 0) {
    set_amount(from.amount());
  }
  if (from.htscprovided() != 0) {
    set_htscprovided(from.htscprovided());
  }
}

void ADValidSecurityLendingEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADValidSecurityLendingEntry::CopyFrom(const ADValidSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADValidSecurityLendingEntry::IsInitialized() const {

  return true;
}

void ADValidSecurityLendingEntry::Swap(ADValidSecurityLendingEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADValidSecurityLendingEntry::InternalSwap(ADValidSecurityLendingEntry* other) {
  std::swap(level_, other->level_);
  std::swap(rate_, other->rate_);
  std::swap(term_, other->term_);
  std::swap(amount_, other->amount_);
  std::swap(htscprovided_, other->htscprovided_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADValidSecurityLendingEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADValidSecurityLendingEntry_descriptor_;
  metadata.reflection = ADValidSecurityLendingEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADValidSecurityLendingEntry

// optional int32 Level = 1;
void ADValidSecurityLendingEntry::clear_level() {
  level_ = 0;
}
::google::protobuf::int32 ADValidSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Level)
  return level_;
}
void ADValidSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
void ADValidSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADValidSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Rate)
  return rate_;
}
void ADValidSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Rate)
}

// optional int32 Term = 3;
void ADValidSecurityLendingEntry::clear_term() {
  term_ = 0;
}
::google::protobuf::int32 ADValidSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Term)
  return term_;
}
void ADValidSecurityLendingEntry::set_term(::google::protobuf::int32 value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Term)
}

// optional int64 Amount = 4;
void ADValidSecurityLendingEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADValidSecurityLendingEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Amount)
  return amount_;
}
void ADValidSecurityLendingEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Amount)
}

// optional bool HtscProvided = 5;
void ADValidSecurityLendingEntry::clear_htscprovided() {
  htscprovided_ = false;
}
bool ADValidSecurityLendingEntry::htscprovided() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.HtscProvided)
  return htscprovided_;
}
void ADValidSecurityLendingEntry::set_htscprovided(bool value) {
  
  htscprovided_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.HtscProvided)
}

inline const ADValidSecurityLendingEntry* ADValidSecurityLendingEntry::internal_default_instance() {
  return &ADValidSecurityLendingEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADSecurityLendingEntry::kLevelFieldNumber;
const int ADSecurityLendingEntry::kRateFieldNumber;
const int ADSecurityLendingEntry::kTermFieldNumber;
const int ADSecurityLendingEntry::kTotalAmountFieldNumber;
const int ADSecurityLendingEntry::kMatchedAmountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADSecurityLendingEntry::ADSecurityLendingEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSecurityLending_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
}

void ADSecurityLendingEntry::InitAsDefaultInstance() {
}

ADSecurityLendingEntry::ADSecurityLendingEntry(const ADSecurityLendingEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
}

void ADSecurityLendingEntry::SharedCtor() {
  ::memset(&rate_, 0, reinterpret_cast<char*>(&matchedamount_) -
    reinterpret_cast<char*>(&rate_) + sizeof(matchedamount_));
  _cached_size_ = 0;
}

ADSecurityLendingEntry::~ADSecurityLendingEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  SharedDtor();
}

void ADSecurityLendingEntry::SharedDtor() {
}

void ADSecurityLendingEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADSecurityLendingEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADSecurityLendingEntry_descriptor_;
}

const ADSecurityLendingEntry& ADSecurityLendingEntry::default_instance() {
  protobuf_InitDefaults_MDSecurityLending_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADSecurityLendingEntry> ADSecurityLendingEntry_default_instance_;

ADSecurityLendingEntry* ADSecurityLendingEntry::New(::google::protobuf::Arena* arena) const {
  ADSecurityLendingEntry* n = new ADSecurityLendingEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADSecurityLendingEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADSecurityLendingEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADSecurityLendingEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(rate_, matchedamount_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADSecurityLendingEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Level = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &level_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Rate;
        break;
      }

      // optional int64 Rate = 2;
      case 2: {
        if (tag == 16) {
         parse_Rate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &rate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Term;
        break;
      }

      // optional int32 Term = 3;
      case 3: {
        if (tag == 24) {
         parse_Term:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &term_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_TotalAmount;
        break;
      }

      // optional int64 TotalAmount = 4;
      case 4: {
        if (tag == 32) {
         parse_TotalAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MatchedAmount;
        break;
      }

      // optional int64 MatchedAmount = 5;
      case 5: {
        if (tag == 40) {
         parse_MatchedAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &matchedamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  return false;
#undef DO_
}

void ADSecurityLendingEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->level(), output);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->rate(), output);
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->term(), output);
  }

  // optional int64 TotalAmount = 4;
  if (this->totalamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->totalamount(), output);
  }

  // optional int64 MatchedAmount = 5;
  if (this->matchedamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->matchedamount(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
}

::google::protobuf::uint8* ADSecurityLendingEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->level(), target);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->rate(), target);
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->term(), target);
  }

  // optional int64 TotalAmount = 4;
  if (this->totalamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->totalamount(), target);
  }

  // optional int64 MatchedAmount = 5;
  if (this->matchedamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->matchedamount(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  return target;
}

size_t ADSecurityLendingEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  size_t total_size = 0;

  // optional int32 Level = 1;
  if (this->level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->level());
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->rate());
  }

  // optional int32 Term = 3;
  if (this->term() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->term());
  }

  // optional int64 TotalAmount = 4;
  if (this->totalamount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalamount());
  }

  // optional int64 MatchedAmount = 5;
  if (this->matchedamount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->matchedamount());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADSecurityLendingEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADSecurityLendingEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADSecurityLendingEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
    UnsafeMergeFrom(*source);
  }
}

void ADSecurityLendingEntry::MergeFrom(const ADSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADSecurityLendingEntry::UnsafeMergeFrom(const ADSecurityLendingEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.level() != 0) {
    set_level(from.level());
  }
  if (from.rate() != 0) {
    set_rate(from.rate());
  }
  if (from.term() != 0) {
    set_term(from.term());
  }
  if (from.totalamount() != 0) {
    set_totalamount(from.totalamount());
  }
  if (from.matchedamount() != 0) {
    set_matchedamount(from.matchedamount());
  }
}

void ADSecurityLendingEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADSecurityLendingEntry::CopyFrom(const ADSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADSecurityLendingEntry::IsInitialized() const {

  return true;
}

void ADSecurityLendingEntry::Swap(ADSecurityLendingEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADSecurityLendingEntry::InternalSwap(ADSecurityLendingEntry* other) {
  std::swap(level_, other->level_);
  std::swap(rate_, other->rate_);
  std::swap(term_, other->term_);
  std::swap(totalamount_, other->totalamount_);
  std::swap(matchedamount_, other->matchedamount_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADSecurityLendingEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADSecurityLendingEntry_descriptor_;
  metadata.reflection = ADSecurityLendingEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADSecurityLendingEntry

// optional int32 Level = 1;
void ADSecurityLendingEntry::clear_level() {
  level_ = 0;
}
::google::protobuf::int32 ADSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Level)
  return level_;
}
void ADSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
void ADSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Rate)
  return rate_;
}
void ADSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Rate)
}

// optional int32 Term = 3;
void ADSecurityLendingEntry::clear_term() {
  term_ = 0;
}
::google::protobuf::int32 ADSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Term)
  return term_;
}
void ADSecurityLendingEntry::set_term(::google::protobuf::int32 value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Term)
}

// optional int64 TotalAmount = 4;
void ADSecurityLendingEntry::clear_totalamount() {
  totalamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADSecurityLendingEntry::totalamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.TotalAmount)
  return totalamount_;
}
void ADSecurityLendingEntry::set_totalamount(::google::protobuf::int64 value) {
  
  totalamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.TotalAmount)
}

// optional int64 MatchedAmount = 5;
void ADSecurityLendingEntry::clear_matchedamount() {
  matchedamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADSecurityLendingEntry::matchedamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.MatchedAmount)
  return matchedamount_;
}
void ADSecurityLendingEntry::set_matchedamount(::google::protobuf::int64 value) {
  
  matchedamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.MatchedAmount)
}

inline const ADSecurityLendingEntry* ADSecurityLendingEntry::internal_default_instance() {
  return &ADSecurityLendingEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADEstimatedSecurityLendingEntry::kLevelFieldNumber;
const int ADEstimatedSecurityLendingEntry::kRateFieldNumber;
const int ADEstimatedSecurityLendingEntry::kTermFieldNumber;
const int ADEstimatedSecurityLendingEntry::kAmountFieldNumber;
const int ADEstimatedSecurityLendingEntry::kPostponeProbabilityFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADEstimatedSecurityLendingEntry::ADEstimatedSecurityLendingEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSecurityLending_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
}

void ADEstimatedSecurityLendingEntry::InitAsDefaultInstance() {
}

ADEstimatedSecurityLendingEntry::ADEstimatedSecurityLendingEntry(const ADEstimatedSecurityLendingEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
}

void ADEstimatedSecurityLendingEntry::SharedCtor() {
  term_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&rate_, 0, reinterpret_cast<char*>(&amount_) -
    reinterpret_cast<char*>(&rate_) + sizeof(amount_));
  _cached_size_ = 0;
}

ADEstimatedSecurityLendingEntry::~ADEstimatedSecurityLendingEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  SharedDtor();
}

void ADEstimatedSecurityLendingEntry::SharedDtor() {
  term_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADEstimatedSecurityLendingEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADEstimatedSecurityLendingEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADEstimatedSecurityLendingEntry_descriptor_;
}

const ADEstimatedSecurityLendingEntry& ADEstimatedSecurityLendingEntry::default_instance() {
  protobuf_InitDefaults_MDSecurityLending_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADEstimatedSecurityLendingEntry> ADEstimatedSecurityLendingEntry_default_instance_;

ADEstimatedSecurityLendingEntry* ADEstimatedSecurityLendingEntry::New(::google::protobuf::Arena* arena) const {
  ADEstimatedSecurityLendingEntry* n = new ADEstimatedSecurityLendingEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADEstimatedSecurityLendingEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADEstimatedSecurityLendingEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADEstimatedSecurityLendingEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(rate_, amount_);
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ADEstimatedSecurityLendingEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Level = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &level_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Rate;
        break;
      }

      // optional int64 Rate = 2;
      case 2: {
        if (tag == 16) {
         parse_Rate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &rate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Term;
        break;
      }

      // optional string Term = 3;
      case 3: {
        if (tag == 26) {
         parse_Term:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_term()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->term().data(), this->term().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Amount;
        break;
      }

      // optional int64 Amount = 4;
      case 4: {
        if (tag == 32) {
         parse_Amount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &amount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_PostponeProbability;
        break;
      }

      // optional int32 PostponeProbability = 5;
      case 5: {
        if (tag == 40) {
         parse_PostponeProbability:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &postponeprobability_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  return false;
#undef DO_
}

void ADEstimatedSecurityLendingEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->level(), output);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->rate(), output);
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->term().data(), this->term().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->term(), output);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->amount(), output);
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->postponeprobability(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
}

::google::protobuf::uint8* ADEstimatedSecurityLendingEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->level(), target);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->rate(), target);
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->term().data(), this->term().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->term(), target);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->amount(), target);
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->postponeprobability(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  return target;
}

size_t ADEstimatedSecurityLendingEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  size_t total_size = 0;

  // optional int32 Level = 1;
  if (this->level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->level());
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->rate());
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->term());
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->amount());
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->postponeprobability());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADEstimatedSecurityLendingEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADEstimatedSecurityLendingEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADEstimatedSecurityLendingEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
    UnsafeMergeFrom(*source);
  }
}

void ADEstimatedSecurityLendingEntry::MergeFrom(const ADEstimatedSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADEstimatedSecurityLendingEntry::UnsafeMergeFrom(const ADEstimatedSecurityLendingEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.level() != 0) {
    set_level(from.level());
  }
  if (from.rate() != 0) {
    set_rate(from.rate());
  }
  if (from.term().size() > 0) {

    term_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.term_);
  }
  if (from.amount() != 0) {
    set_amount(from.amount());
  }
  if (from.postponeprobability() != 0) {
    set_postponeprobability(from.postponeprobability());
  }
}

void ADEstimatedSecurityLendingEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADEstimatedSecurityLendingEntry::CopyFrom(const ADEstimatedSecurityLendingEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADEstimatedSecurityLendingEntry::IsInitialized() const {

  return true;
}

void ADEstimatedSecurityLendingEntry::Swap(ADEstimatedSecurityLendingEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADEstimatedSecurityLendingEntry::InternalSwap(ADEstimatedSecurityLendingEntry* other) {
  std::swap(level_, other->level_);
  std::swap(rate_, other->rate_);
  term_.Swap(&other->term_);
  std::swap(amount_, other->amount_);
  std::swap(postponeprobability_, other->postponeprobability_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADEstimatedSecurityLendingEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADEstimatedSecurityLendingEntry_descriptor_;
  metadata.reflection = ADEstimatedSecurityLendingEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADEstimatedSecurityLendingEntry

// optional int32 Level = 1;
void ADEstimatedSecurityLendingEntry::clear_level() {
  level_ = 0;
}
::google::protobuf::int32 ADEstimatedSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Level)
  return level_;
}
void ADEstimatedSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
void ADEstimatedSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADEstimatedSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Rate)
  return rate_;
}
void ADEstimatedSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Rate)
}

// optional string Term = 3;
void ADEstimatedSecurityLendingEntry::clear_term() {
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADEstimatedSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  return term_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADEstimatedSecurityLendingEntry::set_term(const ::std::string& value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
void ADEstimatedSecurityLendingEntry::set_term(const char* value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
void ADEstimatedSecurityLendingEntry::set_term(const char* value, size_t size) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
::std::string* ADEstimatedSecurityLendingEntry::mutable_term() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  return term_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADEstimatedSecurityLendingEntry::release_term() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  
  return term_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADEstimatedSecurityLendingEntry::set_allocated_term(::std::string* term) {
  if (term != NULL) {
    
  } else {
    
  }
  term_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), term);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}

// optional int64 Amount = 4;
void ADEstimatedSecurityLendingEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADEstimatedSecurityLendingEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Amount)
  return amount_;
}
void ADEstimatedSecurityLendingEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Amount)
}

// optional int32 PostponeProbability = 5;
void ADEstimatedSecurityLendingEntry::clear_postponeprobability() {
  postponeprobability_ = 0;
}
::google::protobuf::int32 ADEstimatedSecurityLendingEntry::postponeprobability() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.PostponeProbability)
  return postponeprobability_;
}
void ADEstimatedSecurityLendingEntry::set_postponeprobability(::google::protobuf::int32 value) {
  
  postponeprobability_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.PostponeProbability)
}

inline const ADEstimatedSecurityLendingEntry* ADEstimatedSecurityLendingEntry::internal_default_instance() {
  return &ADEstimatedSecurityLendingEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
