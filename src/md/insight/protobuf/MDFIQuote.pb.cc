// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDFIQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDFIQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDFIQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDFIQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADFIQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADFIQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDFIQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDFIQuote_2eproto() {
  protobuf_AddDesc_MDFIQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDFIQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDFIQuote_descriptor_ = file->message_type(0);
  static const int MDFIQuote_offsets_[39] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, messagenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, brokerdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, accruedinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyquoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyquotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyquoter_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buycleanprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buydirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buymaturityyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyquotecomment_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyquotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buybargaintype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyrelationtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyexercisetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, buyyieldtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellquoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellquotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellquoter_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellcleanprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, selldirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellmaturityyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellquotecomment_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellquotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellbargaintype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellrelationtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellexercisetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, sellyieldtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, quotes_),
  };
  MDFIQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDFIQuote_descriptor_,
      MDFIQuote::internal_default_instance(),
      MDFIQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDFIQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFIQuote, _internal_metadata_));
  ADFIQuote_descriptor_ = file->message_type(1);
  static const int ADFIQuote_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, quotebsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, quotelevel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, quoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, quotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, quoter_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, cleanprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, dirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, maturityyield_),
  };
  ADFIQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADFIQuote_descriptor_,
      ADFIQuote::internal_default_instance(),
      ADFIQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADFIQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADFIQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDFIQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDFIQuote_descriptor_, MDFIQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADFIQuote_descriptor_, ADFIQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDFIQuote_2eproto() {
  MDFIQuote_default_instance_.Shutdown();
  delete MDFIQuote_reflection_;
  ADFIQuote_default_instance_.Shutdown();
  delete ADFIQuote_reflection_;
}

void protobuf_InitDefaults_MDFIQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDFIQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADFIQuote_default_instance_.DefaultConstruct();
  MDFIQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADFIQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDFIQuote_2eproto_once_);
void protobuf_InitDefaults_MDFIQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDFIQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDFIQuote_2eproto_impl);
}
void protobuf_AddDesc_MDFIQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDFIQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\017MDFIQuote.proto\022\032com.htsc.mdc.insight."
    "model\032\027ESecurityIDSource.proto\032\023ESecurit"
    "yType.proto\"\371\007\n\tMDFIQuote\022\026\n\016HTSCSecurit"
    "yID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001("
    "\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securityIDSo"
    "urce\030\005 \001(\0162%.com.htsc.mdc.model.ESecurit"
    "yIDSource\0227\n\014securityType\030\006 \001(\0162!.com.ht"
    "sc.mdc.model.ESecurityType\022\024\n\014ExchangeDa"
    "te\030\007 \001(\005\022\024\n\014ExchangeTime\030\010 \001(\005\022\025\n\rMessag"
    "eNumber\030\t \001(\003\022\026\n\016BrokerDataType\030\n \001(\005\022\027\n"
    "\017AccruedInterest\030\013 \001(\003\022\022\n\nBuyQuoteID\030\024 \001"
    "(\t\022\024\n\014BuyQuoteTime\030\025 \001(\005\022\021\n\tBuyQuoter\030\026 "
    "\001(\t\022\025\n\rBuyCleanPrice\030\027 \001(\003\022\021\n\tBuyVolume\030"
    "\030 \001(\003\022\025\n\rBuyDirtyPrice\030\031 \001(\003\022\030\n\020BuyMatur"
    "ityYield\030\032 \001(\003\022\027\n\017BuyQuoteComment\030\033 \001(\t\022"
    "\024\n\014BuyQuoteType\030\034 \001(\005\022\026\n\016BuyBargainType\030"
    "\035 \001(\005\022\027\n\017BuyRelationType\030\036 \001(\005\022\027\n\017BuyExe"
    "rciseType\030\037 \001(\005\022\024\n\014BuyYieldType\030  \001(\005\022\023\n"
    "\013SellQuoteID\030! \001(\t\022\025\n\rSellQuoteTime\030\" \001("
    "\005\022\022\n\nSellQuoter\030# \001(\t\022\026\n\016SellCleanPrice\030"
    "$ \001(\003\022\022\n\nSellVolume\030% \001(\003\022\026\n\016SellDirtyPr"
    "ice\030& \001(\003\022\031\n\021SellMaturityYield\030\' \001(\003\022\030\n\020"
    "SellQuoteComment\030( \001(\t\022\025\n\rSellQuoteType\030"
    ") \001(\005\022\027\n\017SellBargainType\030* \001(\005\022\030\n\020SellRe"
    "lationType\030+ \001(\005\022\030\n\020SellExerciseType\030, \001"
    "(\005\022\025\n\rSellYieldType\030- \001(\005\022\035\n\025DataMultipl"
    "ePowerOf10\030. \001(\005\0225\n\006Quotes\030/ \003(\0132%.com.h"
    "tsc.mdc.insight.model.ADFIQuote\"\267\001\n\tADFI"
    "Quote\022\023\n\013QuoteBSFlag\030\001 \001(\005\022\022\n\nQuoteLevel"
    "\030\002 \001(\005\022\017\n\007QuoteID\030\003 \001(\t\022\021\n\tQuoteTime\030\004 \001"
    "(\005\022\016\n\006Quoter\030\005 \001(\t\022\022\n\nCleanPrice\030\006 \001(\003\022\016"
    "\n\006Volume\030\007 \001(\003\022\022\n\nDirtyPrice\030\010 \001(\003\022\025\n\rMa"
    "turityYield\030\t \001(\003B2\n\032com.htsc.mdc.insigh"
    "t.modelB\017MDFIQuoteProtosH\001\240\001\001b\006proto3", 1357);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDFIQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDFIQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDFIQuote_2eproto_once_);
void protobuf_AddDesc_MDFIQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDFIQuote_2eproto_once_,
                 &protobuf_AddDesc_MDFIQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDFIQuote_2eproto {
  StaticDescriptorInitializer_MDFIQuote_2eproto() {
    protobuf_AddDesc_MDFIQuote_2eproto();
  }
} static_descriptor_initializer_MDFIQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDFIQuote::kHTSCSecurityIDFieldNumber;
const int MDFIQuote::kMDDateFieldNumber;
const int MDFIQuote::kMDTimeFieldNumber;
const int MDFIQuote::kDataTimestampFieldNumber;
const int MDFIQuote::kSecurityIDSourceFieldNumber;
const int MDFIQuote::kSecurityTypeFieldNumber;
const int MDFIQuote::kExchangeDateFieldNumber;
const int MDFIQuote::kExchangeTimeFieldNumber;
const int MDFIQuote::kMessageNumberFieldNumber;
const int MDFIQuote::kBrokerDataTypeFieldNumber;
const int MDFIQuote::kAccruedInterestFieldNumber;
const int MDFIQuote::kBuyQuoteIDFieldNumber;
const int MDFIQuote::kBuyQuoteTimeFieldNumber;
const int MDFIQuote::kBuyQuoterFieldNumber;
const int MDFIQuote::kBuyCleanPriceFieldNumber;
const int MDFIQuote::kBuyVolumeFieldNumber;
const int MDFIQuote::kBuyDirtyPriceFieldNumber;
const int MDFIQuote::kBuyMaturityYieldFieldNumber;
const int MDFIQuote::kBuyQuoteCommentFieldNumber;
const int MDFIQuote::kBuyQuoteTypeFieldNumber;
const int MDFIQuote::kBuyBargainTypeFieldNumber;
const int MDFIQuote::kBuyRelationTypeFieldNumber;
const int MDFIQuote::kBuyExerciseTypeFieldNumber;
const int MDFIQuote::kBuyYieldTypeFieldNumber;
const int MDFIQuote::kSellQuoteIDFieldNumber;
const int MDFIQuote::kSellQuoteTimeFieldNumber;
const int MDFIQuote::kSellQuoterFieldNumber;
const int MDFIQuote::kSellCleanPriceFieldNumber;
const int MDFIQuote::kSellVolumeFieldNumber;
const int MDFIQuote::kSellDirtyPriceFieldNumber;
const int MDFIQuote::kSellMaturityYieldFieldNumber;
const int MDFIQuote::kSellQuoteCommentFieldNumber;
const int MDFIQuote::kSellQuoteTypeFieldNumber;
const int MDFIQuote::kSellBargainTypeFieldNumber;
const int MDFIQuote::kSellRelationTypeFieldNumber;
const int MDFIQuote::kSellExerciseTypeFieldNumber;
const int MDFIQuote::kSellYieldTypeFieldNumber;
const int MDFIQuote::kDataMultiplePowerOf10FieldNumber;
const int MDFIQuote::kQuotesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDFIQuote::MDFIQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDFIQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDFIQuote)
}

void MDFIQuote::InitAsDefaultInstance() {
}

MDFIQuote::MDFIQuote(const MDFIQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDFIQuote)
}

void MDFIQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquoter_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquotecomment_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquoter_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquotecomment_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDFIQuote::~MDFIQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDFIQuote)
  SharedDtor();
}

void MDFIQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquoter_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquotecomment_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquoter_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquotecomment_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDFIQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDFIQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDFIQuote_descriptor_;
}

const MDFIQuote& MDFIQuote::default_instance() {
  protobuf_InitDefaults_MDFIQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDFIQuote> MDFIQuote_default_instance_;

MDFIQuote* MDFIQuote::New(::google::protobuf::Arena* arena) const {
  MDFIQuote* n = new MDFIQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDFIQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDFIQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDFIQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDFIQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(messagenumber_, buyvolume_);
  buyquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buyquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(buydirtyprice_, buyyieldtype_);
  buyquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(sellquotetime_, sellmaturityyield_);
  sellquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(sellquotetype_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

  quotes_.Clear();
}

bool MDFIQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDFIQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 9;
      case 9: {
        if (tag == 72) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_BrokerDataType;
        break;
      }

      // optional int32 BrokerDataType = 10;
      case 10: {
        if (tag == 80) {
         parse_BrokerDataType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &brokerdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_AccruedInterest;
        break;
      }

      // optional int64 AccruedInterest = 11;
      case 11: {
        if (tag == 88) {
         parse_AccruedInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accruedinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_BuyQuoteID;
        break;
      }

      // optional string BuyQuoteID = 20;
      case 20: {
        if (tag == 162) {
         parse_BuyQuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buyquoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buyquoteid().data(), this->buyquoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_BuyQuoteTime;
        break;
      }

      // optional int32 BuyQuoteTime = 21;
      case 21: {
        if (tag == 168) {
         parse_BuyQuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buyquotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_BuyQuoter;
        break;
      }

      // optional string BuyQuoter = 22;
      case 22: {
        if (tag == 178) {
         parse_BuyQuoter:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buyquoter()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buyquoter().data(), this->buyquoter().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_BuyCleanPrice;
        break;
      }

      // optional int64 BuyCleanPrice = 23;
      case 23: {
        if (tag == 184) {
         parse_BuyCleanPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buycleanprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_BuyVolume;
        break;
      }

      // optional int64 BuyVolume = 24;
      case 24: {
        if (tag == 192) {
         parse_BuyVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_BuyDirtyPrice;
        break;
      }

      // optional int64 BuyDirtyPrice = 25;
      case 25: {
        if (tag == 200) {
         parse_BuyDirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buydirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_BuyMaturityYield;
        break;
      }

      // optional int64 BuyMaturityYield = 26;
      case 26: {
        if (tag == 208) {
         parse_BuyMaturityYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buymaturityyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_BuyQuoteComment;
        break;
      }

      // optional string BuyQuoteComment = 27;
      case 27: {
        if (tag == 218) {
         parse_BuyQuoteComment:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buyquotecomment()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buyquotecomment().data(), this->buyquotecomment().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_BuyQuoteType;
        break;
      }

      // optional int32 BuyQuoteType = 28;
      case 28: {
        if (tag == 224) {
         parse_BuyQuoteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buyquotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_BuyBargainType;
        break;
      }

      // optional int32 BuyBargainType = 29;
      case 29: {
        if (tag == 232) {
         parse_BuyBargainType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buybargaintype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_BuyRelationType;
        break;
      }

      // optional int32 BuyRelationType = 30;
      case 30: {
        if (tag == 240) {
         parse_BuyRelationType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buyrelationtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_BuyExerciseType;
        break;
      }

      // optional int32 BuyExerciseType = 31;
      case 31: {
        if (tag == 248) {
         parse_BuyExerciseType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buyexercisetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_BuyYieldType;
        break;
      }

      // optional int32 BuyYieldType = 32;
      case 32: {
        if (tag == 256) {
         parse_BuyYieldType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &buyyieldtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_SellQuoteID;
        break;
      }

      // optional string SellQuoteID = 33;
      case 33: {
        if (tag == 266) {
         parse_SellQuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sellquoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->sellquoteid().data(), this->sellquoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_SellQuoteTime;
        break;
      }

      // optional int32 SellQuoteTime = 34;
      case 34: {
        if (tag == 272) {
         parse_SellQuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellquotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_SellQuoter;
        break;
      }

      // optional string SellQuoter = 35;
      case 35: {
        if (tag == 282) {
         parse_SellQuoter:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sellquoter()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->sellquoter().data(), this->sellquoter().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.SellQuoter"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_SellCleanPrice;
        break;
      }

      // optional int64 SellCleanPrice = 36;
      case 36: {
        if (tag == 288) {
         parse_SellCleanPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellcleanprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_SellVolume;
        break;
      }

      // optional int64 SellVolume = 37;
      case 37: {
        if (tag == 296) {
         parse_SellVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_SellDirtyPrice;
        break;
      }

      // optional int64 SellDirtyPrice = 38;
      case 38: {
        if (tag == 304) {
         parse_SellDirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &selldirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_SellMaturityYield;
        break;
      }

      // optional int64 SellMaturityYield = 39;
      case 39: {
        if (tag == 312) {
         parse_SellMaturityYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellmaturityyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_SellQuoteComment;
        break;
      }

      // optional string SellQuoteComment = 40;
      case 40: {
        if (tag == 322) {
         parse_SellQuoteComment:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sellquotecomment()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->sellquotecomment().data(), this->sellquotecomment().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_SellQuoteType;
        break;
      }

      // optional int32 SellQuoteType = 41;
      case 41: {
        if (tag == 328) {
         parse_SellQuoteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellquotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(336)) goto parse_SellBargainType;
        break;
      }

      // optional int32 SellBargainType = 42;
      case 42: {
        if (tag == 336) {
         parse_SellBargainType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellbargaintype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_SellRelationType;
        break;
      }

      // optional int32 SellRelationType = 43;
      case 43: {
        if (tag == 344) {
         parse_SellRelationType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellrelationtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_SellExerciseType;
        break;
      }

      // optional int32 SellExerciseType = 44;
      case 44: {
        if (tag == 352) {
         parse_SellExerciseType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellexercisetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_SellYieldType;
        break;
      }

      // optional int32 SellYieldType = 45;
      case 45: {
        if (tag == 360) {
         parse_SellYieldType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sellyieldtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 46;
      case 46: {
        if (tag == 368) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(378)) goto parse_Quotes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
      case 47: {
        if (tag == 378) {
         parse_Quotes:
          DO_(input->IncrementRecursionDepth());
         parse_loop_Quotes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_quotes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(378)) goto parse_loop_Quotes;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDFIQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDFIQuote)
  return false;
#undef DO_
}

void MDFIQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDFIQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int64 MessageNumber = 9;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->messagenumber(), output);
  }

  // optional int32 BrokerDataType = 10;
  if (this->brokerdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->brokerdatatype(), output);
  }

  // optional int64 AccruedInterest = 11;
  if (this->accruedinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->accruedinterest(), output);
  }

  // optional string BuyQuoteID = 20;
  if (this->buyquoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquoteid().data(), this->buyquoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->buyquoteid(), output);
  }

  // optional int32 BuyQuoteTime = 21;
  if (this->buyquotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->buyquotetime(), output);
  }

  // optional string BuyQuoter = 22;
  if (this->buyquoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquoter().data(), this->buyquoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->buyquoter(), output);
  }

  // optional int64 BuyCleanPrice = 23;
  if (this->buycleanprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->buycleanprice(), output);
  }

  // optional int64 BuyVolume = 24;
  if (this->buyvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->buyvolume(), output);
  }

  // optional int64 BuyDirtyPrice = 25;
  if (this->buydirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->buydirtyprice(), output);
  }

  // optional int64 BuyMaturityYield = 26;
  if (this->buymaturityyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->buymaturityyield(), output);
  }

  // optional string BuyQuoteComment = 27;
  if (this->buyquotecomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquotecomment().data(), this->buyquotecomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      27, this->buyquotecomment(), output);
  }

  // optional int32 BuyQuoteType = 28;
  if (this->buyquotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(28, this->buyquotetype(), output);
  }

  // optional int32 BuyBargainType = 29;
  if (this->buybargaintype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(29, this->buybargaintype(), output);
  }

  // optional int32 BuyRelationType = 30;
  if (this->buyrelationtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->buyrelationtype(), output);
  }

  // optional int32 BuyExerciseType = 31;
  if (this->buyexercisetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(31, this->buyexercisetype(), output);
  }

  // optional int32 BuyYieldType = 32;
  if (this->buyyieldtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(32, this->buyyieldtype(), output);
  }

  // optional string SellQuoteID = 33;
  if (this->sellquoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquoteid().data(), this->sellquoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      33, this->sellquoteid(), output);
  }

  // optional int32 SellQuoteTime = 34;
  if (this->sellquotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(34, this->sellquotetime(), output);
  }

  // optional string SellQuoter = 35;
  if (this->sellquoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquoter().data(), this->sellquoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoter");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      35, this->sellquoter(), output);
  }

  // optional int64 SellCleanPrice = 36;
  if (this->sellcleanprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(36, this->sellcleanprice(), output);
  }

  // optional int64 SellVolume = 37;
  if (this->sellvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(37, this->sellvolume(), output);
  }

  // optional int64 SellDirtyPrice = 38;
  if (this->selldirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(38, this->selldirtyprice(), output);
  }

  // optional int64 SellMaturityYield = 39;
  if (this->sellmaturityyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(39, this->sellmaturityyield(), output);
  }

  // optional string SellQuoteComment = 40;
  if (this->sellquotecomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquotecomment().data(), this->sellquotecomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      40, this->sellquotecomment(), output);
  }

  // optional int32 SellQuoteType = 41;
  if (this->sellquotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(41, this->sellquotetype(), output);
  }

  // optional int32 SellBargainType = 42;
  if (this->sellbargaintype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(42, this->sellbargaintype(), output);
  }

  // optional int32 SellRelationType = 43;
  if (this->sellrelationtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(43, this->sellrelationtype(), output);
  }

  // optional int32 SellExerciseType = 44;
  if (this->sellexercisetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(44, this->sellexercisetype(), output);
  }

  // optional int32 SellYieldType = 45;
  if (this->sellyieldtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(45, this->sellyieldtype(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(46, this->datamultiplepowerof10(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
  for (unsigned int i = 0, n = this->quotes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      47, this->quotes(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDFIQuote)
}

::google::protobuf::uint8* MDFIQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDFIQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int64 MessageNumber = 9;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->messagenumber(), target);
  }

  // optional int32 BrokerDataType = 10;
  if (this->brokerdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->brokerdatatype(), target);
  }

  // optional int64 AccruedInterest = 11;
  if (this->accruedinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->accruedinterest(), target);
  }

  // optional string BuyQuoteID = 20;
  if (this->buyquoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquoteid().data(), this->buyquoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->buyquoteid(), target);
  }

  // optional int32 BuyQuoteTime = 21;
  if (this->buyquotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->buyquotetime(), target);
  }

  // optional string BuyQuoter = 22;
  if (this->buyquoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquoter().data(), this->buyquoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->buyquoter(), target);
  }

  // optional int64 BuyCleanPrice = 23;
  if (this->buycleanprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->buycleanprice(), target);
  }

  // optional int64 BuyVolume = 24;
  if (this->buyvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->buyvolume(), target);
  }

  // optional int64 BuyDirtyPrice = 25;
  if (this->buydirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->buydirtyprice(), target);
  }

  // optional int64 BuyMaturityYield = 26;
  if (this->buymaturityyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->buymaturityyield(), target);
  }

  // optional string BuyQuoteComment = 27;
  if (this->buyquotecomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buyquotecomment().data(), this->buyquotecomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        27, this->buyquotecomment(), target);
  }

  // optional int32 BuyQuoteType = 28;
  if (this->buyquotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(28, this->buyquotetype(), target);
  }

  // optional int32 BuyBargainType = 29;
  if (this->buybargaintype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(29, this->buybargaintype(), target);
  }

  // optional int32 BuyRelationType = 30;
  if (this->buyrelationtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->buyrelationtype(), target);
  }

  // optional int32 BuyExerciseType = 31;
  if (this->buyexercisetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(31, this->buyexercisetype(), target);
  }

  // optional int32 BuyYieldType = 32;
  if (this->buyyieldtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(32, this->buyyieldtype(), target);
  }

  // optional string SellQuoteID = 33;
  if (this->sellquoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquoteid().data(), this->sellquoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        33, this->sellquoteid(), target);
  }

  // optional int32 SellQuoteTime = 34;
  if (this->sellquotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(34, this->sellquotetime(), target);
  }

  // optional string SellQuoter = 35;
  if (this->sellquoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquoter().data(), this->sellquoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoter");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        35, this->sellquoter(), target);
  }

  // optional int64 SellCleanPrice = 36;
  if (this->sellcleanprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(36, this->sellcleanprice(), target);
  }

  // optional int64 SellVolume = 37;
  if (this->sellvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(37, this->sellvolume(), target);
  }

  // optional int64 SellDirtyPrice = 38;
  if (this->selldirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(38, this->selldirtyprice(), target);
  }

  // optional int64 SellMaturityYield = 39;
  if (this->sellmaturityyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(39, this->sellmaturityyield(), target);
  }

  // optional string SellQuoteComment = 40;
  if (this->sellquotecomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellquotecomment().data(), this->sellquotecomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        40, this->sellquotecomment(), target);
  }

  // optional int32 SellQuoteType = 41;
  if (this->sellquotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(41, this->sellquotetype(), target);
  }

  // optional int32 SellBargainType = 42;
  if (this->sellbargaintype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(42, this->sellbargaintype(), target);
  }

  // optional int32 SellRelationType = 43;
  if (this->sellrelationtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(43, this->sellrelationtype(), target);
  }

  // optional int32 SellExerciseType = 44;
  if (this->sellexercisetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(44, this->sellexercisetype(), target);
  }

  // optional int32 SellYieldType = 45;
  if (this->sellyieldtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(45, this->sellyieldtype(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(46, this->datamultiplepowerof10(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
  for (unsigned int i = 0, n = this->quotes_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        47, this->quotes(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDFIQuote)
  return target;
}

size_t MDFIQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDFIQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 MessageNumber = 9;
  if (this->messagenumber() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  // optional int32 BrokerDataType = 10;
  if (this->brokerdatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->brokerdatatype());
  }

  // optional int64 AccruedInterest = 11;
  if (this->accruedinterest() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accruedinterest());
  }

  // optional string BuyQuoteID = 20;
  if (this->buyquoteid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buyquoteid());
  }

  // optional int32 BuyQuoteTime = 21;
  if (this->buyquotetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buyquotetime());
  }

  // optional string BuyQuoter = 22;
  if (this->buyquoter().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buyquoter());
  }

  // optional int64 BuyCleanPrice = 23;
  if (this->buycleanprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buycleanprice());
  }

  // optional int64 BuyVolume = 24;
  if (this->buyvolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyvolume());
  }

  // optional int64 BuyDirtyPrice = 25;
  if (this->buydirtyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buydirtyprice());
  }

  // optional int64 BuyMaturityYield = 26;
  if (this->buymaturityyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buymaturityyield());
  }

  // optional string BuyQuoteComment = 27;
  if (this->buyquotecomment().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buyquotecomment());
  }

  // optional int32 BuyQuoteType = 28;
  if (this->buyquotetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buyquotetype());
  }

  // optional int32 BuyBargainType = 29;
  if (this->buybargaintype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buybargaintype());
  }

  // optional int32 BuyRelationType = 30;
  if (this->buyrelationtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buyrelationtype());
  }

  // optional int32 BuyExerciseType = 31;
  if (this->buyexercisetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buyexercisetype());
  }

  // optional int32 BuyYieldType = 32;
  if (this->buyyieldtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->buyyieldtype());
  }

  // optional string SellQuoteID = 33;
  if (this->sellquoteid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->sellquoteid());
  }

  // optional int32 SellQuoteTime = 34;
  if (this->sellquotetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellquotetime());
  }

  // optional string SellQuoter = 35;
  if (this->sellquoter().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->sellquoter());
  }

  // optional int64 SellCleanPrice = 36;
  if (this->sellcleanprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellcleanprice());
  }

  // optional int64 SellVolume = 37;
  if (this->sellvolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellvolume());
  }

  // optional int64 SellDirtyPrice = 38;
  if (this->selldirtyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->selldirtyprice());
  }

  // optional int64 SellMaturityYield = 39;
  if (this->sellmaturityyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellmaturityyield());
  }

  // optional string SellQuoteComment = 40;
  if (this->sellquotecomment().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->sellquotecomment());
  }

  // optional int32 SellQuoteType = 41;
  if (this->sellquotetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellquotetype());
  }

  // optional int32 SellBargainType = 42;
  if (this->sellbargaintype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellbargaintype());
  }

  // optional int32 SellRelationType = 43;
  if (this->sellrelationtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellrelationtype());
  }

  // optional int32 SellExerciseType = 44;
  if (this->sellexercisetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellexercisetype());
  }

  // optional int32 SellYieldType = 45;
  if (this->sellyieldtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sellyieldtype());
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
  {
    unsigned int count = this->quotes_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->quotes(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDFIQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDFIQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDFIQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDFIQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDFIQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDFIQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDFIQuote::MergeFrom(const MDFIQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDFIQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDFIQuote::UnsafeMergeFrom(const MDFIQuote& from) {
  GOOGLE_DCHECK(&from != this);
  quotes_.MergeFrom(from.quotes_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
  if (from.brokerdatatype() != 0) {
    set_brokerdatatype(from.brokerdatatype());
  }
  if (from.accruedinterest() != 0) {
    set_accruedinterest(from.accruedinterest());
  }
  if (from.buyquoteid().size() > 0) {

    buyquoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buyquoteid_);
  }
  if (from.buyquotetime() != 0) {
    set_buyquotetime(from.buyquotetime());
  }
  if (from.buyquoter().size() > 0) {

    buyquoter_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buyquoter_);
  }
  if (from.buycleanprice() != 0) {
    set_buycleanprice(from.buycleanprice());
  }
  if (from.buyvolume() != 0) {
    set_buyvolume(from.buyvolume());
  }
  if (from.buydirtyprice() != 0) {
    set_buydirtyprice(from.buydirtyprice());
  }
  if (from.buymaturityyield() != 0) {
    set_buymaturityyield(from.buymaturityyield());
  }
  if (from.buyquotecomment().size() > 0) {

    buyquotecomment_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buyquotecomment_);
  }
  if (from.buyquotetype() != 0) {
    set_buyquotetype(from.buyquotetype());
  }
  if (from.buybargaintype() != 0) {
    set_buybargaintype(from.buybargaintype());
  }
  if (from.buyrelationtype() != 0) {
    set_buyrelationtype(from.buyrelationtype());
  }
  if (from.buyexercisetype() != 0) {
    set_buyexercisetype(from.buyexercisetype());
  }
  if (from.buyyieldtype() != 0) {
    set_buyyieldtype(from.buyyieldtype());
  }
  if (from.sellquoteid().size() > 0) {

    sellquoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sellquoteid_);
  }
  if (from.sellquotetime() != 0) {
    set_sellquotetime(from.sellquotetime());
  }
  if (from.sellquoter().size() > 0) {

    sellquoter_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sellquoter_);
  }
  if (from.sellcleanprice() != 0) {
    set_sellcleanprice(from.sellcleanprice());
  }
  if (from.sellvolume() != 0) {
    set_sellvolume(from.sellvolume());
  }
  if (from.selldirtyprice() != 0) {
    set_selldirtyprice(from.selldirtyprice());
  }
  if (from.sellmaturityyield() != 0) {
    set_sellmaturityyield(from.sellmaturityyield());
  }
  if (from.sellquotecomment().size() > 0) {

    sellquotecomment_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sellquotecomment_);
  }
  if (from.sellquotetype() != 0) {
    set_sellquotetype(from.sellquotetype());
  }
  if (from.sellbargaintype() != 0) {
    set_sellbargaintype(from.sellbargaintype());
  }
  if (from.sellrelationtype() != 0) {
    set_sellrelationtype(from.sellrelationtype());
  }
  if (from.sellexercisetype() != 0) {
    set_sellexercisetype(from.sellexercisetype());
  }
  if (from.sellyieldtype() != 0) {
    set_sellyieldtype(from.sellyieldtype());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDFIQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDFIQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDFIQuote::CopyFrom(const MDFIQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDFIQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDFIQuote::IsInitialized() const {

  return true;
}

void MDFIQuote::Swap(MDFIQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDFIQuote::InternalSwap(MDFIQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(messagenumber_, other->messagenumber_);
  std::swap(brokerdatatype_, other->brokerdatatype_);
  std::swap(accruedinterest_, other->accruedinterest_);
  buyquoteid_.Swap(&other->buyquoteid_);
  std::swap(buyquotetime_, other->buyquotetime_);
  buyquoter_.Swap(&other->buyquoter_);
  std::swap(buycleanprice_, other->buycleanprice_);
  std::swap(buyvolume_, other->buyvolume_);
  std::swap(buydirtyprice_, other->buydirtyprice_);
  std::swap(buymaturityyield_, other->buymaturityyield_);
  buyquotecomment_.Swap(&other->buyquotecomment_);
  std::swap(buyquotetype_, other->buyquotetype_);
  std::swap(buybargaintype_, other->buybargaintype_);
  std::swap(buyrelationtype_, other->buyrelationtype_);
  std::swap(buyexercisetype_, other->buyexercisetype_);
  std::swap(buyyieldtype_, other->buyyieldtype_);
  sellquoteid_.Swap(&other->sellquoteid_);
  std::swap(sellquotetime_, other->sellquotetime_);
  sellquoter_.Swap(&other->sellquoter_);
  std::swap(sellcleanprice_, other->sellcleanprice_);
  std::swap(sellvolume_, other->sellvolume_);
  std::swap(selldirtyprice_, other->selldirtyprice_);
  std::swap(sellmaturityyield_, other->sellmaturityyield_);
  sellquotecomment_.Swap(&other->sellquotecomment_);
  std::swap(sellquotetype_, other->sellquotetype_);
  std::swap(sellbargaintype_, other->sellbargaintype_);
  std::swap(sellrelationtype_, other->sellrelationtype_);
  std::swap(sellexercisetype_, other->sellexercisetype_);
  std::swap(sellyieldtype_, other->sellyieldtype_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  quotes_.UnsafeArenaSwap(&other->quotes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDFIQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDFIQuote_descriptor_;
  metadata.reflection = MDFIQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDFIQuote

// optional string HTSCSecurityID = 1;
void MDFIQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
void MDFIQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
void MDFIQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
::std::string* MDFIQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDFIQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDFIQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MDDate)
  return mddate_;
}
void MDFIQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDFIQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDFIQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MDTime)
  return mdtime_;
}
void MDFIQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDFIQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.DataTimestamp)
  return datatimestamp_;
}
void MDFIQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDFIQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDFIQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDFIQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDFIQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDFIQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDFIQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.securityType)
}

// optional int32 ExchangeDate = 7;
void MDFIQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDFIQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.ExchangeDate)
  return exchangedate_;
}
void MDFIQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDFIQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDFIQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.ExchangeTime)
  return exchangetime_;
}
void MDFIQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.ExchangeTime)
}

// optional int64 MessageNumber = 9;
void MDFIQuote::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MessageNumber)
  return messagenumber_;
}
void MDFIQuote::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MessageNumber)
}

// optional int32 BrokerDataType = 10;
void MDFIQuote::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
::google::protobuf::int32 MDFIQuote::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BrokerDataType)
  return brokerdatatype_;
}
void MDFIQuote::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BrokerDataType)
}

// optional int64 AccruedInterest = 11;
void MDFIQuote::clear_accruedinterest() {
  accruedinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::accruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.AccruedInterest)
  return accruedinterest_;
}
void MDFIQuote::set_accruedinterest(::google::protobuf::int64 value) {
  
  accruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.AccruedInterest)
}

// optional string BuyQuoteID = 20;
void MDFIQuote::clear_buyquoteid() {
  buyquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::buyquoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  return buyquoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_buyquoteid(const ::std::string& value) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
void MDFIQuote::set_buyquoteid(const char* value) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
void MDFIQuote::set_buyquoteid(const char* value, size_t size) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
::std::string* MDFIQuote::mutable_buyquoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  return buyquoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_buyquoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  
  return buyquoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_buyquoteid(::std::string* buyquoteid) {
  if (buyquoteid != NULL) {
    
  } else {
    
  }
  buyquoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}

// optional int32 BuyQuoteTime = 21;
void MDFIQuote::clear_buyquotetime() {
  buyquotetime_ = 0;
}
::google::protobuf::int32 MDFIQuote::buyquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteTime)
  return buyquotetime_;
}
void MDFIQuote::set_buyquotetime(::google::protobuf::int32 value) {
  
  buyquotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteTime)
}

// optional string BuyQuoter = 22;
void MDFIQuote::clear_buyquoter() {
  buyquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::buyquoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  return buyquoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_buyquoter(const ::std::string& value) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
void MDFIQuote::set_buyquoter(const char* value) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
void MDFIQuote::set_buyquoter(const char* value, size_t size) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
::std::string* MDFIQuote::mutable_buyquoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  return buyquoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_buyquoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  
  return buyquoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_buyquoter(::std::string* buyquoter) {
  if (buyquoter != NULL) {
    
  } else {
    
  }
  buyquoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}

// optional int64 BuyCleanPrice = 23;
void MDFIQuote::clear_buycleanprice() {
  buycleanprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::buycleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyCleanPrice)
  return buycleanprice_;
}
void MDFIQuote::set_buycleanprice(::google::protobuf::int64 value) {
  
  buycleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyCleanPrice)
}

// optional int64 BuyVolume = 24;
void MDFIQuote::clear_buyvolume() {
  buyvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::buyvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyVolume)
  return buyvolume_;
}
void MDFIQuote::set_buyvolume(::google::protobuf::int64 value) {
  
  buyvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyVolume)
}

// optional int64 BuyDirtyPrice = 25;
void MDFIQuote::clear_buydirtyprice() {
  buydirtyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::buydirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyDirtyPrice)
  return buydirtyprice_;
}
void MDFIQuote::set_buydirtyprice(::google::protobuf::int64 value) {
  
  buydirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyDirtyPrice)
}

// optional int64 BuyMaturityYield = 26;
void MDFIQuote::clear_buymaturityyield() {
  buymaturityyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::buymaturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyMaturityYield)
  return buymaturityyield_;
}
void MDFIQuote::set_buymaturityyield(::google::protobuf::int64 value) {
  
  buymaturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyMaturityYield)
}

// optional string BuyQuoteComment = 27;
void MDFIQuote::clear_buyquotecomment() {
  buyquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::buyquotecomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  return buyquotecomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_buyquotecomment(const ::std::string& value) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
void MDFIQuote::set_buyquotecomment(const char* value) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
void MDFIQuote::set_buyquotecomment(const char* value, size_t size) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
::std::string* MDFIQuote::mutable_buyquotecomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  return buyquotecomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_buyquotecomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  
  return buyquotecomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_buyquotecomment(::std::string* buyquotecomment) {
  if (buyquotecomment != NULL) {
    
  } else {
    
  }
  buyquotecomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquotecomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}

// optional int32 BuyQuoteType = 28;
void MDFIQuote::clear_buyquotetype() {
  buyquotetype_ = 0;
}
::google::protobuf::int32 MDFIQuote::buyquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteType)
  return buyquotetype_;
}
void MDFIQuote::set_buyquotetype(::google::protobuf::int32 value) {
  
  buyquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteType)
}

// optional int32 BuyBargainType = 29;
void MDFIQuote::clear_buybargaintype() {
  buybargaintype_ = 0;
}
::google::protobuf::int32 MDFIQuote::buybargaintype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyBargainType)
  return buybargaintype_;
}
void MDFIQuote::set_buybargaintype(::google::protobuf::int32 value) {
  
  buybargaintype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyBargainType)
}

// optional int32 BuyRelationType = 30;
void MDFIQuote::clear_buyrelationtype() {
  buyrelationtype_ = 0;
}
::google::protobuf::int32 MDFIQuote::buyrelationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyRelationType)
  return buyrelationtype_;
}
void MDFIQuote::set_buyrelationtype(::google::protobuf::int32 value) {
  
  buyrelationtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyRelationType)
}

// optional int32 BuyExerciseType = 31;
void MDFIQuote::clear_buyexercisetype() {
  buyexercisetype_ = 0;
}
::google::protobuf::int32 MDFIQuote::buyexercisetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyExerciseType)
  return buyexercisetype_;
}
void MDFIQuote::set_buyexercisetype(::google::protobuf::int32 value) {
  
  buyexercisetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyExerciseType)
}

// optional int32 BuyYieldType = 32;
void MDFIQuote::clear_buyyieldtype() {
  buyyieldtype_ = 0;
}
::google::protobuf::int32 MDFIQuote::buyyieldtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyYieldType)
  return buyyieldtype_;
}
void MDFIQuote::set_buyyieldtype(::google::protobuf::int32 value) {
  
  buyyieldtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyYieldType)
}

// optional string SellQuoteID = 33;
void MDFIQuote::clear_sellquoteid() {
  sellquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::sellquoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  return sellquoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_sellquoteid(const ::std::string& value) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
void MDFIQuote::set_sellquoteid(const char* value) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
void MDFIQuote::set_sellquoteid(const char* value, size_t size) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
::std::string* MDFIQuote::mutable_sellquoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  return sellquoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_sellquoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  
  return sellquoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_sellquoteid(::std::string* sellquoteid) {
  if (sellquoteid != NULL) {
    
  } else {
    
  }
  sellquoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}

// optional int32 SellQuoteTime = 34;
void MDFIQuote::clear_sellquotetime() {
  sellquotetime_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteTime)
  return sellquotetime_;
}
void MDFIQuote::set_sellquotetime(::google::protobuf::int32 value) {
  
  sellquotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteTime)
}

// optional string SellQuoter = 35;
void MDFIQuote::clear_sellquoter() {
  sellquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::sellquoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  return sellquoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_sellquoter(const ::std::string& value) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
void MDFIQuote::set_sellquoter(const char* value) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
void MDFIQuote::set_sellquoter(const char* value, size_t size) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
::std::string* MDFIQuote::mutable_sellquoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  return sellquoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_sellquoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  
  return sellquoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_sellquoter(::std::string* sellquoter) {
  if (sellquoter != NULL) {
    
  } else {
    
  }
  sellquoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}

// optional int64 SellCleanPrice = 36;
void MDFIQuote::clear_sellcleanprice() {
  sellcleanprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::sellcleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellCleanPrice)
  return sellcleanprice_;
}
void MDFIQuote::set_sellcleanprice(::google::protobuf::int64 value) {
  
  sellcleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellCleanPrice)
}

// optional int64 SellVolume = 37;
void MDFIQuote::clear_sellvolume() {
  sellvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::sellvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellVolume)
  return sellvolume_;
}
void MDFIQuote::set_sellvolume(::google::protobuf::int64 value) {
  
  sellvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellVolume)
}

// optional int64 SellDirtyPrice = 38;
void MDFIQuote::clear_selldirtyprice() {
  selldirtyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::selldirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellDirtyPrice)
  return selldirtyprice_;
}
void MDFIQuote::set_selldirtyprice(::google::protobuf::int64 value) {
  
  selldirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellDirtyPrice)
}

// optional int64 SellMaturityYield = 39;
void MDFIQuote::clear_sellmaturityyield() {
  sellmaturityyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFIQuote::sellmaturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellMaturityYield)
  return sellmaturityyield_;
}
void MDFIQuote::set_sellmaturityyield(::google::protobuf::int64 value) {
  
  sellmaturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellMaturityYield)
}

// optional string SellQuoteComment = 40;
void MDFIQuote::clear_sellquotecomment() {
  sellquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFIQuote::sellquotecomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  return sellquotecomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_sellquotecomment(const ::std::string& value) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
void MDFIQuote::set_sellquotecomment(const char* value) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
void MDFIQuote::set_sellquotecomment(const char* value, size_t size) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
::std::string* MDFIQuote::mutable_sellquotecomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  return sellquotecomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFIQuote::release_sellquotecomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  
  return sellquotecomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFIQuote::set_allocated_sellquotecomment(::std::string* sellquotecomment) {
  if (sellquotecomment != NULL) {
    
  } else {
    
  }
  sellquotecomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquotecomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}

// optional int32 SellQuoteType = 41;
void MDFIQuote::clear_sellquotetype() {
  sellquotetype_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteType)
  return sellquotetype_;
}
void MDFIQuote::set_sellquotetype(::google::protobuf::int32 value) {
  
  sellquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteType)
}

// optional int32 SellBargainType = 42;
void MDFIQuote::clear_sellbargaintype() {
  sellbargaintype_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellbargaintype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellBargainType)
  return sellbargaintype_;
}
void MDFIQuote::set_sellbargaintype(::google::protobuf::int32 value) {
  
  sellbargaintype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellBargainType)
}

// optional int32 SellRelationType = 43;
void MDFIQuote::clear_sellrelationtype() {
  sellrelationtype_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellrelationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellRelationType)
  return sellrelationtype_;
}
void MDFIQuote::set_sellrelationtype(::google::protobuf::int32 value) {
  
  sellrelationtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellRelationType)
}

// optional int32 SellExerciseType = 44;
void MDFIQuote::clear_sellexercisetype() {
  sellexercisetype_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellexercisetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellExerciseType)
  return sellexercisetype_;
}
void MDFIQuote::set_sellexercisetype(::google::protobuf::int32 value) {
  
  sellexercisetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellExerciseType)
}

// optional int32 SellYieldType = 45;
void MDFIQuote::clear_sellyieldtype() {
  sellyieldtype_ = 0;
}
::google::protobuf::int32 MDFIQuote::sellyieldtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellYieldType)
  return sellyieldtype_;
}
void MDFIQuote::set_sellyieldtype(::google::protobuf::int32 value) {
  
  sellyieldtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellYieldType)
}

// optional int32 DataMultiplePowerOf10 = 46;
void MDFIQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDFIQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDFIQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
int MDFIQuote::quotes_size() const {
  return quotes_.size();
}
void MDFIQuote::clear_quotes() {
  quotes_.Clear();
}
const ::com::htsc::mdc::insight::model::ADFIQuote& MDFIQuote::quotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Get(index);
}
::com::htsc::mdc::insight::model::ADFIQuote* MDFIQuote::mutable_quotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADFIQuote* MDFIQuote::add_quotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >*
MDFIQuote::mutable_quotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return &quotes_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >&
MDFIQuote::quotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_;
}

inline const MDFIQuote* MDFIQuote::internal_default_instance() {
  return &MDFIQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADFIQuote::kQuoteBSFlagFieldNumber;
const int ADFIQuote::kQuoteLevelFieldNumber;
const int ADFIQuote::kQuoteIDFieldNumber;
const int ADFIQuote::kQuoteTimeFieldNumber;
const int ADFIQuote::kQuoterFieldNumber;
const int ADFIQuote::kCleanPriceFieldNumber;
const int ADFIQuote::kVolumeFieldNumber;
const int ADFIQuote::kDirtyPriceFieldNumber;
const int ADFIQuote::kMaturityYieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADFIQuote::ADFIQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDFIQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADFIQuote)
}

void ADFIQuote::InitAsDefaultInstance() {
}

ADFIQuote::ADFIQuote(const ADFIQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADFIQuote)
}

void ADFIQuote::SharedCtor() {
  quoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quoter_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&quotebsflag_, 0, reinterpret_cast<char*>(&quotetime_) -
    reinterpret_cast<char*>(&quotebsflag_) + sizeof(quotetime_));
  _cached_size_ = 0;
}

ADFIQuote::~ADFIQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADFIQuote)
  SharedDtor();
}

void ADFIQuote::SharedDtor() {
  quoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quoter_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADFIQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADFIQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADFIQuote_descriptor_;
}

const ADFIQuote& ADFIQuote::default_instance() {
  protobuf_InitDefaults_MDFIQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADFIQuote> ADFIQuote_default_instance_;

ADFIQuote* ADFIQuote::New(::google::protobuf::Arena* arena) const {
  ADFIQuote* n = new ADFIQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADFIQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADFIQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADFIQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADFIQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(quotebsflag_, dirtyprice_);
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotetime_ = 0;
  quoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maturityyield_ = GOOGLE_LONGLONG(0);

#undef ZR_HELPER_
#undef ZR_

}

bool ADFIQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADFIQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 QuoteBSFlag = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotebsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_QuoteLevel;
        break;
      }

      // optional int32 QuoteLevel = 2;
      case 2: {
        if (tag == 16) {
         parse_QuoteLevel:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotelevel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_QuoteID;
        break;
      }

      // optional string QuoteID = 3;
      case 3: {
        if (tag == 26) {
         parse_QuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoteid().data(), this->quoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADFIQuote.QuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_QuoteTime;
        break;
      }

      // optional int32 QuoteTime = 4;
      case 4: {
        if (tag == 32) {
         parse_QuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_Quoter;
        break;
      }

      // optional string Quoter = 5;
      case 5: {
        if (tag == 42) {
         parse_Quoter:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoter()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoter().data(), this->quoter().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADFIQuote.Quoter"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_CleanPrice;
        break;
      }

      // optional int64 CleanPrice = 6;
      case 6: {
        if (tag == 48) {
         parse_CleanPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &cleanprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_Volume;
        break;
      }

      // optional int64 Volume = 7;
      case 7: {
        if (tag == 56) {
         parse_Volume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_DirtyPrice;
        break;
      }

      // optional int64 DirtyPrice = 8;
      case 8: {
        if (tag == 64) {
         parse_DirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MaturityYield;
        break;
      }

      // optional int64 MaturityYield = 9;
      case 9: {
        if (tag == 72) {
         parse_MaturityYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maturityyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADFIQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADFIQuote)
  return false;
#undef DO_
}

void ADFIQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADFIQuote)
  // optional int32 QuoteBSFlag = 1;
  if (this->quotebsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->quotebsflag(), output);
  }

  // optional int32 QuoteLevel = 2;
  if (this->quotelevel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->quotelevel(), output);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADFIQuote.QuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->quoteid(), output);
  }

  // optional int32 QuoteTime = 4;
  if (this->quotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->quotetime(), output);
  }

  // optional string Quoter = 5;
  if (this->quoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoter().data(), this->quoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADFIQuote.Quoter");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->quoter(), output);
  }

  // optional int64 CleanPrice = 6;
  if (this->cleanprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->cleanprice(), output);
  }

  // optional int64 Volume = 7;
  if (this->volume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->volume(), output);
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->dirtyprice(), output);
  }

  // optional int64 MaturityYield = 9;
  if (this->maturityyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->maturityyield(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADFIQuote)
}

::google::protobuf::uint8* ADFIQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADFIQuote)
  // optional int32 QuoteBSFlag = 1;
  if (this->quotebsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->quotebsflag(), target);
  }

  // optional int32 QuoteLevel = 2;
  if (this->quotelevel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->quotelevel(), target);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADFIQuote.QuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->quoteid(), target);
  }

  // optional int32 QuoteTime = 4;
  if (this->quotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->quotetime(), target);
  }

  // optional string Quoter = 5;
  if (this->quoter().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoter().data(), this->quoter().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADFIQuote.Quoter");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->quoter(), target);
  }

  // optional int64 CleanPrice = 6;
  if (this->cleanprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->cleanprice(), target);
  }

  // optional int64 Volume = 7;
  if (this->volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->volume(), target);
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->dirtyprice(), target);
  }

  // optional int64 MaturityYield = 9;
  if (this->maturityyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->maturityyield(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADFIQuote)
  return target;
}

size_t ADFIQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADFIQuote)
  size_t total_size = 0;

  // optional int32 QuoteBSFlag = 1;
  if (this->quotebsflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotebsflag());
  }

  // optional int32 QuoteLevel = 2;
  if (this->quotelevel() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotelevel());
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoteid());
  }

  // optional int32 QuoteTime = 4;
  if (this->quotetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetime());
  }

  // optional string Quoter = 5;
  if (this->quoter().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoter());
  }

  // optional int64 CleanPrice = 6;
  if (this->cleanprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->cleanprice());
  }

  // optional int64 Volume = 7;
  if (this->volume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volume());
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dirtyprice());
  }

  // optional int64 MaturityYield = 9;
  if (this->maturityyield() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maturityyield());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADFIQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADFIQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADFIQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADFIQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADFIQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADFIQuote)
    UnsafeMergeFrom(*source);
  }
}

void ADFIQuote::MergeFrom(const ADFIQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADFIQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADFIQuote::UnsafeMergeFrom(const ADFIQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.quotebsflag() != 0) {
    set_quotebsflag(from.quotebsflag());
  }
  if (from.quotelevel() != 0) {
    set_quotelevel(from.quotelevel());
  }
  if (from.quoteid().size() > 0) {

    quoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoteid_);
  }
  if (from.quotetime() != 0) {
    set_quotetime(from.quotetime());
  }
  if (from.quoter().size() > 0) {

    quoter_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoter_);
  }
  if (from.cleanprice() != 0) {
    set_cleanprice(from.cleanprice());
  }
  if (from.volume() != 0) {
    set_volume(from.volume());
  }
  if (from.dirtyprice() != 0) {
    set_dirtyprice(from.dirtyprice());
  }
  if (from.maturityyield() != 0) {
    set_maturityyield(from.maturityyield());
  }
}

void ADFIQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADFIQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADFIQuote::CopyFrom(const ADFIQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADFIQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADFIQuote::IsInitialized() const {

  return true;
}

void ADFIQuote::Swap(ADFIQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADFIQuote::InternalSwap(ADFIQuote* other) {
  std::swap(quotebsflag_, other->quotebsflag_);
  std::swap(quotelevel_, other->quotelevel_);
  quoteid_.Swap(&other->quoteid_);
  std::swap(quotetime_, other->quotetime_);
  quoter_.Swap(&other->quoter_);
  std::swap(cleanprice_, other->cleanprice_);
  std::swap(volume_, other->volume_);
  std::swap(dirtyprice_, other->dirtyprice_);
  std::swap(maturityyield_, other->maturityyield_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADFIQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADFIQuote_descriptor_;
  metadata.reflection = ADFIQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADFIQuote

// optional int32 QuoteBSFlag = 1;
void ADFIQuote::clear_quotebsflag() {
  quotebsflag_ = 0;
}
::google::protobuf::int32 ADFIQuote::quotebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteBSFlag)
  return quotebsflag_;
}
void ADFIQuote::set_quotebsflag(::google::protobuf::int32 value) {
  
  quotebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteBSFlag)
}

// optional int32 QuoteLevel = 2;
void ADFIQuote::clear_quotelevel() {
  quotelevel_ = 0;
}
::google::protobuf::int32 ADFIQuote::quotelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteLevel)
  return quotelevel_;
}
void ADFIQuote::set_quotelevel(::google::protobuf::int32 value) {
  
  quotelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteLevel)
}

// optional string QuoteID = 3;
void ADFIQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADFIQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADFIQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
void ADFIQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
void ADFIQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
::std::string* ADFIQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADFIQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADFIQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}

// optional int32 QuoteTime = 4;
void ADFIQuote::clear_quotetime() {
  quotetime_ = 0;
}
::google::protobuf::int32 ADFIQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteTime)
  return quotetime_;
}
void ADFIQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteTime)
}

// optional string Quoter = 5;
void ADFIQuote::clear_quoter() {
  quoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADFIQuote::quoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  return quoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADFIQuote::set_quoter(const ::std::string& value) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
void ADFIQuote::set_quoter(const char* value) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
void ADFIQuote::set_quoter(const char* value, size_t size) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
::std::string* ADFIQuote::mutable_quoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  return quoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADFIQuote::release_quoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  
  return quoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADFIQuote::set_allocated_quoter(::std::string* quoter) {
  if (quoter != NULL) {
    
  } else {
    
  }
  quoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}

// optional int64 CleanPrice = 6;
void ADFIQuote::clear_cleanprice() {
  cleanprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADFIQuote::cleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.CleanPrice)
  return cleanprice_;
}
void ADFIQuote::set_cleanprice(::google::protobuf::int64 value) {
  
  cleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.CleanPrice)
}

// optional int64 Volume = 7;
void ADFIQuote::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADFIQuote::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.Volume)
  return volume_;
}
void ADFIQuote::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.Volume)
}

// optional int64 DirtyPrice = 8;
void ADFIQuote::clear_dirtyprice() {
  dirtyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADFIQuote::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.DirtyPrice)
  return dirtyprice_;
}
void ADFIQuote::set_dirtyprice(::google::protobuf::int64 value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.DirtyPrice)
}

// optional int64 MaturityYield = 9;
void ADFIQuote::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADFIQuote::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.MaturityYield)
  return maturityyield_;
}
void ADFIQuote::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.MaturityYield)
}

inline const ADFIQuote* ADFIQuote::internal_default_instance() {
  return &ADFIQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
