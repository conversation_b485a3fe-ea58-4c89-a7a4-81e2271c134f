syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDIndex {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  double LastPrice = 10;
  double OpenPrice = 11;
  double HighPrice = 12;
  double LowPrice = 13;
  double ClosePrice = 14;
  double PrevClosePrice = 15;
  double Change = 16;
  double PercentChange = 17;
  double Volume = 18;
  double Turnover = 19;
  double TotalVolume = 20;
  double TotalTurnover = 21;
  int64 MessageNumber = 100;
}
