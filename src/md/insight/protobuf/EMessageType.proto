syntax = "proto3";

package com.htsc.mdc.insight.model;

enum EMessageType {
  UNKNOWN_MESSAGE_TYPE = 0;
  HEARTBEAT_REQUEST = 1;
  HEARTBEAT_RESPONSE = 2;
  GENERAL_ERROR_MESSAGE = 10;
  LOGIN_REQUEST = 11;
  LOGIN_RESPONSE = 12;
  SERVICE_DISCOVERY_REQUEST = 13;
  SERVICE_DISCOVERY_RESPONSE = 14;
  MD_SUBSCRIBE_REQUEST = 15;
  MD_SUBSCRIBE_RESPONSE = 16;
  PUSH_MARKET_DATA = 17;
  MD_QUERY_REQUEST = 18;
  MD_QUERY_RESPONSE = 19;
  PLAYBACK_REQUEST = 20;
  PLAYBACK_RESPONSE = 21;
  PLAYBACK_CONTROL_REQUEST = 22;
  PLAYBACK_CONTROL_RESPONSE = 23;
  PLAYBACK_STATUS_REQUEST = 24;
  PLAYBACK_STATUS = 25;
  PLAYBACK_PAYLOAD = 26;
  PUSH_MARKET_DATA_STREAM = 27;
}
