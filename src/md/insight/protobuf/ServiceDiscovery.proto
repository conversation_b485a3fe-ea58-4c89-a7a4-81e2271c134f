syntax = "proto3";

package com.htsc.mdc.insight.model;

import "InsightErrorContext.proto";

message ServiceDiscoveryRequest {
  int32 appType = 1;
  string appVersion = 2;
  string userName = 3;
  string deviceId = 4;
  bool isSupportCompressed = 5;
}

message ServiceDiscoveryResponse {
  bool isSuccess = 1;
  InsightErrorContext errorContext = 2;
  repeated ServerInfo servers = 3;
}

message ServerInfo {
  string ip = 1;
  int32 port = 2;
  int32 ipType = 3;
  int32 siteType = 4;
  string siteName = 5;
  int32 ipVersion = 6;
  bool isSsl = 7;
}
