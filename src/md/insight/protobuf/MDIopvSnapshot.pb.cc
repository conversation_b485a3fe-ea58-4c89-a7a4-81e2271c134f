// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDIopvSnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDIopvSnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDIopvSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDIopvSnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDIopvSnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDIopvSnapshot_2eproto() {
  protobuf_AddDesc_MDIopvSnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDIopvSnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDIopvSnapshot_descriptor_ = file->message_type(0);
  static const int MDIopvSnapshot_offsets_[14] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, iopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, arrivaldatetime_),
  };
  MDIopvSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDIopvSnapshot_descriptor_,
      MDIopvSnapshot::internal_default_instance(),
      MDIopvSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDIopvSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIopvSnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDIopvSnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDIopvSnapshot_descriptor_, MDIopvSnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDIopvSnapshot_2eproto() {
  MDIopvSnapshot_default_instance_.Shutdown();
  delete MDIopvSnapshot_reflection_;
}

void protobuf_InitDefaults_MDIopvSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDIopvSnapshot_default_instance_.DefaultConstruct();
  MDIopvSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDIopvSnapshot_2eproto_once_);
void protobuf_InitDefaults_MDIopvSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDIopvSnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDIopvSnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDIopvSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDIopvSnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDIopvSnapshot.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\027ESecurityIDSource.proto\032\023ESe"
    "curityType.proto\"\214\003\n\016MDIopvSnapshot\022\026\n\016H"
    "TSCSecurityID\030\001 \001(\t\0227\n\014SecurityType\030\002 \001("
    "\0162!.com.htsc.mdc.model.ESecurityType\022\?\n\020"
    "SecurityIDSource\030\003 \001(\0162%.com.htsc.mdc.mo"
    "del.ESecurityIDSource\022\016\n\006MDDate\030\004 \001(\005\022\016\n"
    "\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp\030\006 \001(\003\022\030\n\020"
    "TradingPhaseCode\030\007 \001(\t\022\014\n\004IOPV\030\010 \001(\003\022\035\n\025"
    "DataMultiplePowerOf10\030\t \001(\005\022\024\n\014ExchangeD"
    "ate\030\025 \001(\t\022\024\n\014ExchangeTime\030\026 \001(\t\022\021\n\tChann"
    "elNo\030\027 \001(\005\022\022\n\nApplSeqNum\030\030 \001(\003\022\027\n\017Arriva"
    "lDateTime\030\031 \001(\003B7\n\032com.htsc.mdc.insight."
    "modelB\024MDIopvSnapshotProtosH\001\240\001\001b\006proto3", 560);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDIopvSnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDIopvSnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDIopvSnapshot_2eproto_once_);
void protobuf_AddDesc_MDIopvSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDIopvSnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDIopvSnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDIopvSnapshot_2eproto {
  StaticDescriptorInitializer_MDIopvSnapshot_2eproto() {
    protobuf_AddDesc_MDIopvSnapshot_2eproto();
  }
} static_descriptor_initializer_MDIopvSnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDIopvSnapshot::kHTSCSecurityIDFieldNumber;
const int MDIopvSnapshot::kSecurityTypeFieldNumber;
const int MDIopvSnapshot::kSecurityIDSourceFieldNumber;
const int MDIopvSnapshot::kMDDateFieldNumber;
const int MDIopvSnapshot::kMDTimeFieldNumber;
const int MDIopvSnapshot::kDataTimestampFieldNumber;
const int MDIopvSnapshot::kTradingPhaseCodeFieldNumber;
const int MDIopvSnapshot::kIOPVFieldNumber;
const int MDIopvSnapshot::kDataMultiplePowerOf10FieldNumber;
const int MDIopvSnapshot::kExchangeDateFieldNumber;
const int MDIopvSnapshot::kExchangeTimeFieldNumber;
const int MDIopvSnapshot::kChannelNoFieldNumber;
const int MDIopvSnapshot::kApplSeqNumFieldNumber;
const int MDIopvSnapshot::kArrivalDateTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDIopvSnapshot::MDIopvSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDIopvSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDIopvSnapshot)
}

void MDIopvSnapshot::InitAsDefaultInstance() {
}

MDIopvSnapshot::MDIopvSnapshot(const MDIopvSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDIopvSnapshot)
}

void MDIopvSnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&arrivaldatetime_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(arrivaldatetime_));
  _cached_size_ = 0;
}

MDIopvSnapshot::~MDIopvSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDIopvSnapshot)
  SharedDtor();
}

void MDIopvSnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDIopvSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDIopvSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDIopvSnapshot_descriptor_;
}

const MDIopvSnapshot& MDIopvSnapshot::default_instance() {
  protobuf_InitDefaults_MDIopvSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDIopvSnapshot> MDIopvSnapshot_default_instance_;

MDIopvSnapshot* MDIopvSnapshot::New(::google::protobuf::Arena* arena) const {
  MDIopvSnapshot* n = new MDIopvSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDIopvSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDIopvSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDIopvSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, iopv_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(datamultiplepowerof10_, arrivaldatetime_);
  exchangedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDIopvSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 7;
      case 7: {
        if (tag == 58) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_IOPV;
        break;
      }

      // optional int64 IOPV = 8;
      case 8: {
        if (tag == 64) {
         parse_IOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &iopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_ExchangeDate;
        break;
      }

      // optional string ExchangeDate = 21;
      case 21: {
        if (tag == 170) {
         parse_ExchangeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchangedate().data(), this->exchangedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_ExchangeTime;
        break;
      }

      // optional string ExchangeTime = 22;
      case 22: {
        if (tag == 178) {
         parse_ExchangeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchangetime().data(), this->exchangetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 23;
      case 23: {
        if (tag == 184) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 24;
      case 24: {
        if (tag == 192) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_ArrivalDateTime;
        break;
      }

      // optional int64 ArrivalDateTime = 25;
      case 25: {
        if (tag == 200) {
         parse_ArrivalDateTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &arrivaldatetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDIopvSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDIopvSnapshot)
  return false;
#undef DO_
}

void MDIopvSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 7;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->tradingphasecode(), output);
  }

  // optional int64 IOPV = 8;
  if (this->iopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->iopv(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional string ExchangeDate = 21;
  if (this->exchangedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangedate().data(), this->exchangedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->exchangedate(), output);
  }

  // optional string ExchangeTime = 22;
  if (this->exchangetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangetime().data(), this->exchangetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 23;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(23, this->channelno(), output);
  }

  // optional int64 ApplSeqNum = 24;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->applseqnum(), output);
  }

  // optional int64 ArrivalDateTime = 25;
  if (this->arrivaldatetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->arrivaldatetime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDIopvSnapshot)
}

::google::protobuf::uint8* MDIopvSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 7;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->tradingphasecode(), target);
  }

  // optional int64 IOPV = 8;
  if (this->iopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->iopv(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional string ExchangeDate = 21;
  if (this->exchangedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangedate().data(), this->exchangedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->exchangedate(), target);
  }

  // optional string ExchangeTime = 22;
  if (this->exchangetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangetime().data(), this->exchangetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 23;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(23, this->channelno(), target);
  }

  // optional int64 ApplSeqNum = 24;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->applseqnum(), target);
  }

  // optional int64 ArrivalDateTime = 25;
  if (this->arrivaldatetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->arrivaldatetime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDIopvSnapshot)
  return target;
}

size_t MDIopvSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 7;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional int64 IOPV = 8;
  if (this->iopv() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->iopv());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string ExchangeDate = 21;
  if (this->exchangedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangedate());
  }

  // optional string ExchangeTime = 22;
  if (this->exchangetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 23;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 ApplSeqNum = 24;
  if (this->applseqnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional int64 ArrivalDateTime = 25;
  if (this->arrivaldatetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->arrivaldatetime());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDIopvSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDIopvSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDIopvSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDIopvSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDIopvSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDIopvSnapshot::MergeFrom(const MDIopvSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDIopvSnapshot::UnsafeMergeFrom(const MDIopvSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.iopv() != 0) {
    set_iopv(from.iopv());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.exchangedate().size() > 0) {

    exchangedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangedate_);
  }
  if (from.exchangetime().size() > 0) {

    exchangetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangetime_);
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.arrivaldatetime() != 0) {
    set_arrivaldatetime(from.arrivaldatetime());
  }
}

void MDIopvSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDIopvSnapshot::CopyFrom(const MDIopvSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDIopvSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDIopvSnapshot::IsInitialized() const {

  return true;
}

void MDIopvSnapshot::Swap(MDIopvSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDIopvSnapshot::InternalSwap(MDIopvSnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(iopv_, other->iopv_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  exchangedate_.Swap(&other->exchangedate_);
  exchangetime_.Swap(&other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  std::swap(applseqnum_, other->applseqnum_);
  std::swap(arrivaldatetime_, other->arrivaldatetime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDIopvSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDIopvSnapshot_descriptor_;
  metadata.reflection = MDIopvSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDIopvSnapshot

// optional string HTSCSecurityID = 1;
void MDIopvSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIopvSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
}
void MDIopvSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
}
void MDIopvSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
}
::std::string* MDIopvSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIopvSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIopvSnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDIopvSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDIopvSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDIopvSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDIopvSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDIopvSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDIopvSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDIopvSnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDIopvSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.MDDate)
  return mddate_;
}
void MDIopvSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.MDDate)
}

// optional int32 MDTime = 5;
void MDIopvSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDIopvSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.MDTime)
  return mdtime_;
}
void MDIopvSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDIopvSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIopvSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDIopvSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.DataTimestamp)
}

// optional string TradingPhaseCode = 7;
void MDIopvSnapshot::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIopvSnapshot::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
}
void MDIopvSnapshot::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
}
void MDIopvSnapshot::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
}
::std::string* MDIopvSnapshot::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIopvSnapshot::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIopvSnapshot.TradingPhaseCode)
}

// optional int64 IOPV = 8;
void MDIopvSnapshot::clear_iopv() {
  iopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIopvSnapshot::iopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.IOPV)
  return iopv_;
}
void MDIopvSnapshot::set_iopv(::google::protobuf::int64 value) {
  
  iopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.IOPV)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDIopvSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDIopvSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDIopvSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.DataMultiplePowerOf10)
}

// optional string ExchangeDate = 21;
void MDIopvSnapshot::clear_exchangedate() {
  exchangedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIopvSnapshot::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
  return exchangedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_exchangedate(const ::std::string& value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
}
void MDIopvSnapshot::set_exchangedate(const char* value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
}
void MDIopvSnapshot::set_exchangedate(const char* value, size_t size) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
}
::std::string* MDIopvSnapshot::mutable_exchangedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
  return exchangedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIopvSnapshot::release_exchangedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
  
  return exchangedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_allocated_exchangedate(::std::string* exchangedate) {
  if (exchangedate != NULL) {
    
  } else {
    
  }
  exchangedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeDate)
}

// optional string ExchangeTime = 22;
void MDIopvSnapshot::clear_exchangetime() {
  exchangetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIopvSnapshot::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
  return exchangetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_exchangetime(const ::std::string& value) {
  
  exchangetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
}
void MDIopvSnapshot::set_exchangetime(const char* value) {
  
  exchangetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
}
void MDIopvSnapshot::set_exchangetime(const char* value, size_t size) {
  
  exchangetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
}
::std::string* MDIopvSnapshot::mutable_exchangetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
  return exchangetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIopvSnapshot::release_exchangetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
  
  return exchangetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIopvSnapshot::set_allocated_exchangetime(::std::string* exchangetime) {
  if (exchangetime != NULL) {
    
  } else {
    
  }
  exchangetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIopvSnapshot.ExchangeTime)
}

// optional int32 ChannelNo = 23;
void MDIopvSnapshot::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDIopvSnapshot::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.ChannelNo)
  return channelno_;
}
void MDIopvSnapshot::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.ChannelNo)
}

// optional int64 ApplSeqNum = 24;
void MDIopvSnapshot::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIopvSnapshot::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.ApplSeqNum)
  return applseqnum_;
}
void MDIopvSnapshot::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.ApplSeqNum)
}

// optional int64 ArrivalDateTime = 25;
void MDIopvSnapshot::clear_arrivaldatetime() {
  arrivaldatetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIopvSnapshot::arrivaldatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIopvSnapshot.ArrivalDateTime)
  return arrivaldatetime_;
}
void MDIopvSnapshot::set_arrivaldatetime(::google::protobuf::int64 value) {
  
  arrivaldatetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIopvSnapshot.ArrivalDateTime)
}

inline const MDIopvSnapshot* MDIopvSnapshot::internal_default_instance() {
  return &MDIopvSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
