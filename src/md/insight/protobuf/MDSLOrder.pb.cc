// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLOrder.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSLOrder.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSLOrder_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSLOrder_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSLOrder_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSLOrder_2eproto() {
  protobuf_AddDesc_MDSLOrder_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSLOrder.proto");
  GOOGLE_CHECK(file != NULL);
  MDSLOrder_descriptor_ = file->message_type(0);
  static const int MDSLOrder_offsets_[16] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, orderindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, ordertype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, orderprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, orderbsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, orderterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, ordernum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, datamultiplepowerof10_),
  };
  MDSLOrder_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSLOrder_descriptor_,
      MDSLOrder::internal_default_instance(),
      MDSLOrder_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSLOrder),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLOrder, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSLOrder_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSLOrder_descriptor_, MDSLOrder::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSLOrder_2eproto() {
  MDSLOrder_default_instance_.Shutdown();
  delete MDSLOrder_reflection_;
}

void protobuf_InitDefaults_MDSLOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSLOrder_default_instance_.DefaultConstruct();
  MDSLOrder_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSLOrder_2eproto_once_);
void protobuf_InitDefaults_MDSLOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSLOrder_2eproto_once_,
                 &protobuf_InitDefaults_MDSLOrder_2eproto_impl);
}
void protobuf_AddDesc_MDSLOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSLOrder_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\017MDSLOrder.proto\022\032com.htsc.mdc.insight."
    "model\032\027ESecurityIDSource.proto\032\023ESecurit"
    "yType.proto\"\246\003\n\tMDSLOrder\022\026\n\016HTSCSecurit"
    "yID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001("
    "\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securityIDSo"
    "urce\030\005 \001(\0162%.com.htsc.mdc.model.ESecurit"
    "yIDSource\0227\n\014securityType\030\006 \001(\0162!.com.ht"
    "sc.mdc.model.ESecurityType\022\024\n\014ExchangeDa"
    "te\030\007 \001(\005\022\024\n\014ExchangeTime\030\010 \001(\005\022\022\n\nOrderI"
    "ndex\030\t \001(\003\022\021\n\tOrderType\030\n \001(\005\022\022\n\nOrderPr"
    "ice\030\013 \001(\003\022\020\n\010OrderQty\030\014 \001(\003\022\023\n\013OrderBSFl"
    "ag\030\r \001(\005\022\021\n\tOrderTerm\030\016 \001(\005\022\020\n\010OrderNum\030"
    "\017 \001(\t\022\035\n\025DataMultiplePowerOf10\030\020 \001(\005B2\n\032"
    "com.htsc.mdc.insight.modelB\017MDSLOrderPro"
    "tosH\001\240\001\001b\006proto3", 576);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSLOrder.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSLOrder_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSLOrder_2eproto_once_);
void protobuf_AddDesc_MDSLOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSLOrder_2eproto_once_,
                 &protobuf_AddDesc_MDSLOrder_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSLOrder_2eproto {
  StaticDescriptorInitializer_MDSLOrder_2eproto() {
    protobuf_AddDesc_MDSLOrder_2eproto();
  }
} static_descriptor_initializer_MDSLOrder_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSLOrder::kHTSCSecurityIDFieldNumber;
const int MDSLOrder::kMDDateFieldNumber;
const int MDSLOrder::kMDTimeFieldNumber;
const int MDSLOrder::kDataTimestampFieldNumber;
const int MDSLOrder::kSecurityIDSourceFieldNumber;
const int MDSLOrder::kSecurityTypeFieldNumber;
const int MDSLOrder::kExchangeDateFieldNumber;
const int MDSLOrder::kExchangeTimeFieldNumber;
const int MDSLOrder::kOrderIndexFieldNumber;
const int MDSLOrder::kOrderTypeFieldNumber;
const int MDSLOrder::kOrderPriceFieldNumber;
const int MDSLOrder::kOrderQtyFieldNumber;
const int MDSLOrder::kOrderBSFlagFieldNumber;
const int MDSLOrder::kOrderTermFieldNumber;
const int MDSLOrder::kOrderNumFieldNumber;
const int MDSLOrder::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSLOrder::MDSLOrder()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLOrder_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSLOrder)
}

void MDSLOrder::InitAsDefaultInstance() {
}

MDSLOrder::MDSLOrder(const MDSLOrder& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSLOrder)
}

void MDSLOrder::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ordernum_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSLOrder::~MDSLOrder() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSLOrder)
  SharedDtor();
}

void MDSLOrder::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ordernum_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSLOrder::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSLOrder::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSLOrder_descriptor_;
}

const MDSLOrder& MDSLOrder::default_instance() {
  protobuf_InitDefaults_MDSLOrder_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSLOrder> MDSLOrder_default_instance_;

MDSLOrder* MDSLOrder::New(::google::protobuf::Arena* arena) const {
  MDSLOrder* n = new MDSLOrder;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSLOrder::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSLOrder)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSLOrder, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSLOrder*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(orderindex_, datamultiplepowerof10_);
  ordernum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDSLOrder::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSLOrder)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_OrderIndex;
        break;
      }

      // optional int64 OrderIndex = 9;
      case 9: {
        if (tag == 72) {
         parse_OrderIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_OrderType;
        break;
      }

      // optional int32 OrderType = 10;
      case 10: {
        if (tag == 80) {
         parse_OrderType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ordertype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_OrderPrice;
        break;
      }

      // optional int64 OrderPrice = 11;
      case 11: {
        if (tag == 88) {
         parse_OrderPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_OrderQty;
        break;
      }

      // optional int64 OrderQty = 12;
      case 12: {
        if (tag == 96) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_OrderBSFlag;
        break;
      }

      // optional int32 OrderBSFlag = 13;
      case 13: {
        if (tag == 104) {
         parse_OrderBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &orderbsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OrderTerm;
        break;
      }

      // optional int32 OrderTerm = 14;
      case 14: {
        if (tag == 112) {
         parse_OrderTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &orderterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_OrderNum;
        break;
      }

      // optional string OrderNum = 15;
      case 15: {
        if (tag == 122) {
         parse_OrderNum:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ordernum()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->ordernum().data(), this->ordernum().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLOrder.OrderNum"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 16;
      case 16: {
        if (tag == 128) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSLOrder)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSLOrder)
  return false;
#undef DO_
}

void MDSLOrder::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSLOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int64 OrderIndex = 9;
  if (this->orderindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->orderindex(), output);
  }

  // optional int32 OrderType = 10;
  if (this->ordertype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->ordertype(), output);
  }

  // optional int64 OrderPrice = 11;
  if (this->orderprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->orderprice(), output);
  }

  // optional int64 OrderQty = 12;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->orderqty(), output);
  }

  // optional int32 OrderBSFlag = 13;
  if (this->orderbsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->orderbsflag(), output);
  }

  // optional int32 OrderTerm = 14;
  if (this->orderterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->orderterm(), output);
  }

  // optional string OrderNum = 15;
  if (this->ordernum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ordernum().data(), this->ordernum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLOrder.OrderNum");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->ordernum(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 16;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSLOrder)
}

::google::protobuf::uint8* MDSLOrder::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSLOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int64 OrderIndex = 9;
  if (this->orderindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->orderindex(), target);
  }

  // optional int32 OrderType = 10;
  if (this->ordertype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->ordertype(), target);
  }

  // optional int64 OrderPrice = 11;
  if (this->orderprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->orderprice(), target);
  }

  // optional int64 OrderQty = 12;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->orderqty(), target);
  }

  // optional int32 OrderBSFlag = 13;
  if (this->orderbsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->orderbsflag(), target);
  }

  // optional int32 OrderTerm = 14;
  if (this->orderterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->orderterm(), target);
  }

  // optional string OrderNum = 15;
  if (this->ordernum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ordernum().data(), this->ordernum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLOrder.OrderNum");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->ordernum(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 16;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSLOrder)
  return target;
}

size_t MDSLOrder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSLOrder)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 OrderIndex = 9;
  if (this->orderindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderindex());
  }

  // optional int32 OrderType = 10;
  if (this->ordertype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ordertype());
  }

  // optional int64 OrderPrice = 11;
  if (this->orderprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderprice());
  }

  // optional int64 OrderQty = 12;
  if (this->orderqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderqty());
  }

  // optional int32 OrderBSFlag = 13;
  if (this->orderbsflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->orderbsflag());
  }

  // optional int32 OrderTerm = 14;
  if (this->orderterm() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->orderterm());
  }

  // optional string OrderNum = 15;
  if (this->ordernum().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->ordernum());
  }

  // optional int32 DataMultiplePowerOf10 = 16;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSLOrder::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSLOrder)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSLOrder* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSLOrder>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSLOrder)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSLOrder)
    UnsafeMergeFrom(*source);
  }
}

void MDSLOrder::MergeFrom(const MDSLOrder& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSLOrder)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSLOrder::UnsafeMergeFrom(const MDSLOrder& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.orderindex() != 0) {
    set_orderindex(from.orderindex());
  }
  if (from.ordertype() != 0) {
    set_ordertype(from.ordertype());
  }
  if (from.orderprice() != 0) {
    set_orderprice(from.orderprice());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.orderbsflag() != 0) {
    set_orderbsflag(from.orderbsflag());
  }
  if (from.orderterm() != 0) {
    set_orderterm(from.orderterm());
  }
  if (from.ordernum().size() > 0) {

    ordernum_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ordernum_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDSLOrder::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSLOrder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSLOrder::CopyFrom(const MDSLOrder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSLOrder)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSLOrder::IsInitialized() const {

  return true;
}

void MDSLOrder::Swap(MDSLOrder* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSLOrder::InternalSwap(MDSLOrder* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(orderindex_, other->orderindex_);
  std::swap(ordertype_, other->ordertype_);
  std::swap(orderprice_, other->orderprice_);
  std::swap(orderqty_, other->orderqty_);
  std::swap(orderbsflag_, other->orderbsflag_);
  std::swap(orderterm_, other->orderterm_);
  ordernum_.Swap(&other->ordernum_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSLOrder::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSLOrder_descriptor_;
  metadata.reflection = MDSLOrder_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLOrder

// optional string HTSCSecurityID = 1;
void MDSLOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
void MDSLOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
void MDSLOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
::std::string* MDSLOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSLOrder::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSLOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.MDDate)
  return mddate_;
}
void MDSLOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.MDDate)
}

// optional int32 MDTime = 3;
void MDSLOrder::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSLOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.MDTime)
  return mdtime_;
}
void MDSLOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSLOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.DataTimestamp)
  return datatimestamp_;
}
void MDSLOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDSLOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSLOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSLOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDSLOrder::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSLOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSLOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.securityType)
}

// optional int32 ExchangeDate = 7;
void MDSLOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDSLOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.ExchangeDate)
  return exchangedate_;
}
void MDSLOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDSLOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDSLOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.ExchangeTime)
  return exchangetime_;
}
void MDSLOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.ExchangeTime)
}

// optional int64 OrderIndex = 9;
void MDSLOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderIndex)
  return orderindex_;
}
void MDSLOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderIndex)
}

// optional int32 OrderType = 10;
void MDSLOrder::clear_ordertype() {
  ordertype_ = 0;
}
::google::protobuf::int32 MDSLOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderType)
  return ordertype_;
}
void MDSLOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderType)
}

// optional int64 OrderPrice = 11;
void MDSLOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderPrice)
  return orderprice_;
}
void MDSLOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderPrice)
}

// optional int64 OrderQty = 12;
void MDSLOrder::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderQty)
  return orderqty_;
}
void MDSLOrder::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderQty)
}

// optional int32 OrderBSFlag = 13;
void MDSLOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
::google::protobuf::int32 MDSLOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderBSFlag)
  return orderbsflag_;
}
void MDSLOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderBSFlag)
}

// optional int32 OrderTerm = 14;
void MDSLOrder::clear_orderterm() {
  orderterm_ = 0;
}
::google::protobuf::int32 MDSLOrder::orderterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderTerm)
  return orderterm_;
}
void MDSLOrder::set_orderterm(::google::protobuf::int32 value) {
  
  orderterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderTerm)
}

// optional string OrderNum = 15;
void MDSLOrder::clear_ordernum() {
  ordernum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLOrder::ordernum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  return ordernum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLOrder::set_ordernum(const ::std::string& value) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
void MDSLOrder::set_ordernum(const char* value) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
void MDSLOrder::set_ordernum(const char* value, size_t size) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
::std::string* MDSLOrder::mutable_ordernum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  return ordernum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLOrder::release_ordernum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  
  return ordernum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLOrder::set_allocated_ordernum(::std::string* ordernum) {
  if (ordernum != NULL) {
    
  } else {
    
  }
  ordernum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ordernum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}

// optional int32 DataMultiplePowerOf10 = 16;
void MDSLOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSLOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSLOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.DataMultiplePowerOf10)
}

inline const MDSLOrder* MDSLOrder::internal_default_instance() {
  return &MDSLOrder_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
