// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MessageHeader.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MessageHeader.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MessageHeader_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageHeader_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MessageHeader_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MessageHeader_2eproto() {
  protobuf_AddDesc_MessageHeader_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MessageHeader.proto");
  GOOGLE_CHECK(file != NULL);
  MessageHeader_descriptor_ = file->message_type(0);
  static const int MessageHeader_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, apptype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, messageclassification_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, messageid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, sendingtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, senderid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, targetid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, traceid_),
  };
  MessageHeader_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageHeader_descriptor_,
      MessageHeader::internal_default_instance(),
      MessageHeader_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageHeader),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageHeader, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MessageHeader_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageHeader_descriptor_, MessageHeader::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MessageHeader_2eproto() {
  MessageHeader_default_instance_.Shutdown();
  delete MessageHeader_reflection_;
}

void protobuf_InitDefaults_MessageHeader_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::GetEmptyString();
  MessageHeader_default_instance_.DefaultConstruct();
  MessageHeader_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MessageHeader_2eproto_once_);
void protobuf_InitDefaults_MessageHeader_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MessageHeader_2eproto_once_,
                 &protobuf_InitDefaults_MessageHeader_2eproto_impl);
}
void protobuf_AddDesc_MessageHeader_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MessageHeader_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\023MessageHeader.proto\022\032com.htsc.mdc.insi"
    "ght.model\"\234\001\n\rMessageHeader\022\017\n\007appType\030\001"
    " \001(\005\022\035\n\025messageClassification\030\002 \001(\005\022\021\n\tm"
    "essageId\030\003 \001(\003\022\023\n\013sendingTime\030\004 \001(\003\022\020\n\010s"
    "enderId\030\005 \001(\t\022\020\n\010targetId\030\006 \001(\t\022\017\n\007trace"
    "Id\030\007 \001(\tB5\n\032com.htsc.mdc.insight.modelB\022"
    "MessageHeaderProtoH\001\240\001\001b\006proto3", 271);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MessageHeader.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MessageHeader_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MessageHeader_2eproto_once_);
void protobuf_AddDesc_MessageHeader_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MessageHeader_2eproto_once_,
                 &protobuf_AddDesc_MessageHeader_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MessageHeader_2eproto {
  StaticDescriptorInitializer_MessageHeader_2eproto() {
    protobuf_AddDesc_MessageHeader_2eproto();
  }
} static_descriptor_initializer_MessageHeader_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageHeader::kAppTypeFieldNumber;
const int MessageHeader::kMessageClassificationFieldNumber;
const int MessageHeader::kMessageIdFieldNumber;
const int MessageHeader::kSendingTimeFieldNumber;
const int MessageHeader::kSenderIdFieldNumber;
const int MessageHeader::kTargetIdFieldNumber;
const int MessageHeader::kTraceIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageHeader::MessageHeader()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MessageHeader_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MessageHeader)
}

void MessageHeader::InitAsDefaultInstance() {
}

MessageHeader::MessageHeader(const MessageHeader& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MessageHeader)
}

void MessageHeader::SharedCtor() {
  senderid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  targetid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  traceid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&apptype_, 0, reinterpret_cast<char*>(&sendingtime_) -
    reinterpret_cast<char*>(&apptype_) + sizeof(sendingtime_));
  _cached_size_ = 0;
}

MessageHeader::~MessageHeader() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MessageHeader)
  SharedDtor();
}

void MessageHeader::SharedDtor() {
  senderid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  targetid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  traceid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MessageHeader::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageHeader::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageHeader_descriptor_;
}

const MessageHeader& MessageHeader::default_instance() {
  protobuf_InitDefaults_MessageHeader_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageHeader> MessageHeader_default_instance_;

MessageHeader* MessageHeader::New(::google::protobuf::Arena* arena) const {
  MessageHeader* n = new MessageHeader;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MessageHeader::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MessageHeader)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MessageHeader, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MessageHeader*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(apptype_, sendingtime_);
  senderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  targetid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  traceid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MessageHeader::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MessageHeader)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 appType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &apptype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_messageClassification;
        break;
      }

      // optional int32 messageClassification = 2;
      case 2: {
        if (tag == 16) {
         parse_messageClassification:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &messageclassification_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_messageId;
        break;
      }

      // optional int64 messageId = 3;
      case 3: {
        if (tag == 24) {
         parse_messageId:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messageid_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_sendingTime;
        break;
      }

      // optional int64 sendingTime = 4;
      case 4: {
        if (tag == 32) {
         parse_sendingTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sendingtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_senderId;
        break;
      }

      // optional string senderId = 5;
      case 5: {
        if (tag == 42) {
         parse_senderId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_senderid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->senderid().data(), this->senderid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MessageHeader.senderId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_targetId;
        break;
      }

      // optional string targetId = 6;
      case 6: {
        if (tag == 50) {
         parse_targetId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_targetid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->targetid().data(), this->targetid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MessageHeader.targetId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_traceId;
        break;
      }

      // optional string traceId = 7;
      case 7: {
        if (tag == 58) {
         parse_traceId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_traceid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->traceid().data(), this->traceid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MessageHeader.traceId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MessageHeader)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MessageHeader)
  return false;
#undef DO_
}

void MessageHeader::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MessageHeader)
  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->apptype(), output);
  }

  // optional int32 messageClassification = 2;
  if (this->messageclassification() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->messageclassification(), output);
  }

  // optional int64 messageId = 3;
  if (this->messageid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->messageid(), output);
  }

  // optional int64 sendingTime = 4;
  if (this->sendingtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->sendingtime(), output);
  }

  // optional string senderId = 5;
  if (this->senderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->senderid().data(), this->senderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.senderId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->senderid(), output);
  }

  // optional string targetId = 6;
  if (this->targetid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->targetid().data(), this->targetid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.targetId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->targetid(), output);
  }

  // optional string traceId = 7;
  if (this->traceid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->traceid().data(), this->traceid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.traceId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->traceid(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MessageHeader)
}

::google::protobuf::uint8* MessageHeader::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MessageHeader)
  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->apptype(), target);
  }

  // optional int32 messageClassification = 2;
  if (this->messageclassification() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->messageclassification(), target);
  }

  // optional int64 messageId = 3;
  if (this->messageid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->messageid(), target);
  }

  // optional int64 sendingTime = 4;
  if (this->sendingtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->sendingtime(), target);
  }

  // optional string senderId = 5;
  if (this->senderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->senderid().data(), this->senderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.senderId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->senderid(), target);
  }

  // optional string targetId = 6;
  if (this->targetid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->targetid().data(), this->targetid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.targetId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->targetid(), target);
  }

  // optional string traceId = 7;
  if (this->traceid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->traceid().data(), this->traceid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MessageHeader.traceId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->traceid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MessageHeader)
  return target;
}

size_t MessageHeader::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MessageHeader)
  size_t total_size = 0;

  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->apptype());
  }

  // optional int32 messageClassification = 2;
  if (this->messageclassification() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->messageclassification());
  }

  // optional int64 messageId = 3;
  if (this->messageid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messageid());
  }

  // optional int64 sendingTime = 4;
  if (this->sendingtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sendingtime());
  }

  // optional string senderId = 5;
  if (this->senderid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->senderid());
  }

  // optional string targetId = 6;
  if (this->targetid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->targetid());
  }

  // optional string traceId = 7;
  if (this->traceid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->traceid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageHeader::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MessageHeader)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageHeader* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageHeader>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MessageHeader)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MessageHeader)
    UnsafeMergeFrom(*source);
  }
}

void MessageHeader::MergeFrom(const MessageHeader& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MessageHeader)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageHeader::UnsafeMergeFrom(const MessageHeader& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.apptype() != 0) {
    set_apptype(from.apptype());
  }
  if (from.messageclassification() != 0) {
    set_messageclassification(from.messageclassification());
  }
  if (from.messageid() != 0) {
    set_messageid(from.messageid());
  }
  if (from.sendingtime() != 0) {
    set_sendingtime(from.sendingtime());
  }
  if (from.senderid().size() > 0) {

    senderid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.senderid_);
  }
  if (from.targetid().size() > 0) {

    targetid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.targetid_);
  }
  if (from.traceid().size() > 0) {

    traceid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.traceid_);
  }
}

void MessageHeader::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MessageHeader)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageHeader::CopyFrom(const MessageHeader& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MessageHeader)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageHeader::IsInitialized() const {

  return true;
}

void MessageHeader::Swap(MessageHeader* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MessageHeader::InternalSwap(MessageHeader* other) {
  std::swap(apptype_, other->apptype_);
  std::swap(messageclassification_, other->messageclassification_);
  std::swap(messageid_, other->messageid_);
  std::swap(sendingtime_, other->sendingtime_);
  senderid_.Swap(&other->senderid_);
  targetid_.Swap(&other->targetid_);
  traceid_.Swap(&other->traceid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageHeader::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageHeader_descriptor_;
  metadata.reflection = MessageHeader_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageHeader

// optional int32 appType = 1;
void MessageHeader::clear_apptype() {
  apptype_ = 0;
}
::google::protobuf::int32 MessageHeader::apptype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.appType)
  return apptype_;
}
void MessageHeader::set_apptype(::google::protobuf::int32 value) {
  
  apptype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.appType)
}

// optional int32 messageClassification = 2;
void MessageHeader::clear_messageclassification() {
  messageclassification_ = 0;
}
::google::protobuf::int32 MessageHeader::messageclassification() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.messageClassification)
  return messageclassification_;
}
void MessageHeader::set_messageclassification(::google::protobuf::int32 value) {
  
  messageclassification_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.messageClassification)
}

// optional int64 messageId = 3;
void MessageHeader::clear_messageid() {
  messageid_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MessageHeader::messageid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.messageId)
  return messageid_;
}
void MessageHeader::set_messageid(::google::protobuf::int64 value) {
  
  messageid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.messageId)
}

// optional int64 sendingTime = 4;
void MessageHeader::clear_sendingtime() {
  sendingtime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MessageHeader::sendingtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.sendingTime)
  return sendingtime_;
}
void MessageHeader::set_sendingtime(::google::protobuf::int64 value) {
  
  sendingtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.sendingTime)
}

// optional string senderId = 5;
void MessageHeader::clear_senderid() {
  senderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MessageHeader::senderid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.senderId)
  return senderid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_senderid(const ::std::string& value) {
  
  senderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.senderId)
}
void MessageHeader::set_senderid(const char* value) {
  
  senderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MessageHeader.senderId)
}
void MessageHeader::set_senderid(const char* value, size_t size) {
  
  senderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MessageHeader.senderId)
}
::std::string* MessageHeader::mutable_senderid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageHeader.senderId)
  return senderid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MessageHeader::release_senderid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageHeader.senderId)
  
  return senderid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_allocated_senderid(::std::string* senderid) {
  if (senderid != NULL) {
    
  } else {
    
  }
  senderid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), senderid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageHeader.senderId)
}

// optional string targetId = 6;
void MessageHeader::clear_targetid() {
  targetid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MessageHeader::targetid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.targetId)
  return targetid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_targetid(const ::std::string& value) {
  
  targetid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.targetId)
}
void MessageHeader::set_targetid(const char* value) {
  
  targetid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MessageHeader.targetId)
}
void MessageHeader::set_targetid(const char* value, size_t size) {
  
  targetid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MessageHeader.targetId)
}
::std::string* MessageHeader::mutable_targetid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageHeader.targetId)
  return targetid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MessageHeader::release_targetid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageHeader.targetId)
  
  return targetid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_allocated_targetid(::std::string* targetid) {
  if (targetid != NULL) {
    
  } else {
    
  }
  targetid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), targetid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageHeader.targetId)
}

// optional string traceId = 7;
void MessageHeader::clear_traceid() {
  traceid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MessageHeader::traceid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageHeader.traceId)
  return traceid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_traceid(const ::std::string& value) {
  
  traceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageHeader.traceId)
}
void MessageHeader::set_traceid(const char* value) {
  
  traceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MessageHeader.traceId)
}
void MessageHeader::set_traceid(const char* value, size_t size) {
  
  traceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MessageHeader.traceId)
}
::std::string* MessageHeader::mutable_traceid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageHeader.traceId)
  return traceid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MessageHeader::release_traceid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageHeader.traceId)
  
  return traceid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MessageHeader::set_allocated_traceid(::std::string* traceid) {
  if (traceid != NULL) {
    
  } else {
    
  }
  traceid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), traceid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageHeader.traceId)
}

inline const MessageHeader* MessageHeader::internal_default_instance() {
  return &MessageHeader_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
