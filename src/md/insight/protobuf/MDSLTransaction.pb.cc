// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLTransaction.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSLTransaction.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSLTransaction_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSLTransaction_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSLTransaction_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSLTransaction_2eproto() {
  protobuf_AddDesc_MDSLTransaction_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSLTransaction.proto");
  GOOGLE_CHECK(file != NULL);
  MDSLTransaction_descriptor_ = file->message_type(0);
  static const int MDSLTransaction_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradebuyno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradesellno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradebsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradeterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, tradenum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, datamultiplepowerof10_),
  };
  MDSLTransaction_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSLTransaction_descriptor_,
      MDSLTransaction::internal_default_instance(),
      MDSLTransaction_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSLTransaction),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLTransaction, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSLTransaction_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSLTransaction_descriptor_, MDSLTransaction::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSLTransaction_2eproto() {
  MDSLTransaction_default_instance_.Shutdown();
  delete MDSLTransaction_reflection_;
}

void protobuf_InitDefaults_MDSLTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSLTransaction_default_instance_.DefaultConstruct();
  MDSLTransaction_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSLTransaction_2eproto_once_);
void protobuf_InitDefaults_MDSLTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSLTransaction_2eproto_once_,
                 &protobuf_InitDefaults_MDSLTransaction_2eproto_impl);
}
void protobuf_AddDesc_MDSLTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSLTransaction_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025MDSLTransaction.proto\022\032com.htsc.mdc.in"
    "sight.model\032\027ESecurityIDSource.proto\032\023ES"
    "ecurityType.proto\"\374\003\n\017MDSLTransaction\022\026\n"
    "\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n"
    "\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020"
    "securityIDSource\030\005 \001(\0162%.com.htsc.mdc.mo"
    "del.ESecurityIDSource\0227\n\014securityType\030\006 "
    "\001(\0162!.com.htsc.mdc.model.ESecurityType\022\024"
    "\n\014ExchangeDate\030\007 \001(\005\022\024\n\014ExchangeTime\030\010 \001"
    "(\005\022\022\n\nTradeIndex\030\t \001(\003\022\022\n\nTradeBuyNo\030\n \001"
    "(\003\022\023\n\013TradeSellNo\030\013 \001(\003\022\021\n\tTradeType\030\014 \001"
    "(\005\022\023\n\013TradeBSFlag\030\r \001(\005\022\022\n\nTradePrice\030\016 "
    "\001(\003\022\020\n\010TradeQty\030\017 \001(\003\022\022\n\nTradeMoney\030\020 \001("
    "\003\022\021\n\tNumTrades\030\021 \001(\003\022\021\n\tTradeTerm\030\022 \001(\005\022"
    "\020\n\010TradeNum\030\023 \001(\t\022\035\n\025DataMultiplePowerOf"
    "10\030\024 \001(\005B8\n\032com.htsc.mdc.insight.modelB\025"
    "MDSLTransactionProtosH\001\240\001\001b\006proto3", 674);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSLTransaction.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSLTransaction_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSLTransaction_2eproto_once_);
void protobuf_AddDesc_MDSLTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSLTransaction_2eproto_once_,
                 &protobuf_AddDesc_MDSLTransaction_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSLTransaction_2eproto {
  StaticDescriptorInitializer_MDSLTransaction_2eproto() {
    protobuf_AddDesc_MDSLTransaction_2eproto();
  }
} static_descriptor_initializer_MDSLTransaction_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSLTransaction::kHTSCSecurityIDFieldNumber;
const int MDSLTransaction::kMDDateFieldNumber;
const int MDSLTransaction::kMDTimeFieldNumber;
const int MDSLTransaction::kDataTimestampFieldNumber;
const int MDSLTransaction::kSecurityIDSourceFieldNumber;
const int MDSLTransaction::kSecurityTypeFieldNumber;
const int MDSLTransaction::kExchangeDateFieldNumber;
const int MDSLTransaction::kExchangeTimeFieldNumber;
const int MDSLTransaction::kTradeIndexFieldNumber;
const int MDSLTransaction::kTradeBuyNoFieldNumber;
const int MDSLTransaction::kTradeSellNoFieldNumber;
const int MDSLTransaction::kTradeTypeFieldNumber;
const int MDSLTransaction::kTradeBSFlagFieldNumber;
const int MDSLTransaction::kTradePriceFieldNumber;
const int MDSLTransaction::kTradeQtyFieldNumber;
const int MDSLTransaction::kTradeMoneyFieldNumber;
const int MDSLTransaction::kNumTradesFieldNumber;
const int MDSLTransaction::kTradeTermFieldNumber;
const int MDSLTransaction::kTradeNumFieldNumber;
const int MDSLTransaction::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSLTransaction::MDSLTransaction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLTransaction_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSLTransaction)
}

void MDSLTransaction::InitAsDefaultInstance() {
}

MDSLTransaction::MDSLTransaction(const MDSLTransaction& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSLTransaction)
}

void MDSLTransaction::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradenum_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSLTransaction::~MDSLTransaction() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSLTransaction)
  SharedDtor();
}

void MDSLTransaction::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradenum_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSLTransaction::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSLTransaction::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSLTransaction_descriptor_;
}

const MDSLTransaction& MDSLTransaction::default_instance() {
  protobuf_InitDefaults_MDSLTransaction_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSLTransaction> MDSLTransaction_default_instance_;

MDSLTransaction* MDSLTransaction::New(::google::protobuf::Arena* arena) const {
  MDSLTransaction* n = new MDSLTransaction;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSLTransaction::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSLTransaction)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSLTransaction, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSLTransaction*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(tradeindex_, trademoney_);
  ZR_(numtrades_, datamultiplepowerof10_);
  tradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDSLTransaction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSLTransaction)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TradeIndex;
        break;
      }

      // optional int64 TradeIndex = 9;
      case 9: {
        if (tag == 72) {
         parse_TradeIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_TradeBuyNo;
        break;
      }

      // optional int64 TradeBuyNo = 10;
      case 10: {
        if (tag == 80) {
         parse_TradeBuyNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradebuyno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_TradeSellNo;
        break;
      }

      // optional int64 TradeSellNo = 11;
      case 11: {
        if (tag == 88) {
         parse_TradeSellNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradesellno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TradeType;
        break;
      }

      // optional int32 TradeType = 12;
      case 12: {
        if (tag == 96) {
         parse_TradeType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TradeBSFlag;
        break;
      }

      // optional int32 TradeBSFlag = 13;
      case 13: {
        if (tag == 104) {
         parse_TradeBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradebsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_TradePrice;
        break;
      }

      // optional int64 TradePrice = 14;
      case 14: {
        if (tag == 112) {
         parse_TradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_TradeQty;
        break;
      }

      // optional int64 TradeQty = 15;
      case 15: {
        if (tag == 120) {
         parse_TradeQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_TradeMoney;
        break;
      }

      // optional int64 TradeMoney = 16;
      case 16: {
        if (tag == 128) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 17;
      case 17: {
        if (tag == 136) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_TradeTerm;
        break;
      }

      // optional int32 TradeTerm = 18;
      case 18: {
        if (tag == 144) {
         parse_TradeTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradeterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_TradeNum;
        break;
      }

      // optional string TradeNum = 19;
      case 19: {
        if (tag == 154) {
         parse_TradeNum:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradenum()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradenum().data(), this->tradenum().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLTransaction.TradeNum"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 20;
      case 20: {
        if (tag == 160) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSLTransaction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSLTransaction)
  return false;
#undef DO_
}

void MDSLTransaction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSLTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int64 TradeIndex = 9;
  if (this->tradeindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->tradeindex(), output);
  }

  // optional int64 TradeBuyNo = 10;
  if (this->tradebuyno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->tradebuyno(), output);
  }

  // optional int64 TradeSellNo = 11;
  if (this->tradesellno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->tradesellno(), output);
  }

  // optional int32 TradeType = 12;
  if (this->tradetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->tradetype(), output);
  }

  // optional int32 TradeBSFlag = 13;
  if (this->tradebsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->tradebsflag(), output);
  }

  // optional int64 TradePrice = 14;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->tradeprice(), output);
  }

  // optional int64 TradeQty = 15;
  if (this->tradeqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->tradeqty(), output);
  }

  // optional int64 TradeMoney = 16;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->trademoney(), output);
  }

  // optional int64 NumTrades = 17;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->numtrades(), output);
  }

  // optional int32 TradeTerm = 18;
  if (this->tradeterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->tradeterm(), output);
  }

  // optional string TradeNum = 19;
  if (this->tradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradenum().data(), this->tradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLTransaction.TradeNum");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->tradenum(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSLTransaction)
}

::google::protobuf::uint8* MDSLTransaction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSLTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int64 TradeIndex = 9;
  if (this->tradeindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->tradeindex(), target);
  }

  // optional int64 TradeBuyNo = 10;
  if (this->tradebuyno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->tradebuyno(), target);
  }

  // optional int64 TradeSellNo = 11;
  if (this->tradesellno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->tradesellno(), target);
  }

  // optional int32 TradeType = 12;
  if (this->tradetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->tradetype(), target);
  }

  // optional int32 TradeBSFlag = 13;
  if (this->tradebsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->tradebsflag(), target);
  }

  // optional int64 TradePrice = 14;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->tradeprice(), target);
  }

  // optional int64 TradeQty = 15;
  if (this->tradeqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->tradeqty(), target);
  }

  // optional int64 TradeMoney = 16;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->trademoney(), target);
  }

  // optional int64 NumTrades = 17;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->numtrades(), target);
  }

  // optional int32 TradeTerm = 18;
  if (this->tradeterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->tradeterm(), target);
  }

  // optional string TradeNum = 19;
  if (this->tradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradenum().data(), this->tradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLTransaction.TradeNum");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->tradenum(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSLTransaction)
  return target;
}

size_t MDSLTransaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSLTransaction)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 TradeIndex = 9;
  if (this->tradeindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeindex());
  }

  // optional int64 TradeBuyNo = 10;
  if (this->tradebuyno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradebuyno());
  }

  // optional int64 TradeSellNo = 11;
  if (this->tradesellno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradesellno());
  }

  // optional int32 TradeType = 12;
  if (this->tradetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradetype());
  }

  // optional int32 TradeBSFlag = 13;
  if (this->tradebsflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradebsflag());
  }

  // optional int64 TradePrice = 14;
  if (this->tradeprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeprice());
  }

  // optional int64 TradeQty = 15;
  if (this->tradeqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeqty());
  }

  // optional int64 TradeMoney = 16;
  if (this->trademoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->trademoney());
  }

  // optional int64 NumTrades = 17;
  if (this->numtrades() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int32 TradeTerm = 18;
  if (this->tradeterm() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradeterm());
  }

  // optional string TradeNum = 19;
  if (this->tradenum().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradenum());
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSLTransaction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSLTransaction)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSLTransaction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSLTransaction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSLTransaction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSLTransaction)
    UnsafeMergeFrom(*source);
  }
}

void MDSLTransaction::MergeFrom(const MDSLTransaction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSLTransaction)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSLTransaction::UnsafeMergeFrom(const MDSLTransaction& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.tradeindex() != 0) {
    set_tradeindex(from.tradeindex());
  }
  if (from.tradebuyno() != 0) {
    set_tradebuyno(from.tradebuyno());
  }
  if (from.tradesellno() != 0) {
    set_tradesellno(from.tradesellno());
  }
  if (from.tradetype() != 0) {
    set_tradetype(from.tradetype());
  }
  if (from.tradebsflag() != 0) {
    set_tradebsflag(from.tradebsflag());
  }
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.tradeqty() != 0) {
    set_tradeqty(from.tradeqty());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.tradeterm() != 0) {
    set_tradeterm(from.tradeterm());
  }
  if (from.tradenum().size() > 0) {

    tradenum_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradenum_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDSLTransaction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSLTransaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSLTransaction::CopyFrom(const MDSLTransaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSLTransaction)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSLTransaction::IsInitialized() const {

  return true;
}

void MDSLTransaction::Swap(MDSLTransaction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSLTransaction::InternalSwap(MDSLTransaction* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(tradeindex_, other->tradeindex_);
  std::swap(tradebuyno_, other->tradebuyno_);
  std::swap(tradesellno_, other->tradesellno_);
  std::swap(tradetype_, other->tradetype_);
  std::swap(tradebsflag_, other->tradebsflag_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(tradeterm_, other->tradeterm_);
  tradenum_.Swap(&other->tradenum_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSLTransaction::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSLTransaction_descriptor_;
  metadata.reflection = MDSLTransaction_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLTransaction

// optional string HTSCSecurityID = 1;
void MDSLTransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLTransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLTransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
}
void MDSLTransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
}
void MDSLTransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
}
::std::string* MDSLTransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLTransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLTransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLTransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSLTransaction::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSLTransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.MDDate)
  return mddate_;
}
void MDSLTransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.MDDate)
}

// optional int32 MDTime = 3;
void MDSLTransaction::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSLTransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.MDTime)
  return mdtime_;
}
void MDSLTransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSLTransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.DataTimestamp)
  return datatimestamp_;
}
void MDSLTransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDSLTransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSLTransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSLTransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDSLTransaction::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSLTransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSLTransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.securityType)
}

// optional int32 ExchangeDate = 7;
void MDSLTransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDSLTransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.ExchangeDate)
  return exchangedate_;
}
void MDSLTransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDSLTransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDSLTransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.ExchangeTime)
  return exchangetime_;
}
void MDSLTransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.ExchangeTime)
}

// optional int64 TradeIndex = 9;
void MDSLTransaction::clear_tradeindex() {
  tradeindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeIndex)
  return tradeindex_;
}
void MDSLTransaction::set_tradeindex(::google::protobuf::int64 value) {
  
  tradeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeIndex)
}

// optional int64 TradeBuyNo = 10;
void MDSLTransaction::clear_tradebuyno() {
  tradebuyno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::tradebuyno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeBuyNo)
  return tradebuyno_;
}
void MDSLTransaction::set_tradebuyno(::google::protobuf::int64 value) {
  
  tradebuyno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeBuyNo)
}

// optional int64 TradeSellNo = 11;
void MDSLTransaction::clear_tradesellno() {
  tradesellno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::tradesellno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeSellNo)
  return tradesellno_;
}
void MDSLTransaction::set_tradesellno(::google::protobuf::int64 value) {
  
  tradesellno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeSellNo)
}

// optional int32 TradeType = 12;
void MDSLTransaction::clear_tradetype() {
  tradetype_ = 0;
}
::google::protobuf::int32 MDSLTransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeType)
  return tradetype_;
}
void MDSLTransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeType)
}

// optional int32 TradeBSFlag = 13;
void MDSLTransaction::clear_tradebsflag() {
  tradebsflag_ = 0;
}
::google::protobuf::int32 MDSLTransaction::tradebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeBSFlag)
  return tradebsflag_;
}
void MDSLTransaction::set_tradebsflag(::google::protobuf::int32 value) {
  
  tradebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeBSFlag)
}

// optional int64 TradePrice = 14;
void MDSLTransaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradePrice)
  return tradeprice_;
}
void MDSLTransaction::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradePrice)
}

// optional int64 TradeQty = 15;
void MDSLTransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeQty)
  return tradeqty_;
}
void MDSLTransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeQty)
}

// optional int64 TradeMoney = 16;
void MDSLTransaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeMoney)
  return trademoney_;
}
void MDSLTransaction::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeMoney)
}

// optional int64 NumTrades = 17;
void MDSLTransaction::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLTransaction::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.NumTrades)
  return numtrades_;
}
void MDSLTransaction::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.NumTrades)
}

// optional int32 TradeTerm = 18;
void MDSLTransaction::clear_tradeterm() {
  tradeterm_ = 0;
}
::google::protobuf::int32 MDSLTransaction::tradeterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeTerm)
  return tradeterm_;
}
void MDSLTransaction::set_tradeterm(::google::protobuf::int32 value) {
  
  tradeterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeTerm)
}

// optional string TradeNum = 19;
void MDSLTransaction::clear_tradenum() {
  tradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLTransaction::tradenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
  return tradenum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLTransaction::set_tradenum(const ::std::string& value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
}
void MDSLTransaction::set_tradenum(const char* value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
}
void MDSLTransaction::set_tradenum(const char* value, size_t size) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
}
::std::string* MDSLTransaction::mutable_tradenum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
  return tradenum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLTransaction::release_tradenum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
  
  return tradenum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLTransaction::set_allocated_tradenum(::std::string* tradenum) {
  if (tradenum != NULL) {
    
  } else {
    
  }
  tradenum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradenum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLTransaction.TradeNum)
}

// optional int32 DataMultiplePowerOf10 = 20;
void MDSLTransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSLTransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLTransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSLTransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLTransaction.DataMultiplePowerOf10)
}

inline const MDSLTransaction* MDSLTransaction::internal_default_instance() {
  return &MDSLTransaction_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
