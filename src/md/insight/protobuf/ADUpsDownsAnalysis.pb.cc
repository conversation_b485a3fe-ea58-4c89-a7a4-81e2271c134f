// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADUpsDownsAnalysis.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "ADUpsDownsAnalysis.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* ADUpsDownsAnalysis_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADUpsDownsAnalysis_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADUpsDownsCount_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADUpsDownsCount_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADUpsDownsLimitCount_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADUpsDownsLimitCount_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADUpsDownsPartitionDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADUpsDownsPartitionDetail_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto() {
  protobuf_AddDesc_ADUpsDownsAnalysis_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "ADUpsDownsAnalysis.proto");
  GOOGLE_CHECK(file != NULL);
  ADUpsDownsAnalysis_descriptor_ = file->message_type(0);
  static const int ADUpsDownsAnalysis_offsets_[12] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, upsdownscount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, upsdownslimitcount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, upsdownspartitiondetail_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, datamultiplepowerof10_),
  };
  ADUpsDownsAnalysis_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADUpsDownsAnalysis_descriptor_,
      ADUpsDownsAnalysis::internal_default_instance(),
      ADUpsDownsAnalysis_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADUpsDownsAnalysis),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsAnalysis, _internal_metadata_));
  ADUpsDownsCount_descriptor_ = file->message_type(1);
  static const int ADUpsDownsCount_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, ups_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, downs_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, equals_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, preups_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, predowns_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, preequals_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, upspercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, preupspercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, leadingupids_),
  };
  ADUpsDownsCount_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADUpsDownsCount_descriptor_,
      ADUpsDownsCount::internal_default_instance(),
      ADUpsDownsCount_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADUpsDownsCount),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsCount, _internal_metadata_));
  ADUpsDownsLimitCount_descriptor_ = file->message_type(2);
  static const int ADUpsDownsLimitCount_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, noreachedlimitpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, uplimits_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, downlimits_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, prenoreachedlimitpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, preuplimits_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, predownlimits_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, preuplimitsaveragechangepercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, uplimitspercent_),
  };
  ADUpsDownsLimitCount_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADUpsDownsLimitCount_descriptor_,
      ADUpsDownsLimitCount::internal_default_instance(),
      ADUpsDownsLimitCount_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADUpsDownsLimitCount),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsLimitCount, _internal_metadata_));
  ADUpsDownsPartitionDetail_descriptor_ = file->message_type(3);
  static const int ADUpsDownsPartitionDetail_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsPartitionDetail, numbers_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsPartitionDetail, partitionchangepercent_),
  };
  ADUpsDownsPartitionDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADUpsDownsPartitionDetail_descriptor_,
      ADUpsDownsPartitionDetail::internal_default_instance(),
      ADUpsDownsPartitionDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADUpsDownsPartitionDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADUpsDownsPartitionDetail, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADUpsDownsAnalysis_descriptor_, ADUpsDownsAnalysis::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADUpsDownsCount_descriptor_, ADUpsDownsCount::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADUpsDownsLimitCount_descriptor_, ADUpsDownsLimitCount::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADUpsDownsPartitionDetail_descriptor_, ADUpsDownsPartitionDetail::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto() {
  ADUpsDownsAnalysis_default_instance_.Shutdown();
  delete ADUpsDownsAnalysis_reflection_;
  ADUpsDownsCount_default_instance_.Shutdown();
  delete ADUpsDownsCount_reflection_;
  ADUpsDownsLimitCount_default_instance_.Shutdown();
  delete ADUpsDownsLimitCount_reflection_;
  ADUpsDownsPartitionDetail_default_instance_.Shutdown();
  delete ADUpsDownsPartitionDetail_reflection_;
}

void protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  ADUpsDownsAnalysis_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADUpsDownsCount_default_instance_.DefaultConstruct();
  ADUpsDownsLimitCount_default_instance_.DefaultConstruct();
  ADUpsDownsPartitionDetail_default_instance_.DefaultConstruct();
  ADUpsDownsAnalysis_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADUpsDownsCount_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADUpsDownsLimitCount_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADUpsDownsPartitionDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_once_);
void protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_once_,
                 &protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl);
}
void protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\030ADUpsDownsAnalysis.proto\022\032com.htsc.mdc"
    ".insight.model\032\023ESecurityType.proto\032\027ESe"
    "curityIDSource.proto\"\222\004\n\022ADUpsDownsAnaly"
    "sis\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 "
    "\001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001"
    "(\003\022\?\n\020securityIDSource\030\005 \001(\0162%.com.htsc."
    "mdc.model.ESecurityIDSource\0227\n\014securityT"
    "ype\030\006 \001(\0162!.com.htsc.mdc.model.ESecurity"
    "Type\022B\n\rUpsDownsCount\030\007 \001(\0132+.com.htsc.m"
    "dc.insight.model.ADUpsDownsCount\022L\n\022UpsD"
    "ownsLimitCount\030\010 \001(\01320.com.htsc.mdc.insi"
    "ght.model.ADUpsDownsLimitCount\022V\n\027UpsDow"
    "nsPartitionDetail\030\t \003(\01325.com.htsc.mdc.i"
    "nsight.model.ADUpsDownsPartitionDetail\022\024"
    "\n\014ExchangeDate\030\n \001(\005\022\024\n\014ExchangeTime\030\013 \001"
    "(\005\022\035\n\025DataMultiplePowerOf10\030\014 \001(\005\"\263\001\n\017AD"
    "UpsDownsCount\022\013\n\003Ups\030\001 \001(\005\022\r\n\005Downs\030\002 \001("
    "\005\022\016\n\006Equals\030\003 \001(\005\022\016\n\006PreUps\030\004 \001(\005\022\020\n\010Pre"
    "Downs\030\005 \001(\005\022\021\n\tPreEquals\030\006 \001(\005\022\022\n\nUpsPer"
    "cent\030\007 \001(\001\022\025\n\rPreUpsPercent\030\010 \001(\001\022\024\n\014Lea"
    "dingUpIds\030\t \003(\t\"\341\001\n\024ADUpsDownsLimitCount"
    "\022\030\n\020NoReachedLimitPx\030\001 \001(\005\022\020\n\010UpLimits\030\002"
    " \001(\005\022\022\n\nDownLimits\030\003 \001(\005\022\033\n\023PreNoReached"
    "LimitPx\030\004 \001(\005\022\023\n\013PreUpLimits\030\005 \001(\005\022\025\n\rPr"
    "eDownLimits\030\006 \001(\005\022\'\n\037PreUpLimitsAverageC"
    "hangePercent\030\007 \001(\001\022\027\n\017UpLimitsPercent\030\010 "
    "\001(\001\"L\n\031ADUpsDownsPartitionDetail\022\017\n\007Numb"
    "ers\030\001 \001(\005\022\036\n\026PartitionChangePercent\030\002 \001("
    "\005B;\n\032com.htsc.mdc.insight.modelB\030ADUpsDo"
    "wnsAnalysisProtosH\001\240\001\001b\006proto3", 1190);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "ADUpsDownsAnalysis.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_once_);
void protobuf_AddDesc_ADUpsDownsAnalysis_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_once_,
                 &protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_ADUpsDownsAnalysis_2eproto {
  StaticDescriptorInitializer_ADUpsDownsAnalysis_2eproto() {
    protobuf_AddDesc_ADUpsDownsAnalysis_2eproto();
  }
} static_descriptor_initializer_ADUpsDownsAnalysis_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADUpsDownsAnalysis::kHTSCSecurityIDFieldNumber;
const int ADUpsDownsAnalysis::kMDDateFieldNumber;
const int ADUpsDownsAnalysis::kMDTimeFieldNumber;
const int ADUpsDownsAnalysis::kDataTimestampFieldNumber;
const int ADUpsDownsAnalysis::kSecurityIDSourceFieldNumber;
const int ADUpsDownsAnalysis::kSecurityTypeFieldNumber;
const int ADUpsDownsAnalysis::kUpsDownsCountFieldNumber;
const int ADUpsDownsAnalysis::kUpsDownsLimitCountFieldNumber;
const int ADUpsDownsAnalysis::kUpsDownsPartitionDetailFieldNumber;
const int ADUpsDownsAnalysis::kExchangeDateFieldNumber;
const int ADUpsDownsAnalysis::kExchangeTimeFieldNumber;
const int ADUpsDownsAnalysis::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADUpsDownsAnalysis::ADUpsDownsAnalysis()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
}

void ADUpsDownsAnalysis::InitAsDefaultInstance() {
  upsdownscount_ = const_cast< ::com::htsc::mdc::insight::model::ADUpsDownsCount*>(
      ::com::htsc::mdc::insight::model::ADUpsDownsCount::internal_default_instance());
  upsdownslimitcount_ = const_cast< ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount*>(
      ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount::internal_default_instance());
}

ADUpsDownsAnalysis::ADUpsDownsAnalysis(const ADUpsDownsAnalysis& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
}

void ADUpsDownsAnalysis::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  upsdownscount_ = NULL;
  upsdownslimitcount_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

ADUpsDownsAnalysis::~ADUpsDownsAnalysis() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  SharedDtor();
}

void ADUpsDownsAnalysis::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &ADUpsDownsAnalysis_default_instance_.get()) {
    delete upsdownscount_;
    delete upsdownslimitcount_;
  }
}

void ADUpsDownsAnalysis::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADUpsDownsAnalysis::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADUpsDownsAnalysis_descriptor_;
}

const ADUpsDownsAnalysis& ADUpsDownsAnalysis::default_instance() {
  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsAnalysis> ADUpsDownsAnalysis_default_instance_;

ADUpsDownsAnalysis* ADUpsDownsAnalysis::New(::google::protobuf::Arena* arena) const {
  ADUpsDownsAnalysis* n = new ADUpsDownsAnalysis;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADUpsDownsAnalysis::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADUpsDownsAnalysis, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADUpsDownsAnalysis*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && upsdownscount_ != NULL) delete upsdownscount_;
  upsdownscount_ = NULL;
  if (GetArenaNoVirtual() == NULL && upsdownslimitcount_ != NULL) delete upsdownslimitcount_;
  upsdownslimitcount_ = NULL;
  ZR_(exchangedate_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

  upsdownspartitiondetail_.Clear();
}

bool ADUpsDownsAnalysis::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_UpsDownsCount;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
      case 7: {
        if (tag == 58) {
         parse_UpsDownsCount:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_upsdownscount()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_UpsDownsLimitCount;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
      case 8: {
        if (tag == 66) {
         parse_UpsDownsLimitCount:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_upsdownslimitcount()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_UpsDownsPartitionDetail;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
      case 9: {
        if (tag == 74) {
         parse_UpsDownsPartitionDetail:
          DO_(input->IncrementRecursionDepth());
         parse_loop_UpsDownsPartitionDetail:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_upsdownspartitiondetail()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_UpsDownsPartitionDetail;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(80)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 10;
      case 10: {
        if (tag == 80) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 11;
      case 11: {
        if (tag == 88) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 12;
      case 12: {
        if (tag == 96) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  return false;
#undef DO_
}

void ADUpsDownsAnalysis::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
  if (this->has_upsdownscount()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->upsdownscount_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
  if (this->has_upsdownslimitcount()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->upsdownslimitcount_, output);
  }

  // repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
  for (unsigned int i = 0, n = this->upsdownspartitiondetail_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->upsdownspartitiondetail(i), output);
  }

  // optional int32 ExchangeDate = 10;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 11;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->exchangetime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
}

::google::protobuf::uint8* ADUpsDownsAnalysis::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
  if (this->has_upsdownscount()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        7, *this->upsdownscount_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
  if (this->has_upsdownslimitcount()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->upsdownslimitcount_, false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
  for (unsigned int i = 0, n = this->upsdownspartitiondetail_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->upsdownspartitiondetail(i), false, target);
  }

  // optional int32 ExchangeDate = 10;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 11;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->exchangetime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  return target;
}

size_t ADUpsDownsAnalysis::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
  if (this->has_upsdownscount()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->upsdownscount_);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
  if (this->has_upsdownslimitcount()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->upsdownslimitcount_);
  }

  // optional int32 ExchangeDate = 10;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 11;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
  {
    unsigned int count = this->upsdownspartitiondetail_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->upsdownspartitiondetail(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADUpsDownsAnalysis::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADUpsDownsAnalysis* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADUpsDownsAnalysis>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
    UnsafeMergeFrom(*source);
  }
}

void ADUpsDownsAnalysis::MergeFrom(const ADUpsDownsAnalysis& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADUpsDownsAnalysis::UnsafeMergeFrom(const ADUpsDownsAnalysis& from) {
  GOOGLE_DCHECK(&from != this);
  upsdownspartitiondetail_.MergeFrom(from.upsdownspartitiondetail_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.has_upsdownscount()) {
    mutable_upsdownscount()->::com::htsc::mdc::insight::model::ADUpsDownsCount::MergeFrom(from.upsdownscount());
  }
  if (from.has_upsdownslimitcount()) {
    mutable_upsdownslimitcount()->::com::htsc::mdc::insight::model::ADUpsDownsLimitCount::MergeFrom(from.upsdownslimitcount());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void ADUpsDownsAnalysis::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADUpsDownsAnalysis::CopyFrom(const ADUpsDownsAnalysis& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADUpsDownsAnalysis::IsInitialized() const {

  return true;
}

void ADUpsDownsAnalysis::Swap(ADUpsDownsAnalysis* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADUpsDownsAnalysis::InternalSwap(ADUpsDownsAnalysis* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(upsdownscount_, other->upsdownscount_);
  std::swap(upsdownslimitcount_, other->upsdownslimitcount_);
  upsdownspartitiondetail_.UnsafeArenaSwap(&other->upsdownspartitiondetail_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADUpsDownsAnalysis::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADUpsDownsAnalysis_descriptor_;
  metadata.reflection = ADUpsDownsAnalysis_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADUpsDownsAnalysis

// optional string HTSCSecurityID = 1;
void ADUpsDownsAnalysis::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADUpsDownsAnalysis::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADUpsDownsAnalysis::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
void ADUpsDownsAnalysis::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
void ADUpsDownsAnalysis::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
::std::string* ADUpsDownsAnalysis::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADUpsDownsAnalysis::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADUpsDownsAnalysis::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void ADUpsDownsAnalysis::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 ADUpsDownsAnalysis::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDDate)
  return mddate_;
}
void ADUpsDownsAnalysis::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDDate)
}

// optional int32 MDTime = 3;
void ADUpsDownsAnalysis::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 ADUpsDownsAnalysis::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDTime)
  return mdtime_;
}
void ADUpsDownsAnalysis::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDTime)
}

// optional int64 DataTimestamp = 4;
void ADUpsDownsAnalysis::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADUpsDownsAnalysis::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataTimestamp)
  return datatimestamp_;
}
void ADUpsDownsAnalysis::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void ADUpsDownsAnalysis::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource ADUpsDownsAnalysis::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void ADUpsDownsAnalysis::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void ADUpsDownsAnalysis::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType ADUpsDownsAnalysis::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void ADUpsDownsAnalysis::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityType)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
bool ADUpsDownsAnalysis::has_upsdownscount() const {
  return this != internal_default_instance() && upsdownscount_ != NULL;
}
void ADUpsDownsAnalysis::clear_upsdownscount() {
  if (GetArenaNoVirtual() == NULL && upsdownscount_ != NULL) delete upsdownscount_;
  upsdownscount_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADUpsDownsCount& ADUpsDownsAnalysis::upsdownscount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  return upsdownscount_ != NULL ? *upsdownscount_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsCount::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADUpsDownsCount* ADUpsDownsAnalysis::mutable_upsdownscount() {
  
  if (upsdownscount_ == NULL) {
    upsdownscount_ = new ::com::htsc::mdc::insight::model::ADUpsDownsCount;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  return upsdownscount_;
}
::com::htsc::mdc::insight::model::ADUpsDownsCount* ADUpsDownsAnalysis::release_upsdownscount() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsCount* temp = upsdownscount_;
  upsdownscount_ = NULL;
  return temp;
}
void ADUpsDownsAnalysis::set_allocated_upsdownscount(::com::htsc::mdc::insight::model::ADUpsDownsCount* upsdownscount) {
  delete upsdownscount_;
  upsdownscount_ = upsdownscount;
  if (upsdownscount) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
bool ADUpsDownsAnalysis::has_upsdownslimitcount() const {
  return this != internal_default_instance() && upsdownslimitcount_ != NULL;
}
void ADUpsDownsAnalysis::clear_upsdownslimitcount() {
  if (GetArenaNoVirtual() == NULL && upsdownslimitcount_ != NULL) delete upsdownslimitcount_;
  upsdownslimitcount_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount& ADUpsDownsAnalysis::upsdownslimitcount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  return upsdownslimitcount_ != NULL ? *upsdownslimitcount_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsLimitCount::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* ADUpsDownsAnalysis::mutable_upsdownslimitcount() {
  
  if (upsdownslimitcount_ == NULL) {
    upsdownslimitcount_ = new ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  return upsdownslimitcount_;
}
::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* ADUpsDownsAnalysis::release_upsdownslimitcount() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* temp = upsdownslimitcount_;
  upsdownslimitcount_ = NULL;
  return temp;
}
void ADUpsDownsAnalysis::set_allocated_upsdownslimitcount(::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* upsdownslimitcount) {
  delete upsdownslimitcount_;
  upsdownslimitcount_ = upsdownslimitcount;
  if (upsdownslimitcount) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
}

// repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
int ADUpsDownsAnalysis::upsdownspartitiondetail_size() const {
  return upsdownspartitiondetail_.size();
}
void ADUpsDownsAnalysis::clear_upsdownspartitiondetail() {
  upsdownspartitiondetail_.Clear();
}
const ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail& ADUpsDownsAnalysis::upsdownspartitiondetail(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Get(index);
}
::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* ADUpsDownsAnalysis::mutable_upsdownspartitiondetail(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* ADUpsDownsAnalysis::add_upsdownspartitiondetail() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >*
ADUpsDownsAnalysis::mutable_upsdownspartitiondetail() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return &upsdownspartitiondetail_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >&
ADUpsDownsAnalysis::upsdownspartitiondetail() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_;
}

// optional int32 ExchangeDate = 10;
void ADUpsDownsAnalysis::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 ADUpsDownsAnalysis::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeDate)
  return exchangedate_;
}
void ADUpsDownsAnalysis::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeDate)
}

// optional int32 ExchangeTime = 11;
void ADUpsDownsAnalysis::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 ADUpsDownsAnalysis::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeTime)
  return exchangetime_;
}
void ADUpsDownsAnalysis::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 12;
void ADUpsDownsAnalysis::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 ADUpsDownsAnalysis::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void ADUpsDownsAnalysis::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataMultiplePowerOf10)
}

inline const ADUpsDownsAnalysis* ADUpsDownsAnalysis::internal_default_instance() {
  return &ADUpsDownsAnalysis_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADUpsDownsCount::kUpsFieldNumber;
const int ADUpsDownsCount::kDownsFieldNumber;
const int ADUpsDownsCount::kEqualsFieldNumber;
const int ADUpsDownsCount::kPreUpsFieldNumber;
const int ADUpsDownsCount::kPreDownsFieldNumber;
const int ADUpsDownsCount::kPreEqualsFieldNumber;
const int ADUpsDownsCount::kUpsPercentFieldNumber;
const int ADUpsDownsCount::kPreUpsPercentFieldNumber;
const int ADUpsDownsCount::kLeadingUpIdsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADUpsDownsCount::ADUpsDownsCount()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADUpsDownsCount)
}

void ADUpsDownsCount::InitAsDefaultInstance() {
}

ADUpsDownsCount::ADUpsDownsCount(const ADUpsDownsCount& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADUpsDownsCount)
}

void ADUpsDownsCount::SharedCtor() {
  ::memset(&ups_, 0, reinterpret_cast<char*>(&preupspercent_) -
    reinterpret_cast<char*>(&ups_) + sizeof(preupspercent_));
  _cached_size_ = 0;
}

ADUpsDownsCount::~ADUpsDownsCount() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADUpsDownsCount)
  SharedDtor();
}

void ADUpsDownsCount::SharedDtor() {
}

void ADUpsDownsCount::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADUpsDownsCount::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADUpsDownsCount_descriptor_;
}

const ADUpsDownsCount& ADUpsDownsCount::default_instance() {
  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsCount> ADUpsDownsCount_default_instance_;

ADUpsDownsCount* ADUpsDownsCount::New(::google::protobuf::Arena* arena) const {
  ADUpsDownsCount* n = new ADUpsDownsCount;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADUpsDownsCount::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADUpsDownsCount, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADUpsDownsCount*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(ups_, preupspercent_);

#undef ZR_HELPER_
#undef ZR_

  leadingupids_.Clear();
}

bool ADUpsDownsCount::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Ups = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ups_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Downs;
        break;
      }

      // optional int32 Downs = 2;
      case 2: {
        if (tag == 16) {
         parse_Downs:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &downs_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Equals;
        break;
      }

      // optional int32 Equals = 3;
      case 3: {
        if (tag == 24) {
         parse_Equals:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &equals_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_PreUps;
        break;
      }

      // optional int32 PreUps = 4;
      case 4: {
        if (tag == 32) {
         parse_PreUps:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preups_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_PreDowns;
        break;
      }

      // optional int32 PreDowns = 5;
      case 5: {
        if (tag == 40) {
         parse_PreDowns:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &predowns_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_PreEquals;
        break;
      }

      // optional int32 PreEquals = 6;
      case 6: {
        if (tag == 48) {
         parse_PreEquals:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preequals_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_UpsPercent;
        break;
      }

      // optional double UpsPercent = 7;
      case 7: {
        if (tag == 57) {
         parse_UpsPercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &upspercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_PreUpsPercent;
        break;
      }

      // optional double PreUpsPercent = 8;
      case 8: {
        if (tag == 65) {
         parse_PreUpsPercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preupspercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_LeadingUpIds;
        break;
      }

      // repeated string LeadingUpIds = 9;
      case 9: {
        if (tag == 74) {
         parse_LeadingUpIds:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_leadingupids()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->leadingupids(this->leadingupids_size() - 1).data(),
            this->leadingupids(this->leadingupids_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_LeadingUpIds;
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADUpsDownsCount)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADUpsDownsCount)
  return false;
#undef DO_
}

void ADUpsDownsCount::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  // optional int32 Ups = 1;
  if (this->ups() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->ups(), output);
  }

  // optional int32 Downs = 2;
  if (this->downs() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->downs(), output);
  }

  // optional int32 Equals = 3;
  if (this->equals() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->equals(), output);
  }

  // optional int32 PreUps = 4;
  if (this->preups() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->preups(), output);
  }

  // optional int32 PreDowns = 5;
  if (this->predowns() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->predowns(), output);
  }

  // optional int32 PreEquals = 6;
  if (this->preequals() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->preequals(), output);
  }

  // optional double UpsPercent = 7;
  if (this->upspercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->upspercent(), output);
  }

  // optional double PreUpsPercent = 8;
  if (this->preupspercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->preupspercent(), output);
  }

  // repeated string LeadingUpIds = 9;
  for (int i = 0; i < this->leadingupids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->leadingupids(i).data(), this->leadingupids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      9, this->leadingupids(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADUpsDownsCount)
}

::google::protobuf::uint8* ADUpsDownsCount::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  // optional int32 Ups = 1;
  if (this->ups() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->ups(), target);
  }

  // optional int32 Downs = 2;
  if (this->downs() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->downs(), target);
  }

  // optional int32 Equals = 3;
  if (this->equals() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->equals(), target);
  }

  // optional int32 PreUps = 4;
  if (this->preups() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->preups(), target);
  }

  // optional int32 PreDowns = 5;
  if (this->predowns() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->predowns(), target);
  }

  // optional int32 PreEquals = 6;
  if (this->preequals() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->preequals(), target);
  }

  // optional double UpsPercent = 7;
  if (this->upspercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->upspercent(), target);
  }

  // optional double PreUpsPercent = 8;
  if (this->preupspercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->preupspercent(), target);
  }

  // repeated string LeadingUpIds = 9;
  for (int i = 0; i < this->leadingupids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->leadingupids(i).data(), this->leadingupids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(9, this->leadingupids(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADUpsDownsCount)
  return target;
}

size_t ADUpsDownsCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  size_t total_size = 0;

  // optional int32 Ups = 1;
  if (this->ups() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ups());
  }

  // optional int32 Downs = 2;
  if (this->downs() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->downs());
  }

  // optional int32 Equals = 3;
  if (this->equals() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->equals());
  }

  // optional int32 PreUps = 4;
  if (this->preups() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preups());
  }

  // optional int32 PreDowns = 5;
  if (this->predowns() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->predowns());
  }

  // optional int32 PreEquals = 6;
  if (this->preequals() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preequals());
  }

  // optional double UpsPercent = 7;
  if (this->upspercent() != 0) {
    total_size += 1 + 8;
  }

  // optional double PreUpsPercent = 8;
  if (this->preupspercent() != 0) {
    total_size += 1 + 8;
  }

  // repeated string LeadingUpIds = 9;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->leadingupids_size());
  for (int i = 0; i < this->leadingupids_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->leadingupids(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADUpsDownsCount::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADUpsDownsCount* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADUpsDownsCount>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADUpsDownsCount)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADUpsDownsCount)
    UnsafeMergeFrom(*source);
  }
}

void ADUpsDownsCount::MergeFrom(const ADUpsDownsCount& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADUpsDownsCount::UnsafeMergeFrom(const ADUpsDownsCount& from) {
  GOOGLE_DCHECK(&from != this);
  leadingupids_.UnsafeMergeFrom(from.leadingupids_);
  if (from.ups() != 0) {
    set_ups(from.ups());
  }
  if (from.downs() != 0) {
    set_downs(from.downs());
  }
  if (from.equals() != 0) {
    set_equals(from.equals());
  }
  if (from.preups() != 0) {
    set_preups(from.preups());
  }
  if (from.predowns() != 0) {
    set_predowns(from.predowns());
  }
  if (from.preequals() != 0) {
    set_preequals(from.preequals());
  }
  if (from.upspercent() != 0) {
    set_upspercent(from.upspercent());
  }
  if (from.preupspercent() != 0) {
    set_preupspercent(from.preupspercent());
  }
}

void ADUpsDownsCount::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADUpsDownsCount::CopyFrom(const ADUpsDownsCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsCount)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADUpsDownsCount::IsInitialized() const {

  return true;
}

void ADUpsDownsCount::Swap(ADUpsDownsCount* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADUpsDownsCount::InternalSwap(ADUpsDownsCount* other) {
  std::swap(ups_, other->ups_);
  std::swap(downs_, other->downs_);
  std::swap(equals_, other->equals_);
  std::swap(preups_, other->preups_);
  std::swap(predowns_, other->predowns_);
  std::swap(preequals_, other->preequals_);
  std::swap(upspercent_, other->upspercent_);
  std::swap(preupspercent_, other->preupspercent_);
  leadingupids_.UnsafeArenaSwap(&other->leadingupids_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADUpsDownsCount::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADUpsDownsCount_descriptor_;
  metadata.reflection = ADUpsDownsCount_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADUpsDownsCount

// optional int32 Ups = 1;
void ADUpsDownsCount::clear_ups() {
  ups_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::ups() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Ups)
  return ups_;
}
void ADUpsDownsCount::set_ups(::google::protobuf::int32 value) {
  
  ups_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Ups)
}

// optional int32 Downs = 2;
void ADUpsDownsCount::clear_downs() {
  downs_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::downs() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Downs)
  return downs_;
}
void ADUpsDownsCount::set_downs(::google::protobuf::int32 value) {
  
  downs_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Downs)
}

// optional int32 Equals = 3;
void ADUpsDownsCount::clear_equals() {
  equals_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::equals() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Equals)
  return equals_;
}
void ADUpsDownsCount::set_equals(::google::protobuf::int32 value) {
  
  equals_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Equals)
}

// optional int32 PreUps = 4;
void ADUpsDownsCount::clear_preups() {
  preups_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::preups() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUps)
  return preups_;
}
void ADUpsDownsCount::set_preups(::google::protobuf::int32 value) {
  
  preups_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUps)
}

// optional int32 PreDowns = 5;
void ADUpsDownsCount::clear_predowns() {
  predowns_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::predowns() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreDowns)
  return predowns_;
}
void ADUpsDownsCount::set_predowns(::google::protobuf::int32 value) {
  
  predowns_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreDowns)
}

// optional int32 PreEquals = 6;
void ADUpsDownsCount::clear_preequals() {
  preequals_ = 0;
}
::google::protobuf::int32 ADUpsDownsCount::preequals() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreEquals)
  return preequals_;
}
void ADUpsDownsCount::set_preequals(::google::protobuf::int32 value) {
  
  preequals_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreEquals)
}

// optional double UpsPercent = 7;
void ADUpsDownsCount::clear_upspercent() {
  upspercent_ = 0;
}
double ADUpsDownsCount::upspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.UpsPercent)
  return upspercent_;
}
void ADUpsDownsCount::set_upspercent(double value) {
  
  upspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.UpsPercent)
}

// optional double PreUpsPercent = 8;
void ADUpsDownsCount::clear_preupspercent() {
  preupspercent_ = 0;
}
double ADUpsDownsCount::preupspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUpsPercent)
  return preupspercent_;
}
void ADUpsDownsCount::set_preupspercent(double value) {
  
  preupspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUpsPercent)
}

// repeated string LeadingUpIds = 9;
int ADUpsDownsCount::leadingupids_size() const {
  return leadingupids_.size();
}
void ADUpsDownsCount::clear_leadingupids() {
  leadingupids_.Clear();
}
const ::std::string& ADUpsDownsCount::leadingupids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Get(index);
}
::std::string* ADUpsDownsCount::mutable_leadingupids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Mutable(index);
}
void ADUpsDownsCount::set_leadingupids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  leadingupids_.Mutable(index)->assign(value);
}
void ADUpsDownsCount::set_leadingupids(int index, const char* value) {
  leadingupids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
void ADUpsDownsCount::set_leadingupids(int index, const char* value, size_t size) {
  leadingupids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
::std::string* ADUpsDownsCount::add_leadingupids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Add();
}
void ADUpsDownsCount::add_leadingupids(const ::std::string& value) {
  leadingupids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
void ADUpsDownsCount::add_leadingupids(const char* value) {
  leadingupids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
void ADUpsDownsCount::add_leadingupids(const char* value, size_t size) {
  leadingupids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
ADUpsDownsCount::leadingupids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
ADUpsDownsCount::mutable_leadingupids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return &leadingupids_;
}

inline const ADUpsDownsCount* ADUpsDownsCount::internal_default_instance() {
  return &ADUpsDownsCount_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADUpsDownsLimitCount::kNoReachedLimitPxFieldNumber;
const int ADUpsDownsLimitCount::kUpLimitsFieldNumber;
const int ADUpsDownsLimitCount::kDownLimitsFieldNumber;
const int ADUpsDownsLimitCount::kPreNoReachedLimitPxFieldNumber;
const int ADUpsDownsLimitCount::kPreUpLimitsFieldNumber;
const int ADUpsDownsLimitCount::kPreDownLimitsFieldNumber;
const int ADUpsDownsLimitCount::kPreUpLimitsAverageChangePercentFieldNumber;
const int ADUpsDownsLimitCount::kUpLimitsPercentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADUpsDownsLimitCount::ADUpsDownsLimitCount()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
}

void ADUpsDownsLimitCount::InitAsDefaultInstance() {
}

ADUpsDownsLimitCount::ADUpsDownsLimitCount(const ADUpsDownsLimitCount& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
}

void ADUpsDownsLimitCount::SharedCtor() {
  ::memset(&noreachedlimitpx_, 0, reinterpret_cast<char*>(&uplimitspercent_) -
    reinterpret_cast<char*>(&noreachedlimitpx_) + sizeof(uplimitspercent_));
  _cached_size_ = 0;
}

ADUpsDownsLimitCount::~ADUpsDownsLimitCount() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  SharedDtor();
}

void ADUpsDownsLimitCount::SharedDtor() {
}

void ADUpsDownsLimitCount::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADUpsDownsLimitCount::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADUpsDownsLimitCount_descriptor_;
}

const ADUpsDownsLimitCount& ADUpsDownsLimitCount::default_instance() {
  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsLimitCount> ADUpsDownsLimitCount_default_instance_;

ADUpsDownsLimitCount* ADUpsDownsLimitCount::New(::google::protobuf::Arena* arena) const {
  ADUpsDownsLimitCount* n = new ADUpsDownsLimitCount;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADUpsDownsLimitCount::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADUpsDownsLimitCount, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADUpsDownsLimitCount*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(noreachedlimitpx_, uplimitspercent_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADUpsDownsLimitCount::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 NoReachedLimitPx = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &noreachedlimitpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_UpLimits;
        break;
      }

      // optional int32 UpLimits = 2;
      case 2: {
        if (tag == 16) {
         parse_UpLimits:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &uplimits_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_DownLimits;
        break;
      }

      // optional int32 DownLimits = 3;
      case 3: {
        if (tag == 24) {
         parse_DownLimits:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &downlimits_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_PreNoReachedLimitPx;
        break;
      }

      // optional int32 PreNoReachedLimitPx = 4;
      case 4: {
        if (tag == 32) {
         parse_PreNoReachedLimitPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &prenoreachedlimitpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_PreUpLimits;
        break;
      }

      // optional int32 PreUpLimits = 5;
      case 5: {
        if (tag == 40) {
         parse_PreUpLimits:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preuplimits_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_PreDownLimits;
        break;
      }

      // optional int32 PreDownLimits = 6;
      case 6: {
        if (tag == 48) {
         parse_PreDownLimits:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &predownlimits_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_PreUpLimitsAverageChangePercent;
        break;
      }

      // optional double PreUpLimitsAverageChangePercent = 7;
      case 7: {
        if (tag == 57) {
         parse_PreUpLimitsAverageChangePercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preuplimitsaveragechangepercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_UpLimitsPercent;
        break;
      }

      // optional double UpLimitsPercent = 8;
      case 8: {
        if (tag == 65) {
         parse_UpLimitsPercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &uplimitspercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  return false;
#undef DO_
}

void ADUpsDownsLimitCount::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  // optional int32 NoReachedLimitPx = 1;
  if (this->noreachedlimitpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->noreachedlimitpx(), output);
  }

  // optional int32 UpLimits = 2;
  if (this->uplimits() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->uplimits(), output);
  }

  // optional int32 DownLimits = 3;
  if (this->downlimits() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->downlimits(), output);
  }

  // optional int32 PreNoReachedLimitPx = 4;
  if (this->prenoreachedlimitpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->prenoreachedlimitpx(), output);
  }

  // optional int32 PreUpLimits = 5;
  if (this->preuplimits() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->preuplimits(), output);
  }

  // optional int32 PreDownLimits = 6;
  if (this->predownlimits() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->predownlimits(), output);
  }

  // optional double PreUpLimitsAverageChangePercent = 7;
  if (this->preuplimitsaveragechangepercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->preuplimitsaveragechangepercent(), output);
  }

  // optional double UpLimitsPercent = 8;
  if (this->uplimitspercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->uplimitspercent(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
}

::google::protobuf::uint8* ADUpsDownsLimitCount::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  // optional int32 NoReachedLimitPx = 1;
  if (this->noreachedlimitpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->noreachedlimitpx(), target);
  }

  // optional int32 UpLimits = 2;
  if (this->uplimits() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->uplimits(), target);
  }

  // optional int32 DownLimits = 3;
  if (this->downlimits() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->downlimits(), target);
  }

  // optional int32 PreNoReachedLimitPx = 4;
  if (this->prenoreachedlimitpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->prenoreachedlimitpx(), target);
  }

  // optional int32 PreUpLimits = 5;
  if (this->preuplimits() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->preuplimits(), target);
  }

  // optional int32 PreDownLimits = 6;
  if (this->predownlimits() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->predownlimits(), target);
  }

  // optional double PreUpLimitsAverageChangePercent = 7;
  if (this->preuplimitsaveragechangepercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->preuplimitsaveragechangepercent(), target);
  }

  // optional double UpLimitsPercent = 8;
  if (this->uplimitspercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->uplimitspercent(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  return target;
}

size_t ADUpsDownsLimitCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  size_t total_size = 0;

  // optional int32 NoReachedLimitPx = 1;
  if (this->noreachedlimitpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->noreachedlimitpx());
  }

  // optional int32 UpLimits = 2;
  if (this->uplimits() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->uplimits());
  }

  // optional int32 DownLimits = 3;
  if (this->downlimits() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->downlimits());
  }

  // optional int32 PreNoReachedLimitPx = 4;
  if (this->prenoreachedlimitpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->prenoreachedlimitpx());
  }

  // optional int32 PreUpLimits = 5;
  if (this->preuplimits() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preuplimits());
  }

  // optional int32 PreDownLimits = 6;
  if (this->predownlimits() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->predownlimits());
  }

  // optional double PreUpLimitsAverageChangePercent = 7;
  if (this->preuplimitsaveragechangepercent() != 0) {
    total_size += 1 + 8;
  }

  // optional double UpLimitsPercent = 8;
  if (this->uplimitspercent() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADUpsDownsLimitCount::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADUpsDownsLimitCount* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADUpsDownsLimitCount>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
    UnsafeMergeFrom(*source);
  }
}

void ADUpsDownsLimitCount::MergeFrom(const ADUpsDownsLimitCount& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADUpsDownsLimitCount::UnsafeMergeFrom(const ADUpsDownsLimitCount& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.noreachedlimitpx() != 0) {
    set_noreachedlimitpx(from.noreachedlimitpx());
  }
  if (from.uplimits() != 0) {
    set_uplimits(from.uplimits());
  }
  if (from.downlimits() != 0) {
    set_downlimits(from.downlimits());
  }
  if (from.prenoreachedlimitpx() != 0) {
    set_prenoreachedlimitpx(from.prenoreachedlimitpx());
  }
  if (from.preuplimits() != 0) {
    set_preuplimits(from.preuplimits());
  }
  if (from.predownlimits() != 0) {
    set_predownlimits(from.predownlimits());
  }
  if (from.preuplimitsaveragechangepercent() != 0) {
    set_preuplimitsaveragechangepercent(from.preuplimitsaveragechangepercent());
  }
  if (from.uplimitspercent() != 0) {
    set_uplimitspercent(from.uplimitspercent());
  }
}

void ADUpsDownsLimitCount::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADUpsDownsLimitCount::CopyFrom(const ADUpsDownsLimitCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADUpsDownsLimitCount::IsInitialized() const {

  return true;
}

void ADUpsDownsLimitCount::Swap(ADUpsDownsLimitCount* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADUpsDownsLimitCount::InternalSwap(ADUpsDownsLimitCount* other) {
  std::swap(noreachedlimitpx_, other->noreachedlimitpx_);
  std::swap(uplimits_, other->uplimits_);
  std::swap(downlimits_, other->downlimits_);
  std::swap(prenoreachedlimitpx_, other->prenoreachedlimitpx_);
  std::swap(preuplimits_, other->preuplimits_);
  std::swap(predownlimits_, other->predownlimits_);
  std::swap(preuplimitsaveragechangepercent_, other->preuplimitsaveragechangepercent_);
  std::swap(uplimitspercent_, other->uplimitspercent_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADUpsDownsLimitCount::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADUpsDownsLimitCount_descriptor_;
  metadata.reflection = ADUpsDownsLimitCount_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADUpsDownsLimitCount

// optional int32 NoReachedLimitPx = 1;
void ADUpsDownsLimitCount::clear_noreachedlimitpx() {
  noreachedlimitpx_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::noreachedlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.NoReachedLimitPx)
  return noreachedlimitpx_;
}
void ADUpsDownsLimitCount::set_noreachedlimitpx(::google::protobuf::int32 value) {
  
  noreachedlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.NoReachedLimitPx)
}

// optional int32 UpLimits = 2;
void ADUpsDownsLimitCount::clear_uplimits() {
  uplimits_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::uplimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimits)
  return uplimits_;
}
void ADUpsDownsLimitCount::set_uplimits(::google::protobuf::int32 value) {
  
  uplimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimits)
}

// optional int32 DownLimits = 3;
void ADUpsDownsLimitCount::clear_downlimits() {
  downlimits_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::downlimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.DownLimits)
  return downlimits_;
}
void ADUpsDownsLimitCount::set_downlimits(::google::protobuf::int32 value) {
  
  downlimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.DownLimits)
}

// optional int32 PreNoReachedLimitPx = 4;
void ADUpsDownsLimitCount::clear_prenoreachedlimitpx() {
  prenoreachedlimitpx_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::prenoreachedlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreNoReachedLimitPx)
  return prenoreachedlimitpx_;
}
void ADUpsDownsLimitCount::set_prenoreachedlimitpx(::google::protobuf::int32 value) {
  
  prenoreachedlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreNoReachedLimitPx)
}

// optional int32 PreUpLimits = 5;
void ADUpsDownsLimitCount::clear_preuplimits() {
  preuplimits_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::preuplimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimits)
  return preuplimits_;
}
void ADUpsDownsLimitCount::set_preuplimits(::google::protobuf::int32 value) {
  
  preuplimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimits)
}

// optional int32 PreDownLimits = 6;
void ADUpsDownsLimitCount::clear_predownlimits() {
  predownlimits_ = 0;
}
::google::protobuf::int32 ADUpsDownsLimitCount::predownlimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreDownLimits)
  return predownlimits_;
}
void ADUpsDownsLimitCount::set_predownlimits(::google::protobuf::int32 value) {
  
  predownlimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreDownLimits)
}

// optional double PreUpLimitsAverageChangePercent = 7;
void ADUpsDownsLimitCount::clear_preuplimitsaveragechangepercent() {
  preuplimitsaveragechangepercent_ = 0;
}
double ADUpsDownsLimitCount::preuplimitsaveragechangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimitsAverageChangePercent)
  return preuplimitsaveragechangepercent_;
}
void ADUpsDownsLimitCount::set_preuplimitsaveragechangepercent(double value) {
  
  preuplimitsaveragechangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimitsAverageChangePercent)
}

// optional double UpLimitsPercent = 8;
void ADUpsDownsLimitCount::clear_uplimitspercent() {
  uplimitspercent_ = 0;
}
double ADUpsDownsLimitCount::uplimitspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimitsPercent)
  return uplimitspercent_;
}
void ADUpsDownsLimitCount::set_uplimitspercent(double value) {
  
  uplimitspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimitsPercent)
}

inline const ADUpsDownsLimitCount* ADUpsDownsLimitCount::internal_default_instance() {
  return &ADUpsDownsLimitCount_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADUpsDownsPartitionDetail::kNumbersFieldNumber;
const int ADUpsDownsPartitionDetail::kPartitionChangePercentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADUpsDownsPartitionDetail::ADUpsDownsPartitionDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
}

void ADUpsDownsPartitionDetail::InitAsDefaultInstance() {
}

ADUpsDownsPartitionDetail::ADUpsDownsPartitionDetail(const ADUpsDownsPartitionDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
}

void ADUpsDownsPartitionDetail::SharedCtor() {
  ::memset(&numbers_, 0, reinterpret_cast<char*>(&partitionchangepercent_) -
    reinterpret_cast<char*>(&numbers_) + sizeof(partitionchangepercent_));
  _cached_size_ = 0;
}

ADUpsDownsPartitionDetail::~ADUpsDownsPartitionDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  SharedDtor();
}

void ADUpsDownsPartitionDetail::SharedDtor() {
}

void ADUpsDownsPartitionDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADUpsDownsPartitionDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADUpsDownsPartitionDetail_descriptor_;
}

const ADUpsDownsPartitionDetail& ADUpsDownsPartitionDetail::default_instance() {
  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsPartitionDetail> ADUpsDownsPartitionDetail_default_instance_;

ADUpsDownsPartitionDetail* ADUpsDownsPartitionDetail::New(::google::protobuf::Arena* arena) const {
  ADUpsDownsPartitionDetail* n = new ADUpsDownsPartitionDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADUpsDownsPartitionDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADUpsDownsPartitionDetail, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADUpsDownsPartitionDetail*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(numbers_, partitionchangepercent_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADUpsDownsPartitionDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Numbers = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numbers_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_PartitionChangePercent;
        break;
      }

      // optional int32 PartitionChangePercent = 2;
      case 2: {
        if (tag == 16) {
         parse_PartitionChangePercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &partitionchangepercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  return false;
#undef DO_
}

void ADUpsDownsPartitionDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  // optional int32 Numbers = 1;
  if (this->numbers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->numbers(), output);
  }

  // optional int32 PartitionChangePercent = 2;
  if (this->partitionchangepercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->partitionchangepercent(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
}

::google::protobuf::uint8* ADUpsDownsPartitionDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  // optional int32 Numbers = 1;
  if (this->numbers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->numbers(), target);
  }

  // optional int32 PartitionChangePercent = 2;
  if (this->partitionchangepercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->partitionchangepercent(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  return target;
}

size_t ADUpsDownsPartitionDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  size_t total_size = 0;

  // optional int32 Numbers = 1;
  if (this->numbers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numbers());
  }

  // optional int32 PartitionChangePercent = 2;
  if (this->partitionchangepercent() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->partitionchangepercent());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADUpsDownsPartitionDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADUpsDownsPartitionDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADUpsDownsPartitionDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
    UnsafeMergeFrom(*source);
  }
}

void ADUpsDownsPartitionDetail::MergeFrom(const ADUpsDownsPartitionDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADUpsDownsPartitionDetail::UnsafeMergeFrom(const ADUpsDownsPartitionDetail& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.numbers() != 0) {
    set_numbers(from.numbers());
  }
  if (from.partitionchangepercent() != 0) {
    set_partitionchangepercent(from.partitionchangepercent());
  }
}

void ADUpsDownsPartitionDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADUpsDownsPartitionDetail::CopyFrom(const ADUpsDownsPartitionDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADUpsDownsPartitionDetail::IsInitialized() const {

  return true;
}

void ADUpsDownsPartitionDetail::Swap(ADUpsDownsPartitionDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADUpsDownsPartitionDetail::InternalSwap(ADUpsDownsPartitionDetail* other) {
  std::swap(numbers_, other->numbers_);
  std::swap(partitionchangepercent_, other->partitionchangepercent_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADUpsDownsPartitionDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADUpsDownsPartitionDetail_descriptor_;
  metadata.reflection = ADUpsDownsPartitionDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADUpsDownsPartitionDetail

// optional int32 Numbers = 1;
void ADUpsDownsPartitionDetail::clear_numbers() {
  numbers_ = 0;
}
::google::protobuf::int32 ADUpsDownsPartitionDetail::numbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.Numbers)
  return numbers_;
}
void ADUpsDownsPartitionDetail::set_numbers(::google::protobuf::int32 value) {
  
  numbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.Numbers)
}

// optional int32 PartitionChangePercent = 2;
void ADUpsDownsPartitionDetail::clear_partitionchangepercent() {
  partitionchangepercent_ = 0;
}
::google::protobuf::int32 ADUpsDownsPartitionDetail::partitionchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.PartitionChangePercent)
  return partitionchangepercent_;
}
void ADUpsDownsPartitionDetail::set_partitionchangepercent(::google::protobuf::int32 value) {
  
  partitionchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.PartitionChangePercent)
}

inline const ADUpsDownsPartitionDetail* ADUpsDownsPartitionDetail::internal_default_instance() {
  return &ADUpsDownsPartitionDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
