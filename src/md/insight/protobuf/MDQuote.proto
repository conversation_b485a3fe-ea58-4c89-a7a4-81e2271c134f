syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDQuote {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  double BidPrice = 7;
  double AskPrice = 8;
  double BidSize = 9;
  double AskSize = 10;
  int32 DataMultiplePowerOf10 = 11;
  int64 MessageNumber = 100;
}
