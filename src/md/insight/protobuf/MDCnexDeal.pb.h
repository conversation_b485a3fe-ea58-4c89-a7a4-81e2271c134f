// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCnexDeal.proto

#ifndef PROTOBUF_MDCnexDeal_2eproto__INCLUDED
#define PROTOBUF_MDCnexDeal_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCnexDeal_2eproto();
void protobuf_InitDefaults_MDCnexDeal_2eproto();
void protobuf_AssignDesc_MDCnexDeal_2eproto();
void protobuf_ShutdownFile_MDCnexDeal_2eproto();

class MDCnexDeal;

// ===================================================================

class MDCnexDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCnexDeal) */ {
 public:
  MDCnexDeal();
  virtual ~MDCnexDeal();

  MDCnexDeal(const MDCnexDeal& from);

  inline MDCnexDeal& operator=(const MDCnexDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCnexDeal& default_instance();

  static const MDCnexDeal* internal_default_instance();

  void Swap(MDCnexDeal* other);

  // implements Message ----------------------------------------------

  inline MDCnexDeal* New() const { return New(NULL); }

  MDCnexDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCnexDeal& from);
  void MergeFrom(const MDCnexDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCnexDeal* other);
  void UnsafeMergeFrom(const MDCnexDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 CnexDataType = 7;
  void clear_cnexdatatype();
  static const int kCnexDataTypeFieldNumber = 7;
  ::google::protobuf::int32 cnexdatatype() const;
  void set_cnexdatatype(::google::protobuf::int32 value);

  // optional int64 IssueDataTime = 8;
  void clear_issuedatatime();
  static const int kIssueDataTimeFieldNumber = 8;
  ::google::protobuf::int64 issuedatatime() const;
  void set_issuedatatime(::google::protobuf::int64 value);

  // optional string DealID = 9;
  void clear_dealid();
  static const int kDealIDFieldNumber = 9;
  const ::std::string& dealid() const;
  void set_dealid(const ::std::string& value);
  void set_dealid(const char* value);
  void set_dealid(const char* value, size_t size);
  ::std::string* mutable_dealid();
  ::std::string* release_dealid();
  void set_allocated_dealid(::std::string* dealid);

  // optional int32 DealType = 10;
  void clear_dealtype();
  static const int kDealTypeFieldNumber = 10;
  ::google::protobuf::int32 dealtype() const;
  void set_dealtype(::google::protobuf::int32 value);

  // optional int64 DealPrice = 11;
  void clear_dealprice();
  static const int kDealPriceFieldNumber = 11;
  ::google::protobuf::int64 dealprice() const;
  void set_dealprice(::google::protobuf::int64 value);

  // optional int64 DealSize = 12;
  void clear_dealsize();
  static const int kDealSizeFieldNumber = 12;
  ::google::protobuf::int64 dealsize() const;
  void set_dealsize(::google::protobuf::int64 value);

  // optional int64 Yield = 13;
  void clear_yield();
  static const int kYieldFieldNumber = 13;
  ::google::protobuf::int64 yield() const;
  void set_yield(::google::protobuf::int64 value);

  // optional int32 DealDate = 14;
  void clear_dealdate();
  static const int kDealDateFieldNumber = 14;
  ::google::protobuf::int32 dealdate() const;
  void set_dealdate(::google::protobuf::int32 value);

  // optional int32 DealTime = 15;
  void clear_dealtime();
  static const int kDealTimeFieldNumber = 15;
  ::google::protobuf::int32 dealtime() const;
  void set_dealtime(::google::protobuf::int32 value);

  // optional int32 QuoteStatus = 16;
  void clear_quotestatus();
  static const int kQuoteStatusFieldNumber = 16;
  ::google::protobuf::int32 quotestatus() const;
  void set_quotestatus(::google::protobuf::int32 value);

  // optional int32 QuotePriceType = 17;
  void clear_quotepricetype();
  static const int kQuotePriceTypeFieldNumber = 17;
  ::google::protobuf::int32 quotepricetype() const;
  void set_quotepricetype(::google::protobuf::int32 value);

  // optional int32 MaturityDate = 18;
  void clear_maturitydate();
  static const int kMaturityDateFieldNumber = 18;
  ::google::protobuf::int32 maturitydate() const;
  void set_maturitydate(::google::protobuf::int32 value);

  // optional string CnexSecurityType = 19;
  void clear_cnexsecuritytype();
  static const int kCnexSecurityTypeFieldNumber = 19;
  const ::std::string& cnexsecuritytype() const;
  void set_cnexsecuritytype(const ::std::string& value);
  void set_cnexsecuritytype(const char* value);
  void set_cnexsecuritytype(const char* value, size_t size);
  ::std::string* mutable_cnexsecuritytype();
  ::std::string* release_cnexsecuritytype();
  void set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype);

  // optional string CreditRating = 20;
  void clear_creditrating();
  static const int kCreditRatingFieldNumber = 20;
  const ::std::string& creditrating() const;
  void set_creditrating(const ::std::string& value);
  void set_creditrating(const char* value);
  void set_creditrating(const char* value, size_t size);
  ::std::string* mutable_creditrating();
  ::std::string* release_creditrating();
  void set_allocated_creditrating(::std::string* creditrating);

  // optional string Text = 21;
  void clear_text();
  static const int kTextFieldNumber = 21;
  const ::std::string& text() const;
  void set_text(const ::std::string& value);
  void set_text(const char* value);
  void set_text(const char* value, size_t size);
  ::std::string* mutable_text();
  ::std::string* release_text();
  void set_allocated_text(::std::string* text);

  // optional int32 StatusValue = 22;
  void clear_statusvalue();
  static const int kStatusValueFieldNumber = 22;
  ::google::protobuf::int32 statusvalue() const;
  void set_statusvalue(::google::protobuf::int32 value);

  // optional string ExerciseFlag = 23;
  void clear_exerciseflag();
  static const int kExerciseFlagFieldNumber = 23;
  const ::std::string& exerciseflag() const;
  void set_exerciseflag(const ::std::string& value);
  void set_exerciseflag(const char* value);
  void set_exerciseflag(const char* value, size_t size);
  ::std::string* mutable_exerciseflag();
  ::std::string* release_exerciseflag();
  void set_allocated_exerciseflag(::std::string* exerciseflag);

  // optional string Tenor = 24;
  void clear_tenor();
  static const int kTenorFieldNumber = 24;
  const ::std::string& tenor() const;
  void set_tenor(const ::std::string& value);
  void set_tenor(const char* value);
  void set_tenor(const char* value, size_t size);
  ::std::string* mutable_tenor();
  ::std::string* release_tenor();
  void set_allocated_tenor(::std::string* tenor);

  // optional int32 WorkBench = 25;
  void clear_workbench();
  static const int kWorkBenchFieldNumber = 25;
  ::google::protobuf::int32 workbench() const;
  void set_workbench(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 26;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 26;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCnexDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr dealid_;
  ::google::protobuf::internal::ArenaStringPtr cnexsecuritytype_;
  ::google::protobuf::internal::ArenaStringPtr creditrating_;
  ::google::protobuf::internal::ArenaStringPtr text_;
  ::google::protobuf::internal::ArenaStringPtr exerciseflag_;
  ::google::protobuf::internal::ArenaStringPtr tenor_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 issuedatatime_;
  ::google::protobuf::int32 cnexdatatype_;
  ::google::protobuf::int32 dealtype_;
  ::google::protobuf::int64 dealprice_;
  ::google::protobuf::int64 dealsize_;
  ::google::protobuf::int64 yield_;
  ::google::protobuf::int32 dealdate_;
  ::google::protobuf::int32 dealtime_;
  ::google::protobuf::int32 quotestatus_;
  ::google::protobuf::int32 quotepricetype_;
  ::google::protobuf::int32 maturitydate_;
  ::google::protobuf::int32 statusvalue_;
  ::google::protobuf::int32 workbench_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCnexDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCnexDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCnexDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCnexDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCnexDeal> MDCnexDeal_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCnexDeal

// optional string HTSCSecurityID = 1;
inline void MDCnexDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
inline void MDCnexDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
inline void MDCnexDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
inline ::std::string* MDCnexDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCnexDeal::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MDDate)
  return mddate_;
}
inline void MDCnexDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCnexDeal::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MDTime)
  return mdtime_;
}
inline void MDCnexDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCnexDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DataTimestamp)
  return datatimestamp_;
}
inline void MDCnexDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCnexDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCnexDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCnexDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCnexDeal::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCnexDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCnexDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.securityType)
}

// optional int32 CnexDataType = 7;
inline void MDCnexDeal::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CnexDataType)
  return cnexdatatype_;
}
inline void MDCnexDeal::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CnexDataType)
}

// optional int64 IssueDataTime = 8;
inline void MDCnexDeal::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexDeal::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.IssueDataTime)
  return issuedatatime_;
}
inline void MDCnexDeal::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.IssueDataTime)
}

// optional string DealID = 9;
inline void MDCnexDeal::clear_dealid() {
  dealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::dealid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  return dealid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_dealid(const ::std::string& value) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
inline void MDCnexDeal::set_dealid(const char* value) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
inline void MDCnexDeal::set_dealid(const char* value, size_t size) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
inline ::std::string* MDCnexDeal::mutable_dealid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  return dealid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_dealid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  
  return dealid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_dealid(::std::string* dealid) {
  if (dealid != NULL) {
    
  } else {
    
  }
  dealid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}

// optional int32 DealType = 10;
inline void MDCnexDeal::clear_dealtype() {
  dealtype_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::dealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealType)
  return dealtype_;
}
inline void MDCnexDeal::set_dealtype(::google::protobuf::int32 value) {
  
  dealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealType)
}

// optional int64 DealPrice = 11;
inline void MDCnexDeal::clear_dealprice() {
  dealprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexDeal::dealprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealPrice)
  return dealprice_;
}
inline void MDCnexDeal::set_dealprice(::google::protobuf::int64 value) {
  
  dealprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealPrice)
}

// optional int64 DealSize = 12;
inline void MDCnexDeal::clear_dealsize() {
  dealsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexDeal::dealsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealSize)
  return dealsize_;
}
inline void MDCnexDeal::set_dealsize(::google::protobuf::int64 value) {
  
  dealsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealSize)
}

// optional int64 Yield = 13;
inline void MDCnexDeal::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexDeal::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Yield)
  return yield_;
}
inline void MDCnexDeal::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Yield)
}

// optional int32 DealDate = 14;
inline void MDCnexDeal::clear_dealdate() {
  dealdate_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::dealdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealDate)
  return dealdate_;
}
inline void MDCnexDeal::set_dealdate(::google::protobuf::int32 value) {
  
  dealdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealDate)
}

// optional int32 DealTime = 15;
inline void MDCnexDeal::clear_dealtime() {
  dealtime_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::dealtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealTime)
  return dealtime_;
}
inline void MDCnexDeal::set_dealtime(::google::protobuf::int32 value) {
  
  dealtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealTime)
}

// optional int32 QuoteStatus = 16;
inline void MDCnexDeal::clear_quotestatus() {
  quotestatus_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.QuoteStatus)
  return quotestatus_;
}
inline void MDCnexDeal::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.QuoteStatus)
}

// optional int32 QuotePriceType = 17;
inline void MDCnexDeal::clear_quotepricetype() {
  quotepricetype_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.QuotePriceType)
  return quotepricetype_;
}
inline void MDCnexDeal::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.QuotePriceType)
}

// optional int32 MaturityDate = 18;
inline void MDCnexDeal::clear_maturitydate() {
  maturitydate_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MaturityDate)
  return maturitydate_;
}
inline void MDCnexDeal::set_maturitydate(::google::protobuf::int32 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MaturityDate)
}

// optional string CnexSecurityType = 19;
inline void MDCnexDeal::clear_cnexsecuritytype() {
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::cnexsecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  return cnexsecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_cnexsecuritytype(const ::std::string& value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
inline void MDCnexDeal::set_cnexsecuritytype(const char* value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
inline void MDCnexDeal::set_cnexsecuritytype(const char* value, size_t size) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
inline ::std::string* MDCnexDeal::mutable_cnexsecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  return cnexsecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_cnexsecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  
  return cnexsecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype) {
  if (cnexsecuritytype != NULL) {
    
  } else {
    
  }
  cnexsecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cnexsecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}

// optional string CreditRating = 20;
inline void MDCnexDeal::clear_creditrating() {
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::creditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  return creditrating_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_creditrating(const ::std::string& value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
inline void MDCnexDeal::set_creditrating(const char* value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
inline void MDCnexDeal::set_creditrating(const char* value, size_t size) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
inline ::std::string* MDCnexDeal::mutable_creditrating() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  return creditrating_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_creditrating() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  
  return creditrating_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_creditrating(::std::string* creditrating) {
  if (creditrating != NULL) {
    
  } else {
    
  }
  creditrating_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creditrating);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}

// optional string Text = 21;
inline void MDCnexDeal::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::text() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
inline void MDCnexDeal::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
inline void MDCnexDeal::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
inline ::std::string* MDCnexDeal::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_text() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}

// optional int32 StatusValue = 22;
inline void MDCnexDeal::clear_statusvalue() {
  statusvalue_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::statusvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.StatusValue)
  return statusvalue_;
}
inline void MDCnexDeal::set_statusvalue(::google::protobuf::int32 value) {
  
  statusvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.StatusValue)
}

// optional string ExerciseFlag = 23;
inline void MDCnexDeal::clear_exerciseflag() {
  exerciseflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::exerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  return exerciseflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_exerciseflag(const ::std::string& value) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
inline void MDCnexDeal::set_exerciseflag(const char* value) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
inline void MDCnexDeal::set_exerciseflag(const char* value, size_t size) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
inline ::std::string* MDCnexDeal::mutable_exerciseflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  return exerciseflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_exerciseflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  
  return exerciseflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_exerciseflag(::std::string* exerciseflag) {
  if (exerciseflag != NULL) {
    
  } else {
    
  }
  exerciseflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exerciseflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}

// optional string Tenor = 24;
inline void MDCnexDeal::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexDeal::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
inline void MDCnexDeal::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
inline void MDCnexDeal::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
inline ::std::string* MDCnexDeal::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexDeal::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexDeal::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}

// optional int32 WorkBench = 25;
inline void MDCnexDeal::clear_workbench() {
  workbench_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::workbench() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.WorkBench)
  return workbench_;
}
inline void MDCnexDeal::set_workbench(::google::protobuf::int32 value) {
  
  workbench_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.WorkBench)
}

// optional int32 DataMultiplePowerOf10 = 26;
inline void MDCnexDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCnexDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCnexDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DataMultiplePowerOf10)
}

inline const MDCnexDeal* MDCnexDeal::internal_default_instance() {
  return &MDCnexDeal_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCnexDeal_2eproto__INCLUDED
