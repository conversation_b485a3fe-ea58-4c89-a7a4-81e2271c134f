// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MessageBody.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MessageBody.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MessageBody_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MessageBody_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MessageBody_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MessageBody_2eproto() {
  protobuf_AddDesc_MessageBody_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MessageBody.proto");
  GOOGLE_CHECK(file != NULL);
  MessageBody_descriptor_ = file->message_type(0);
  static const int MessageBody_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, type_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, interactionid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, generalerrormessage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, loginrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, loginresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, servicediscoveryrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, servicediscoveryresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, mdsubscriberequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, mdsubscriberesponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, pushmarketdata_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, pushmarketdatastream_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, mdqueryrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, mdqueryresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackcontrolrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackcontrolresponse_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackstatusrequest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackstatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, playbackpayload_),
  };
  MessageBody_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MessageBody_descriptor_,
      MessageBody::internal_default_instance(),
      MessageBody_offsets_,
      -1,
      -1,
      -1,
      sizeof(MessageBody),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MessageBody, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MessageBody_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MessageBody_descriptor_, MessageBody::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MessageBody_2eproto() {
  MessageBody_default_instance_.Shutdown();
  delete MessageBody_reflection_;
}

void protobuf_InitDefaults_MessageBody_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_EMessageType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_Login_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ServiceDiscovery_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSubscribe_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MarketData_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDQuery_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDPlayback_2eproto();
  MessageBody_default_instance_.DefaultConstruct();
  MessageBody_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MessageBody_2eproto_once_);
void protobuf_InitDefaults_MessageBody_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MessageBody_2eproto_once_,
                 &protobuf_InitDefaults_MessageBody_2eproto_impl);
}
void protobuf_AddDesc_MessageBody_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MessageBody_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\021MessageBody.proto\022\032com.htsc.mdc.insigh"
    "t.model\032\022EMessageType.proto\032\013Login.proto"
    "\032\026ServiceDiscovery.proto\032\021MDSubscribe.pr"
    "oto\032\020MarketData.proto\032\031InsightErrorConte"
    "xt.proto\032\rMDQuery.proto\032\020MDPlayback.prot"
    "o\"\236\013\n\013MessageBody\0226\n\004type\030\001 \001(\0162(.com.ht"
    "sc.mdc.insight.model.EMessageType\022\025\n\rint"
    "eractionId\030\002 \001(\003\022L\n\023generalErrorMessage\030"
    "\n \001(\0132/.com.htsc.mdc.insight.model.Insig"
    "htErrorContext\022>\n\014loginRequest\030\013 \001(\0132(.c"
    "om.htsc.mdc.insight.model.LoginRequest\022@"
    "\n\rloginResponse\030\014 \001(\0132).com.htsc.mdc.ins"
    "ight.model.LoginResponse\022T\n\027serviceDisco"
    "veryRequest\030\r \001(\01323.com.htsc.mdc.insight"
    ".model.ServiceDiscoveryRequest\022V\n\030servic"
    "eDiscoveryResponse\030\016 \001(\01324.com.htsc.mdc."
    "insight.model.ServiceDiscoveryResponse\022J"
    "\n\022mdSubscribeRequest\030\017 \001(\0132..com.htsc.md"
    "c.insight.model.MDSubscribeRequest\022L\n\023md"
    "SubscribeResponse\030\020 \001(\0132/.com.htsc.mdc.i"
    "nsight.model.MDSubscribeResponse\022>\n\016push"
    "MarketData\030\024 \001(\0132&.com.htsc.mdc.insight."
    "model.MarketData\022J\n\024pushMarketDataStream"
    "\030\025 \001(\0132,.com.htsc.mdc.insight.model.Mark"
    "etDataStream\022B\n\016mdQueryRequest\030\036 \001(\0132*.c"
    "om.htsc.mdc.insight.model.MDQueryRequest"
    "\022D\n\017mdQueryResponse\030\037 \001(\0132+.com.htsc.mdc"
    ".insight.model.MDQueryResponse\022D\n\017playba"
    "ckRequest\030  \001(\0132+.com.htsc.mdc.insight.m"
    "odel.PlaybackRequest\022F\n\020playbackResponse"
    "\030! \001(\0132,.com.htsc.mdc.insight.model.Play"
    "backResponse\022R\n\026playbackControlRequest\030\""
    " \001(\01322.com.htsc.mdc.insight.model.Playba"
    "ckControlRequest\022T\n\027playbackControlRespo"
    "nse\030# \001(\01323.com.htsc.mdc.insight.model.P"
    "laybackControlResponse\022P\n\025playbackStatus"
    "Request\030$ \001(\01321.com.htsc.mdc.insight.mod"
    "el.PlaybackStatusRequest\022B\n\016playbackStat"
    "us\030% \001(\0132*.com.htsc.mdc.insight.model.Pl"
    "aybackStatus\022D\n\017playbackPayload\030& \001(\0132+."
    "com.htsc.mdc.insight.model.PlaybackPaylo"
    "adB3\n\032com.htsc.mdc.insight.modelB\020Messag"
    "eBodyProtoH\001\240\001\001b\006proto3", 1703);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MessageBody.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_EMessageType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_Login_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ServiceDiscovery_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSubscribe_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MarketData_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDQuery_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDPlayback_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MessageBody_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MessageBody_2eproto_once_);
void protobuf_AddDesc_MessageBody_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MessageBody_2eproto_once_,
                 &protobuf_AddDesc_MessageBody_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MessageBody_2eproto {
  StaticDescriptorInitializer_MessageBody_2eproto() {
    protobuf_AddDesc_MessageBody_2eproto();
  }
} static_descriptor_initializer_MessageBody_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MessageBody::kTypeFieldNumber;
const int MessageBody::kInteractionIdFieldNumber;
const int MessageBody::kGeneralErrorMessageFieldNumber;
const int MessageBody::kLoginRequestFieldNumber;
const int MessageBody::kLoginResponseFieldNumber;
const int MessageBody::kServiceDiscoveryRequestFieldNumber;
const int MessageBody::kServiceDiscoveryResponseFieldNumber;
const int MessageBody::kMdSubscribeRequestFieldNumber;
const int MessageBody::kMdSubscribeResponseFieldNumber;
const int MessageBody::kPushMarketDataFieldNumber;
const int MessageBody::kPushMarketDataStreamFieldNumber;
const int MessageBody::kMdQueryRequestFieldNumber;
const int MessageBody::kMdQueryResponseFieldNumber;
const int MessageBody::kPlaybackRequestFieldNumber;
const int MessageBody::kPlaybackResponseFieldNumber;
const int MessageBody::kPlaybackControlRequestFieldNumber;
const int MessageBody::kPlaybackControlResponseFieldNumber;
const int MessageBody::kPlaybackStatusRequestFieldNumber;
const int MessageBody::kPlaybackStatusFieldNumber;
const int MessageBody::kPlaybackPayloadFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MessageBody::MessageBody()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MessageBody_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MessageBody)
}

void MessageBody::InitAsDefaultInstance() {
  generalerrormessage_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
  loginrequest_ = const_cast< ::com::htsc::mdc::insight::model::LoginRequest*>(
      ::com::htsc::mdc::insight::model::LoginRequest::internal_default_instance());
  loginresponse_ = const_cast< ::com::htsc::mdc::insight::model::LoginResponse*>(
      ::com::htsc::mdc::insight::model::LoginResponse::internal_default_instance());
  servicediscoveryrequest_ = const_cast< ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest*>(
      ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest::internal_default_instance());
  servicediscoveryresponse_ = const_cast< ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse*>(
      ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse::internal_default_instance());
  mdsubscriberequest_ = const_cast< ::com::htsc::mdc::insight::model::MDSubscribeRequest*>(
      ::com::htsc::mdc::insight::model::MDSubscribeRequest::internal_default_instance());
  mdsubscriberesponse_ = const_cast< ::com::htsc::mdc::insight::model::MDSubscribeResponse*>(
      ::com::htsc::mdc::insight::model::MDSubscribeResponse::internal_default_instance());
  pushmarketdata_ = const_cast< ::com::htsc::mdc::insight::model::MarketData*>(
      ::com::htsc::mdc::insight::model::MarketData::internal_default_instance());
  pushmarketdatastream_ = const_cast< ::com::htsc::mdc::insight::model::MarketDataStream*>(
      ::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance());
  mdqueryrequest_ = const_cast< ::com::htsc::mdc::insight::model::MDQueryRequest*>(
      ::com::htsc::mdc::insight::model::MDQueryRequest::internal_default_instance());
  mdqueryresponse_ = const_cast< ::com::htsc::mdc::insight::model::MDQueryResponse*>(
      ::com::htsc::mdc::insight::model::MDQueryResponse::internal_default_instance());
  playbackrequest_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackRequest*>(
      ::com::htsc::mdc::insight::model::PlaybackRequest::internal_default_instance());
  playbackresponse_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackResponse*>(
      ::com::htsc::mdc::insight::model::PlaybackResponse::internal_default_instance());
  playbackcontrolrequest_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackControlRequest*>(
      ::com::htsc::mdc::insight::model::PlaybackControlRequest::internal_default_instance());
  playbackcontrolresponse_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackControlResponse*>(
      ::com::htsc::mdc::insight::model::PlaybackControlResponse::internal_default_instance());
  playbackstatusrequest_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackStatusRequest*>(
      ::com::htsc::mdc::insight::model::PlaybackStatusRequest::internal_default_instance());
  playbackstatus_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackStatus*>(
      ::com::htsc::mdc::insight::model::PlaybackStatus::internal_default_instance());
  playbackpayload_ = const_cast< ::com::htsc::mdc::insight::model::PlaybackPayload*>(
      ::com::htsc::mdc::insight::model::PlaybackPayload::internal_default_instance());
}

MessageBody::MessageBody(const MessageBody& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MessageBody)
}

void MessageBody::SharedCtor() {
  generalerrormessage_ = NULL;
  loginrequest_ = NULL;
  loginresponse_ = NULL;
  servicediscoveryrequest_ = NULL;
  servicediscoveryresponse_ = NULL;
  mdsubscriberequest_ = NULL;
  mdsubscriberesponse_ = NULL;
  pushmarketdata_ = NULL;
  pushmarketdatastream_ = NULL;
  mdqueryrequest_ = NULL;
  mdqueryresponse_ = NULL;
  playbackrequest_ = NULL;
  playbackresponse_ = NULL;
  playbackcontrolrequest_ = NULL;
  playbackcontrolresponse_ = NULL;
  playbackstatusrequest_ = NULL;
  playbackstatus_ = NULL;
  playbackpayload_ = NULL;
  ::memset(&interactionid_, 0, reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&interactionid_) + sizeof(type_));
  _cached_size_ = 0;
}

MessageBody::~MessageBody() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MessageBody)
  SharedDtor();
}

void MessageBody::SharedDtor() {
  if (this != &MessageBody_default_instance_.get()) {
    delete generalerrormessage_;
    delete loginrequest_;
    delete loginresponse_;
    delete servicediscoveryrequest_;
    delete servicediscoveryresponse_;
    delete mdsubscriberequest_;
    delete mdsubscriberesponse_;
    delete pushmarketdata_;
    delete pushmarketdatastream_;
    delete mdqueryrequest_;
    delete mdqueryresponse_;
    delete playbackrequest_;
    delete playbackresponse_;
    delete playbackcontrolrequest_;
    delete playbackcontrolresponse_;
    delete playbackstatusrequest_;
    delete playbackstatus_;
    delete playbackpayload_;
  }
}

void MessageBody::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MessageBody::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MessageBody_descriptor_;
}

const MessageBody& MessageBody::default_instance() {
  protobuf_InitDefaults_MessageBody_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MessageBody> MessageBody_default_instance_;

MessageBody* MessageBody::New(::google::protobuf::Arena* arena) const {
  MessageBody* n = new MessageBody;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MessageBody::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MessageBody)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MessageBody, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MessageBody*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(interactionid_, type_);
  if (GetArenaNoVirtual() == NULL && generalerrormessage_ != NULL) delete generalerrormessage_;
  generalerrormessage_ = NULL;
  if (GetArenaNoVirtual() == NULL && loginrequest_ != NULL) delete loginrequest_;
  loginrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && loginresponse_ != NULL) delete loginresponse_;
  loginresponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && servicediscoveryrequest_ != NULL) delete servicediscoveryrequest_;
  servicediscoveryrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && servicediscoveryresponse_ != NULL) delete servicediscoveryresponse_;
  servicediscoveryresponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdsubscriberequest_ != NULL) delete mdsubscriberequest_;
  mdsubscriberequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdsubscriberesponse_ != NULL) delete mdsubscriberesponse_;
  mdsubscriberesponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && pushmarketdata_ != NULL) delete pushmarketdata_;
  pushmarketdata_ = NULL;
  if (GetArenaNoVirtual() == NULL && pushmarketdatastream_ != NULL) delete pushmarketdatastream_;
  pushmarketdatastream_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdqueryrequest_ != NULL) delete mdqueryrequest_;
  mdqueryrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdqueryresponse_ != NULL) delete mdqueryresponse_;
  mdqueryresponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackrequest_ != NULL) delete playbackrequest_;
  playbackrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackresponse_ != NULL) delete playbackresponse_;
  playbackresponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackcontrolrequest_ != NULL) delete playbackcontrolrequest_;
  playbackcontrolrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackcontrolresponse_ != NULL) delete playbackcontrolresponse_;
  playbackcontrolresponse_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackstatusrequest_ != NULL) delete playbackstatusrequest_;
  playbackstatusrequest_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackstatus_ != NULL) delete playbackstatus_;
  playbackstatus_ = NULL;
  if (GetArenaNoVirtual() == NULL && playbackpayload_ != NULL) delete playbackpayload_;
  playbackpayload_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MessageBody::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MessageBody)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .com.htsc.mdc.insight.model.EMessageType type = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::com::htsc::mdc::insight::model::EMessageType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_interactionId;
        break;
      }

      // optional int64 interactionId = 2;
      case 2: {
        if (tag == 16) {
         parse_interactionId:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &interactionid_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_generalErrorMessage;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
      case 10: {
        if (tag == 82) {
         parse_generalErrorMessage:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_generalerrormessage()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_loginRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
      case 11: {
        if (tag == 90) {
         parse_loginRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_loginrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loginResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
      case 12: {
        if (tag == 98) {
         parse_loginResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_loginresponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_serviceDiscoveryRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
      case 13: {
        if (tag == 106) {
         parse_serviceDiscoveryRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_servicediscoveryrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_serviceDiscoveryResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
      case 14: {
        if (tag == 114) {
         parse_serviceDiscoveryResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_servicediscoveryresponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_mdSubscribeRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
      case 15: {
        if (tag == 122) {
         parse_mdSubscribeRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdsubscriberequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_mdSubscribeResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
      case 16: {
        if (tag == 130) {
         parse_mdSubscribeResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdsubscriberesponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_pushMarketData;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
      case 20: {
        if (tag == 162) {
         parse_pushMarketData:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_pushmarketdata()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_pushMarketDataStream;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
      case 21: {
        if (tag == 170) {
         parse_pushMarketDataStream:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_pushmarketdatastream()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_mdQueryRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
      case 30: {
        if (tag == 242) {
         parse_mdQueryRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdqueryrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_mdQueryResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
      case 31: {
        if (tag == 250) {
         parse_mdQueryResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdqueryresponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_playbackRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
      case 32: {
        if (tag == 258) {
         parse_playbackRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_playbackResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
      case 33: {
        if (tag == 266) {
         parse_playbackResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackresponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_playbackControlRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
      case 34: {
        if (tag == 274) {
         parse_playbackControlRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackcontrolrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_playbackControlResponse;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
      case 35: {
        if (tag == 282) {
         parse_playbackControlResponse:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackcontrolresponse()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_playbackStatusRequest;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
      case 36: {
        if (tag == 290) {
         parse_playbackStatusRequest:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackstatusrequest()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_playbackStatus;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
      case 37: {
        if (tag == 298) {
         parse_playbackStatus:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackstatus()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_playbackPayload;
        break;
      }

      // optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
      case 38: {
        if (tag == 306) {
         parse_playbackPayload:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_playbackpayload()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MessageBody)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MessageBody)
  return false;
#undef DO_
}

void MessageBody::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MessageBody)
  // optional .com.htsc.mdc.insight.model.EMessageType type = 1;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // optional int64 interactionId = 2;
  if (this->interactionid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->interactionid(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
  if (this->has_generalerrormessage()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->generalerrormessage_, output);
  }

  // optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
  if (this->has_loginrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->loginrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
  if (this->has_loginresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->loginresponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
  if (this->has_servicediscoveryrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->servicediscoveryrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
  if (this->has_servicediscoveryresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->servicediscoveryresponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
  if (this->has_mdsubscriberequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->mdsubscriberequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
  if (this->has_mdsubscriberesponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *this->mdsubscriberesponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
  if (this->has_pushmarketdata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->pushmarketdata_, output);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
  if (this->has_pushmarketdatastream()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, *this->pushmarketdatastream_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
  if (this->has_mdqueryrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      30, *this->mdqueryrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
  if (this->has_mdqueryresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, *this->mdqueryresponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
  if (this->has_playbackrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      32, *this->playbackrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
  if (this->has_playbackresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      33, *this->playbackresponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
  if (this->has_playbackcontrolrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      34, *this->playbackcontrolrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
  if (this->has_playbackcontrolresponse()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      35, *this->playbackcontrolresponse_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
  if (this->has_playbackstatusrequest()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      36, *this->playbackstatusrequest_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
  if (this->has_playbackstatus()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      37, *this->playbackstatus_, output);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
  if (this->has_playbackpayload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      38, *this->playbackpayload_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MessageBody)
}

::google::protobuf::uint8* MessageBody::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MessageBody)
  // optional .com.htsc.mdc.insight.model.EMessageType type = 1;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // optional int64 interactionId = 2;
  if (this->interactionid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->interactionid(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
  if (this->has_generalerrormessage()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->generalerrormessage_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
  if (this->has_loginrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->loginrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
  if (this->has_loginresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->loginresponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
  if (this->has_servicediscoveryrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->servicediscoveryrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
  if (this->has_servicediscoveryresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->servicediscoveryresponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
  if (this->has_mdsubscriberequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->mdsubscriberequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
  if (this->has_mdsubscriberesponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *this->mdsubscriberesponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
  if (this->has_pushmarketdata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->pushmarketdata_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
  if (this->has_pushmarketdatastream()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, *this->pushmarketdatastream_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
  if (this->has_mdqueryrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        30, *this->mdqueryrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
  if (this->has_mdqueryresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        31, *this->mdqueryresponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
  if (this->has_playbackrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        32, *this->playbackrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
  if (this->has_playbackresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        33, *this->playbackresponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
  if (this->has_playbackcontrolrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        34, *this->playbackcontrolrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
  if (this->has_playbackcontrolresponse()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        35, *this->playbackcontrolresponse_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
  if (this->has_playbackstatusrequest()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        36, *this->playbackstatusrequest_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
  if (this->has_playbackstatus()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        37, *this->playbackstatus_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
  if (this->has_playbackpayload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        38, *this->playbackpayload_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MessageBody)
  return target;
}

size_t MessageBody::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MessageBody)
  size_t total_size = 0;

  // optional .com.htsc.mdc.insight.model.EMessageType type = 1;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  // optional int64 interactionId = 2;
  if (this->interactionid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->interactionid());
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
  if (this->has_generalerrormessage()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->generalerrormessage_);
  }

  // optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
  if (this->has_loginrequest()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->loginrequest_);
  }

  // optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
  if (this->has_loginresponse()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->loginresponse_);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
  if (this->has_servicediscoveryrequest()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->servicediscoveryrequest_);
  }

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
  if (this->has_servicediscoveryresponse()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->servicediscoveryresponse_);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
  if (this->has_mdsubscriberequest()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdsubscriberequest_);
  }

  // optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
  if (this->has_mdsubscriberesponse()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdsubscriberesponse_);
  }

  // optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
  if (this->has_pushmarketdata()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->pushmarketdata_);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
  if (this->has_pushmarketdatastream()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->pushmarketdatastream_);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
  if (this->has_mdqueryrequest()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdqueryrequest_);
  }

  // optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
  if (this->has_mdqueryresponse()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdqueryresponse_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
  if (this->has_playbackrequest()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackrequest_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
  if (this->has_playbackresponse()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackresponse_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
  if (this->has_playbackcontrolrequest()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackcontrolrequest_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
  if (this->has_playbackcontrolresponse()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackcontrolresponse_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
  if (this->has_playbackstatusrequest()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackstatusrequest_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
  if (this->has_playbackstatus()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackstatus_);
  }

  // optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
  if (this->has_playbackpayload()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->playbackpayload_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MessageBody::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MessageBody)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MessageBody* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MessageBody>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MessageBody)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MessageBody)
    UnsafeMergeFrom(*source);
  }
}

void MessageBody::MergeFrom(const MessageBody& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MessageBody)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MessageBody::UnsafeMergeFrom(const MessageBody& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.type() != 0) {
    set_type(from.type());
  }
  if (from.interactionid() != 0) {
    set_interactionid(from.interactionid());
  }
  if (from.has_generalerrormessage()) {
    mutable_generalerrormessage()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.generalerrormessage());
  }
  if (from.has_loginrequest()) {
    mutable_loginrequest()->::com::htsc::mdc::insight::model::LoginRequest::MergeFrom(from.loginrequest());
  }
  if (from.has_loginresponse()) {
    mutable_loginresponse()->::com::htsc::mdc::insight::model::LoginResponse::MergeFrom(from.loginresponse());
  }
  if (from.has_servicediscoveryrequest()) {
    mutable_servicediscoveryrequest()->::com::htsc::mdc::insight::model::ServiceDiscoveryRequest::MergeFrom(from.servicediscoveryrequest());
  }
  if (from.has_servicediscoveryresponse()) {
    mutable_servicediscoveryresponse()->::com::htsc::mdc::insight::model::ServiceDiscoveryResponse::MergeFrom(from.servicediscoveryresponse());
  }
  if (from.has_mdsubscriberequest()) {
    mutable_mdsubscriberequest()->::com::htsc::mdc::insight::model::MDSubscribeRequest::MergeFrom(from.mdsubscriberequest());
  }
  if (from.has_mdsubscriberesponse()) {
    mutable_mdsubscriberesponse()->::com::htsc::mdc::insight::model::MDSubscribeResponse::MergeFrom(from.mdsubscriberesponse());
  }
  if (from.has_pushmarketdata()) {
    mutable_pushmarketdata()->::com::htsc::mdc::insight::model::MarketData::MergeFrom(from.pushmarketdata());
  }
  if (from.has_pushmarketdatastream()) {
    mutable_pushmarketdatastream()->::com::htsc::mdc::insight::model::MarketDataStream::MergeFrom(from.pushmarketdatastream());
  }
  if (from.has_mdqueryrequest()) {
    mutable_mdqueryrequest()->::com::htsc::mdc::insight::model::MDQueryRequest::MergeFrom(from.mdqueryrequest());
  }
  if (from.has_mdqueryresponse()) {
    mutable_mdqueryresponse()->::com::htsc::mdc::insight::model::MDQueryResponse::MergeFrom(from.mdqueryresponse());
  }
  if (from.has_playbackrequest()) {
    mutable_playbackrequest()->::com::htsc::mdc::insight::model::PlaybackRequest::MergeFrom(from.playbackrequest());
  }
  if (from.has_playbackresponse()) {
    mutable_playbackresponse()->::com::htsc::mdc::insight::model::PlaybackResponse::MergeFrom(from.playbackresponse());
  }
  if (from.has_playbackcontrolrequest()) {
    mutable_playbackcontrolrequest()->::com::htsc::mdc::insight::model::PlaybackControlRequest::MergeFrom(from.playbackcontrolrequest());
  }
  if (from.has_playbackcontrolresponse()) {
    mutable_playbackcontrolresponse()->::com::htsc::mdc::insight::model::PlaybackControlResponse::MergeFrom(from.playbackcontrolresponse());
  }
  if (from.has_playbackstatusrequest()) {
    mutable_playbackstatusrequest()->::com::htsc::mdc::insight::model::PlaybackStatusRequest::MergeFrom(from.playbackstatusrequest());
  }
  if (from.has_playbackstatus()) {
    mutable_playbackstatus()->::com::htsc::mdc::insight::model::PlaybackStatus::MergeFrom(from.playbackstatus());
  }
  if (from.has_playbackpayload()) {
    mutable_playbackpayload()->::com::htsc::mdc::insight::model::PlaybackPayload::MergeFrom(from.playbackpayload());
  }
}

void MessageBody::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MessageBody)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MessageBody::CopyFrom(const MessageBody& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MessageBody)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MessageBody::IsInitialized() const {

  return true;
}

void MessageBody::Swap(MessageBody* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MessageBody::InternalSwap(MessageBody* other) {
  std::swap(type_, other->type_);
  std::swap(interactionid_, other->interactionid_);
  std::swap(generalerrormessage_, other->generalerrormessage_);
  std::swap(loginrequest_, other->loginrequest_);
  std::swap(loginresponse_, other->loginresponse_);
  std::swap(servicediscoveryrequest_, other->servicediscoveryrequest_);
  std::swap(servicediscoveryresponse_, other->servicediscoveryresponse_);
  std::swap(mdsubscriberequest_, other->mdsubscriberequest_);
  std::swap(mdsubscriberesponse_, other->mdsubscriberesponse_);
  std::swap(pushmarketdata_, other->pushmarketdata_);
  std::swap(pushmarketdatastream_, other->pushmarketdatastream_);
  std::swap(mdqueryrequest_, other->mdqueryrequest_);
  std::swap(mdqueryresponse_, other->mdqueryresponse_);
  std::swap(playbackrequest_, other->playbackrequest_);
  std::swap(playbackresponse_, other->playbackresponse_);
  std::swap(playbackcontrolrequest_, other->playbackcontrolrequest_);
  std::swap(playbackcontrolresponse_, other->playbackcontrolresponse_);
  std::swap(playbackstatusrequest_, other->playbackstatusrequest_);
  std::swap(playbackstatus_, other->playbackstatus_);
  std::swap(playbackpayload_, other->playbackpayload_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MessageBody::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MessageBody_descriptor_;
  metadata.reflection = MessageBody_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageBody

// optional .com.htsc.mdc.insight.model.EMessageType type = 1;
void MessageBody::clear_type() {
  type_ = 0;
}
::com::htsc::mdc::insight::model::EMessageType MessageBody::type() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.type)
  return static_cast< ::com::htsc::mdc::insight::model::EMessageType >(type_);
}
void MessageBody::set_type(::com::htsc::mdc::insight::model::EMessageType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageBody.type)
}

// optional int64 interactionId = 2;
void MessageBody::clear_interactionid() {
  interactionid_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MessageBody::interactionid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.interactionId)
  return interactionid_;
}
void MessageBody::set_interactionid(::google::protobuf::int64 value) {
  
  interactionid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageBody.interactionId)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
bool MessageBody::has_generalerrormessage() const {
  return this != internal_default_instance() && generalerrormessage_ != NULL;
}
void MessageBody::clear_generalerrormessage() {
  if (GetArenaNoVirtual() == NULL && generalerrormessage_ != NULL) delete generalerrormessage_;
  generalerrormessage_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& MessageBody::generalerrormessage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  return generalerrormessage_ != NULL ? *generalerrormessage_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* MessageBody::mutable_generalerrormessage() {
  
  if (generalerrormessage_ == NULL) {
    generalerrormessage_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  return generalerrormessage_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* MessageBody::release_generalerrormessage() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = generalerrormessage_;
  generalerrormessage_ = NULL;
  return temp;
}
void MessageBody::set_allocated_generalerrormessage(::com::htsc::mdc::insight::model::InsightErrorContext* generalerrormessage) {
  delete generalerrormessage_;
  generalerrormessage_ = generalerrormessage;
  if (generalerrormessage) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
}

// optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
bool MessageBody::has_loginrequest() const {
  return this != internal_default_instance() && loginrequest_ != NULL;
}
void MessageBody::clear_loginrequest() {
  if (GetArenaNoVirtual() == NULL && loginrequest_ != NULL) delete loginrequest_;
  loginrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::LoginRequest& MessageBody::loginrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  return loginrequest_ != NULL ? *loginrequest_
                         : *::com::htsc::mdc::insight::model::LoginRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::LoginRequest* MessageBody::mutable_loginrequest() {
  
  if (loginrequest_ == NULL) {
    loginrequest_ = new ::com::htsc::mdc::insight::model::LoginRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  return loginrequest_;
}
::com::htsc::mdc::insight::model::LoginRequest* MessageBody::release_loginrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  
  ::com::htsc::mdc::insight::model::LoginRequest* temp = loginrequest_;
  loginrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_loginrequest(::com::htsc::mdc::insight::model::LoginRequest* loginrequest) {
  delete loginrequest_;
  loginrequest_ = loginrequest;
  if (loginrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.loginRequest)
}

// optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
bool MessageBody::has_loginresponse() const {
  return this != internal_default_instance() && loginresponse_ != NULL;
}
void MessageBody::clear_loginresponse() {
  if (GetArenaNoVirtual() == NULL && loginresponse_ != NULL) delete loginresponse_;
  loginresponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::LoginResponse& MessageBody::loginresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  return loginresponse_ != NULL ? *loginresponse_
                         : *::com::htsc::mdc::insight::model::LoginResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::LoginResponse* MessageBody::mutable_loginresponse() {
  
  if (loginresponse_ == NULL) {
    loginresponse_ = new ::com::htsc::mdc::insight::model::LoginResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  return loginresponse_;
}
::com::htsc::mdc::insight::model::LoginResponse* MessageBody::release_loginresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  
  ::com::htsc::mdc::insight::model::LoginResponse* temp = loginresponse_;
  loginresponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_loginresponse(::com::htsc::mdc::insight::model::LoginResponse* loginresponse) {
  delete loginresponse_;
  loginresponse_ = loginresponse;
  if (loginresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.loginResponse)
}

// optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
bool MessageBody::has_servicediscoveryrequest() const {
  return this != internal_default_instance() && servicediscoveryrequest_ != NULL;
}
void MessageBody::clear_servicediscoveryrequest() {
  if (GetArenaNoVirtual() == NULL && servicediscoveryrequest_ != NULL) delete servicediscoveryrequest_;
  servicediscoveryrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest& MessageBody::servicediscoveryrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  return servicediscoveryrequest_ != NULL ? *servicediscoveryrequest_
                         : *::com::htsc::mdc::insight::model::ServiceDiscoveryRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* MessageBody::mutable_servicediscoveryrequest() {
  
  if (servicediscoveryrequest_ == NULL) {
    servicediscoveryrequest_ = new ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  return servicediscoveryrequest_;
}
::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* MessageBody::release_servicediscoveryrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  
  ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* temp = servicediscoveryrequest_;
  servicediscoveryrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_servicediscoveryrequest(::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* servicediscoveryrequest) {
  delete servicediscoveryrequest_;
  servicediscoveryrequest_ = servicediscoveryrequest;
  if (servicediscoveryrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
}

// optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
bool MessageBody::has_servicediscoveryresponse() const {
  return this != internal_default_instance() && servicediscoveryresponse_ != NULL;
}
void MessageBody::clear_servicediscoveryresponse() {
  if (GetArenaNoVirtual() == NULL && servicediscoveryresponse_ != NULL) delete servicediscoveryresponse_;
  servicediscoveryresponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse& MessageBody::servicediscoveryresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  return servicediscoveryresponse_ != NULL ? *servicediscoveryresponse_
                         : *::com::htsc::mdc::insight::model::ServiceDiscoveryResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* MessageBody::mutable_servicediscoveryresponse() {
  
  if (servicediscoveryresponse_ == NULL) {
    servicediscoveryresponse_ = new ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  return servicediscoveryresponse_;
}
::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* MessageBody::release_servicediscoveryresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  
  ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* temp = servicediscoveryresponse_;
  servicediscoveryresponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_servicediscoveryresponse(::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* servicediscoveryresponse) {
  delete servicediscoveryresponse_;
  servicediscoveryresponse_ = servicediscoveryresponse;
  if (servicediscoveryresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
}

// optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
bool MessageBody::has_mdsubscriberequest() const {
  return this != internal_default_instance() && mdsubscriberequest_ != NULL;
}
void MessageBody::clear_mdsubscriberequest() {
  if (GetArenaNoVirtual() == NULL && mdsubscriberequest_ != NULL) delete mdsubscriberequest_;
  mdsubscriberequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSubscribeRequest& MessageBody::mdsubscriberequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  return mdsubscriberequest_ != NULL ? *mdsubscriberequest_
                         : *::com::htsc::mdc::insight::model::MDSubscribeRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSubscribeRequest* MessageBody::mutable_mdsubscriberequest() {
  
  if (mdsubscriberequest_ == NULL) {
    mdsubscriberequest_ = new ::com::htsc::mdc::insight::model::MDSubscribeRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  return mdsubscriberequest_;
}
::com::htsc::mdc::insight::model::MDSubscribeRequest* MessageBody::release_mdsubscriberequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  
  ::com::htsc::mdc::insight::model::MDSubscribeRequest* temp = mdsubscriberequest_;
  mdsubscriberequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_mdsubscriberequest(::com::htsc::mdc::insight::model::MDSubscribeRequest* mdsubscriberequest) {
  delete mdsubscriberequest_;
  mdsubscriberequest_ = mdsubscriberequest;
  if (mdsubscriberequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
}

// optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
bool MessageBody::has_mdsubscriberesponse() const {
  return this != internal_default_instance() && mdsubscriberesponse_ != NULL;
}
void MessageBody::clear_mdsubscriberesponse() {
  if (GetArenaNoVirtual() == NULL && mdsubscriberesponse_ != NULL) delete mdsubscriberesponse_;
  mdsubscriberesponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSubscribeResponse& MessageBody::mdsubscriberesponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  return mdsubscriberesponse_ != NULL ? *mdsubscriberesponse_
                         : *::com::htsc::mdc::insight::model::MDSubscribeResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSubscribeResponse* MessageBody::mutable_mdsubscriberesponse() {
  
  if (mdsubscriberesponse_ == NULL) {
    mdsubscriberesponse_ = new ::com::htsc::mdc::insight::model::MDSubscribeResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  return mdsubscriberesponse_;
}
::com::htsc::mdc::insight::model::MDSubscribeResponse* MessageBody::release_mdsubscriberesponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  
  ::com::htsc::mdc::insight::model::MDSubscribeResponse* temp = mdsubscriberesponse_;
  mdsubscriberesponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_mdsubscriberesponse(::com::htsc::mdc::insight::model::MDSubscribeResponse* mdsubscriberesponse) {
  delete mdsubscriberesponse_;
  mdsubscriberesponse_ = mdsubscriberesponse;
  if (mdsubscriberesponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
}

// optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
bool MessageBody::has_pushmarketdata() const {
  return this != internal_default_instance() && pushmarketdata_ != NULL;
}
void MessageBody::clear_pushmarketdata() {
  if (GetArenaNoVirtual() == NULL && pushmarketdata_ != NULL) delete pushmarketdata_;
  pushmarketdata_ = NULL;
}
const ::com::htsc::mdc::insight::model::MarketData& MessageBody::pushmarketdata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  return pushmarketdata_ != NULL ? *pushmarketdata_
                         : *::com::htsc::mdc::insight::model::MarketData::internal_default_instance();
}
::com::htsc::mdc::insight::model::MarketData* MessageBody::mutable_pushmarketdata() {
  
  if (pushmarketdata_ == NULL) {
    pushmarketdata_ = new ::com::htsc::mdc::insight::model::MarketData;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  return pushmarketdata_;
}
::com::htsc::mdc::insight::model::MarketData* MessageBody::release_pushmarketdata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  
  ::com::htsc::mdc::insight::model::MarketData* temp = pushmarketdata_;
  pushmarketdata_ = NULL;
  return temp;
}
void MessageBody::set_allocated_pushmarketdata(::com::htsc::mdc::insight::model::MarketData* pushmarketdata) {
  delete pushmarketdata_;
  pushmarketdata_ = pushmarketdata;
  if (pushmarketdata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
bool MessageBody::has_pushmarketdatastream() const {
  return this != internal_default_instance() && pushmarketdatastream_ != NULL;
}
void MessageBody::clear_pushmarketdatastream() {
  if (GetArenaNoVirtual() == NULL && pushmarketdatastream_ != NULL) delete pushmarketdatastream_;
  pushmarketdatastream_ = NULL;
}
const ::com::htsc::mdc::insight::model::MarketDataStream& MessageBody::pushmarketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  return pushmarketdatastream_ != NULL ? *pushmarketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
::com::htsc::mdc::insight::model::MarketDataStream* MessageBody::mutable_pushmarketdatastream() {
  
  if (pushmarketdatastream_ == NULL) {
    pushmarketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  return pushmarketdatastream_;
}
::com::htsc::mdc::insight::model::MarketDataStream* MessageBody::release_pushmarketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = pushmarketdatastream_;
  pushmarketdatastream_ = NULL;
  return temp;
}
void MessageBody::set_allocated_pushmarketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* pushmarketdatastream) {
  delete pushmarketdatastream_;
  pushmarketdatastream_ = pushmarketdatastream;
  if (pushmarketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
}

// optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
bool MessageBody::has_mdqueryrequest() const {
  return this != internal_default_instance() && mdqueryrequest_ != NULL;
}
void MessageBody::clear_mdqueryrequest() {
  if (GetArenaNoVirtual() == NULL && mdqueryrequest_ != NULL) delete mdqueryrequest_;
  mdqueryrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDQueryRequest& MessageBody::mdqueryrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  return mdqueryrequest_ != NULL ? *mdqueryrequest_
                         : *::com::htsc::mdc::insight::model::MDQueryRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDQueryRequest* MessageBody::mutable_mdqueryrequest() {
  
  if (mdqueryrequest_ == NULL) {
    mdqueryrequest_ = new ::com::htsc::mdc::insight::model::MDQueryRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  return mdqueryrequest_;
}
::com::htsc::mdc::insight::model::MDQueryRequest* MessageBody::release_mdqueryrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  
  ::com::htsc::mdc::insight::model::MDQueryRequest* temp = mdqueryrequest_;
  mdqueryrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_mdqueryrequest(::com::htsc::mdc::insight::model::MDQueryRequest* mdqueryrequest) {
  delete mdqueryrequest_;
  mdqueryrequest_ = mdqueryrequest;
  if (mdqueryrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
}

// optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
bool MessageBody::has_mdqueryresponse() const {
  return this != internal_default_instance() && mdqueryresponse_ != NULL;
}
void MessageBody::clear_mdqueryresponse() {
  if (GetArenaNoVirtual() == NULL && mdqueryresponse_ != NULL) delete mdqueryresponse_;
  mdqueryresponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDQueryResponse& MessageBody::mdqueryresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  return mdqueryresponse_ != NULL ? *mdqueryresponse_
                         : *::com::htsc::mdc::insight::model::MDQueryResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDQueryResponse* MessageBody::mutable_mdqueryresponse() {
  
  if (mdqueryresponse_ == NULL) {
    mdqueryresponse_ = new ::com::htsc::mdc::insight::model::MDQueryResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  return mdqueryresponse_;
}
::com::htsc::mdc::insight::model::MDQueryResponse* MessageBody::release_mdqueryresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  
  ::com::htsc::mdc::insight::model::MDQueryResponse* temp = mdqueryresponse_;
  mdqueryresponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_mdqueryresponse(::com::htsc::mdc::insight::model::MDQueryResponse* mdqueryresponse) {
  delete mdqueryresponse_;
  mdqueryresponse_ = mdqueryresponse;
  if (mdqueryresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
bool MessageBody::has_playbackrequest() const {
  return this != internal_default_instance() && playbackrequest_ != NULL;
}
void MessageBody::clear_playbackrequest() {
  if (GetArenaNoVirtual() == NULL && playbackrequest_ != NULL) delete playbackrequest_;
  playbackrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackRequest& MessageBody::playbackrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  return playbackrequest_ != NULL ? *playbackrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackRequest* MessageBody::mutable_playbackrequest() {
  
  if (playbackrequest_ == NULL) {
    playbackrequest_ = new ::com::htsc::mdc::insight::model::PlaybackRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  return playbackrequest_;
}
::com::htsc::mdc::insight::model::PlaybackRequest* MessageBody::release_playbackrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackRequest* temp = playbackrequest_;
  playbackrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackrequest(::com::htsc::mdc::insight::model::PlaybackRequest* playbackrequest) {
  delete playbackrequest_;
  playbackrequest_ = playbackrequest;
  if (playbackrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
bool MessageBody::has_playbackresponse() const {
  return this != internal_default_instance() && playbackresponse_ != NULL;
}
void MessageBody::clear_playbackresponse() {
  if (GetArenaNoVirtual() == NULL && playbackresponse_ != NULL) delete playbackresponse_;
  playbackresponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackResponse& MessageBody::playbackresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  return playbackresponse_ != NULL ? *playbackresponse_
                         : *::com::htsc::mdc::insight::model::PlaybackResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackResponse* MessageBody::mutable_playbackresponse() {
  
  if (playbackresponse_ == NULL) {
    playbackresponse_ = new ::com::htsc::mdc::insight::model::PlaybackResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  return playbackresponse_;
}
::com::htsc::mdc::insight::model::PlaybackResponse* MessageBody::release_playbackresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  
  ::com::htsc::mdc::insight::model::PlaybackResponse* temp = playbackresponse_;
  playbackresponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackresponse(::com::htsc::mdc::insight::model::PlaybackResponse* playbackresponse) {
  delete playbackresponse_;
  playbackresponse_ = playbackresponse;
  if (playbackresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
bool MessageBody::has_playbackcontrolrequest() const {
  return this != internal_default_instance() && playbackcontrolrequest_ != NULL;
}
void MessageBody::clear_playbackcontrolrequest() {
  if (GetArenaNoVirtual() == NULL && playbackcontrolrequest_ != NULL) delete playbackcontrolrequest_;
  playbackcontrolrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackControlRequest& MessageBody::playbackcontrolrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  return playbackcontrolrequest_ != NULL ? *playbackcontrolrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackControlRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackControlRequest* MessageBody::mutable_playbackcontrolrequest() {
  
  if (playbackcontrolrequest_ == NULL) {
    playbackcontrolrequest_ = new ::com::htsc::mdc::insight::model::PlaybackControlRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  return playbackcontrolrequest_;
}
::com::htsc::mdc::insight::model::PlaybackControlRequest* MessageBody::release_playbackcontrolrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackControlRequest* temp = playbackcontrolrequest_;
  playbackcontrolrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackcontrolrequest(::com::htsc::mdc::insight::model::PlaybackControlRequest* playbackcontrolrequest) {
  delete playbackcontrolrequest_;
  playbackcontrolrequest_ = playbackcontrolrequest;
  if (playbackcontrolrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
bool MessageBody::has_playbackcontrolresponse() const {
  return this != internal_default_instance() && playbackcontrolresponse_ != NULL;
}
void MessageBody::clear_playbackcontrolresponse() {
  if (GetArenaNoVirtual() == NULL && playbackcontrolresponse_ != NULL) delete playbackcontrolresponse_;
  playbackcontrolresponse_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackControlResponse& MessageBody::playbackcontrolresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  return playbackcontrolresponse_ != NULL ? *playbackcontrolresponse_
                         : *::com::htsc::mdc::insight::model::PlaybackControlResponse::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackControlResponse* MessageBody::mutable_playbackcontrolresponse() {
  
  if (playbackcontrolresponse_ == NULL) {
    playbackcontrolresponse_ = new ::com::htsc::mdc::insight::model::PlaybackControlResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  return playbackcontrolresponse_;
}
::com::htsc::mdc::insight::model::PlaybackControlResponse* MessageBody::release_playbackcontrolresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  
  ::com::htsc::mdc::insight::model::PlaybackControlResponse* temp = playbackcontrolresponse_;
  playbackcontrolresponse_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackcontrolresponse(::com::htsc::mdc::insight::model::PlaybackControlResponse* playbackcontrolresponse) {
  delete playbackcontrolresponse_;
  playbackcontrolresponse_ = playbackcontrolresponse;
  if (playbackcontrolresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
bool MessageBody::has_playbackstatusrequest() const {
  return this != internal_default_instance() && playbackstatusrequest_ != NULL;
}
void MessageBody::clear_playbackstatusrequest() {
  if (GetArenaNoVirtual() == NULL && playbackstatusrequest_ != NULL) delete playbackstatusrequest_;
  playbackstatusrequest_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackStatusRequest& MessageBody::playbackstatusrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  return playbackstatusrequest_ != NULL ? *playbackstatusrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackStatusRequest::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackStatusRequest* MessageBody::mutable_playbackstatusrequest() {
  
  if (playbackstatusrequest_ == NULL) {
    playbackstatusrequest_ = new ::com::htsc::mdc::insight::model::PlaybackStatusRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  return playbackstatusrequest_;
}
::com::htsc::mdc::insight::model::PlaybackStatusRequest* MessageBody::release_playbackstatusrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackStatusRequest* temp = playbackstatusrequest_;
  playbackstatusrequest_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackstatusrequest(::com::htsc::mdc::insight::model::PlaybackStatusRequest* playbackstatusrequest) {
  delete playbackstatusrequest_;
  playbackstatusrequest_ = playbackstatusrequest;
  if (playbackstatusrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
bool MessageBody::has_playbackstatus() const {
  return this != internal_default_instance() && playbackstatus_ != NULL;
}
void MessageBody::clear_playbackstatus() {
  if (GetArenaNoVirtual() == NULL && playbackstatus_ != NULL) delete playbackstatus_;
  playbackstatus_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackStatus& MessageBody::playbackstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  return playbackstatus_ != NULL ? *playbackstatus_
                         : *::com::htsc::mdc::insight::model::PlaybackStatus::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackStatus* MessageBody::mutable_playbackstatus() {
  
  if (playbackstatus_ == NULL) {
    playbackstatus_ = new ::com::htsc::mdc::insight::model::PlaybackStatus;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  return playbackstatus_;
}
::com::htsc::mdc::insight::model::PlaybackStatus* MessageBody::release_playbackstatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  
  ::com::htsc::mdc::insight::model::PlaybackStatus* temp = playbackstatus_;
  playbackstatus_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackstatus(::com::htsc::mdc::insight::model::PlaybackStatus* playbackstatus) {
  delete playbackstatus_;
  playbackstatus_ = playbackstatus;
  if (playbackstatus) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
}

// optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
bool MessageBody::has_playbackpayload() const {
  return this != internal_default_instance() && playbackpayload_ != NULL;
}
void MessageBody::clear_playbackpayload() {
  if (GetArenaNoVirtual() == NULL && playbackpayload_ != NULL) delete playbackpayload_;
  playbackpayload_ = NULL;
}
const ::com::htsc::mdc::insight::model::PlaybackPayload& MessageBody::playbackpayload() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  return playbackpayload_ != NULL ? *playbackpayload_
                         : *::com::htsc::mdc::insight::model::PlaybackPayload::internal_default_instance();
}
::com::htsc::mdc::insight::model::PlaybackPayload* MessageBody::mutable_playbackpayload() {
  
  if (playbackpayload_ == NULL) {
    playbackpayload_ = new ::com::htsc::mdc::insight::model::PlaybackPayload;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  return playbackpayload_;
}
::com::htsc::mdc::insight::model::PlaybackPayload* MessageBody::release_playbackpayload() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  
  ::com::htsc::mdc::insight::model::PlaybackPayload* temp = playbackpayload_;
  playbackpayload_ = NULL;
  return temp;
}
void MessageBody::set_allocated_playbackpayload(::com::htsc::mdc::insight::model::PlaybackPayload* playbackpayload) {
  delete playbackpayload_;
  playbackpayload_ = playbackpayload;
  if (playbackpayload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
}

inline const MessageBody* MessageBody::internal_default_instance() {
  return &MessageBody_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
