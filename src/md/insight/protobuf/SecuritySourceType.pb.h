// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SecuritySourceType.proto

#ifndef PROTOBUF_SecuritySourceType_2eproto__INCLUDED
#define PROTOBUF_SecuritySourceType_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_SecuritySourceType_2eproto();
void protobuf_InitDefaults_SecuritySourceType_2eproto();
void protobuf_AssignDesc_SecuritySourceType_2eproto();
void protobuf_ShutdownFile_SecuritySourceType_2eproto();

class SecuritySourceType;

// ===================================================================

class SecuritySourceType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SecuritySourceType) */ {
 public:
  SecuritySourceType();
  virtual ~SecuritySourceType();

  SecuritySourceType(const SecuritySourceType& from);

  inline SecuritySourceType& operator=(const SecuritySourceType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SecuritySourceType& default_instance();

  static const SecuritySourceType* internal_default_instance();

  void Swap(SecuritySourceType* other);

  // implements Message ----------------------------------------------

  inline SecuritySourceType* New() const { return New(NULL); }

  SecuritySourceType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SecuritySourceType& from);
  void MergeFrom(const SecuritySourceType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SecuritySourceType* other);
  void UnsafeMergeFrom(const SecuritySourceType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 1;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SecuritySourceType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  int securityidsource_;
  int securitytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_SecuritySourceType_2eproto_impl();
  friend void  protobuf_AddDesc_SecuritySourceType_2eproto_impl();
  friend void protobuf_AssignDesc_SecuritySourceType_2eproto();
  friend void protobuf_ShutdownFile_SecuritySourceType_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SecuritySourceType> SecuritySourceType_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// SecuritySourceType

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
inline void SecuritySourceType::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource SecuritySourceType::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SecuritySourceType.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void SecuritySourceType::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SecuritySourceType.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 2;
inline void SecuritySourceType::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType SecuritySourceType::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SecuritySourceType.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void SecuritySourceType::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SecuritySourceType.securityType)
}

inline const SecuritySourceType* SecuritySourceType::internal_default_instance() {
  return &SecuritySourceType_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_SecuritySourceType_2eproto__INCLUDED
