// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSATransaction.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDUSATransaction.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDUSATransaction_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDUSATransaction_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDUSATransaction_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDUSATransaction_2eproto() {
  protobuf_AddDesc_MDUSATransaction_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDUSATransaction.proto");
  GOOGLE_CHECK(file != NULL);
  MDUSATransaction_descriptor_ = file->message_type(0);
  static const int MDUSATransaction_offsets_[29] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, nanosecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradenum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, originaltradenum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradebuyno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradesellno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradebsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, navoffsetamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, totalconsolidatevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, saleconditionlv1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, saleconditionlv2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, saleconditionlv3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, saleconditionlv4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, trackingnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, timeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, dataindex_),
  };
  MDUSATransaction_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDUSATransaction_descriptor_,
      MDUSATransaction::internal_default_instance(),
      MDUSATransaction_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDUSATransaction),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSATransaction, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDUSATransaction_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDUSATransaction_descriptor_, MDUSATransaction::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDUSATransaction_2eproto() {
  MDUSATransaction_default_instance_.Shutdown();
  delete MDUSATransaction_reflection_;
}

void protobuf_InitDefaults_MDUSATransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDUSATransaction_default_instance_.DefaultConstruct();
  MDUSATransaction_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDUSATransaction_2eproto_once_);
void protobuf_InitDefaults_MDUSATransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDUSATransaction_2eproto_once_,
                 &protobuf_InitDefaults_MDUSATransaction_2eproto_impl);
}
void protobuf_AddDesc_MDUSATransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDUSATransaction_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026MDUSATransaction.proto\022\032com.htsc.mdc.i"
    "nsight.model\032\023ESecurityType.proto\032\027ESecu"
    "rityIDSource.proto\"\340\005\n\020MDUSATransaction\022"
    "\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022"
    "\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?"
    "\n\020securityIDSource\030\005 \001(\0162%.com.htsc.mdc."
    "model.ESecurityIDSource\0227\n\014securityType\030"
    "\006 \001(\0162!.com.htsc.mdc.model.ESecurityType"
    "\022\024\n\014ExchangeDate\030\007 \001(\005\022\024\n\014ExchangeTime\030\010"
    " \001(\005\022\022\n\nNanosecond\030\t \001(\005\022\021\n\tChannelNo\030\n "
    "\001(\005\022\020\n\010TradeNum\030\013 \001(\t\022\030\n\020OriginalTradeNu"
    "m\030\014 \001(\t\022\022\n\nTradeBuyNo\030\r \001(\003\022\023\n\013TradeSell"
    "No\030\016 \001(\003\022\021\n\tTradeType\030\017 \001(\005\022\023\n\013TradeBSFl"
    "ag\030\020 \001(\005\022\022\n\nTradePrice\030\021 \001(\003\022\020\n\010TradeQty"
    "\030\022 \001(\003\022\022\n\nTradeMoney\030\023 \001(\003\022\027\n\017NAVOffsetA"
    "mount\030\024 \001(\003\022\036\n\026TotalConsolidateVolume\030\025 "
    "\001(\003\022\030\n\020SaleConditionLV1\030\026 \001(\t\022\030\n\020SaleCon"
    "ditionLV2\030\027 \001(\t\022\030\n\020SaleConditionLV3\030\030 \001("
    "\t\022\030\n\020SaleConditionLV4\030\031 \001(\t\022\023\n\013TrackingN"
    "um\030\032 \001(\005\022\035\n\025DataMultiplePowerOf10\030\033 \001(\005\022"
    "\021\n\tTimeIndex\030\034 \001(\005\022\021\n\tDataIndex\030\035 \001(\003B9\n"
    "\032com.htsc.mdc.insight.modelB\026MDUSATransa"
    "ctionProtosH\001\240\001\001b\006proto3", 904);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDUSATransaction.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDUSATransaction_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDUSATransaction_2eproto_once_);
void protobuf_AddDesc_MDUSATransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDUSATransaction_2eproto_once_,
                 &protobuf_AddDesc_MDUSATransaction_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDUSATransaction_2eproto {
  StaticDescriptorInitializer_MDUSATransaction_2eproto() {
    protobuf_AddDesc_MDUSATransaction_2eproto();
  }
} static_descriptor_initializer_MDUSATransaction_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDUSATransaction::kHTSCSecurityIDFieldNumber;
const int MDUSATransaction::kMDDateFieldNumber;
const int MDUSATransaction::kMDTimeFieldNumber;
const int MDUSATransaction::kDataTimestampFieldNumber;
const int MDUSATransaction::kSecurityIDSourceFieldNumber;
const int MDUSATransaction::kSecurityTypeFieldNumber;
const int MDUSATransaction::kExchangeDateFieldNumber;
const int MDUSATransaction::kExchangeTimeFieldNumber;
const int MDUSATransaction::kNanosecondFieldNumber;
const int MDUSATransaction::kChannelNoFieldNumber;
const int MDUSATransaction::kTradeNumFieldNumber;
const int MDUSATransaction::kOriginalTradeNumFieldNumber;
const int MDUSATransaction::kTradeBuyNoFieldNumber;
const int MDUSATransaction::kTradeSellNoFieldNumber;
const int MDUSATransaction::kTradeTypeFieldNumber;
const int MDUSATransaction::kTradeBSFlagFieldNumber;
const int MDUSATransaction::kTradePriceFieldNumber;
const int MDUSATransaction::kTradeQtyFieldNumber;
const int MDUSATransaction::kTradeMoneyFieldNumber;
const int MDUSATransaction::kNAVOffsetAmountFieldNumber;
const int MDUSATransaction::kTotalConsolidateVolumeFieldNumber;
const int MDUSATransaction::kSaleConditionLV1FieldNumber;
const int MDUSATransaction::kSaleConditionLV2FieldNumber;
const int MDUSATransaction::kSaleConditionLV3FieldNumber;
const int MDUSATransaction::kSaleConditionLV4FieldNumber;
const int MDUSATransaction::kTrackingNumFieldNumber;
const int MDUSATransaction::kDataMultiplePowerOf10FieldNumber;
const int MDUSATransaction::kTimeIndexFieldNumber;
const int MDUSATransaction::kDataIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDUSATransaction::MDUSATransaction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDUSATransaction_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDUSATransaction)
}

void MDUSATransaction::InitAsDefaultInstance() {
}

MDUSATransaction::MDUSATransaction(const MDUSATransaction& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDUSATransaction)
}

void MDUSATransaction::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradenum_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  originaltradenum_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&timeindex_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(timeindex_));
  _cached_size_ = 0;
}

MDUSATransaction::~MDUSATransaction() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDUSATransaction)
  SharedDtor();
}

void MDUSATransaction::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradenum_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  originaltradenum_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDUSATransaction::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDUSATransaction::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDUSATransaction_descriptor_;
}

const MDUSATransaction& MDUSATransaction::default_instance() {
  protobuf_InitDefaults_MDUSATransaction_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDUSATransaction> MDUSATransaction_default_instance_;

MDUSATransaction* MDUSATransaction::New(::google::protobuf::Arena* arena) const {
  MDUSATransaction* n = new MDUSATransaction;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDUSATransaction::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDUSATransaction)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDUSATransaction, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDUSATransaction*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(nanosecond_, tradebsflag_);
  tradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  originaltradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(tradeprice_, totalconsolidatevolume_);
  saleconditionlv1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  saleconditionlv3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(trackingnum_, timeindex_);
  saleconditionlv4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDUSATransaction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDUSATransaction)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_Nanosecond;
        break;
      }

      // optional int32 Nanosecond = 9;
      case 9: {
        if (tag == 72) {
         parse_Nanosecond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &nanosecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 10;
      case 10: {
        if (tag == 80) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_TradeNum;
        break;
      }

      // optional string TradeNum = 11;
      case 11: {
        if (tag == 90) {
         parse_TradeNum:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradenum()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradenum().data(), this->tradenum().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.TradeNum"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_OriginalTradeNum;
        break;
      }

      // optional string OriginalTradeNum = 12;
      case 12: {
        if (tag == 98) {
         parse_OriginalTradeNum:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_originaltradenum()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->originaltradenum().data(), this->originaltradenum().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TradeBuyNo;
        break;
      }

      // optional int64 TradeBuyNo = 13;
      case 13: {
        if (tag == 104) {
         parse_TradeBuyNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradebuyno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_TradeSellNo;
        break;
      }

      // optional int64 TradeSellNo = 14;
      case 14: {
        if (tag == 112) {
         parse_TradeSellNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradesellno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_TradeType;
        break;
      }

      // optional int32 TradeType = 15;
      case 15: {
        if (tag == 120) {
         parse_TradeType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_TradeBSFlag;
        break;
      }

      // optional int32 TradeBSFlag = 16;
      case 16: {
        if (tag == 128) {
         parse_TradeBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradebsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_TradePrice;
        break;
      }

      // optional int64 TradePrice = 17;
      case 17: {
        if (tag == 136) {
         parse_TradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_TradeQty;
        break;
      }

      // optional int64 TradeQty = 18;
      case 18: {
        if (tag == 144) {
         parse_TradeQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_TradeMoney;
        break;
      }

      // optional int64 TradeMoney = 19;
      case 19: {
        if (tag == 152) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_NAVOffsetAmount;
        break;
      }

      // optional int64 NAVOffsetAmount = 20;
      case 20: {
        if (tag == 160) {
         parse_NAVOffsetAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &navoffsetamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TotalConsolidateVolume;
        break;
      }

      // optional int64 TotalConsolidateVolume = 21;
      case 21: {
        if (tag == 168) {
         parse_TotalConsolidateVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalconsolidatevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_SaleConditionLV1;
        break;
      }

      // optional string SaleConditionLV1 = 22;
      case 22: {
        if (tag == 178) {
         parse_SaleConditionLV1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_saleconditionlv1()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->saleconditionlv1().data(), this->saleconditionlv1().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_SaleConditionLV2;
        break;
      }

      // optional string SaleConditionLV2 = 23;
      case 23: {
        if (tag == 186) {
         parse_SaleConditionLV2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_saleconditionlv2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->saleconditionlv2().data(), this->saleconditionlv2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_SaleConditionLV3;
        break;
      }

      // optional string SaleConditionLV3 = 24;
      case 24: {
        if (tag == 194) {
         parse_SaleConditionLV3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_saleconditionlv3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->saleconditionlv3().data(), this->saleconditionlv3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_SaleConditionLV4;
        break;
      }

      // optional string SaleConditionLV4 = 25;
      case 25: {
        if (tag == 202) {
         parse_SaleConditionLV4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_saleconditionlv4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->saleconditionlv4().data(), this->saleconditionlv4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_TrackingNum;
        break;
      }

      // optional int32 TrackingNum = 26;
      case 26: {
        if (tag == 208) {
         parse_TrackingNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trackingnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 27;
      case 27: {
        if (tag == 216) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_TimeIndex;
        break;
      }

      // optional int32 TimeIndex = 28;
      case 28: {
        if (tag == 224) {
         parse_TimeIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &timeindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_DataIndex;
        break;
      }

      // optional int64 DataIndex = 29;
      case 29: {
        if (tag == 232) {
         parse_DataIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dataindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDUSATransaction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDUSATransaction)
  return false;
#undef DO_
}

void MDUSATransaction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDUSATransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->nanosecond(), output);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->channelno(), output);
  }

  // optional string TradeNum = 11;
  if (this->tradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradenum().data(), this->tradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.TradeNum");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->tradenum(), output);
  }

  // optional string OriginalTradeNum = 12;
  if (this->originaltradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->originaltradenum().data(), this->originaltradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->originaltradenum(), output);
  }

  // optional int64 TradeBuyNo = 13;
  if (this->tradebuyno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->tradebuyno(), output);
  }

  // optional int64 TradeSellNo = 14;
  if (this->tradesellno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->tradesellno(), output);
  }

  // optional int32 TradeType = 15;
  if (this->tradetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->tradetype(), output);
  }

  // optional int32 TradeBSFlag = 16;
  if (this->tradebsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->tradebsflag(), output);
  }

  // optional int64 TradePrice = 17;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->tradeprice(), output);
  }

  // optional int64 TradeQty = 18;
  if (this->tradeqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->tradeqty(), output);
  }

  // optional int64 TradeMoney = 19;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->trademoney(), output);
  }

  // optional int64 NAVOffsetAmount = 20;
  if (this->navoffsetamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->navoffsetamount(), output);
  }

  // optional int64 TotalConsolidateVolume = 21;
  if (this->totalconsolidatevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->totalconsolidatevolume(), output);
  }

  // optional string SaleConditionLV1 = 22;
  if (this->saleconditionlv1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv1().data(), this->saleconditionlv1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->saleconditionlv1(), output);
  }

  // optional string SaleConditionLV2 = 23;
  if (this->saleconditionlv2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv2().data(), this->saleconditionlv2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->saleconditionlv2(), output);
  }

  // optional string SaleConditionLV3 = 24;
  if (this->saleconditionlv3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv3().data(), this->saleconditionlv3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->saleconditionlv3(), output);
  }

  // optional string SaleConditionLV4 = 25;
  if (this->saleconditionlv4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv4().data(), this->saleconditionlv4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->saleconditionlv4(), output);
  }

  // optional int32 TrackingNum = 26;
  if (this->trackingnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->trackingnum(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(27, this->datamultiplepowerof10(), output);
  }

  // optional int32 TimeIndex = 28;
  if (this->timeindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(28, this->timeindex(), output);
  }

  // optional int64 DataIndex = 29;
  if (this->dataindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->dataindex(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDUSATransaction)
}

::google::protobuf::uint8* MDUSATransaction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDUSATransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->nanosecond(), target);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->channelno(), target);
  }

  // optional string TradeNum = 11;
  if (this->tradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradenum().data(), this->tradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.TradeNum");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->tradenum(), target);
  }

  // optional string OriginalTradeNum = 12;
  if (this->originaltradenum().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->originaltradenum().data(), this->originaltradenum().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->originaltradenum(), target);
  }

  // optional int64 TradeBuyNo = 13;
  if (this->tradebuyno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->tradebuyno(), target);
  }

  // optional int64 TradeSellNo = 14;
  if (this->tradesellno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->tradesellno(), target);
  }

  // optional int32 TradeType = 15;
  if (this->tradetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->tradetype(), target);
  }

  // optional int32 TradeBSFlag = 16;
  if (this->tradebsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->tradebsflag(), target);
  }

  // optional int64 TradePrice = 17;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->tradeprice(), target);
  }

  // optional int64 TradeQty = 18;
  if (this->tradeqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->tradeqty(), target);
  }

  // optional int64 TradeMoney = 19;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->trademoney(), target);
  }

  // optional int64 NAVOffsetAmount = 20;
  if (this->navoffsetamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->navoffsetamount(), target);
  }

  // optional int64 TotalConsolidateVolume = 21;
  if (this->totalconsolidatevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->totalconsolidatevolume(), target);
  }

  // optional string SaleConditionLV1 = 22;
  if (this->saleconditionlv1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv1().data(), this->saleconditionlv1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->saleconditionlv1(), target);
  }

  // optional string SaleConditionLV2 = 23;
  if (this->saleconditionlv2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv2().data(), this->saleconditionlv2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->saleconditionlv2(), target);
  }

  // optional string SaleConditionLV3 = 24;
  if (this->saleconditionlv3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv3().data(), this->saleconditionlv3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->saleconditionlv3(), target);
  }

  // optional string SaleConditionLV4 = 25;
  if (this->saleconditionlv4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->saleconditionlv4().data(), this->saleconditionlv4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->saleconditionlv4(), target);
  }

  // optional int32 TrackingNum = 26;
  if (this->trackingnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->trackingnum(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(27, this->datamultiplepowerof10(), target);
  }

  // optional int32 TimeIndex = 28;
  if (this->timeindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(28, this->timeindex(), target);
  }

  // optional int64 DataIndex = 29;
  if (this->dataindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->dataindex(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDUSATransaction)
  return target;
}

size_t MDUSATransaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDUSATransaction)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->nanosecond());
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional string TradeNum = 11;
  if (this->tradenum().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradenum());
  }

  // optional string OriginalTradeNum = 12;
  if (this->originaltradenum().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->originaltradenum());
  }

  // optional int64 TradeBuyNo = 13;
  if (this->tradebuyno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradebuyno());
  }

  // optional int64 TradeSellNo = 14;
  if (this->tradesellno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradesellno());
  }

  // optional int32 TradeType = 15;
  if (this->tradetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradetype());
  }

  // optional int32 TradeBSFlag = 16;
  if (this->tradebsflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradebsflag());
  }

  // optional int64 TradePrice = 17;
  if (this->tradeprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeprice());
  }

  // optional int64 TradeQty = 18;
  if (this->tradeqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeqty());
  }

  // optional int64 TradeMoney = 19;
  if (this->trademoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->trademoney());
  }

  // optional int64 NAVOffsetAmount = 20;
  if (this->navoffsetamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->navoffsetamount());
  }

  // optional int64 TotalConsolidateVolume = 21;
  if (this->totalconsolidatevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalconsolidatevolume());
  }

  // optional string SaleConditionLV1 = 22;
  if (this->saleconditionlv1().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->saleconditionlv1());
  }

  // optional string SaleConditionLV2 = 23;
  if (this->saleconditionlv2().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->saleconditionlv2());
  }

  // optional string SaleConditionLV3 = 24;
  if (this->saleconditionlv3().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->saleconditionlv3());
  }

  // optional string SaleConditionLV4 = 25;
  if (this->saleconditionlv4().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->saleconditionlv4());
  }

  // optional int32 TrackingNum = 26;
  if (this->trackingnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trackingnum());
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 TimeIndex = 28;
  if (this->timeindex() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->timeindex());
  }

  // optional int64 DataIndex = 29;
  if (this->dataindex() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dataindex());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDUSATransaction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDUSATransaction)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDUSATransaction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDUSATransaction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDUSATransaction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDUSATransaction)
    UnsafeMergeFrom(*source);
  }
}

void MDUSATransaction::MergeFrom(const MDUSATransaction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDUSATransaction)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDUSATransaction::UnsafeMergeFrom(const MDUSATransaction& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.nanosecond() != 0) {
    set_nanosecond(from.nanosecond());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.tradenum().size() > 0) {

    tradenum_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradenum_);
  }
  if (from.originaltradenum().size() > 0) {

    originaltradenum_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.originaltradenum_);
  }
  if (from.tradebuyno() != 0) {
    set_tradebuyno(from.tradebuyno());
  }
  if (from.tradesellno() != 0) {
    set_tradesellno(from.tradesellno());
  }
  if (from.tradetype() != 0) {
    set_tradetype(from.tradetype());
  }
  if (from.tradebsflag() != 0) {
    set_tradebsflag(from.tradebsflag());
  }
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.tradeqty() != 0) {
    set_tradeqty(from.tradeqty());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.navoffsetamount() != 0) {
    set_navoffsetamount(from.navoffsetamount());
  }
  if (from.totalconsolidatevolume() != 0) {
    set_totalconsolidatevolume(from.totalconsolidatevolume());
  }
  if (from.saleconditionlv1().size() > 0) {

    saleconditionlv1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.saleconditionlv1_);
  }
  if (from.saleconditionlv2().size() > 0) {

    saleconditionlv2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.saleconditionlv2_);
  }
  if (from.saleconditionlv3().size() > 0) {

    saleconditionlv3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.saleconditionlv3_);
  }
  if (from.saleconditionlv4().size() > 0) {

    saleconditionlv4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.saleconditionlv4_);
  }
  if (from.trackingnum() != 0) {
    set_trackingnum(from.trackingnum());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.timeindex() != 0) {
    set_timeindex(from.timeindex());
  }
  if (from.dataindex() != 0) {
    set_dataindex(from.dataindex());
  }
}

void MDUSATransaction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDUSATransaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDUSATransaction::CopyFrom(const MDUSATransaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDUSATransaction)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDUSATransaction::IsInitialized() const {

  return true;
}

void MDUSATransaction::Swap(MDUSATransaction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDUSATransaction::InternalSwap(MDUSATransaction* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(nanosecond_, other->nanosecond_);
  std::swap(channelno_, other->channelno_);
  tradenum_.Swap(&other->tradenum_);
  originaltradenum_.Swap(&other->originaltradenum_);
  std::swap(tradebuyno_, other->tradebuyno_);
  std::swap(tradesellno_, other->tradesellno_);
  std::swap(tradetype_, other->tradetype_);
  std::swap(tradebsflag_, other->tradebsflag_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(navoffsetamount_, other->navoffsetamount_);
  std::swap(totalconsolidatevolume_, other->totalconsolidatevolume_);
  saleconditionlv1_.Swap(&other->saleconditionlv1_);
  saleconditionlv2_.Swap(&other->saleconditionlv2_);
  saleconditionlv3_.Swap(&other->saleconditionlv3_);
  saleconditionlv4_.Swap(&other->saleconditionlv4_);
  std::swap(trackingnum_, other->trackingnum_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(timeindex_, other->timeindex_);
  std::swap(dataindex_, other->dataindex_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDUSATransaction::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDUSATransaction_descriptor_;
  metadata.reflection = MDUSATransaction_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSATransaction

// optional string HTSCSecurityID = 1;
void MDUSATransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
void MDUSATransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
void MDUSATransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}
::std::string* MDUSATransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDUSATransaction::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDUSATransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.MDDate)
  return mddate_;
}
void MDUSATransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.MDDate)
}

// optional int32 MDTime = 3;
void MDUSATransaction::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDUSATransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.MDTime)
  return mdtime_;
}
void MDUSATransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDUSATransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataTimestamp)
  return datatimestamp_;
}
void MDUSATransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDUSATransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDUSATransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDUSATransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDUSATransaction::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDUSATransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDUSATransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.securityType)
}

// optional int32 ExchangeDate = 7;
void MDUSATransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDUSATransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeDate)
  return exchangedate_;
}
void MDUSATransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDUSATransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDUSATransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeTime)
  return exchangetime_;
}
void MDUSATransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ExchangeTime)
}

// optional int32 Nanosecond = 9;
void MDUSATransaction::clear_nanosecond() {
  nanosecond_ = 0;
}
::google::protobuf::int32 MDUSATransaction::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.Nanosecond)
  return nanosecond_;
}
void MDUSATransaction::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.Nanosecond)
}

// optional int32 ChannelNo = 10;
void MDUSATransaction::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDUSATransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.ChannelNo)
  return channelno_;
}
void MDUSATransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.ChannelNo)
}

// optional string TradeNum = 11;
void MDUSATransaction::clear_tradenum() {
  tradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::tradenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  return tradenum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_tradenum(const ::std::string& value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
void MDUSATransaction::set_tradenum(const char* value) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
void MDUSATransaction::set_tradenum(const char* value, size_t size) {
  
  tradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}
::std::string* MDUSATransaction::mutable_tradenum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  return tradenum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_tradenum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
  
  return tradenum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_tradenum(::std::string* tradenum) {
  if (tradenum != NULL) {
    
  } else {
    
  }
  tradenum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradenum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.TradeNum)
}

// optional string OriginalTradeNum = 12;
void MDUSATransaction::clear_originaltradenum() {
  originaltradenum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::originaltradenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  return originaltradenum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_originaltradenum(const ::std::string& value) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
void MDUSATransaction::set_originaltradenum(const char* value) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
void MDUSATransaction::set_originaltradenum(const char* value, size_t size) {
  
  originaltradenum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}
::std::string* MDUSATransaction::mutable_originaltradenum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  return originaltradenum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_originaltradenum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
  
  return originaltradenum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_originaltradenum(::std::string* originaltradenum) {
  if (originaltradenum != NULL) {
    
  } else {
    
  }
  originaltradenum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), originaltradenum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.OriginalTradeNum)
}

// optional int64 TradeBuyNo = 13;
void MDUSATransaction::clear_tradebuyno() {
  tradebuyno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::tradebuyno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeBuyNo)
  return tradebuyno_;
}
void MDUSATransaction::set_tradebuyno(::google::protobuf::int64 value) {
  
  tradebuyno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeBuyNo)
}

// optional int64 TradeSellNo = 14;
void MDUSATransaction::clear_tradesellno() {
  tradesellno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::tradesellno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeSellNo)
  return tradesellno_;
}
void MDUSATransaction::set_tradesellno(::google::protobuf::int64 value) {
  
  tradesellno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeSellNo)
}

// optional int32 TradeType = 15;
void MDUSATransaction::clear_tradetype() {
  tradetype_ = 0;
}
::google::protobuf::int32 MDUSATransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeType)
  return tradetype_;
}
void MDUSATransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeType)
}

// optional int32 TradeBSFlag = 16;
void MDUSATransaction::clear_tradebsflag() {
  tradebsflag_ = 0;
}
::google::protobuf::int32 MDUSATransaction::tradebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeBSFlag)
  return tradebsflag_;
}
void MDUSATransaction::set_tradebsflag(::google::protobuf::int32 value) {
  
  tradebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeBSFlag)
}

// optional int64 TradePrice = 17;
void MDUSATransaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradePrice)
  return tradeprice_;
}
void MDUSATransaction::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradePrice)
}

// optional int64 TradeQty = 18;
void MDUSATransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeQty)
  return tradeqty_;
}
void MDUSATransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeQty)
}

// optional int64 TradeMoney = 19;
void MDUSATransaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TradeMoney)
  return trademoney_;
}
void MDUSATransaction::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TradeMoney)
}

// optional int64 NAVOffsetAmount = 20;
void MDUSATransaction::clear_navoffsetamount() {
  navoffsetamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::navoffsetamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.NAVOffsetAmount)
  return navoffsetamount_;
}
void MDUSATransaction::set_navoffsetamount(::google::protobuf::int64 value) {
  
  navoffsetamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.NAVOffsetAmount)
}

// optional int64 TotalConsolidateVolume = 21;
void MDUSATransaction::clear_totalconsolidatevolume() {
  totalconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::totalconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TotalConsolidateVolume)
  return totalconsolidatevolume_;
}
void MDUSATransaction::set_totalconsolidatevolume(::google::protobuf::int64 value) {
  
  totalconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TotalConsolidateVolume)
}

// optional string SaleConditionLV1 = 22;
void MDUSATransaction::clear_saleconditionlv1() {
  saleconditionlv1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::saleconditionlv1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  return saleconditionlv1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_saleconditionlv1(const ::std::string& value) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
void MDUSATransaction::set_saleconditionlv1(const char* value) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
void MDUSATransaction::set_saleconditionlv1(const char* value, size_t size) {
  
  saleconditionlv1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}
::std::string* MDUSATransaction::mutable_saleconditionlv1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  return saleconditionlv1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_saleconditionlv1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
  
  return saleconditionlv1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_saleconditionlv1(::std::string* saleconditionlv1) {
  if (saleconditionlv1 != NULL) {
    
  } else {
    
  }
  saleconditionlv1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV1)
}

// optional string SaleConditionLV2 = 23;
void MDUSATransaction::clear_saleconditionlv2() {
  saleconditionlv2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::saleconditionlv2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  return saleconditionlv2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_saleconditionlv2(const ::std::string& value) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
void MDUSATransaction::set_saleconditionlv2(const char* value) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
void MDUSATransaction::set_saleconditionlv2(const char* value, size_t size) {
  
  saleconditionlv2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}
::std::string* MDUSATransaction::mutable_saleconditionlv2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  return saleconditionlv2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_saleconditionlv2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
  
  return saleconditionlv2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_saleconditionlv2(::std::string* saleconditionlv2) {
  if (saleconditionlv2 != NULL) {
    
  } else {
    
  }
  saleconditionlv2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV2)
}

// optional string SaleConditionLV3 = 24;
void MDUSATransaction::clear_saleconditionlv3() {
  saleconditionlv3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::saleconditionlv3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  return saleconditionlv3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_saleconditionlv3(const ::std::string& value) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
void MDUSATransaction::set_saleconditionlv3(const char* value) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
void MDUSATransaction::set_saleconditionlv3(const char* value, size_t size) {
  
  saleconditionlv3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}
::std::string* MDUSATransaction::mutable_saleconditionlv3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  return saleconditionlv3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_saleconditionlv3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
  
  return saleconditionlv3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_saleconditionlv3(::std::string* saleconditionlv3) {
  if (saleconditionlv3 != NULL) {
    
  } else {
    
  }
  saleconditionlv3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV3)
}

// optional string SaleConditionLV4 = 25;
void MDUSATransaction::clear_saleconditionlv4() {
  saleconditionlv4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSATransaction::saleconditionlv4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  return saleconditionlv4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_saleconditionlv4(const ::std::string& value) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
void MDUSATransaction::set_saleconditionlv4(const char* value) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
void MDUSATransaction::set_saleconditionlv4(const char* value, size_t size) {
  
  saleconditionlv4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}
::std::string* MDUSATransaction::mutable_saleconditionlv4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  return saleconditionlv4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSATransaction::release_saleconditionlv4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
  
  return saleconditionlv4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSATransaction::set_allocated_saleconditionlv4(::std::string* saleconditionlv4) {
  if (saleconditionlv4 != NULL) {
    
  } else {
    
  }
  saleconditionlv4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), saleconditionlv4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSATransaction.SaleConditionLV4)
}

// optional int32 TrackingNum = 26;
void MDUSATransaction::clear_trackingnum() {
  trackingnum_ = 0;
}
::google::protobuf::int32 MDUSATransaction::trackingnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TrackingNum)
  return trackingnum_;
}
void MDUSATransaction::set_trackingnum(::google::protobuf::int32 value) {
  
  trackingnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TrackingNum)
}

// optional int32 DataMultiplePowerOf10 = 27;
void MDUSATransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDUSATransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDUSATransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 28;
void MDUSATransaction::clear_timeindex() {
  timeindex_ = 0;
}
::google::protobuf::int32 MDUSATransaction::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.TimeIndex)
  return timeindex_;
}
void MDUSATransaction::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.TimeIndex)
}

// optional int64 DataIndex = 29;
void MDUSATransaction::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSATransaction::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSATransaction.DataIndex)
  return dataindex_;
}
void MDUSATransaction::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSATransaction.DataIndex)
}

inline const MDUSATransaction* MDUSATransaction::internal_default_instance() {
  return &MDUSATransaction_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
