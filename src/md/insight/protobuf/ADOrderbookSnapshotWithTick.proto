syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";
import "MDStock.proto";
import "ADOrderbookSnapshot.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADOrderbookSnapshotWithTickProtos";
option optimize_for = SPEED;

// ADOrderbookSnapshotWithTick message represents combined orderbook snapshot and tick data
message ADOrderbookSnapshotWithTick {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 7;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 8;
    
    // Orderbook snapshot data
    ADOrderbookSnapshot orderbookSnapshot = 10;
    
    // Market tick data for stocks
    MDStock mdStock = 11;
    
    // Data scaling factor (power of 10 multiplier for price/value fields)
    int32 DataMultiplePowerOf10 = 12;
}
