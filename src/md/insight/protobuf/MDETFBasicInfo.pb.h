// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDETFBasicInfo.proto

#ifndef PROTOBUF_MDETFBasicInfo_2eproto__INCLUDED
#define PROTOBUF_MDETFBasicInfo_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDETFBasicInfo_2eproto();
void protobuf_InitDefaults_MDETFBasicInfo_2eproto();
void protobuf_AssignDesc_MDETFBasicInfo_2eproto();
void protobuf_ShutdownFile_MDETFBasicInfo_2eproto();

class MDETFBasicInfo;
class MDETFComponentDetail;

enum MDETFSubstituteFlag {
  DEFAULT_SUBSTITUTE_FLAG = 0,
  DISALLOW_SUBSTITUTE = 1,
  ALLOW_SUBSTITUTE = 2,
  MUST_SUBSTITUTE = 3,
  MDETFSubstituteFlag_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  MDETFSubstituteFlag_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool MDETFSubstituteFlag_IsValid(int value);
const MDETFSubstituteFlag MDETFSubstituteFlag_MIN = DEFAULT_SUBSTITUTE_FLAG;
const MDETFSubstituteFlag MDETFSubstituteFlag_MAX = MUST_SUBSTITUTE;
const int MDETFSubstituteFlag_ARRAYSIZE = MDETFSubstituteFlag_MAX + 1;

const ::google::protobuf::EnumDescriptor* MDETFSubstituteFlag_descriptor();
inline const ::std::string& MDETFSubstituteFlag_Name(MDETFSubstituteFlag value) {
  return ::google::protobuf::internal::NameOfEnum(
    MDETFSubstituteFlag_descriptor(), value);
}
inline bool MDETFSubstituteFlag_Parse(
    const ::std::string& name, MDETFSubstituteFlag* value) {
  return ::google::protobuf::internal::ParseNamedEnum<MDETFSubstituteFlag>(
    MDETFSubstituteFlag_descriptor(), name, value);
}
// ===================================================================

class MDETFBasicInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDETFBasicInfo) */ {
 public:
  MDETFBasicInfo();
  virtual ~MDETFBasicInfo();

  MDETFBasicInfo(const MDETFBasicInfo& from);

  inline MDETFBasicInfo& operator=(const MDETFBasicInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDETFBasicInfo& default_instance();

  static const MDETFBasicInfo* internal_default_instance();

  void Swap(MDETFBasicInfo* other);

  // implements Message ----------------------------------------------

  inline MDETFBasicInfo* New() const { return New(NULL); }

  MDETFBasicInfo* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDETFBasicInfo& from);
  void MergeFrom(const MDETFBasicInfo& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDETFBasicInfo* other);
  void UnsafeMergeFrom(const MDETFBasicInfo& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional string SecurityID = 2;
  void clear_securityid();
  static const int kSecurityIDFieldNumber = 2;
  const ::std::string& securityid() const;
  void set_securityid(const ::std::string& value);
  void set_securityid(const char* value);
  void set_securityid(const char* value, size_t size);
  ::std::string* mutable_securityid();
  ::std::string* release_securityid();
  void set_allocated_securityid(::std::string* securityid);

  // optional string Symbol = 3;
  void clear_symbol();
  static const int kSymbolFieldNumber = 3;
  const ::std::string& symbol() const;
  void set_symbol(const ::std::string& value);
  void set_symbol(const char* value);
  void set_symbol(const char* value, size_t size);
  ::std::string* mutable_symbol();
  ::std::string* release_symbol();
  void set_allocated_symbol(::std::string* symbol);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string CreationID = 11;
  void clear_creationid();
  static const int kCreationIDFieldNumber = 11;
  const ::std::string& creationid() const;
  void set_creationid(const ::std::string& value);
  void set_creationid(const char* value);
  void set_creationid(const char* value, size_t size);
  ::std::string* mutable_creationid();
  ::std::string* release_creationid();
  void set_allocated_creationid(::std::string* creationid);

  // optional string CreationSymbol = 12;
  void clear_creationsymbol();
  static const int kCreationSymbolFieldNumber = 12;
  const ::std::string& creationsymbol() const;
  void set_creationsymbol(const ::std::string& value);
  void set_creationsymbol(const char* value);
  void set_creationsymbol(const char* value, size_t size);
  ::std::string* mutable_creationsymbol();
  ::std::string* release_creationsymbol();
  void set_allocated_creationsymbol(::std::string* creationsymbol);

  // optional string RedemptionID = 13;
  void clear_redemptionid();
  static const int kRedemptionIDFieldNumber = 13;
  const ::std::string& redemptionid() const;
  void set_redemptionid(const ::std::string& value);
  void set_redemptionid(const char* value);
  void set_redemptionid(const char* value, size_t size);
  ::std::string* mutable_redemptionid();
  ::std::string* release_redemptionid();
  void set_allocated_redemptionid(::std::string* redemptionid);

  // optional string RedemptionSymbol = 14;
  void clear_redemptionsymbol();
  static const int kRedemptionSymbolFieldNumber = 14;
  const ::std::string& redemptionsymbol() const;
  void set_redemptionsymbol(const ::std::string& value);
  void set_redemptionsymbol(const char* value);
  void set_redemptionsymbol(const char* value, size_t size);
  ::std::string* mutable_redemptionsymbol();
  ::std::string* release_redemptionsymbol();
  void set_allocated_redemptionsymbol(::std::string* redemptionsymbol);

  // optional string CreationRedemptionCapitalID = 15;
  void clear_creationredemptioncapitalid();
  static const int kCreationRedemptionCapitalIDFieldNumber = 15;
  const ::std::string& creationredemptioncapitalid() const;
  void set_creationredemptioncapitalid(const ::std::string& value);
  void set_creationredemptioncapitalid(const char* value);
  void set_creationredemptioncapitalid(const char* value, size_t size);
  ::std::string* mutable_creationredemptioncapitalid();
  ::std::string* release_creationredemptioncapitalid();
  void set_allocated_creationredemptioncapitalid(::std::string* creationredemptioncapitalid);

  // optional string CreationRedemptionCapitalSymbol = 16;
  void clear_creationredemptioncapitalsymbol();
  static const int kCreationRedemptionCapitalSymbolFieldNumber = 16;
  const ::std::string& creationredemptioncapitalsymbol() const;
  void set_creationredemptioncapitalsymbol(const ::std::string& value);
  void set_creationredemptioncapitalsymbol(const char* value);
  void set_creationredemptioncapitalsymbol(const char* value, size_t size);
  ::std::string* mutable_creationredemptioncapitalsymbol();
  ::std::string* release_creationredemptioncapitalsymbol();
  void set_allocated_creationredemptioncapitalsymbol(::std::string* creationredemptioncapitalsymbol);

  // optional string CrossSourceCapitalID = 17;
  void clear_crosssourcecapitalid();
  static const int kCrossSourceCapitalIDFieldNumber = 17;
  const ::std::string& crosssourcecapitalid() const;
  void set_crosssourcecapitalid(const ::std::string& value);
  void set_crosssourcecapitalid(const char* value);
  void set_crosssourcecapitalid(const char* value, size_t size);
  ::std::string* mutable_crosssourcecapitalid();
  ::std::string* release_crosssourcecapitalid();
  void set_allocated_crosssourcecapitalid(::std::string* crosssourcecapitalid);

  // optional string CrossSourceCapitalSymbol = 18;
  void clear_crosssourcecapitalsymbol();
  static const int kCrossSourceCapitalSymbolFieldNumber = 18;
  const ::std::string& crosssourcecapitalsymbol() const;
  void set_crosssourcecapitalsymbol(const ::std::string& value);
  void set_crosssourcecapitalsymbol(const char* value);
  void set_crosssourcecapitalsymbol(const char* value, size_t size);
  ::std::string* mutable_crosssourcecapitalsymbol();
  ::std::string* release_crosssourcecapitalsymbol();
  void set_allocated_crosssourcecapitalsymbol(::std::string* crosssourcecapitalsymbol);

  // optional string FundManagementCompany = 19;
  void clear_fundmanagementcompany();
  static const int kFundManagementCompanyFieldNumber = 19;
  const ::std::string& fundmanagementcompany() const;
  void set_fundmanagementcompany(const ::std::string& value);
  void set_fundmanagementcompany(const char* value);
  void set_fundmanagementcompany(const char* value, size_t size);
  ::std::string* mutable_fundmanagementcompany();
  ::std::string* release_fundmanagementcompany();
  void set_allocated_fundmanagementcompany(::std::string* fundmanagementcompany);

  // optional string UnderlyingSecurityID = 20;
  void clear_underlyingsecurityid();
  static const int kUnderlyingSecurityIDFieldNumber = 20;
  const ::std::string& underlyingsecurityid() const;
  void set_underlyingsecurityid(const ::std::string& value);
  void set_underlyingsecurityid(const char* value);
  void set_underlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_underlyingsecurityid();
  ::std::string* release_underlyingsecurityid();
  void set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid);

  // optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
  void clear_underlyingsecurityidsource();
  static const int kUnderlyingSecurityIDSourceFieldNumber = 21;
  ::com::htsc::mdc::model::ESecurityIDSource underlyingsecurityidsource() const;
  void set_underlyingsecurityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional double CreationRedemptionUnit = 22;
  void clear_creationredemptionunit();
  static const int kCreationRedemptionUnitFieldNumber = 22;
  double creationredemptionunit() const;
  void set_creationredemptionunit(double value);

  // optional double EstimateCashComponent = 23;
  void clear_estimatecashcomponent();
  static const int kEstimateCashComponentFieldNumber = 23;
  double estimatecashcomponent() const;
  void set_estimatecashcomponent(double value);

  // optional double MaxCashRatio = 24;
  void clear_maxcashratio();
  static const int kMaxCashRatioFieldNumber = 24;
  double maxcashratio() const;
  void set_maxcashratio(double value);

  // optional bool IsPublish = 25;
  void clear_ispublish();
  static const int kIsPublishFieldNumber = 25;
  bool ispublish() const;
  void set_ispublish(bool value);

  // optional bool IsAllowCreation = 26;
  void clear_isallowcreation();
  static const int kIsAllowCreationFieldNumber = 26;
  bool isallowcreation() const;
  void set_isallowcreation(bool value);

  // optional bool IsAllowRedemption = 27;
  void clear_isallowredemption();
  static const int kIsAllowRedemptionFieldNumber = 27;
  bool isallowredemption() const;
  void set_isallowredemption(bool value);

  // optional int64 RecordNum = 28;
  void clear_recordnum();
  static const int kRecordNumFieldNumber = 28;
  ::google::protobuf::int64 recordnum() const;
  void set_recordnum(::google::protobuf::int64 value);

  // optional string TradingDay = 29;
  void clear_tradingday();
  static const int kTradingDayFieldNumber = 29;
  const ::std::string& tradingday() const;
  void set_tradingday(const ::std::string& value);
  void set_tradingday(const char* value);
  void set_tradingday(const char* value, size_t size);
  ::std::string* mutable_tradingday();
  ::std::string* release_tradingday();
  void set_allocated_tradingday(::std::string* tradingday);

  // optional string PreTradingDay = 30;
  void clear_pretradingday();
  static const int kPreTradingDayFieldNumber = 30;
  const ::std::string& pretradingday() const;
  void set_pretradingday(const ::std::string& value);
  void set_pretradingday(const char* value);
  void set_pretradingday(const char* value, size_t size);
  ::std::string* mutable_pretradingday();
  ::std::string* release_pretradingday();
  void set_allocated_pretradingday(::std::string* pretradingday);

  // optional double CashComponent = 31;
  void clear_cashcomponent();
  static const int kCashComponentFieldNumber = 31;
  double cashcomponent() const;
  void set_cashcomponent(double value);

  // optional double NAVperCU = 32;
  void clear_navpercu();
  static const int kNAVperCUFieldNumber = 32;
  double navpercu() const;
  void set_navpercu(double value);

  // optional double NAV = 33;
  void clear_nav();
  static const int kNAVFieldNumber = 33;
  double nav() const;
  void set_nav(double value);

  // optional double DividendPerCU = 34;
  void clear_dividendpercu();
  static const int kDividendPerCUFieldNumber = 34;
  double dividendpercu() const;
  void set_dividendpercu(double value);

  // optional double CreationLimit = 35;
  void clear_creationlimit();
  static const int kCreationLimitFieldNumber = 35;
  double creationlimit() const;
  void set_creationlimit(double value);

  // optional double RedemptionLimit = 36;
  void clear_redemptionlimit();
  static const int kRedemptionLimitFieldNumber = 36;
  double redemptionlimit() const;
  void set_redemptionlimit(double value);

  // optional double CreationLimitPerUser = 37;
  void clear_creationlimitperuser();
  static const int kCreationLimitPerUserFieldNumber = 37;
  double creationlimitperuser() const;
  void set_creationlimitperuser(double value);

  // optional double RedemptionLimitPerUser = 38;
  void clear_redemptionlimitperuser();
  static const int kRedemptionLimitPerUserFieldNumber = 38;
  double redemptionlimitperuser() const;
  void set_redemptionlimitperuser(double value);

  // optional double NetCreationLimit = 39;
  void clear_netcreationlimit();
  static const int kNetCreationLimitFieldNumber = 39;
  double netcreationlimit() const;
  void set_netcreationlimit(double value);

  // optional double NetRedemptionLimit = 40;
  void clear_netredemptionlimit();
  static const int kNetRedemptionLimitFieldNumber = 40;
  double netredemptionlimit() const;
  void set_netredemptionlimit(double value);

  // optional double NetCreationLimitPerUser = 41;
  void clear_netcreationlimitperuser();
  static const int kNetCreationLimitPerUserFieldNumber = 41;
  double netcreationlimitperuser() const;
  void set_netcreationlimitperuser(double value);

  // optional double NetRedemptionLimitPerUser = 42;
  void clear_netredemptionlimitperuser();
  static const int kNetRedemptionLimitPerUserFieldNumber = 42;
  double netredemptionlimitperuser() const;
  void set_netredemptionlimitperuser(double value);

  // repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
  int etfcomponents_size() const;
  void clear_etfcomponents();
  static const int kETFComponentsFieldNumber = 43;
  const ::com::htsc::mdc::insight::model::MDETFComponentDetail& etfcomponents(int index) const;
  ::com::htsc::mdc::insight::model::MDETFComponentDetail* mutable_etfcomponents(int index);
  ::com::htsc::mdc::insight::model::MDETFComponentDetail* add_etfcomponents();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >*
      mutable_etfcomponents();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >&
      etfcomponents() const;

  // optional string FormerSymbol = 44;
  void clear_formersymbol();
  static const int kFormerSymbolFieldNumber = 44;
  const ::std::string& formersymbol() const;
  void set_formersymbol(const ::std::string& value);
  void set_formersymbol(const char* value);
  void set_formersymbol(const char* value, size_t size);
  ::std::string* mutable_formersymbol();
  ::std::string* release_formersymbol();
  void set_allocated_formersymbol(::std::string* formersymbol);

  // optional bool CrossMarket = 45;
  void clear_crossmarket();
  static const int kCrossMarketFieldNumber = 45;
  bool crossmarket() const;
  void set_crossmarket(bool value);

  // optional int32 DataMultiplePowerOf10 = 46;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 46;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDETFBasicInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail > etfcomponents_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securityid_;
  ::google::protobuf::internal::ArenaStringPtr symbol_;
  ::google::protobuf::internal::ArenaStringPtr creationid_;
  ::google::protobuf::internal::ArenaStringPtr creationsymbol_;
  ::google::protobuf::internal::ArenaStringPtr redemptionid_;
  ::google::protobuf::internal::ArenaStringPtr redemptionsymbol_;
  ::google::protobuf::internal::ArenaStringPtr creationredemptioncapitalid_;
  ::google::protobuf::internal::ArenaStringPtr creationredemptioncapitalsymbol_;
  ::google::protobuf::internal::ArenaStringPtr crosssourcecapitalid_;
  ::google::protobuf::internal::ArenaStringPtr crosssourcecapitalsymbol_;
  ::google::protobuf::internal::ArenaStringPtr fundmanagementcompany_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingday_;
  ::google::protobuf::internal::ArenaStringPtr pretradingday_;
  ::google::protobuf::internal::ArenaStringPtr formersymbol_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  int securityidsource_;
  int securitytype_;
  double creationredemptionunit_;
  double estimatecashcomponent_;
  double maxcashratio_;
  ::google::protobuf::int64 recordnum_;
  int underlyingsecurityidsource_;
  bool ispublish_;
  bool isallowcreation_;
  bool isallowredemption_;
  bool crossmarket_;
  double cashcomponent_;
  double navpercu_;
  double nav_;
  double dividendpercu_;
  double creationlimit_;
  double redemptionlimit_;
  double creationlimitperuser_;
  double redemptionlimitperuser_;
  double netcreationlimit_;
  double netredemptionlimit_;
  double netcreationlimitperuser_;
  double netredemptionlimitperuser_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDETFBasicInfo_2eproto_impl();
  friend void  protobuf_AddDesc_MDETFBasicInfo_2eproto_impl();
  friend void protobuf_AssignDesc_MDETFBasicInfo_2eproto();
  friend void protobuf_ShutdownFile_MDETFBasicInfo_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDETFBasicInfo> MDETFBasicInfo_default_instance_;

// -------------------------------------------------------------------

class MDETFComponentDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDETFComponentDetail) */ {
 public:
  MDETFComponentDetail();
  virtual ~MDETFComponentDetail();

  MDETFComponentDetail(const MDETFComponentDetail& from);

  inline MDETFComponentDetail& operator=(const MDETFComponentDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDETFComponentDetail& default_instance();

  static const MDETFComponentDetail* internal_default_instance();

  void Swap(MDETFComponentDetail* other);

  // implements Message ----------------------------------------------

  inline MDETFComponentDetail* New() const { return New(NULL); }

  MDETFComponentDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDETFComponentDetail& from);
  void MergeFrom(const MDETFComponentDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDETFComponentDetail* other);
  void UnsafeMergeFrom(const MDETFComponentDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional string SecurityID = 2;
  void clear_securityid();
  static const int kSecurityIDFieldNumber = 2;
  const ::std::string& securityid() const;
  void set_securityid(const ::std::string& value);
  void set_securityid(const char* value);
  void set_securityid(const char* value, size_t size);
  ::std::string* mutable_securityid();
  ::std::string* release_securityid();
  void set_allocated_securityid(::std::string* securityid);

  // optional string Symbol = 3;
  void clear_symbol();
  static const int kSymbolFieldNumber = 3;
  const ::std::string& symbol() const;
  void set_symbol(const ::std::string& value);
  void set_symbol(const char* value);
  void set_symbol(const char* value, size_t size);
  ::std::string* mutable_symbol();
  ::std::string* release_symbol();
  void set_allocated_symbol(::std::string* symbol);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 4;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional double ComponentShare = 5;
  void clear_componentshare();
  static const int kComponentShareFieldNumber = 5;
  double componentshare() const;
  void set_componentshare(double value);

  // optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
  void clear_substituteflag();
  static const int kSubstituteFlagFieldNumber = 6;
  ::com::htsc::mdc::insight::model::MDETFSubstituteFlag substituteflag() const;
  void set_substituteflag(::com::htsc::mdc::insight::model::MDETFSubstituteFlag value);

  // optional double PremiumRatio = 7;
  void clear_premiumratio();
  static const int kPremiumRatioFieldNumber = 7;
  double premiumratio() const;
  void set_premiumratio(double value);

  // optional double CreationCashSubstitute = 8;
  void clear_creationcashsubstitute();
  static const int kCreationCashSubstituteFieldNumber = 8;
  double creationcashsubstitute() const;
  void set_creationcashsubstitute(double value);

  // optional double RedemptionCashSubstitute = 9;
  void clear_redemptioncashsubstitute();
  static const int kRedemptionCashSubstituteFieldNumber = 9;
  double redemptioncashsubstitute() const;
  void set_redemptioncashsubstitute(double value);

  // optional double TotalCashSubstitute = 10;
  void clear_totalcashsubstitute();
  static const int kTotalCashSubstituteFieldNumber = 10;
  double totalcashsubstitute() const;
  void set_totalcashsubstitute(double value);

  // optional double DiscountRatio = 11;
  void clear_discountratio();
  static const int kDiscountRatioFieldNumber = 11;
  double discountratio() const;
  void set_discountratio(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDETFComponentDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securityid_;
  ::google::protobuf::internal::ArenaStringPtr symbol_;
  double componentshare_;
  int securityidsource_;
  int substituteflag_;
  double premiumratio_;
  double creationcashsubstitute_;
  double redemptioncashsubstitute_;
  double totalcashsubstitute_;
  double discountratio_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDETFBasicInfo_2eproto_impl();
  friend void  protobuf_AddDesc_MDETFBasicInfo_2eproto_impl();
  friend void protobuf_AssignDesc_MDETFBasicInfo_2eproto();
  friend void protobuf_ShutdownFile_MDETFBasicInfo_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDETFComponentDetail> MDETFComponentDetail_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDETFBasicInfo

// optional string HTSCSecurityID = 1;
inline void MDETFBasicInfo::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
inline void MDETFBasicInfo::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
inline void MDETFBasicInfo::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
inline ::std::string* MDETFBasicInfo::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}

// optional string SecurityID = 2;
inline void MDETFBasicInfo::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
inline void MDETFBasicInfo::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
inline void MDETFBasicInfo::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
inline ::std::string* MDETFBasicInfo::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}

// optional string Symbol = 3;
inline void MDETFBasicInfo::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
inline void MDETFBasicInfo::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
inline void MDETFBasicInfo::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
inline ::std::string* MDETFBasicInfo::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}

// optional int32 MDDate = 4;
inline void MDETFBasicInfo::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDETFBasicInfo::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MDDate)
  return mddate_;
}
inline void MDETFBasicInfo::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MDDate)
}

// optional int32 MDTime = 5;
inline void MDETFBasicInfo::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDETFBasicInfo::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MDTime)
  return mdtime_;
}
inline void MDETFBasicInfo::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MDTime)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDETFBasicInfo::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDETFBasicInfo::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDETFBasicInfo::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDETFBasicInfo::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDETFBasicInfo::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDETFBasicInfo::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.securityType)
}

// optional string CreationID = 11;
inline void MDETFBasicInfo::clear_creationid() {
  creationid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::creationid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  return creationid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_creationid(const ::std::string& value) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
inline void MDETFBasicInfo::set_creationid(const char* value) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
inline void MDETFBasicInfo::set_creationid(const char* value, size_t size) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
inline ::std::string* MDETFBasicInfo::mutable_creationid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  return creationid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_creationid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  
  return creationid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_creationid(::std::string* creationid) {
  if (creationid != NULL) {
    
  } else {
    
  }
  creationid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}

// optional string CreationSymbol = 12;
inline void MDETFBasicInfo::clear_creationsymbol() {
  creationsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::creationsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  return creationsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_creationsymbol(const ::std::string& value) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
inline void MDETFBasicInfo::set_creationsymbol(const char* value) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
inline void MDETFBasicInfo::set_creationsymbol(const char* value, size_t size) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
inline ::std::string* MDETFBasicInfo::mutable_creationsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  return creationsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_creationsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  
  return creationsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_creationsymbol(::std::string* creationsymbol) {
  if (creationsymbol != NULL) {
    
  } else {
    
  }
  creationsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}

// optional string RedemptionID = 13;
inline void MDETFBasicInfo::clear_redemptionid() {
  redemptionid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::redemptionid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  return redemptionid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_redemptionid(const ::std::string& value) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
inline void MDETFBasicInfo::set_redemptionid(const char* value) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
inline void MDETFBasicInfo::set_redemptionid(const char* value, size_t size) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
inline ::std::string* MDETFBasicInfo::mutable_redemptionid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  return redemptionid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_redemptionid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  
  return redemptionid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_redemptionid(::std::string* redemptionid) {
  if (redemptionid != NULL) {
    
  } else {
    
  }
  redemptionid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), redemptionid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}

// optional string RedemptionSymbol = 14;
inline void MDETFBasicInfo::clear_redemptionsymbol() {
  redemptionsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::redemptionsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  return redemptionsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_redemptionsymbol(const ::std::string& value) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
inline void MDETFBasicInfo::set_redemptionsymbol(const char* value) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
inline void MDETFBasicInfo::set_redemptionsymbol(const char* value, size_t size) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
inline ::std::string* MDETFBasicInfo::mutable_redemptionsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  return redemptionsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_redemptionsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  
  return redemptionsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_redemptionsymbol(::std::string* redemptionsymbol) {
  if (redemptionsymbol != NULL) {
    
  } else {
    
  }
  redemptionsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), redemptionsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}

// optional string CreationRedemptionCapitalID = 15;
inline void MDETFBasicInfo::clear_creationredemptioncapitalid() {
  creationredemptioncapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::creationredemptioncapitalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  return creationredemptioncapitalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_creationredemptioncapitalid(const ::std::string& value) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
inline void MDETFBasicInfo::set_creationredemptioncapitalid(const char* value) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
inline void MDETFBasicInfo::set_creationredemptioncapitalid(const char* value, size_t size) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
inline ::std::string* MDETFBasicInfo::mutable_creationredemptioncapitalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  return creationredemptioncapitalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_creationredemptioncapitalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  
  return creationredemptioncapitalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_creationredemptioncapitalid(::std::string* creationredemptioncapitalid) {
  if (creationredemptioncapitalid != NULL) {
    
  } else {
    
  }
  creationredemptioncapitalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationredemptioncapitalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}

// optional string CreationRedemptionCapitalSymbol = 16;
inline void MDETFBasicInfo::clear_creationredemptioncapitalsymbol() {
  creationredemptioncapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::creationredemptioncapitalsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  return creationredemptioncapitalsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const ::std::string& value) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
inline void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const char* value) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
inline void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const char* value, size_t size) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
inline ::std::string* MDETFBasicInfo::mutable_creationredemptioncapitalsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  return creationredemptioncapitalsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_creationredemptioncapitalsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  
  return creationredemptioncapitalsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_creationredemptioncapitalsymbol(::std::string* creationredemptioncapitalsymbol) {
  if (creationredemptioncapitalsymbol != NULL) {
    
  } else {
    
  }
  creationredemptioncapitalsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationredemptioncapitalsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}

// optional string CrossSourceCapitalID = 17;
inline void MDETFBasicInfo::clear_crosssourcecapitalid() {
  crosssourcecapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::crosssourcecapitalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  return crosssourcecapitalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_crosssourcecapitalid(const ::std::string& value) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
inline void MDETFBasicInfo::set_crosssourcecapitalid(const char* value) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
inline void MDETFBasicInfo::set_crosssourcecapitalid(const char* value, size_t size) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
inline ::std::string* MDETFBasicInfo::mutable_crosssourcecapitalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  return crosssourcecapitalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_crosssourcecapitalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  
  return crosssourcecapitalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_crosssourcecapitalid(::std::string* crosssourcecapitalid) {
  if (crosssourcecapitalid != NULL) {
    
  } else {
    
  }
  crosssourcecapitalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), crosssourcecapitalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}

// optional string CrossSourceCapitalSymbol = 18;
inline void MDETFBasicInfo::clear_crosssourcecapitalsymbol() {
  crosssourcecapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::crosssourcecapitalsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  return crosssourcecapitalsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_crosssourcecapitalsymbol(const ::std::string& value) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
inline void MDETFBasicInfo::set_crosssourcecapitalsymbol(const char* value) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
inline void MDETFBasicInfo::set_crosssourcecapitalsymbol(const char* value, size_t size) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
inline ::std::string* MDETFBasicInfo::mutable_crosssourcecapitalsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  return crosssourcecapitalsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_crosssourcecapitalsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  
  return crosssourcecapitalsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_crosssourcecapitalsymbol(::std::string* crosssourcecapitalsymbol) {
  if (crosssourcecapitalsymbol != NULL) {
    
  } else {
    
  }
  crosssourcecapitalsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), crosssourcecapitalsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}

// optional string FundManagementCompany = 19;
inline void MDETFBasicInfo::clear_fundmanagementcompany() {
  fundmanagementcompany_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::fundmanagementcompany() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  return fundmanagementcompany_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_fundmanagementcompany(const ::std::string& value) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
inline void MDETFBasicInfo::set_fundmanagementcompany(const char* value) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
inline void MDETFBasicInfo::set_fundmanagementcompany(const char* value, size_t size) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
inline ::std::string* MDETFBasicInfo::mutable_fundmanagementcompany() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  return fundmanagementcompany_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_fundmanagementcompany() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  
  return fundmanagementcompany_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_fundmanagementcompany(::std::string* fundmanagementcompany) {
  if (fundmanagementcompany != NULL) {
    
  } else {
    
  }
  fundmanagementcompany_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fundmanagementcompany);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}

// optional string UnderlyingSecurityID = 20;
inline void MDETFBasicInfo::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
inline void MDETFBasicInfo::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
inline void MDETFBasicInfo::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
inline ::std::string* MDETFBasicInfo::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
inline void MDETFBasicInfo::clear_underlyingsecurityidsource() {
  underlyingsecurityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDETFBasicInfo::underlyingsecurityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(underlyingsecurityidsource_);
}
inline void MDETFBasicInfo::set_underlyingsecurityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  underlyingsecurityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityIDSource)
}

// optional double CreationRedemptionUnit = 22;
inline void MDETFBasicInfo::clear_creationredemptionunit() {
  creationredemptionunit_ = 0;
}
inline double MDETFBasicInfo::creationredemptionunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionUnit)
  return creationredemptionunit_;
}
inline void MDETFBasicInfo::set_creationredemptionunit(double value) {
  
  creationredemptionunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionUnit)
}

// optional double EstimateCashComponent = 23;
inline void MDETFBasicInfo::clear_estimatecashcomponent() {
  estimatecashcomponent_ = 0;
}
inline double MDETFBasicInfo::estimatecashcomponent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.EstimateCashComponent)
  return estimatecashcomponent_;
}
inline void MDETFBasicInfo::set_estimatecashcomponent(double value) {
  
  estimatecashcomponent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.EstimateCashComponent)
}

// optional double MaxCashRatio = 24;
inline void MDETFBasicInfo::clear_maxcashratio() {
  maxcashratio_ = 0;
}
inline double MDETFBasicInfo::maxcashratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MaxCashRatio)
  return maxcashratio_;
}
inline void MDETFBasicInfo::set_maxcashratio(double value) {
  
  maxcashratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MaxCashRatio)
}

// optional bool IsPublish = 25;
inline void MDETFBasicInfo::clear_ispublish() {
  ispublish_ = false;
}
inline bool MDETFBasicInfo::ispublish() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsPublish)
  return ispublish_;
}
inline void MDETFBasicInfo::set_ispublish(bool value) {
  
  ispublish_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsPublish)
}

// optional bool IsAllowCreation = 26;
inline void MDETFBasicInfo::clear_isallowcreation() {
  isallowcreation_ = false;
}
inline bool MDETFBasicInfo::isallowcreation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowCreation)
  return isallowcreation_;
}
inline void MDETFBasicInfo::set_isallowcreation(bool value) {
  
  isallowcreation_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowCreation)
}

// optional bool IsAllowRedemption = 27;
inline void MDETFBasicInfo::clear_isallowredemption() {
  isallowredemption_ = false;
}
inline bool MDETFBasicInfo::isallowredemption() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowRedemption)
  return isallowredemption_;
}
inline void MDETFBasicInfo::set_isallowredemption(bool value) {
  
  isallowredemption_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowRedemption)
}

// optional int64 RecordNum = 28;
inline void MDETFBasicInfo::clear_recordnum() {
  recordnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDETFBasicInfo::recordnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RecordNum)
  return recordnum_;
}
inline void MDETFBasicInfo::set_recordnum(::google::protobuf::int64 value) {
  
  recordnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RecordNum)
}

// optional string TradingDay = 29;
inline void MDETFBasicInfo::clear_tradingday() {
  tradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::tradingday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  return tradingday_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_tradingday(const ::std::string& value) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
inline void MDETFBasicInfo::set_tradingday(const char* value) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
inline void MDETFBasicInfo::set_tradingday(const char* value, size_t size) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
inline ::std::string* MDETFBasicInfo::mutable_tradingday() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  return tradingday_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_tradingday() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  
  return tradingday_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_tradingday(::std::string* tradingday) {
  if (tradingday != NULL) {
    
  } else {
    
  }
  tradingday_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingday);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}

// optional string PreTradingDay = 30;
inline void MDETFBasicInfo::clear_pretradingday() {
  pretradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::pretradingday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  return pretradingday_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_pretradingday(const ::std::string& value) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
inline void MDETFBasicInfo::set_pretradingday(const char* value) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
inline void MDETFBasicInfo::set_pretradingday(const char* value, size_t size) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
inline ::std::string* MDETFBasicInfo::mutable_pretradingday() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  return pretradingday_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_pretradingday() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  
  return pretradingday_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_pretradingday(::std::string* pretradingday) {
  if (pretradingday != NULL) {
    
  } else {
    
  }
  pretradingday_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pretradingday);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}

// optional double CashComponent = 31;
inline void MDETFBasicInfo::clear_cashcomponent() {
  cashcomponent_ = 0;
}
inline double MDETFBasicInfo::cashcomponent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CashComponent)
  return cashcomponent_;
}
inline void MDETFBasicInfo::set_cashcomponent(double value) {
  
  cashcomponent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CashComponent)
}

// optional double NAVperCU = 32;
inline void MDETFBasicInfo::clear_navpercu() {
  navpercu_ = 0;
}
inline double MDETFBasicInfo::navpercu() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NAVperCU)
  return navpercu_;
}
inline void MDETFBasicInfo::set_navpercu(double value) {
  
  navpercu_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NAVperCU)
}

// optional double NAV = 33;
inline void MDETFBasicInfo::clear_nav() {
  nav_ = 0;
}
inline double MDETFBasicInfo::nav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NAV)
  return nav_;
}
inline void MDETFBasicInfo::set_nav(double value) {
  
  nav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NAV)
}

// optional double DividendPerCU = 34;
inline void MDETFBasicInfo::clear_dividendpercu() {
  dividendpercu_ = 0;
}
inline double MDETFBasicInfo::dividendpercu() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.DividendPerCU)
  return dividendpercu_;
}
inline void MDETFBasicInfo::set_dividendpercu(double value) {
  
  dividendpercu_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.DividendPerCU)
}

// optional double CreationLimit = 35;
inline void MDETFBasicInfo::clear_creationlimit() {
  creationlimit_ = 0;
}
inline double MDETFBasicInfo::creationlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimit)
  return creationlimit_;
}
inline void MDETFBasicInfo::set_creationlimit(double value) {
  
  creationlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimit)
}

// optional double RedemptionLimit = 36;
inline void MDETFBasicInfo::clear_redemptionlimit() {
  redemptionlimit_ = 0;
}
inline double MDETFBasicInfo::redemptionlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimit)
  return redemptionlimit_;
}
inline void MDETFBasicInfo::set_redemptionlimit(double value) {
  
  redemptionlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimit)
}

// optional double CreationLimitPerUser = 37;
inline void MDETFBasicInfo::clear_creationlimitperuser() {
  creationlimitperuser_ = 0;
}
inline double MDETFBasicInfo::creationlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimitPerUser)
  return creationlimitperuser_;
}
inline void MDETFBasicInfo::set_creationlimitperuser(double value) {
  
  creationlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimitPerUser)
}

// optional double RedemptionLimitPerUser = 38;
inline void MDETFBasicInfo::clear_redemptionlimitperuser() {
  redemptionlimitperuser_ = 0;
}
inline double MDETFBasicInfo::redemptionlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimitPerUser)
  return redemptionlimitperuser_;
}
inline void MDETFBasicInfo::set_redemptionlimitperuser(double value) {
  
  redemptionlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimitPerUser)
}

// optional double NetCreationLimit = 39;
inline void MDETFBasicInfo::clear_netcreationlimit() {
  netcreationlimit_ = 0;
}
inline double MDETFBasicInfo::netcreationlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimit)
  return netcreationlimit_;
}
inline void MDETFBasicInfo::set_netcreationlimit(double value) {
  
  netcreationlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimit)
}

// optional double NetRedemptionLimit = 40;
inline void MDETFBasicInfo::clear_netredemptionlimit() {
  netredemptionlimit_ = 0;
}
inline double MDETFBasicInfo::netredemptionlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimit)
  return netredemptionlimit_;
}
inline void MDETFBasicInfo::set_netredemptionlimit(double value) {
  
  netredemptionlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimit)
}

// optional double NetCreationLimitPerUser = 41;
inline void MDETFBasicInfo::clear_netcreationlimitperuser() {
  netcreationlimitperuser_ = 0;
}
inline double MDETFBasicInfo::netcreationlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimitPerUser)
  return netcreationlimitperuser_;
}
inline void MDETFBasicInfo::set_netcreationlimitperuser(double value) {
  
  netcreationlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimitPerUser)
}

// optional double NetRedemptionLimitPerUser = 42;
inline void MDETFBasicInfo::clear_netredemptionlimitperuser() {
  netredemptionlimitperuser_ = 0;
}
inline double MDETFBasicInfo::netredemptionlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimitPerUser)
  return netredemptionlimitperuser_;
}
inline void MDETFBasicInfo::set_netredemptionlimitperuser(double value) {
  
  netredemptionlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimitPerUser)
}

// repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
inline int MDETFBasicInfo::etfcomponents_size() const {
  return etfcomponents_.size();
}
inline void MDETFBasicInfo::clear_etfcomponents() {
  etfcomponents_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDETFComponentDetail& MDETFBasicInfo::etfcomponents(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDETFComponentDetail* MDETFBasicInfo::mutable_etfcomponents(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDETFComponentDetail* MDETFBasicInfo::add_etfcomponents() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >*
MDETFBasicInfo::mutable_etfcomponents() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return &etfcomponents_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >&
MDETFBasicInfo::etfcomponents() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_;
}

// optional string FormerSymbol = 44;
inline void MDETFBasicInfo::clear_formersymbol() {
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFBasicInfo::formersymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  return formersymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_formersymbol(const ::std::string& value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
inline void MDETFBasicInfo::set_formersymbol(const char* value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
inline void MDETFBasicInfo::set_formersymbol(const char* value, size_t size) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
inline ::std::string* MDETFBasicInfo::mutable_formersymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  return formersymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFBasicInfo::release_formersymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  
  return formersymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFBasicInfo::set_allocated_formersymbol(::std::string* formersymbol) {
  if (formersymbol != NULL) {
    
  } else {
    
  }
  formersymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), formersymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}

// optional bool CrossMarket = 45;
inline void MDETFBasicInfo::clear_crossmarket() {
  crossmarket_ = false;
}
inline bool MDETFBasicInfo::crossmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossMarket)
  return crossmarket_;
}
inline void MDETFBasicInfo::set_crossmarket(bool value) {
  
  crossmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossMarket)
}

// optional int32 DataMultiplePowerOf10 = 46;
inline void MDETFBasicInfo::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDETFBasicInfo::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDETFBasicInfo::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.DataMultiplePowerOf10)
}

inline const MDETFBasicInfo* MDETFBasicInfo::internal_default_instance() {
  return &MDETFBasicInfo_default_instance_.get();
}
// -------------------------------------------------------------------

// MDETFComponentDetail

// optional string HTSCSecurityID = 1;
inline void MDETFComponentDetail::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFComponentDetail::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
inline void MDETFComponentDetail::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
inline void MDETFComponentDetail::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
inline ::std::string* MDETFComponentDetail::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFComponentDetail::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}

// optional string SecurityID = 2;
inline void MDETFComponentDetail::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFComponentDetail::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
inline void MDETFComponentDetail::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
inline void MDETFComponentDetail::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
inline ::std::string* MDETFComponentDetail::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFComponentDetail::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}

// optional string Symbol = 3;
inline void MDETFComponentDetail::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDETFComponentDetail::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
inline void MDETFComponentDetail::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
inline void MDETFComponentDetail::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
inline ::std::string* MDETFComponentDetail::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDETFComponentDetail::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDETFComponentDetail::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
inline void MDETFComponentDetail::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDETFComponentDetail::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDETFComponentDetail::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.securityIDSource)
}

// optional double ComponentShare = 5;
inline void MDETFComponentDetail::clear_componentshare() {
  componentshare_ = 0;
}
inline double MDETFComponentDetail::componentshare() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.ComponentShare)
  return componentshare_;
}
inline void MDETFComponentDetail::set_componentshare(double value) {
  
  componentshare_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.ComponentShare)
}

// optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
inline void MDETFComponentDetail::clear_substituteflag() {
  substituteflag_ = 0;
}
inline ::com::htsc::mdc::insight::model::MDETFSubstituteFlag MDETFComponentDetail::substituteflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.SubstituteFlag)
  return static_cast< ::com::htsc::mdc::insight::model::MDETFSubstituteFlag >(substituteflag_);
}
inline void MDETFComponentDetail::set_substituteflag(::com::htsc::mdc::insight::model::MDETFSubstituteFlag value) {
  
  substituteflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.SubstituteFlag)
}

// optional double PremiumRatio = 7;
inline void MDETFComponentDetail::clear_premiumratio() {
  premiumratio_ = 0;
}
inline double MDETFComponentDetail::premiumratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.PremiumRatio)
  return premiumratio_;
}
inline void MDETFComponentDetail::set_premiumratio(double value) {
  
  premiumratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.PremiumRatio)
}

// optional double CreationCashSubstitute = 8;
inline void MDETFComponentDetail::clear_creationcashsubstitute() {
  creationcashsubstitute_ = 0;
}
inline double MDETFComponentDetail::creationcashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.CreationCashSubstitute)
  return creationcashsubstitute_;
}
inline void MDETFComponentDetail::set_creationcashsubstitute(double value) {
  
  creationcashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.CreationCashSubstitute)
}

// optional double RedemptionCashSubstitute = 9;
inline void MDETFComponentDetail::clear_redemptioncashsubstitute() {
  redemptioncashsubstitute_ = 0;
}
inline double MDETFComponentDetail::redemptioncashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.RedemptionCashSubstitute)
  return redemptioncashsubstitute_;
}
inline void MDETFComponentDetail::set_redemptioncashsubstitute(double value) {
  
  redemptioncashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.RedemptionCashSubstitute)
}

// optional double TotalCashSubstitute = 10;
inline void MDETFComponentDetail::clear_totalcashsubstitute() {
  totalcashsubstitute_ = 0;
}
inline double MDETFComponentDetail::totalcashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.TotalCashSubstitute)
  return totalcashsubstitute_;
}
inline void MDETFComponentDetail::set_totalcashsubstitute(double value) {
  
  totalcashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.TotalCashSubstitute)
}

// optional double DiscountRatio = 11;
inline void MDETFComponentDetail::clear_discountratio() {
  discountratio_ = 0;
}
inline double MDETFComponentDetail::discountratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.DiscountRatio)
  return discountratio_;
}
inline void MDETFComponentDetail::set_discountratio(double value) {
  
  discountratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.DiscountRatio)
}

inline const MDETFComponentDetail* MDETFComponentDetail::internal_default_instance() {
  return &MDETFComponentDetail_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::com::htsc::mdc::insight::model::MDETFSubstituteFlag> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::htsc::mdc::insight::model::MDETFSubstituteFlag>() {
  return ::com::htsc::mdc::insight::model::MDETFSubstituteFlag_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDETFBasicInfo_2eproto__INCLUDED
