// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDIndex.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDIndex.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDIndex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDIndex_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDIndex_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDIndex_2eproto() {
  protobuf_AddDesc_MDIndex_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDIndex.proto");
  GOOGLE_CHECK(file != NULL);
  MDIndex_descriptor_ = file->message_type(0);
  static const int MDIndex_offsets_[29] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalbuyvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalbuyvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalsellvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalsellvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, totalsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, delaytype_),
  };
  MDIndex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDIndex_descriptor_,
      MDIndex::internal_default_instance(),
      MDIndex_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDIndex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIndex, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDIndex_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDIndex_descriptor_, MDIndex::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDIndex_2eproto() {
  MDIndex_default_instance_.Shutdown();
  delete MDIndex_reflection_;
}

void protobuf_InitDefaults_MDIndex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDIndex_default_instance_.DefaultConstruct();
  MDIndex_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDIndex_2eproto_once_);
void protobuf_InitDefaults_MDIndex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDIndex_2eproto_once_,
                 &protobuf_InitDefaults_MDIndex_2eproto_impl);
}
void protobuf_AddDesc_MDIndex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDIndex_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rMDIndex.proto\022\032com.htsc.mdc.insight.mo"
    "del\032\027ESecurityIDSource.proto\032\023ESecurityT"
    "ype.proto\"\312\005\n\007MDIndex\022\026\n\016HTSCSecurityID\030"
    "\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n"
    "\rDataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode"
    "\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\0227\n\014secur"
    "ityType\030\007 \001(\0162!.com.htsc.mdc.model.ESecu"
    "rityType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022"
    "\n\nPreClosePx\030\n \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022\030\n"
    "\020TotalVolumeTrade\030\014 \001(\003\022\027\n\017TotalValueTra"
    "de\030\r \001(\003\022\016\n\006LastPx\030\016 \001(\003\022\016\n\006OpenPx\030\017 \001(\003"
    "\022\017\n\007ClosePx\030\020 \001(\003\022\016\n\006HighPx\030\021 \001(\003\022\r\n\005Low"
    "Px\030\022 \001(\003\022\021\n\tChannelNo\030\023 \001(\005\022\024\n\014ExchangeD"
    "ate\030\024 \001(\005\022\024\n\014ExchangeTime\030\025 \001(\005\022\033\n\023Total"
    "BuyVolumeTrade\030\026 \001(\003\022\032\n\022TotalBuyValueTra"
    "de\030\027 \001(\003\022\026\n\016TotalBuyNumber\030\030 \001(\003\022\034\n\024Tota"
    "lSellVolumeTrade\030\031 \001(\003\022\033\n\023TotalSellValue"
    "Trade\030\032 \001(\003\022\027\n\017TotalSellNumber\030\033 \001(\003\022\035\n\025"
    "DataMultiplePowerOf10\030\034 \001(\005\022\021\n\tDelayType"
    "\030e \001(\005B0\n\032com.htsc.mdc.insight.modelB\rMD"
    "IndexProtosH\001\240\001\001b\006proto3", 864);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDIndex.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDIndex_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDIndex_2eproto_once_);
void protobuf_AddDesc_MDIndex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDIndex_2eproto_once_,
                 &protobuf_AddDesc_MDIndex_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDIndex_2eproto {
  StaticDescriptorInitializer_MDIndex_2eproto() {
    protobuf_AddDesc_MDIndex_2eproto();
  }
} static_descriptor_initializer_MDIndex_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDIndex::kHTSCSecurityIDFieldNumber;
const int MDIndex::kMDDateFieldNumber;
const int MDIndex::kMDTimeFieldNumber;
const int MDIndex::kDataTimestampFieldNumber;
const int MDIndex::kTradingPhaseCodeFieldNumber;
const int MDIndex::kSecurityIDSourceFieldNumber;
const int MDIndex::kSecurityTypeFieldNumber;
const int MDIndex::kMaxPxFieldNumber;
const int MDIndex::kMinPxFieldNumber;
const int MDIndex::kPreClosePxFieldNumber;
const int MDIndex::kNumTradesFieldNumber;
const int MDIndex::kTotalVolumeTradeFieldNumber;
const int MDIndex::kTotalValueTradeFieldNumber;
const int MDIndex::kLastPxFieldNumber;
const int MDIndex::kOpenPxFieldNumber;
const int MDIndex::kClosePxFieldNumber;
const int MDIndex::kHighPxFieldNumber;
const int MDIndex::kLowPxFieldNumber;
const int MDIndex::kChannelNoFieldNumber;
const int MDIndex::kExchangeDateFieldNumber;
const int MDIndex::kExchangeTimeFieldNumber;
const int MDIndex::kTotalBuyVolumeTradeFieldNumber;
const int MDIndex::kTotalBuyValueTradeFieldNumber;
const int MDIndex::kTotalBuyNumberFieldNumber;
const int MDIndex::kTotalSellVolumeTradeFieldNumber;
const int MDIndex::kTotalSellValueTradeFieldNumber;
const int MDIndex::kTotalSellNumberFieldNumber;
const int MDIndex::kDataMultiplePowerOf10FieldNumber;
const int MDIndex::kDelayTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDIndex::MDIndex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDIndex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDIndex)
}

void MDIndex::InitAsDefaultInstance() {
}

MDIndex::MDIndex(const MDIndex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDIndex)
}

void MDIndex::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&delaytype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(delaytype_));
  _cached_size_ = 0;
}

MDIndex::~MDIndex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDIndex)
  SharedDtor();
}

void MDIndex::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDIndex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDIndex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDIndex_descriptor_;
}

const MDIndex& MDIndex::default_instance() {
  protobuf_InitDefaults_MDIndex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDIndex> MDIndex_default_instance_;

MDIndex* MDIndex::New(::google::protobuf::Arena* arena) const {
  MDIndex* n = new MDIndex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDIndex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDIndex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDIndex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDIndex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, exchangetime_);
  ZR_(datamultiplepowerof10_, delaytype_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDIndex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDIndex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 19;
      case 19: {
        if (tag == 152) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 20;
      case 20: {
        if (tag == 160) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 21;
      case 21: {
        if (tag == 168) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TotalBuyVolumeTrade;
        break;
      }

      // optional int64 TotalBuyVolumeTrade = 22;
      case 22: {
        if (tag == 176) {
         parse_TotalBuyVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuyvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_TotalBuyValueTrade;
        break;
      }

      // optional int64 TotalBuyValueTrade = 23;
      case 23: {
        if (tag == 184) {
         parse_TotalBuyValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuyvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_TotalBuyNumber;
        break;
      }

      // optional int64 TotalBuyNumber = 24;
      case 24: {
        if (tag == 192) {
         parse_TotalBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_TotalSellVolumeTrade;
        break;
      }

      // optional int64 TotalSellVolumeTrade = 25;
      case 25: {
        if (tag == 200) {
         parse_TotalSellVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_TotalSellValueTrade;
        break;
      }

      // optional int64 TotalSellValueTrade = 26;
      case 26: {
        if (tag == 208) {
         parse_TotalSellValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_TotalSellNumber;
        break;
      }

      // optional int64 TotalSellNumber = 27;
      case 27: {
        if (tag == 216) {
         parse_TotalSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 28;
      case 28: {
        if (tag == 224) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_DelayType;
        break;
      }

      // optional int32 DelayType = 101;
      case 101: {
        if (tag == 808) {
         parse_DelayType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDIndex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDIndex)
  return false;
#undef DO_
}

void MDIndex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDIndex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int32 ChannelNo = 19;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->channelno(), output);
  }

  // optional int32 ExchangeDate = 20;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 21;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->exchangetime(), output);
  }

  // optional int64 TotalBuyVolumeTrade = 22;
  if (this->totalbuyvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->totalbuyvolumetrade(), output);
  }

  // optional int64 TotalBuyValueTrade = 23;
  if (this->totalbuyvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->totalbuyvaluetrade(), output);
  }

  // optional int64 TotalBuyNumber = 24;
  if (this->totalbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->totalbuynumber(), output);
  }

  // optional int64 TotalSellVolumeTrade = 25;
  if (this->totalsellvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->totalsellvolumetrade(), output);
  }

  // optional int64 TotalSellValueTrade = 26;
  if (this->totalsellvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->totalsellvaluetrade(), output);
  }

  // optional int64 TotalSellNumber = 27;
  if (this->totalsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->totalsellnumber(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 28;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(28, this->datamultiplepowerof10(), output);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(101, this->delaytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDIndex)
}

::google::protobuf::uint8* MDIndex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDIndex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int32 ChannelNo = 19;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->channelno(), target);
  }

  // optional int32 ExchangeDate = 20;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 21;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->exchangetime(), target);
  }

  // optional int64 TotalBuyVolumeTrade = 22;
  if (this->totalbuyvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->totalbuyvolumetrade(), target);
  }

  // optional int64 TotalBuyValueTrade = 23;
  if (this->totalbuyvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->totalbuyvaluetrade(), target);
  }

  // optional int64 TotalBuyNumber = 24;
  if (this->totalbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->totalbuynumber(), target);
  }

  // optional int64 TotalSellVolumeTrade = 25;
  if (this->totalsellvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->totalsellvolumetrade(), target);
  }

  // optional int64 TotalSellValueTrade = 26;
  if (this->totalsellvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->totalsellvaluetrade(), target);
  }

  // optional int64 TotalSellNumber = 27;
  if (this->totalsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->totalsellnumber(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 28;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(28, this->datamultiplepowerof10(), target);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(101, this->delaytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDIndex)
  return target;
}

size_t MDIndex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDIndex)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int32 ChannelNo = 19;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 ExchangeDate = 20;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 21;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 TotalBuyVolumeTrade = 22;
  if (this->totalbuyvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuyvolumetrade());
  }

  // optional int64 TotalBuyValueTrade = 23;
  if (this->totalbuyvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuyvaluetrade());
  }

  // optional int64 TotalBuyNumber = 24;
  if (this->totalbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuynumber());
  }

  // optional int64 TotalSellVolumeTrade = 25;
  if (this->totalsellvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellvolumetrade());
  }

  // optional int64 TotalSellValueTrade = 26;
  if (this->totalsellvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellvaluetrade());
  }

  // optional int64 TotalSellNumber = 27;
  if (this->totalsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellnumber());
  }

  // optional int32 DataMultiplePowerOf10 = 28;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaytype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDIndex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDIndex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDIndex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDIndex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDIndex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDIndex)
    UnsafeMergeFrom(*source);
  }
}

void MDIndex::MergeFrom(const MDIndex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDIndex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDIndex::UnsafeMergeFrom(const MDIndex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.totalbuyvolumetrade() != 0) {
    set_totalbuyvolumetrade(from.totalbuyvolumetrade());
  }
  if (from.totalbuyvaluetrade() != 0) {
    set_totalbuyvaluetrade(from.totalbuyvaluetrade());
  }
  if (from.totalbuynumber() != 0) {
    set_totalbuynumber(from.totalbuynumber());
  }
  if (from.totalsellvolumetrade() != 0) {
    set_totalsellvolumetrade(from.totalsellvolumetrade());
  }
  if (from.totalsellvaluetrade() != 0) {
    set_totalsellvaluetrade(from.totalsellvaluetrade());
  }
  if (from.totalsellnumber() != 0) {
    set_totalsellnumber(from.totalsellnumber());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.delaytype() != 0) {
    set_delaytype(from.delaytype());
  }
}

void MDIndex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDIndex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDIndex::CopyFrom(const MDIndex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDIndex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDIndex::IsInitialized() const {

  return true;
}

void MDIndex::Swap(MDIndex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDIndex::InternalSwap(MDIndex* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(channelno_, other->channelno_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(totalbuyvolumetrade_, other->totalbuyvolumetrade_);
  std::swap(totalbuyvaluetrade_, other->totalbuyvaluetrade_);
  std::swap(totalbuynumber_, other->totalbuynumber_);
  std::swap(totalsellvolumetrade_, other->totalsellvolumetrade_);
  std::swap(totalsellvaluetrade_, other->totalsellvaluetrade_);
  std::swap(totalsellnumber_, other->totalsellnumber_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(delaytype_, other->delaytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDIndex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDIndex_descriptor_;
  metadata.reflection = MDIndex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDIndex

// optional string HTSCSecurityID = 1;
void MDIndex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIndex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIndex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
void MDIndex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
void MDIndex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
::std::string* MDIndex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIndex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIndex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDIndex::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDIndex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MDDate)
  return mddate_;
}
void MDIndex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MDDate)
}

// optional int32 MDTime = 3;
void MDIndex::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDIndex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MDTime)
  return mdtime_;
}
void MDIndex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDIndex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DataTimestamp)
  return datatimestamp_;
}
void MDIndex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDIndex::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIndex::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIndex::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
void MDIndex::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
void MDIndex::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
::std::string* MDIndex::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIndex::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIndex::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDIndex::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDIndex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDIndex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDIndex::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDIndex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDIndex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.securityType)
}

// optional int64 MaxPx = 8;
void MDIndex::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MaxPx)
  return maxpx_;
}
void MDIndex::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MaxPx)
}

// optional int64 MinPx = 9;
void MDIndex::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MinPx)
  return minpx_;
}
void MDIndex::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MinPx)
}

// optional int64 PreClosePx = 10;
void MDIndex::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.PreClosePx)
  return preclosepx_;
}
void MDIndex::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDIndex::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.NumTrades)
  return numtrades_;
}
void MDIndex::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDIndex::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDIndex::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDIndex::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalValueTrade)
  return totalvaluetrade_;
}
void MDIndex::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDIndex::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.LastPx)
  return lastpx_;
}
void MDIndex::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.LastPx)
}

// optional int64 OpenPx = 15;
void MDIndex::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.OpenPx)
  return openpx_;
}
void MDIndex::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.OpenPx)
}

// optional int64 ClosePx = 16;
void MDIndex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ClosePx)
  return closepx_;
}
void MDIndex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ClosePx)
}

// optional int64 HighPx = 17;
void MDIndex::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.HighPx)
  return highpx_;
}
void MDIndex::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.HighPx)
}

// optional int64 LowPx = 18;
void MDIndex::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.LowPx)
  return lowpx_;
}
void MDIndex::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.LowPx)
}

// optional int32 ChannelNo = 19;
void MDIndex::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDIndex::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ChannelNo)
  return channelno_;
}
void MDIndex::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ChannelNo)
}

// optional int32 ExchangeDate = 20;
void MDIndex::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDIndex::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ExchangeDate)
  return exchangedate_;
}
void MDIndex::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ExchangeDate)
}

// optional int32 ExchangeTime = 21;
void MDIndex::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDIndex::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ExchangeTime)
  return exchangetime_;
}
void MDIndex::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ExchangeTime)
}

// optional int64 TotalBuyVolumeTrade = 22;
void MDIndex::clear_totalbuyvolumetrade() {
  totalbuyvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalbuyvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyVolumeTrade)
  return totalbuyvolumetrade_;
}
void MDIndex::set_totalbuyvolumetrade(::google::protobuf::int64 value) {
  
  totalbuyvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyVolumeTrade)
}

// optional int64 TotalBuyValueTrade = 23;
void MDIndex::clear_totalbuyvaluetrade() {
  totalbuyvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalbuyvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyValueTrade)
  return totalbuyvaluetrade_;
}
void MDIndex::set_totalbuyvaluetrade(::google::protobuf::int64 value) {
  
  totalbuyvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyValueTrade)
}

// optional int64 TotalBuyNumber = 24;
void MDIndex::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyNumber)
  return totalbuynumber_;
}
void MDIndex::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyNumber)
}

// optional int64 TotalSellVolumeTrade = 25;
void MDIndex::clear_totalsellvolumetrade() {
  totalsellvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalsellvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellVolumeTrade)
  return totalsellvolumetrade_;
}
void MDIndex::set_totalsellvolumetrade(::google::protobuf::int64 value) {
  
  totalsellvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellVolumeTrade)
}

// optional int64 TotalSellValueTrade = 26;
void MDIndex::clear_totalsellvaluetrade() {
  totalsellvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalsellvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellValueTrade)
  return totalsellvaluetrade_;
}
void MDIndex::set_totalsellvaluetrade(::google::protobuf::int64 value) {
  
  totalsellvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellValueTrade)
}

// optional int64 TotalSellNumber = 27;
void MDIndex::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIndex::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellNumber)
  return totalsellnumber_;
}
void MDIndex::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellNumber)
}

// optional int32 DataMultiplePowerOf10 = 28;
void MDIndex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDIndex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDIndex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DataMultiplePowerOf10)
}

// optional int32 DelayType = 101;
void MDIndex::clear_delaytype() {
  delaytype_ = 0;
}
::google::protobuf::int32 MDIndex::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DelayType)
  return delaytype_;
}
void MDIndex::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DelayType)
}

inline const MDIndex* MDIndex::internal_default_instance() {
  return &MDIndex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
