// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDWarrant.proto

#ifndef PROTOBUF_MDWarrant_2eproto__INCLUDED
#define PROTOBUF_MDWarrant_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDWarrant_2eproto();
void protobuf_InitDefaults_MDWarrant_2eproto();
void protobuf_AssignDesc_MDWarrant_2eproto();
void protobuf_ShutdownFile_MDWarrant_2eproto();

class MDWarrant;

// ===================================================================

class MDWarrant : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDWarrant) */ {
 public:
  MDWarrant();
  virtual ~MDWarrant();

  MDWarrant(const MDWarrant& from);

  inline MDWarrant& operator=(const MDWarrant& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDWarrant& default_instance();

  static const MDWarrant* internal_default_instance();

  void Swap(MDWarrant* other);

  // implements Message ----------------------------------------------

  inline MDWarrant* New() const { return New(NULL); }

  MDWarrant* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDWarrant& from);
  void MergeFrom(const MDWarrant& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDWarrant* other);
  void UnsafeMergeFrom(const MDWarrant& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int64 DiffPx1 = 19;
  void clear_diffpx1();
  static const int kDiffPx1FieldNumber = 19;
  ::google::protobuf::int64 diffpx1() const;
  void set_diffpx1(::google::protobuf::int64 value);

  // optional int64 DiffPx2 = 20;
  void clear_diffpx2();
  static const int kDiffPx2FieldNumber = 20;
  ::google::protobuf::int64 diffpx2() const;
  void set_diffpx2(::google::protobuf::int64 value);

  // optional int64 TotalBuyQty = 21;
  void clear_totalbuyqty();
  static const int kTotalBuyQtyFieldNumber = 21;
  ::google::protobuf::int64 totalbuyqty() const;
  void set_totalbuyqty(::google::protobuf::int64 value);

  // optional int64 TotalSellQty = 22;
  void clear_totalsellqty();
  static const int kTotalSellQtyFieldNumber = 22;
  ::google::protobuf::int64 totalsellqty() const;
  void set_totalsellqty(::google::protobuf::int64 value);

  // optional int64 WeightedAvgBuyPx = 23;
  void clear_weightedavgbuypx();
  static const int kWeightedAvgBuyPxFieldNumber = 23;
  ::google::protobuf::int64 weightedavgbuypx() const;
  void set_weightedavgbuypx(::google::protobuf::int64 value);

  // optional int64 WeightedAvgSellPx = 24;
  void clear_weightedavgsellpx();
  static const int kWeightedAvgSellPxFieldNumber = 24;
  ::google::protobuf::int64 weightedavgsellpx() const;
  void set_weightedavgsellpx(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyNumber = 25;
  void clear_withdrawbuynumber();
  static const int kWithdrawBuyNumberFieldNumber = 25;
  ::google::protobuf::int64 withdrawbuynumber() const;
  void set_withdrawbuynumber(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyAmount = 26;
  void clear_withdrawbuyamount();
  static const int kWithdrawBuyAmountFieldNumber = 26;
  ::google::protobuf::int64 withdrawbuyamount() const;
  void set_withdrawbuyamount(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyMoney = 27;
  void clear_withdrawbuymoney();
  static const int kWithdrawBuyMoneyFieldNumber = 27;
  ::google::protobuf::int64 withdrawbuymoney() const;
  void set_withdrawbuymoney(::google::protobuf::int64 value);

  // optional int64 WithdrawSellNumber = 28;
  void clear_withdrawsellnumber();
  static const int kWithdrawSellNumberFieldNumber = 28;
  ::google::protobuf::int64 withdrawsellnumber() const;
  void set_withdrawsellnumber(::google::protobuf::int64 value);

  // optional int64 WithdrawSellAmount = 29;
  void clear_withdrawsellamount();
  static const int kWithdrawSellAmountFieldNumber = 29;
  ::google::protobuf::int64 withdrawsellamount() const;
  void set_withdrawsellamount(::google::protobuf::int64 value);

  // optional int64 WithdrawSellMoney = 30;
  void clear_withdrawsellmoney();
  static const int kWithdrawSellMoneyFieldNumber = 30;
  ::google::protobuf::int64 withdrawsellmoney() const;
  void set_withdrawsellmoney(::google::protobuf::int64 value);

  // optional int64 TotalBuyNumber = 31;
  void clear_totalbuynumber();
  static const int kTotalBuyNumberFieldNumber = 31;
  ::google::protobuf::int64 totalbuynumber() const;
  void set_totalbuynumber(::google::protobuf::int64 value);

  // optional int64 TotalSellNumber = 32;
  void clear_totalsellnumber();
  static const int kTotalSellNumberFieldNumber = 32;
  ::google::protobuf::int64 totalsellnumber() const;
  void set_totalsellnumber(::google::protobuf::int64 value);

  // optional int64 BuyTradeMaxDuration = 33;
  void clear_buytrademaxduration();
  static const int kBuyTradeMaxDurationFieldNumber = 33;
  ::google::protobuf::int64 buytrademaxduration() const;
  void set_buytrademaxduration(::google::protobuf::int64 value);

  // optional int64 SellTradeMaxDuration = 34;
  void clear_selltrademaxduration();
  static const int kSellTradeMaxDurationFieldNumber = 34;
  ::google::protobuf::int64 selltrademaxduration() const;
  void set_selltrademaxduration(::google::protobuf::int64 value);

  // optional int32 NumBuyOrders = 35;
  void clear_numbuyorders();
  static const int kNumBuyOrdersFieldNumber = 35;
  ::google::protobuf::int32 numbuyorders() const;
  void set_numbuyorders(::google::protobuf::int32 value);

  // optional int32 NumSellOrders = 36;
  void clear_numsellorders();
  static const int kNumSellOrdersFieldNumber = 36;
  ::google::protobuf::int32 numsellorders() const;
  void set_numsellorders(::google::protobuf::int32 value);

  // optional int64 WarrantPremiumRate = 37;
  void clear_warrantpremiumrate();
  static const int kWarrantPremiumRateFieldNumber = 37;
  ::google::protobuf::int64 warrantpremiumrate() const;
  void set_warrantpremiumrate(::google::protobuf::int64 value);

  // optional int64 TotalWarrantExecuteQty = 38;
  void clear_totalwarrantexecuteqty();
  static const int kTotalWarrantExecuteQtyFieldNumber = 38;
  ::google::protobuf::int64 totalwarrantexecuteqty() const;
  void set_totalwarrantexecuteqty(::google::protobuf::int64 value);

  // optional int64 TotalWarrantCreateQty = 39;
  void clear_totalwarrantcreateqty();
  static const int kTotalWarrantCreateQtyFieldNumber = 39;
  ::google::protobuf::int64 totalwarrantcreateqty() const;
  void set_totalwarrantcreateqty(::google::protobuf::int64 value);

  // optional int64 TotalWarrantCancelQty = 40;
  void clear_totalwarrantcancelqty();
  static const int kTotalWarrantCancelQtyFieldNumber = 40;
  ::google::protobuf::int64 totalwarrantcancelqty() const;
  void set_totalwarrantcancelqty(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 41;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 41;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 42;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 42;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 50;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 50;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int64 NorminalPx = 59;
  void clear_norminalpx();
  static const int kNorminalPxFieldNumber = 59;
  ::google::protobuf::int64 norminalpx() const;
  void set_norminalpx(::google::protobuf::int64 value);

  // optional int64 ShortSellSharesTraded = 60;
  void clear_shortsellsharestraded();
  static const int kShortSellSharesTradedFieldNumber = 60;
  ::google::protobuf::int64 shortsellsharestraded() const;
  void set_shortsellsharestraded(::google::protobuf::int64 value);

  // optional int64 ShortSellTurnover = 61;
  void clear_shortsellturnover();
  static const int kShortSellTurnoverFieldNumber = 61;
  ::google::protobuf::int64 shortsellturnover() const;
  void set_shortsellturnover(::google::protobuf::int64 value);

  // optional int64 PreMarketLastPx = 62;
  void clear_premarketlastpx();
  static const int kPreMarketLastPxFieldNumber = 62;
  ::google::protobuf::int64 premarketlastpx() const;
  void set_premarketlastpx(::google::protobuf::int64 value);

  // optional int64 PreMarketTotalVolumeTrade = 63;
  void clear_premarkettotalvolumetrade();
  static const int kPreMarketTotalVolumeTradeFieldNumber = 63;
  ::google::protobuf::int64 premarkettotalvolumetrade() const;
  void set_premarkettotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 PreMarketTotalValueTrade = 64;
  void clear_premarkettotalvaluetrade();
  static const int kPreMarketTotalValueTradeFieldNumber = 64;
  ::google::protobuf::int64 premarkettotalvaluetrade() const;
  void set_premarkettotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 PreMarketHighPx = 65;
  void clear_premarkethighpx();
  static const int kPreMarketHighPxFieldNumber = 65;
  ::google::protobuf::int64 premarkethighpx() const;
  void set_premarkethighpx(::google::protobuf::int64 value);

  // optional int64 PreMarketLowPx = 66;
  void clear_premarketlowpx();
  static const int kPreMarketLowPxFieldNumber = 66;
  ::google::protobuf::int64 premarketlowpx() const;
  void set_premarketlowpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursLastPx = 67;
  void clear_afterhourslastpx();
  static const int kAfterHoursLastPxFieldNumber = 67;
  ::google::protobuf::int64 afterhourslastpx() const;
  void set_afterhourslastpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursTotalVolumeTrade = 68;
  void clear_afterhourstotalvolumetrade();
  static const int kAfterHoursTotalVolumeTradeFieldNumber = 68;
  ::google::protobuf::int64 afterhourstotalvolumetrade() const;
  void set_afterhourstotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 AfterHoursTotalValueTrade = 69;
  void clear_afterhourstotalvaluetrade();
  static const int kAfterHoursTotalValueTradeFieldNumber = 69;
  ::google::protobuf::int64 afterhourstotalvaluetrade() const;
  void set_afterhourstotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 AfterHoursHighPx = 70;
  void clear_afterhourshighpx();
  static const int kAfterHoursHighPxFieldNumber = 70;
  ::google::protobuf::int64 afterhourshighpx() const;
  void set_afterhourshighpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursLowPx = 71;
  void clear_afterhourslowpx();
  static const int kAfterHoursLowPxFieldNumber = 71;
  ::google::protobuf::int64 afterhourslowpx() const;
  void set_afterhourslowpx(::google::protobuf::int64 value);

  // optional string MarketPhaseCode = 72;
  void clear_marketphasecode();
  static const int kMarketPhaseCodeFieldNumber = 72;
  const ::std::string& marketphasecode() const;
  void set_marketphasecode(const ::std::string& value);
  void set_marketphasecode(const char* value);
  void set_marketphasecode(const char* value, size_t size);
  ::std::string* mutable_marketphasecode();
  ::std::string* release_marketphasecode();
  void set_allocated_marketphasecode(::std::string* marketphasecode);

  // optional int64 USConsolidateVolume = 73;
  void clear_usconsolidatevolume();
  static const int kUSConsolidateVolumeFieldNumber = 73;
  ::google::protobuf::int64 usconsolidatevolume() const;
  void set_usconsolidatevolume(::google::protobuf::int64 value);

  // optional int64 USCompositeClosePx = 74;
  void clear_uscompositeclosepx();
  static const int kUSCompositeClosePxFieldNumber = 74;
  ::google::protobuf::int64 uscompositeclosepx() const;
  void set_uscompositeclosepx(::google::protobuf::int64 value);

  // optional string TradingHaltReason = 75;
  void clear_tradinghaltreason();
  static const int kTradingHaltReasonFieldNumber = 75;
  const ::std::string& tradinghaltreason() const;
  void set_tradinghaltreason(const ::std::string& value);
  void set_tradinghaltreason(const char* value);
  void set_tradinghaltreason(const char* value, size_t size);
  ::std::string* mutable_tradinghaltreason();
  ::std::string* release_tradinghaltreason();
  void set_allocated_tradinghaltreason(::std::string* tradinghaltreason);

  // optional int64 OtcTotalVolumeTrade = 76;
  void clear_otctotalvolumetrade();
  static const int kOtcTotalVolumeTradeFieldNumber = 76;
  ::google::protobuf::int64 otctotalvolumetrade() const;
  void set_otctotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 OtcTotalValueTrade = 77;
  void clear_otctotalvaluetrade();
  static const int kOtcTotalValueTradeFieldNumber = 77;
  ::google::protobuf::int64 otctotalvaluetrade() const;
  void set_otctotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 OtcNumTrades = 78;
  void clear_otcnumtrades();
  static const int kOtcNumTradesFieldNumber = 78;
  ::google::protobuf::int64 otcnumtrades() const;
  void set_otcnumtrades(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 79;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 79;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 ReferencePx = 80;
  void clear_referencepx();
  static const int kReferencePxFieldNumber = 80;
  ::google::protobuf::int64 referencepx() const;
  void set_referencepx(::google::protobuf::int64 value);

  // optional int64 MaxBuyPrice = 81;
  void clear_maxbuyprice();
  static const int kMaxBuyPriceFieldNumber = 81;
  ::google::protobuf::int64 maxbuyprice() const;
  void set_maxbuyprice(::google::protobuf::int64 value);

  // optional int64 MinBuyPrice = 82;
  void clear_minbuyprice();
  static const int kMinBuyPriceFieldNumber = 82;
  ::google::protobuf::int64 minbuyprice() const;
  void set_minbuyprice(::google::protobuf::int64 value);

  // optional int64 MaxSellPrice = 83;
  void clear_maxsellprice();
  static const int kMaxSellPriceFieldNumber = 83;
  ::google::protobuf::int64 maxsellprice() const;
  void set_maxsellprice(::google::protobuf::int64 value);

  // optional int64 MinSellPrice = 84;
  void clear_minsellprice();
  static const int kMinSellPriceFieldNumber = 84;
  ::google::protobuf::int64 minsellprice() const;
  void set_minsellprice(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDWarrant)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr marketphasecode_;
  ::google::protobuf::internal::ArenaStringPtr tradinghaltreason_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 diffpx1_;
  ::google::protobuf::int64 diffpx2_;
  ::google::protobuf::int64 totalbuyqty_;
  ::google::protobuf::int64 totalsellqty_;
  ::google::protobuf::int64 weightedavgbuypx_;
  ::google::protobuf::int64 weightedavgsellpx_;
  ::google::protobuf::int64 withdrawbuynumber_;
  ::google::protobuf::int64 withdrawbuyamount_;
  ::google::protobuf::int64 withdrawbuymoney_;
  ::google::protobuf::int64 withdrawsellnumber_;
  ::google::protobuf::int64 withdrawsellamount_;
  ::google::protobuf::int64 withdrawsellmoney_;
  ::google::protobuf::int64 totalbuynumber_;
  ::google::protobuf::int64 totalsellnumber_;
  ::google::protobuf::int64 buytrademaxduration_;
  ::google::protobuf::int64 selltrademaxduration_;
  ::google::protobuf::int32 numbuyorders_;
  ::google::protobuf::int32 numsellorders_;
  ::google::protobuf::int64 warrantpremiumrate_;
  ::google::protobuf::int64 totalwarrantexecuteqty_;
  ::google::protobuf::int64 totalwarrantcreateqty_;
  ::google::protobuf::int64 totalwarrantcancelqty_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 norminalpx_;
  ::google::protobuf::int64 shortsellsharestraded_;
  ::google::protobuf::int64 shortsellturnover_;
  ::google::protobuf::int64 premarketlastpx_;
  ::google::protobuf::int64 premarkettotalvolumetrade_;
  ::google::protobuf::int64 premarkettotalvaluetrade_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int64 premarkethighpx_;
  ::google::protobuf::int64 premarketlowpx_;
  ::google::protobuf::int64 afterhourslastpx_;
  ::google::protobuf::int64 afterhourstotalvolumetrade_;
  ::google::protobuf::int64 afterhourstotalvaluetrade_;
  ::google::protobuf::int64 afterhourshighpx_;
  ::google::protobuf::int64 afterhourslowpx_;
  ::google::protobuf::int64 usconsolidatevolume_;
  ::google::protobuf::int64 uscompositeclosepx_;
  ::google::protobuf::int64 otctotalvolumetrade_;
  ::google::protobuf::int64 otctotalvaluetrade_;
  ::google::protobuf::int64 otcnumtrades_;
  ::google::protobuf::int64 referencepx_;
  ::google::protobuf::int64 maxbuyprice_;
  ::google::protobuf::int64 minbuyprice_;
  ::google::protobuf::int64 maxsellprice_;
  ::google::protobuf::int64 minsellprice_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDWarrant_2eproto_impl();
  friend void  protobuf_AddDesc_MDWarrant_2eproto_impl();
  friend void protobuf_AssignDesc_MDWarrant_2eproto();
  friend void protobuf_ShutdownFile_MDWarrant_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDWarrant> MDWarrant_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDWarrant

// optional string HTSCSecurityID = 1;
inline void MDWarrant::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDWarrant::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
}
inline void MDWarrant::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
}
inline void MDWarrant::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
}
inline ::std::string* MDWarrant::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDWarrant::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDWarrant.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDWarrant::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MDDate)
  return mddate_;
}
inline void MDWarrant::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MDDate)
}

// optional int32 MDTime = 3;
inline void MDWarrant::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MDTime)
  return mdtime_;
}
inline void MDWarrant::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDWarrant::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.DataTimestamp)
  return datatimestamp_;
}
inline void MDWarrant::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDWarrant::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDWarrant::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
}
inline void MDWarrant::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
}
inline void MDWarrant::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
}
inline ::std::string* MDWarrant::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDWarrant::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDWarrant.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDWarrant::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDWarrant::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDWarrant::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDWarrant::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDWarrant::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDWarrant::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.securityType)
}

// optional int64 MaxPx = 8;
inline void MDWarrant::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MaxPx)
  return maxpx_;
}
inline void MDWarrant::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDWarrant::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MinPx)
  return minpx_;
}
inline void MDWarrant::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDWarrant::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreClosePx)
  return preclosepx_;
}
inline void MDWarrant::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDWarrant::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.NumTrades)
  return numtrades_;
}
inline void MDWarrant::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDWarrant::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDWarrant::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDWarrant::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDWarrant::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void MDWarrant::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.LastPx)
  return lastpx_;
}
inline void MDWarrant::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.LastPx)
}

// optional int64 OpenPx = 15;
inline void MDWarrant::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.OpenPx)
  return openpx_;
}
inline void MDWarrant::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.OpenPx)
}

// optional int64 ClosePx = 16;
inline void MDWarrant::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ClosePx)
  return closepx_;
}
inline void MDWarrant::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ClosePx)
}

// optional int64 HighPx = 17;
inline void MDWarrant::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.HighPx)
  return highpx_;
}
inline void MDWarrant::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.HighPx)
}

// optional int64 LowPx = 18;
inline void MDWarrant::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.LowPx)
  return lowpx_;
}
inline void MDWarrant::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.LowPx)
}

// optional int64 DiffPx1 = 19;
inline void MDWarrant::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.DiffPx1)
  return diffpx1_;
}
inline void MDWarrant::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.DiffPx1)
}

// optional int64 DiffPx2 = 20;
inline void MDWarrant::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.DiffPx2)
  return diffpx2_;
}
inline void MDWarrant::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
inline void MDWarrant::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalBuyQty)
  return totalbuyqty_;
}
inline void MDWarrant::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
inline void MDWarrant::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalSellQty)
  return totalsellqty_;
}
inline void MDWarrant::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
inline void MDWarrant::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
inline void MDWarrant::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
inline void MDWarrant::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
inline void MDWarrant::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
inline void MDWarrant::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
inline void MDWarrant::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
inline void MDWarrant::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
inline void MDWarrant::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
inline void MDWarrant::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
inline void MDWarrant::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
inline void MDWarrant::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellNumber)
  return withdrawsellnumber_;
}
inline void MDWarrant::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
inline void MDWarrant::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellAmount)
  return withdrawsellamount_;
}
inline void MDWarrant::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
inline void MDWarrant::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellMoney)
  return withdrawsellmoney_;
}
inline void MDWarrant::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
inline void MDWarrant::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalBuyNumber)
  return totalbuynumber_;
}
inline void MDWarrant::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
inline void MDWarrant::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalSellNumber)
  return totalsellnumber_;
}
inline void MDWarrant::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
inline void MDWarrant::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
inline void MDWarrant::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
inline void MDWarrant::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.SellTradeMaxDuration)
  return selltrademaxduration_;
}
inline void MDWarrant::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
inline void MDWarrant::clear_numbuyorders() {
  numbuyorders_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.NumBuyOrders)
  return numbuyorders_;
}
inline void MDWarrant::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
inline void MDWarrant::clear_numsellorders() {
  numsellorders_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.NumSellOrders)
  return numsellorders_;
}
inline void MDWarrant::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.NumSellOrders)
}

// optional int64 WarrantPremiumRate = 37;
inline void MDWarrant::clear_warrantpremiumrate() {
  warrantpremiumrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::warrantpremiumrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.WarrantPremiumRate)
  return warrantpremiumrate_;
}
inline void MDWarrant::set_warrantpremiumrate(::google::protobuf::int64 value) {
  
  warrantpremiumrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.WarrantPremiumRate)
}

// optional int64 TotalWarrantExecuteQty = 38;
inline void MDWarrant::clear_totalwarrantexecuteqty() {
  totalwarrantexecuteqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalwarrantexecuteqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantExecuteQty)
  return totalwarrantexecuteqty_;
}
inline void MDWarrant::set_totalwarrantexecuteqty(::google::protobuf::int64 value) {
  
  totalwarrantexecuteqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantExecuteQty)
}

// optional int64 TotalWarrantCreateQty = 39;
inline void MDWarrant::clear_totalwarrantcreateqty() {
  totalwarrantcreateqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalwarrantcreateqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantCreateQty)
  return totalwarrantcreateqty_;
}
inline void MDWarrant::set_totalwarrantcreateqty(::google::protobuf::int64 value) {
  
  totalwarrantcreateqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantCreateQty)
}

// optional int64 TotalWarrantCancelQty = 40;
inline void MDWarrant::clear_totalwarrantcancelqty() {
  totalwarrantcancelqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::totalwarrantcancelqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantCancelQty)
  return totalwarrantcancelqty_;
}
inline void MDWarrant::set_totalwarrantcancelqty(::google::protobuf::int64 value) {
  
  totalwarrantcancelqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TotalWarrantCancelQty)
}

// optional int32 ExchangeDate = 41;
inline void MDWarrant::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ExchangeDate)
  return exchangedate_;
}
inline void MDWarrant::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ExchangeDate)
}

// optional int32 ExchangeTime = 42;
inline void MDWarrant::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ExchangeTime)
  return exchangetime_;
}
inline void MDWarrant::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ExchangeTime)
}

// optional int32 ChannelNo = 50;
inline void MDWarrant::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ChannelNo)
  return channelno_;
}
inline void MDWarrant::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDWarrant::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDWarrant::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDWarrant::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.BuyPriceQueue)
}
inline void MDWarrant::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDWarrant::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDWarrant::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDWarrant::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQtyQueue)
}
inline void MDWarrant::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDWarrant::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDWarrant::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDWarrant::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.SellPriceQueue)
}
inline void MDWarrant::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDWarrant::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDWarrant::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDWarrant::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.SellOrderQtyQueue)
}
inline void MDWarrant::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDWarrant::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDWarrant::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDWarrant::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQueue)
}
inline void MDWarrant::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDWarrant::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDWarrant::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDWarrant::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.SellOrderQueue)
}
inline void MDWarrant::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDWarrant::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDWarrant::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDWarrant::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.BuyNumOrdersQueue)
}
inline void MDWarrant::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDWarrant::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDWarrant::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDWarrant::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDWarrant::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.SellNumOrdersQueue)
}
inline void MDWarrant::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDWarrant.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDWarrant::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDWarrant.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDWarrant::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDWarrant.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int64 NorminalPx = 59;
inline void MDWarrant::clear_norminalpx() {
  norminalpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::norminalpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.NorminalPx)
  return norminalpx_;
}
inline void MDWarrant::set_norminalpx(::google::protobuf::int64 value) {
  
  norminalpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.NorminalPx)
}

// optional int64 ShortSellSharesTraded = 60;
inline void MDWarrant::clear_shortsellsharestraded() {
  shortsellsharestraded_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::shortsellsharestraded() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ShortSellSharesTraded)
  return shortsellsharestraded_;
}
inline void MDWarrant::set_shortsellsharestraded(::google::protobuf::int64 value) {
  
  shortsellsharestraded_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ShortSellSharesTraded)
}

// optional int64 ShortSellTurnover = 61;
inline void MDWarrant::clear_shortsellturnover() {
  shortsellturnover_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::shortsellturnover() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ShortSellTurnover)
  return shortsellturnover_;
}
inline void MDWarrant::set_shortsellturnover(::google::protobuf::int64 value) {
  
  shortsellturnover_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ShortSellTurnover)
}

// optional int64 PreMarketLastPx = 62;
inline void MDWarrant::clear_premarketlastpx() {
  premarketlastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::premarketlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreMarketLastPx)
  return premarketlastpx_;
}
inline void MDWarrant::set_premarketlastpx(::google::protobuf::int64 value) {
  
  premarketlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreMarketLastPx)
}

// optional int64 PreMarketTotalVolumeTrade = 63;
inline void MDWarrant::clear_premarkettotalvolumetrade() {
  premarkettotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::premarkettotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreMarketTotalVolumeTrade)
  return premarkettotalvolumetrade_;
}
inline void MDWarrant::set_premarkettotalvolumetrade(::google::protobuf::int64 value) {
  
  premarkettotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreMarketTotalVolumeTrade)
}

// optional int64 PreMarketTotalValueTrade = 64;
inline void MDWarrant::clear_premarkettotalvaluetrade() {
  premarkettotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::premarkettotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreMarketTotalValueTrade)
  return premarkettotalvaluetrade_;
}
inline void MDWarrant::set_premarkettotalvaluetrade(::google::protobuf::int64 value) {
  
  premarkettotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreMarketTotalValueTrade)
}

// optional int64 PreMarketHighPx = 65;
inline void MDWarrant::clear_premarkethighpx() {
  premarkethighpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::premarkethighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreMarketHighPx)
  return premarkethighpx_;
}
inline void MDWarrant::set_premarkethighpx(::google::protobuf::int64 value) {
  
  premarkethighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreMarketHighPx)
}

// optional int64 PreMarketLowPx = 66;
inline void MDWarrant::clear_premarketlowpx() {
  premarketlowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::premarketlowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.PreMarketLowPx)
  return premarketlowpx_;
}
inline void MDWarrant::set_premarketlowpx(::google::protobuf::int64 value) {
  
  premarketlowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.PreMarketLowPx)
}

// optional int64 AfterHoursLastPx = 67;
inline void MDWarrant::clear_afterhourslastpx() {
  afterhourslastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::afterhourslastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.AfterHoursLastPx)
  return afterhourslastpx_;
}
inline void MDWarrant::set_afterhourslastpx(::google::protobuf::int64 value) {
  
  afterhourslastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.AfterHoursLastPx)
}

// optional int64 AfterHoursTotalVolumeTrade = 68;
inline void MDWarrant::clear_afterhourstotalvolumetrade() {
  afterhourstotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::afterhourstotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.AfterHoursTotalVolumeTrade)
  return afterhourstotalvolumetrade_;
}
inline void MDWarrant::set_afterhourstotalvolumetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.AfterHoursTotalVolumeTrade)
}

// optional int64 AfterHoursTotalValueTrade = 69;
inline void MDWarrant::clear_afterhourstotalvaluetrade() {
  afterhourstotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::afterhourstotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.AfterHoursTotalValueTrade)
  return afterhourstotalvaluetrade_;
}
inline void MDWarrant::set_afterhourstotalvaluetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.AfterHoursTotalValueTrade)
}

// optional int64 AfterHoursHighPx = 70;
inline void MDWarrant::clear_afterhourshighpx() {
  afterhourshighpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::afterhourshighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.AfterHoursHighPx)
  return afterhourshighpx_;
}
inline void MDWarrant::set_afterhourshighpx(::google::protobuf::int64 value) {
  
  afterhourshighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.AfterHoursHighPx)
}

// optional int64 AfterHoursLowPx = 71;
inline void MDWarrant::clear_afterhourslowpx() {
  afterhourslowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::afterhourslowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.AfterHoursLowPx)
  return afterhourslowpx_;
}
inline void MDWarrant::set_afterhourslowpx(::google::protobuf::int64 value) {
  
  afterhourslowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.AfterHoursLowPx)
}

// optional string MarketPhaseCode = 72;
inline void MDWarrant::clear_marketphasecode() {
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDWarrant::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
  return marketphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_marketphasecode(const ::std::string& value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
}
inline void MDWarrant::set_marketphasecode(const char* value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
}
inline void MDWarrant::set_marketphasecode(const char* value, size_t size) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
}
inline ::std::string* MDWarrant::mutable_marketphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
  return marketphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDWarrant::release_marketphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
  
  return marketphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_allocated_marketphasecode(::std::string* marketphasecode) {
  if (marketphasecode != NULL) {
    
  } else {
    
  }
  marketphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDWarrant.MarketPhaseCode)
}

// optional int64 USConsolidateVolume = 73;
inline void MDWarrant::clear_usconsolidatevolume() {
  usconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::usconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.USConsolidateVolume)
  return usconsolidatevolume_;
}
inline void MDWarrant::set_usconsolidatevolume(::google::protobuf::int64 value) {
  
  usconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.USConsolidateVolume)
}

// optional int64 USCompositeClosePx = 74;
inline void MDWarrant::clear_uscompositeclosepx() {
  uscompositeclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::uscompositeclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.USCompositeClosePx)
  return uscompositeclosepx_;
}
inline void MDWarrant::set_uscompositeclosepx(::google::protobuf::int64 value) {
  
  uscompositeclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.USCompositeClosePx)
}

// optional string TradingHaltReason = 75;
inline void MDWarrant::clear_tradinghaltreason() {
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDWarrant::tradinghaltreason() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
  return tradinghaltreason_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_tradinghaltreason(const ::std::string& value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
}
inline void MDWarrant::set_tradinghaltreason(const char* value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
}
inline void MDWarrant::set_tradinghaltreason(const char* value, size_t size) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
}
inline ::std::string* MDWarrant::mutable_tradinghaltreason() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
  return tradinghaltreason_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDWarrant::release_tradinghaltreason() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
  
  return tradinghaltreason_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDWarrant::set_allocated_tradinghaltreason(::std::string* tradinghaltreason) {
  if (tradinghaltreason != NULL) {
    
  } else {
    
  }
  tradinghaltreason_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradinghaltreason);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDWarrant.TradingHaltReason)
}

// optional int64 OtcTotalVolumeTrade = 76;
inline void MDWarrant::clear_otctotalvolumetrade() {
  otctotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::otctotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.OtcTotalVolumeTrade)
  return otctotalvolumetrade_;
}
inline void MDWarrant::set_otctotalvolumetrade(::google::protobuf::int64 value) {
  
  otctotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.OtcTotalVolumeTrade)
}

// optional int64 OtcTotalValueTrade = 77;
inline void MDWarrant::clear_otctotalvaluetrade() {
  otctotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::otctotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.OtcTotalValueTrade)
  return otctotalvaluetrade_;
}
inline void MDWarrant::set_otctotalvaluetrade(::google::protobuf::int64 value) {
  
  otctotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.OtcTotalValueTrade)
}

// optional int64 OtcNumTrades = 78;
inline void MDWarrant::clear_otcnumtrades() {
  otcnumtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::otcnumtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.OtcNumTrades)
  return otcnumtrades_;
}
inline void MDWarrant::set_otcnumtrades(::google::protobuf::int64 value) {
  
  otcnumtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.OtcNumTrades)
}

// optional int32 DataMultiplePowerOf10 = 79;
inline void MDWarrant::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDWarrant::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDWarrant::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.DataMultiplePowerOf10)
}

// optional int64 ReferencePx = 80;
inline void MDWarrant::clear_referencepx() {
  referencepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::referencepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.ReferencePx)
  return referencepx_;
}
inline void MDWarrant::set_referencepx(::google::protobuf::int64 value) {
  
  referencepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.ReferencePx)
}

// optional int64 MaxBuyPrice = 81;
inline void MDWarrant::clear_maxbuyprice() {
  maxbuyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::maxbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MaxBuyPrice)
  return maxbuyprice_;
}
inline void MDWarrant::set_maxbuyprice(::google::protobuf::int64 value) {
  
  maxbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MaxBuyPrice)
}

// optional int64 MinBuyPrice = 82;
inline void MDWarrant::clear_minbuyprice() {
  minbuyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::minbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MinBuyPrice)
  return minbuyprice_;
}
inline void MDWarrant::set_minbuyprice(::google::protobuf::int64 value) {
  
  minbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MinBuyPrice)
}

// optional int64 MaxSellPrice = 83;
inline void MDWarrant::clear_maxsellprice() {
  maxsellprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::maxsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MaxSellPrice)
  return maxsellprice_;
}
inline void MDWarrant::set_maxsellprice(::google::protobuf::int64 value) {
  
  maxsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MaxSellPrice)
}

// optional int64 MinSellPrice = 84;
inline void MDWarrant::clear_minsellprice() {
  minsellprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDWarrant::minsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDWarrant.MinSellPrice)
  return minsellprice_;
}
inline void MDWarrant::set_minsellprice(::google::protobuf::int64 value) {
  
  minsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDWarrant.MinSellPrice)
}

inline const MDWarrant* MDWarrant::internal_default_instance() {
  return &MDWarrant_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDWarrant_2eproto__INCLUDED
