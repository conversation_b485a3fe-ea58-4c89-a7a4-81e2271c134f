syntax = "proto3";

package com.htsc.mdc.insight.model;

import "EMarketDataType.proto";

message MDQueryRequest {
  repeated string securities = 1;
  repeated com.htsc.mdc.insight.model.EMarketDataType dataTypes = 2;
  int32 queryTime = 3; // Unix timestamp
}

message MDQueryResponse {
  int32 resultCode = 1;
  string resultMsg = 2;
  repeated bytes marketDataPayloads = 3; // serialized MarketData messages
}
