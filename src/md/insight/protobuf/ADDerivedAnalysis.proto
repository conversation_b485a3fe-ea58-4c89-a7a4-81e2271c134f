syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADDerivedAnalysisProtos";
option optimize_for = SPEED;

// ADDerivedAnalysis message represents derived technical analysis data for securities
message ADDerivedAnalysis {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Trading phase code
    string TradingPhaseCode = 5;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 7;
    
    // Number of trades
    int64 NumTrades = 8;
    
    // Total volume traded
    int64 TotalVolumeTrade = 9;
    
    // Total value traded (scaled by DataMultiplePowerOf10)
    int64 TotalValueTrade = 10;
    
    // Previous closing price (scaled by DataMultiplePowerOf10)
    int64 PreClosePx = 11;
    
    // Last price (scaled by DataMultiplePowerOf10)
    int64 LastPx = 12;
    
    // Opening price (scaled by DataMultiplePowerOf10)
    int64 OpenPx = 13;
    
    // Closing price (scaled by DataMultiplePowerOf10)
    int64 ClosePx = 14;
    
    // Highest price (scaled by DataMultiplePowerOf10)
    int64 HighPx = 15;
    
    // Lowest price (scaled by DataMultiplePowerOf10)
    int64 LowPx = 16;
    
    // Indicative Optimized Portfolio Value (for ETFs)
    int64 IOPV = 17;
    
    // Previous IOPV
    int64 PreIOPV = 18;
    
    // Open interest (for futures/options)
    int64 OpenInterest = 19;
    
    // Previous open interest
    int64 PreOpenInterest = 20;
    
    // Settlement price (scaled by DataMultiplePowerOf10)
    int64 SettlePrice = 21;
    
    // Previous settlement price (scaled by DataMultiplePowerOf10)
    int64 PreSettlePrice = 22;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 23;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 24;
    
    // Bull and Bear Index (BBI) - technical indicator
    int64 BBI = 40;
    
    // Amplitude of Variation (AMV) indicator data
    repeated ADAmv Amv = 41;
    
    // AR/BR (Popularity/Willingness) indicator data
    repeated ADArBr ArBr = 42;
    
    // BIAS (Bias Ratio) indicator data
    repeated ADBias Bias = 43;
    
    // Bollinger Bands indicator data
    repeated ADBoll Boll = 44;
    
    // CR (Confidence Index) indicator data
    repeated ADCr Cr = 45;
    
    // VMA/MA (Volume Moving Average/Moving Average) indicator data
    repeated ADVmaMa VmaMa = 46;
    
    // VR (Volume Ratio) indicator data
    repeated ADVr Vr = 47;
    
    // WR (Williams %R) indicator data
    repeated ADWr Wr = 48;
    
    // Data scaling factor (power of 10 multiplier for price/value fields)
    int32 DataMultiplePowerOf10 = 49;
}

// ADAmv represents Amplitude of Variation indicator
message ADAmv {
    // Period parameter (N value)
    int32 NValue = 1;
    
    // AMV indicator value (scaled by parent message's DataMultiplePowerOf10)
    int64 Amv = 2;
}

// ADArBr represents AR/BR (Popularity/Willingness) indicator
message ADArBr {
    // Period parameter (N value)
    int32 NValue = 1;
    
    // AR (Popularity) value (scaled by parent message's DataMultiplePowerOf10)
    int64 Ar = 2;
    
    // BR (Willingness) value (scaled by parent message's DataMultiplePowerOf10)
    int64 Br = 3;
}

// ADBias represents BIAS (Bias Ratio) indicator
message ADBias {
    // Period parameter (N value)
    int32 NValue = 1;
    
    // BIAS indicator value (scaled by parent message's DataMultiplePowerOf10)
    int64 Bias = 2;
    
    // Close price used in calculation (scaled by parent message's DataMultiplePowerOf10)
    int64 ClosePx = 3;
}

// ADBoll represents Bollinger Bands indicator
message ADBoll {
    // Period parameter (N value)
    int32 NValue = 1;
    
    // Standard deviation multiplier (P value)
    int32 PValue = 2;
    
    // Middle band (moving average) (scaled by parent message's DataMultiplePowerOf10)
    int64 Mid = 3;
    
    // Upper band (scaled by parent message's DataMultiplePowerOf10)
    int64 Upper = 4;
    
    // Lower band (scaled by parent message's DataMultiplePowerOf10)
    int64 Lower = 5;
}

// ADCr represents CR (Confidence Index) indicator
message ADCr {
    // Period parameter (N value)
    int32 NValue = 1;

    // CR indicator value (scaled by parent message's DataMultiplePowerOf10)
    int64 Cr = 2;

    // P1 value (scaled by parent message's DataMultiplePowerOf10)
    int64 P1 = 3;

    // P2 value (scaled by parent message's DataMultiplePowerOf10)
    int64 P2 = 4;

    // YM value (scaled by parent message's DataMultiplePowerOf10)
    int64 YM = 5;
}

// ADVmaMa represents VMA/MA (Volume Moving Average/Moving Average) indicator
message ADVmaMa {
    // Period parameter (N value)
    int64 NValue = 1;

    // VMA (Volume Moving Average) value (scaled by parent message's DataMultiplePowerOf10)
    int64 Vma = 2;

    // MA (Moving Average) value (scaled by parent message's DataMultiplePowerOf10)
    int64 Ma = 3;

    // Period type identifier
    int32 PeriodType = 4;
}

// ADVr represents VR (Volume Ratio) indicator
message ADVr {
    // Period parameter (N value)
    int32 NValue = 1;

    // VR indicator value (scaled by parent message's DataMultiplePowerOf10)
    int64 VR = 2;

    // AVS (Average Volume Sum) value (scaled by parent message's DataMultiplePowerOf10)
    int64 AVS = 3;

    // BVS (Below Volume Sum) value (scaled by parent message's DataMultiplePowerOf10)
    int64 BVS = 4;

    // CVS (Close Volume Sum) value (scaled by parent message's DataMultiplePowerOf10)
    int64 CVS = 5;
}

// ADWr represents WR (Williams %R) indicator
message ADWr {
    // Period parameter (N value)
    int32 NValue = 1;

    // WR indicator value (scaled by parent message's DataMultiplePowerOf10)
    int64 Wr = 2;

    // Close price used in calculation (scaled by parent message's DataMultiplePowerOf10)
    int64 ClosePx = 3;

    // N-period highest price (scaled by parent message's DataMultiplePowerOf10)
    int64 NHighPx = 4;

    // N-period lowest price (scaled by parent message's DataMultiplePowerOf10)
    int64 NLowPx = 5;
}
