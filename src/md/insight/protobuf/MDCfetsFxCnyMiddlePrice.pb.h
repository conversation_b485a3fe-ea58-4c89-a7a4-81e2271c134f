// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxCnyMiddlePrice.proto

#ifndef PROTOBUF_MDCfetsFxCnyMiddlePrice_2eproto__INCLUDED
#define PROTOBUF_MDCfetsFxCnyMiddlePrice_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto();
void protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto();
void protobuf_AssignDesc_MDCfetsFxCnyMiddlePrice_2eproto();
void protobuf_ShutdownFile_MDCfetsFxCnyMiddlePrice_2eproto();

class MDCfetsFxCnyMiddlePrice;

// ===================================================================

class MDCfetsFxCnyMiddlePrice : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice) */ {
 public:
  MDCfetsFxCnyMiddlePrice();
  virtual ~MDCfetsFxCnyMiddlePrice();

  MDCfetsFxCnyMiddlePrice(const MDCfetsFxCnyMiddlePrice& from);

  inline MDCfetsFxCnyMiddlePrice& operator=(const MDCfetsFxCnyMiddlePrice& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsFxCnyMiddlePrice& default_instance();

  static const MDCfetsFxCnyMiddlePrice* internal_default_instance();

  void Swap(MDCfetsFxCnyMiddlePrice* other);

  // implements Message ----------------------------------------------

  inline MDCfetsFxCnyMiddlePrice* New() const { return New(NULL); }

  MDCfetsFxCnyMiddlePrice* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsFxCnyMiddlePrice& from);
  void MergeFrom(const MDCfetsFxCnyMiddlePrice& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsFxCnyMiddlePrice* other);
  void UnsafeMergeFrom(const MDCfetsFxCnyMiddlePrice& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string SecuritySubType = 7;
  void clear_securitysubtype();
  static const int kSecuritySubTypeFieldNumber = 7;
  const ::std::string& securitysubtype() const;
  void set_securitysubtype(const ::std::string& value);
  void set_securitysubtype(const char* value);
  void set_securitysubtype(const char* value, size_t size);
  ::std::string* mutable_securitysubtype();
  ::std::string* release_securitysubtype();
  void set_allocated_securitysubtype(::std::string* securitysubtype);

  // optional string TransactTime = 8;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 8;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional int64 MiddlePrice = 9;
  void clear_middleprice();
  static const int kMiddlePriceFieldNumber = 9;
  ::google::protobuf::int64 middleprice() const;
  void set_middleprice(::google::protobuf::int64 value);

  // optional int64 UpperLimitPrice = 10;
  void clear_upperlimitprice();
  static const int kUpperLimitPriceFieldNumber = 10;
  ::google::protobuf::int64 upperlimitprice() const;
  void set_upperlimitprice(::google::protobuf::int64 value);

  // optional int64 LowerLimitPrice = 11;
  void clear_lowerlimitprice();
  static const int kLowerLimitPriceFieldNumber = 11;
  ::google::protobuf::int64 lowerlimitprice() const;
  void set_lowerlimitprice(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 12;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 12;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securitysubtype_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 middleprice_;
  ::google::protobuf::int64 upperlimitprice_;
  ::google::protobuf::int64 lowerlimitprice_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxCnyMiddlePrice_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxCnyMiddlePrice_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxCnyMiddlePrice> MDCfetsFxCnyMiddlePrice_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxCnyMiddlePrice

// optional string HTSCSecurityID = 1;
inline void MDCfetsFxCnyMiddlePrice::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxCnyMiddlePrice::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
inline void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
inline void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCfetsFxCnyMiddlePrice::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDDate)
  return mddate_;
}
inline void MDCfetsFxCnyMiddlePrice::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCfetsFxCnyMiddlePrice::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDTime)
  return mdtime_;
}
inline void MDCfetsFxCnyMiddlePrice::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCfetsFxCnyMiddlePrice::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsFxCnyMiddlePrice::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCfetsFxCnyMiddlePrice::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxCnyMiddlePrice::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsFxCnyMiddlePrice::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCfetsFxCnyMiddlePrice::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsFxCnyMiddlePrice::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsFxCnyMiddlePrice::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityType)
}

// optional string SecuritySubType = 7;
inline void MDCfetsFxCnyMiddlePrice::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxCnyMiddlePrice::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
inline void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
inline void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}

// optional string TransactTime = 8;
inline void MDCfetsFxCnyMiddlePrice::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxCnyMiddlePrice::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
inline void MDCfetsFxCnyMiddlePrice::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
inline void MDCfetsFxCnyMiddlePrice::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxCnyMiddlePrice::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxCnyMiddlePrice::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}

// optional int64 MiddlePrice = 9;
inline void MDCfetsFxCnyMiddlePrice::clear_middleprice() {
  middleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::middleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MiddlePrice)
  return middleprice_;
}
inline void MDCfetsFxCnyMiddlePrice::set_middleprice(::google::protobuf::int64 value) {
  
  middleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MiddlePrice)
}

// optional int64 UpperLimitPrice = 10;
inline void MDCfetsFxCnyMiddlePrice::clear_upperlimitprice() {
  upperlimitprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::upperlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.UpperLimitPrice)
  return upperlimitprice_;
}
inline void MDCfetsFxCnyMiddlePrice::set_upperlimitprice(::google::protobuf::int64 value) {
  
  upperlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.UpperLimitPrice)
}

// optional int64 LowerLimitPrice = 11;
inline void MDCfetsFxCnyMiddlePrice::clear_lowerlimitprice() {
  lowerlimitprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::lowerlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.LowerLimitPrice)
  return lowerlimitprice_;
}
inline void MDCfetsFxCnyMiddlePrice::set_lowerlimitprice(::google::protobuf::int64 value) {
  
  lowerlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.LowerLimitPrice)
}

// optional int32 DataMultiplePowerOf10 = 12;
inline void MDCfetsFxCnyMiddlePrice::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsFxCnyMiddlePrice::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataMultiplePowerOf10)
}

inline const MDCfetsFxCnyMiddlePrice* MDCfetsFxCnyMiddlePrice::internal_default_instance() {
  return &MDCfetsFxCnyMiddlePrice_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsFxCnyMiddlePrice_2eproto__INCLUDED
