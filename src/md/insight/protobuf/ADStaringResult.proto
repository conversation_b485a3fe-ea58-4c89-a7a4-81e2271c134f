syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADStaringResultProtos";
option optimize_for = SPEED;

// ADStaringResult message represents algorithmic trading staring/monitoring results
message ADStaringResult {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 7;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 8;
    
    // Unique identifier for the staring result
    string StaringResultID = 9;
    
    // Strategy identifier
    string StrategyID = 10;
    
    // Algorithm identifier
    string AlgorithmID = 11;
    
    // Algorithm name
    string AlgorithmName = 12;
    
    // Customer identifier
    string CustomerID = 13;
    
    // System identifier
    string SystemID = 14;
    
    // Reminder value (scaled by DataMultiplePowerOf10)
    int64 RmindValue = 15;
    
    // Real calculated value (scaled by DataMultiplePowerOf10)
    int64 RealCalValue = 16;
    
    // Data scaling factor (power of 10 multiplier for value fields)
    int32 DataMultiplePowerOf10 = 17;
}
