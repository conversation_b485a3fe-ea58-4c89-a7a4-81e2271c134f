
// 礼一数据协议——行情
syntax = "proto2";

package LYPROTO.QUOTA;

// 证券（股票、基金、债券、回购、权证）快照数据
message MarketData {
	required string	ExchId             	= 1;	// 交易所代码
	required string	Category           	= 2;	// 品种类型
	required string	stkId              	= 3;	// 证券代码
	optional string	RcvSvrTime         	= 4;	// 服务器收到的时间（HHMMSSmmm），在华泰接收程序中，对于基金在盘后复用为收盘价
	optional string	PubSvrTime         	= 5;	// 服务器发布的时间（HHMMSSmmm）
	optional string	Status             	= 6;	// 当前品种的状态
	optional string	ExchTime           	= 7;	// 更新时间（HHMMSSmmm）
	optional int64	PreClose           	= 8;	// 昨收盘价
	optional int64	HighLimit          	= 9;	// 涨停价
	optional int64	LowLimit           	= 10;	// 跌停价
	optional int64	Open               	= 11;	// 开盘价
	optional int64	High               	= 12;	// 最高价
	optional int64	Low                	= 13;	// 最低价
	optional int64	Latest            	= 14;	// 最新价
	optional int32	Knock              	= 15;	// 成交笔数
	optional int64	Volume             	= 16;	// 成交总量
	optional int64	Value              	= 17;	// 成交总额
	repeated int64	BA                 	= 18 [packed=true];		// 申买量一至十
	repeated int64	BP                 	= 19 [packed=true];		// 申买价一至十
	repeated int64	SA                 	= 20 [packed=true];		// 申卖量一至十
	repeated int64	SP                 	= 21 [packed=true];		// 申卖价一至十
	optional int64	TotalBA            	= 22;	// 委托买入总量
	optional int64	WeightedAvgBidPx   	= 23;	// 加权平均委买价格
	optional int64	TotalSA            	= 24;	// 委托卖出总量
	optional int64	WeightedAvgOfferPx	= 25;	// 加权平均委卖价格
	optional int32	IOPV               	= 26;	// IOPV净值估值(for ETF)
	optional int32	YieldToMaturity    	= 27;	// 到期收益率(for Bond)
	optional int64	TotalWarrantExecQty	= 28;	// 权证执行的总数量
	optional int64	WarLowerPx         	= 29;	// 权证跌停价格
	optional int64	WarUpperPx         	= 30;	// 权证涨停价格
	optional string MdSource            = 31;	// 行情数据来源
	optional int32  WiDBuyNum           = 32;	// 累计买入撤单笔数, 可用于比较上交所 L2 行情和逐笔合成行情的先后
	optional int32  WiDSellNum          = 33;	// 累计卖出撤单笔数, 可用于比较上交所 L2 行情和逐笔合成行情的先后
	optional int32  WiDNum              = 34;	// 累计买、卖撤单笔数, 用于比较上交所 L2 行情和逐笔合成行情的先后时只需要它就足够
	optional int64  Close               = 35;	// 收盘价，扩大10000倍
}

// 证券（股票、基金、债券、回购、权证）逐笔成交
message Transaction {
	required string	ExchangeID		= 1;	// 交易所代码
	required string	Category		= 2;	// 品种类型
	required string	SecurityID		= 3;	// 证券代码
	optional string	PubSvrTime		= 4;	// 发布服务器时间（毫秒）
	optional string	TradeIndex		= 5;	// 成交编号
	optional string	TradeTime		= 6;	// 成交时间
	optional int64	TradePrice		= 7;	// 成交价格
	optional int32	TradeQty		= 8;	// 成交数量
	optional int64	TradeMoney		= 9;	// 成交金额
	optional string	Side			= 10;	// 成交方向
	optional string	TradeType		= 11;	// 成交类型
	optional string	TradeCode		= 12;	// 成交代码
	optional string	OfferIndex		= 13;	// 叫卖方委托序号
	optional string	BidIndex 		= 14;	// 叫买方委托序号
	optional string	reserve			= 15;	// 保留字段
}

// 期货行情
message FutureMarketData {
	required string	ExchId			= 1;	// 交易所代码
	required string	Category		= 2;	// 品种类型
	required string	stkId			= 3;	// 合约编码
	optional string	RcvSvrTime		= 4;	// 服务器收到的时间（HHMMSSmmm）
	optional string	PubSvrTime		= 5;	// 服务器发布的时间（HHMMSSmmm）
	optional string	Status			= 6;	// 当前品种的状态
	optional string	ExchTime		= 7;	// 更新时间（HHMMSSmmm）
	optional string	TradeDate 		= 8;	// 交易日期(YYYYMMDD)
  	optional int64  PreClose		= 9;	// 前收盘价
  	optional int64  PreSettle  		= 10;	// 前结算价
  	optional int32  PreOpenPos		= 11;	// 前持仓量
  	optional int64  HighLimit  		= 12;	// 涨停板价
  	optional int64  LowLimit		= 13;	// 跌停板价
  	optional int64  Open  			= 14;	// 开盘价
	optional int64  Latest  		= 15;	// 最新价
	optional int64  High  			= 16;	// 最高价
	optional int64  Low  			= 17;	// 最低价
	optional int64  Settle  		= 18;	// 结算价
	optional int32  LatestVolume	= 19;	// 现(成交)量
	optional int32  Volume  		= 20;	// 成交量
	optional int32  OpenPos			= 21;	// 持仓量
	optional int64  Value 			= 22;	// 成交额
	repeated int32	BA				= 23 [packed=true];		// 申买量一至五
	repeated int64	BP				= 24 [packed=true];		// 申买价一至五
	repeated int32	SA				= 25 [packed=true];		// 申卖量一至五
	repeated int64	SP				= 26 [packed=true];		// 申卖价一至五
	optional int32  PreDelta  		= 27;	// 昨虚实度
	optional int32  CurDelta  		= 28;	// 今虚实度
}

