// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lyproto.quota.proto

#include "lyproto.quota.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace LYPROTO {
namespace QUOTA {
PROTOBUF_CONSTEXPR MarketData::MarketData(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_._has_bits_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_.ba_)*/{}
  , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
  , /*decltype(_impl_.bp_)*/{}
  , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
  , /*decltype(_impl_.sa_)*/{}
  , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
  , /*decltype(_impl_.sp_)*/{}
  , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
  , /*decltype(_impl_.exchid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.category_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.stkid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.rcvsvrtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.pubsvrtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.status_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.exchtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.mdsource_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.preclose_)*/int64_t{0}
  , /*decltype(_impl_.highlimit_)*/int64_t{0}
  , /*decltype(_impl_.lowlimit_)*/int64_t{0}
  , /*decltype(_impl_.open_)*/int64_t{0}
  , /*decltype(_impl_.high_)*/int64_t{0}
  , /*decltype(_impl_.low_)*/int64_t{0}
  , /*decltype(_impl_.latest_)*/int64_t{0}
  , /*decltype(_impl_.volume_)*/int64_t{0}
  , /*decltype(_impl_.value_)*/int64_t{0}
  , /*decltype(_impl_.knock_)*/0
  , /*decltype(_impl_.iopv_)*/0
  , /*decltype(_impl_.totalba_)*/int64_t{0}
  , /*decltype(_impl_.weightedavgbidpx_)*/int64_t{0}
  , /*decltype(_impl_.totalsa_)*/int64_t{0}
  , /*decltype(_impl_.weightedavgofferpx_)*/int64_t{0}
  , /*decltype(_impl_.totalwarrantexecqty_)*/int64_t{0}
  , /*decltype(_impl_.warlowerpx_)*/int64_t{0}
  , /*decltype(_impl_.yieldtomaturity_)*/0
  , /*decltype(_impl_.widbuynum_)*/0
  , /*decltype(_impl_.warupperpx_)*/int64_t{0}
  , /*decltype(_impl_.widsellnum_)*/0
  , /*decltype(_impl_.widnum_)*/0} {}
struct MarketDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MarketDataDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MarketDataDefaultTypeInternal() {}
  union {
    MarketData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MarketDataDefaultTypeInternal _MarketData_default_instance_;
PROTOBUF_CONSTEXPR Transaction::Transaction(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_._has_bits_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_.exchangeid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.category_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.securityid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.pubsvrtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradeindex_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradetime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.side_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradetype_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradecode_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.offerindex_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.bidindex_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.reserve_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradeprice_)*/int64_t{0}
  , /*decltype(_impl_.trademoney_)*/int64_t{0}
  , /*decltype(_impl_.tradeqty_)*/0} {}
struct TransactionDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TransactionDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~TransactionDefaultTypeInternal() {}
  union {
    Transaction _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TransactionDefaultTypeInternal _Transaction_default_instance_;
PROTOBUF_CONSTEXPR FutureMarketData::FutureMarketData(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_._has_bits_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_.ba_)*/{}
  , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
  , /*decltype(_impl_.bp_)*/{}
  , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
  , /*decltype(_impl_.sa_)*/{}
  , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
  , /*decltype(_impl_.sp_)*/{}
  , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
  , /*decltype(_impl_.exchid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.category_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.stkid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.rcvsvrtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.pubsvrtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.status_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.exchtime_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tradedate_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.preclose_)*/int64_t{0}
  , /*decltype(_impl_.presettle_)*/int64_t{0}
  , /*decltype(_impl_.highlimit_)*/int64_t{0}
  , /*decltype(_impl_.lowlimit_)*/int64_t{0}
  , /*decltype(_impl_.open_)*/int64_t{0}
  , /*decltype(_impl_.latest_)*/int64_t{0}
  , /*decltype(_impl_.preopenpos_)*/0
  , /*decltype(_impl_.latestvolume_)*/0
  , /*decltype(_impl_.high_)*/int64_t{0}
  , /*decltype(_impl_.low_)*/int64_t{0}
  , /*decltype(_impl_.settle_)*/int64_t{0}
  , /*decltype(_impl_.volume_)*/0
  , /*decltype(_impl_.openpos_)*/0
  , /*decltype(_impl_.value_)*/int64_t{0}
  , /*decltype(_impl_.predelta_)*/0
  , /*decltype(_impl_.curdelta_)*/0} {}
struct FutureMarketDataDefaultTypeInternal {
  PROTOBUF_CONSTEXPR FutureMarketDataDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~FutureMarketDataDefaultTypeInternal() {}
  union {
    FutureMarketData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 FutureMarketDataDefaultTypeInternal _FutureMarketData_default_instance_;
}  // namespace QUOTA
}  // namespace LYPROTO
static ::_pb::Metadata file_level_metadata_lyproto_2equota_2eproto[3];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_lyproto_2equota_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_lyproto_2equota_2eproto = nullptr;

const uint32_t TableStruct_lyproto_2equota_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_._has_bits_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.exchid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.category_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.stkid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.rcvsvrtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.pubsvrtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.status_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.exchtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.preclose_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.highlimit_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.lowlimit_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.open_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.high_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.low_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.latest_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.knock_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.volume_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.value_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.ba_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.bp_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.sa_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.sp_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.totalba_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.weightedavgbidpx_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.totalsa_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.weightedavgofferpx_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.iopv_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.yieldtomaturity_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.totalwarrantexecqty_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.warlowerpx_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.warupperpx_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.mdsource_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.widbuynum_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.widsellnum_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::MarketData, _impl_.widnum_),
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  17,
  15,
  16,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  19,
  20,
  21,
  22,
  18,
  25,
  23,
  24,
  27,
  7,
  26,
  28,
  29,
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_._has_bits_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.exchangeid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.category_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.securityid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.pubsvrtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradeindex_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradetime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradeprice_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradeqty_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.trademoney_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.side_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradetype_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.tradecode_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.offerindex_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.bidindex_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::Transaction, _impl_.reserve_),
  0,
  1,
  2,
  3,
  4,
  5,
  12,
  14,
  13,
  6,
  7,
  8,
  9,
  10,
  11,
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_._has_bits_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.exchid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.category_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.stkid_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.rcvsvrtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.pubsvrtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.status_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.exchtime_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.tradedate_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.preclose_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.presettle_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.preopenpos_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.highlimit_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.lowlimit_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.open_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.latest_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.high_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.low_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.settle_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.latestvolume_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.volume_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.openpos_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.value_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.ba_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.bp_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.sa_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.sp_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.predelta_),
  PROTOBUF_FIELD_OFFSET(::LYPROTO::QUOTA::FutureMarketData, _impl_.curdelta_),
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  14,
  10,
  11,
  12,
  13,
  16,
  17,
  18,
  15,
  19,
  20,
  21,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  22,
  23,
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 40, -1, sizeof(::LYPROTO::QUOTA::MarketData)},
  { 74, 95, -1, sizeof(::LYPROTO::QUOTA::Transaction)},
  { 110, 144, -1, sizeof(::LYPROTO::QUOTA::FutureMarketData)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::LYPROTO::QUOTA::_MarketData_default_instance_._instance,
  &::LYPROTO::QUOTA::_Transaction_default_instance_._instance,
  &::LYPROTO::QUOTA::_FutureMarketData_default_instance_._instance,
};

const char descriptor_table_protodef_lyproto_2equota_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023lyproto.quota.proto\022\rLYPROTO.QUOTA\"\362\004\n"
  "\nMarketData\022\016\n\006ExchId\030\001 \002(\t\022\020\n\010Category\030"
  "\002 \002(\t\022\r\n\005stkId\030\003 \002(\t\022\022\n\nRcvSvrTime\030\004 \001(\t"
  "\022\022\n\nPubSvrTime\030\005 \001(\t\022\016\n\006Status\030\006 \001(\t\022\020\n\010"
  "ExchTime\030\007 \001(\t\022\020\n\010PreClose\030\010 \001(\003\022\021\n\tHigh"
  "Limit\030\t \001(\003\022\020\n\010LowLimit\030\n \001(\003\022\014\n\004Open\030\013 "
  "\001(\003\022\014\n\004High\030\014 \001(\003\022\013\n\003Low\030\r \001(\003\022\016\n\006Latest"
  "\030\016 \001(\003\022\r\n\005Knock\030\017 \001(\005\022\016\n\006Volume\030\020 \001(\003\022\r\n"
  "\005Value\030\021 \001(\003\022\016\n\002BA\030\022 \003(\003B\002\020\001\022\016\n\002BP\030\023 \003(\003"
  "B\002\020\001\022\016\n\002SA\030\024 \003(\003B\002\020\001\022\016\n\002SP\030\025 \003(\003B\002\020\001\022\017\n\007"
  "TotalBA\030\026 \001(\003\022\030\n\020WeightedAvgBidPx\030\027 \001(\003\022"
  "\017\n\007TotalSA\030\030 \001(\003\022\032\n\022WeightedAvgOfferPx\030\031"
  " \001(\003\022\014\n\004IOPV\030\032 \001(\005\022\027\n\017YieldToMaturity\030\033 "
  "\001(\005\022\033\n\023TotalWarrantExecQty\030\034 \001(\003\022\022\n\nWarL"
  "owerPx\030\035 \001(\003\022\022\n\nWarUpperPx\030\036 \001(\003\022\020\n\010MdSo"
  "urce\030\037 \001(\t\022\021\n\tWiDBuyNum\030  \001(\005\022\022\n\nWiDSell"
  "Num\030! \001(\005\022\016\n\006WiDNum\030\" \001(\005\"\247\002\n\013Transactio"
  "n\022\022\n\nExchangeID\030\001 \002(\t\022\020\n\010Category\030\002 \002(\t\022"
  "\022\n\nSecurityID\030\003 \002(\t\022\022\n\nPubSvrTime\030\004 \001(\t\022"
  "\022\n\nTradeIndex\030\005 \001(\t\022\021\n\tTradeTime\030\006 \001(\t\022\022"
  "\n\nTradePrice\030\007 \001(\003\022\020\n\010TradeQty\030\010 \001(\005\022\022\n\n"
  "TradeMoney\030\t \001(\003\022\014\n\004Side\030\n \001(\t\022\021\n\tTradeT"
  "ype\030\013 \001(\t\022\021\n\tTradeCode\030\014 \001(\t\022\022\n\nOfferInd"
  "ex\030\r \001(\t\022\020\n\010BidIndex\030\016 \001(\t\022\017\n\007reserve\030\017 "
  "\001(\t\"\361\003\n\020FutureMarketData\022\016\n\006ExchId\030\001 \002(\t"
  "\022\020\n\010Category\030\002 \002(\t\022\r\n\005stkId\030\003 \002(\t\022\022\n\nRcv"
  "SvrTime\030\004 \001(\t\022\022\n\nPubSvrTime\030\005 \001(\t\022\016\n\006Sta"
  "tus\030\006 \001(\t\022\020\n\010ExchTime\030\007 \001(\t\022\021\n\tTradeDate"
  "\030\010 \001(\t\022\020\n\010PreClose\030\t \001(\003\022\021\n\tPreSettle\030\n "
  "\001(\003\022\022\n\nPreOpenPos\030\013 \001(\005\022\021\n\tHighLimit\030\014 \001"
  "(\003\022\020\n\010LowLimit\030\r \001(\003\022\014\n\004Open\030\016 \001(\003\022\016\n\006La"
  "test\030\017 \001(\003\022\014\n\004High\030\020 \001(\003\022\013\n\003Low\030\021 \001(\003\022\016\n"
  "\006Settle\030\022 \001(\003\022\024\n\014LatestVolume\030\023 \001(\005\022\016\n\006V"
  "olume\030\024 \001(\005\022\017\n\007OpenPos\030\025 \001(\005\022\r\n\005Value\030\026 "
  "\001(\003\022\016\n\002BA\030\027 \003(\005B\002\020\001\022\016\n\002BP\030\030 \003(\003B\002\020\001\022\016\n\002S"
  "A\030\031 \003(\005B\002\020\001\022\016\n\002SP\030\032 \003(\003B\002\020\001\022\020\n\010PreDelta\030"
  "\033 \001(\005\022\020\n\010CurDelta\030\034 \001(\005"
  ;
static ::_pbi::once_flag descriptor_table_lyproto_2equota_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_lyproto_2equota_2eproto = {
    false, false, 1463, descriptor_table_protodef_lyproto_2equota_2eproto,
    "lyproto.quota.proto",
    &descriptor_table_lyproto_2equota_2eproto_once, nullptr, 0, 3,
    schemas, file_default_instances, TableStruct_lyproto_2equota_2eproto::offsets,
    file_level_metadata_lyproto_2equota_2eproto, file_level_enum_descriptors_lyproto_2equota_2eproto,
    file_level_service_descriptors_lyproto_2equota_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_lyproto_2equota_2eproto_getter() {
  return &descriptor_table_lyproto_2equota_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_lyproto_2equota_2eproto(&descriptor_table_lyproto_2equota_2eproto);
namespace LYPROTO {
namespace QUOTA {

// ===================================================================

class MarketData::_Internal {
 public:
  using HasBits = decltype(std::declval<MarketData>()._impl_._has_bits_);
  static void set_has_exchid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_category(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_stkid(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_rcvsvrtime(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_pubsvrtime(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_status(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_exchtime(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_preclose(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_highlimit(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_lowlimit(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_open(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_high(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_low(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_latest(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_knock(HasBits* has_bits) {
    (*has_bits)[0] |= 131072u;
  }
  static void set_has_volume(HasBits* has_bits) {
    (*has_bits)[0] |= 32768u;
  }
  static void set_has_value(HasBits* has_bits) {
    (*has_bits)[0] |= 65536u;
  }
  static void set_has_totalba(HasBits* has_bits) {
    (*has_bits)[0] |= 524288u;
  }
  static void set_has_weightedavgbidpx(HasBits* has_bits) {
    (*has_bits)[0] |= 1048576u;
  }
  static void set_has_totalsa(HasBits* has_bits) {
    (*has_bits)[0] |= 2097152u;
  }
  static void set_has_weightedavgofferpx(HasBits* has_bits) {
    (*has_bits)[0] |= 4194304u;
  }
  static void set_has_iopv(HasBits* has_bits) {
    (*has_bits)[0] |= 262144u;
  }
  static void set_has_yieldtomaturity(HasBits* has_bits) {
    (*has_bits)[0] |= 33554432u;
  }
  static void set_has_totalwarrantexecqty(HasBits* has_bits) {
    (*has_bits)[0] |= 8388608u;
  }
  static void set_has_warlowerpx(HasBits* has_bits) {
    (*has_bits)[0] |= 16777216u;
  }
  static void set_has_warupperpx(HasBits* has_bits) {
    (*has_bits)[0] |= 134217728u;
  }
  static void set_has_mdsource(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_widbuynum(HasBits* has_bits) {
    (*has_bits)[0] |= 67108864u;
  }
  static void set_has_widsellnum(HasBits* has_bits) {
    (*has_bits)[0] |= 268435456u;
  }
  static void set_has_widnum(HasBits* has_bits) {
    (*has_bits)[0] |= 536870912u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

MarketData::MarketData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:LYPROTO.QUOTA.MarketData)
}
MarketData::MarketData(const MarketData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  MarketData* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){from._impl_._has_bits_}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.ba_){from._impl_.ba_}
    , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
    , decltype(_impl_.bp_){from._impl_.bp_}
    , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
    , decltype(_impl_.sa_){from._impl_.sa_}
    , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
    , decltype(_impl_.sp_){from._impl_.sp_}
    , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
    , decltype(_impl_.exchid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.stkid_){}
    , decltype(_impl_.rcvsvrtime_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.status_){}
    , decltype(_impl_.exchtime_){}
    , decltype(_impl_.mdsource_){}
    , decltype(_impl_.preclose_){}
    , decltype(_impl_.highlimit_){}
    , decltype(_impl_.lowlimit_){}
    , decltype(_impl_.open_){}
    , decltype(_impl_.high_){}
    , decltype(_impl_.low_){}
    , decltype(_impl_.latest_){}
    , decltype(_impl_.volume_){}
    , decltype(_impl_.value_){}
    , decltype(_impl_.knock_){}
    , decltype(_impl_.iopv_){}
    , decltype(_impl_.totalba_){}
    , decltype(_impl_.weightedavgbidpx_){}
    , decltype(_impl_.totalsa_){}
    , decltype(_impl_.weightedavgofferpx_){}
    , decltype(_impl_.totalwarrantexecqty_){}
    , decltype(_impl_.warlowerpx_){}
    , decltype(_impl_.yieldtomaturity_){}
    , decltype(_impl_.widbuynum_){}
    , decltype(_impl_.warupperpx_){}
    , decltype(_impl_.widsellnum_){}
    , decltype(_impl_.widnum_){}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.exchid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_exchid()) {
    _this->_impl_.exchid_.Set(from._internal_exchid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_category()) {
    _this->_impl_.category_.Set(from._internal_category(), 
      _this->GetArenaForAllocation());
  }
  _impl_.stkid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.stkid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_stkid()) {
    _this->_impl_.stkid_.Set(from._internal_stkid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.rcvsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_rcvsvrtime()) {
    _this->_impl_.rcvsvrtime_.Set(from._internal_rcvsvrtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_pubsvrtime()) {
    _this->_impl_.pubsvrtime_.Set(from._internal_pubsvrtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.status_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.status_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_status()) {
    _this->_impl_.status_.Set(from._internal_status(), 
      _this->GetArenaForAllocation());
  }
  _impl_.exchtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_exchtime()) {
    _this->_impl_.exchtime_.Set(from._internal_exchtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.mdsource_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.mdsource_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_mdsource()) {
    _this->_impl_.mdsource_.Set(from._internal_mdsource(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.preclose_, &from._impl_.preclose_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.widnum_) -
    reinterpret_cast<char*>(&_impl_.preclose_)) + sizeof(_impl_.widnum_));
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.MarketData)
}

inline void MarketData::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.ba_){arena}
    , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
    , decltype(_impl_.bp_){arena}
    , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
    , decltype(_impl_.sa_){arena}
    , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
    , decltype(_impl_.sp_){arena}
    , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
    , decltype(_impl_.exchid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.stkid_){}
    , decltype(_impl_.rcvsvrtime_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.status_){}
    , decltype(_impl_.exchtime_){}
    , decltype(_impl_.mdsource_){}
    , decltype(_impl_.preclose_){int64_t{0}}
    , decltype(_impl_.highlimit_){int64_t{0}}
    , decltype(_impl_.lowlimit_){int64_t{0}}
    , decltype(_impl_.open_){int64_t{0}}
    , decltype(_impl_.high_){int64_t{0}}
    , decltype(_impl_.low_){int64_t{0}}
    , decltype(_impl_.latest_){int64_t{0}}
    , decltype(_impl_.volume_){int64_t{0}}
    , decltype(_impl_.value_){int64_t{0}}
    , decltype(_impl_.knock_){0}
    , decltype(_impl_.iopv_){0}
    , decltype(_impl_.totalba_){int64_t{0}}
    , decltype(_impl_.weightedavgbidpx_){int64_t{0}}
    , decltype(_impl_.totalsa_){int64_t{0}}
    , decltype(_impl_.weightedavgofferpx_){int64_t{0}}
    , decltype(_impl_.totalwarrantexecqty_){int64_t{0}}
    , decltype(_impl_.warlowerpx_){int64_t{0}}
    , decltype(_impl_.yieldtomaturity_){0}
    , decltype(_impl_.widbuynum_){0}
    , decltype(_impl_.warupperpx_){int64_t{0}}
    , decltype(_impl_.widsellnum_){0}
    , decltype(_impl_.widnum_){0}
  };
  _impl_.exchid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.stkid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.stkid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.rcvsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.status_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.status_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.exchtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.mdsource_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.mdsource_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

MarketData::~MarketData() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.MarketData)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void MarketData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.ba_.~RepeatedField();
  _impl_.bp_.~RepeatedField();
  _impl_.sa_.~RepeatedField();
  _impl_.sp_.~RepeatedField();
  _impl_.exchid_.Destroy();
  _impl_.category_.Destroy();
  _impl_.stkid_.Destroy();
  _impl_.rcvsvrtime_.Destroy();
  _impl_.pubsvrtime_.Destroy();
  _impl_.status_.Destroy();
  _impl_.exchtime_.Destroy();
  _impl_.mdsource_.Destroy();
}

void MarketData::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void MarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.MarketData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.ba_.Clear();
  _impl_.bp_.Clear();
  _impl_.sa_.Clear();
  _impl_.sp_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.exchid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.category_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      _impl_.stkid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      _impl_.rcvsvrtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000010u) {
      _impl_.pubsvrtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000020u) {
      _impl_.status_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000040u) {
      _impl_.exchtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000080u) {
      _impl_.mdsource_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    ::memset(&_impl_.preclose_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.volume_) -
        reinterpret_cast<char*>(&_impl_.preclose_)) + sizeof(_impl_.volume_));
  }
  if (cached_has_bits & 0x00ff0000u) {
    ::memset(&_impl_.value_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.totalwarrantexecqty_) -
        reinterpret_cast<char*>(&_impl_.value_)) + sizeof(_impl_.totalwarrantexecqty_));
  }
  if (cached_has_bits & 0x3f000000u) {
    ::memset(&_impl_.warlowerpx_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.widnum_) -
        reinterpret_cast<char*>(&_impl_.warlowerpx_)) + sizeof(_impl_.widnum_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MarketData::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string ExchId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_exchid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.ExchId");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string Category = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_category();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.Category");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string stkId = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_stkid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.stkId");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string RcvSvrTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_rcvsvrtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.RcvSvrTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string PubSvrTime = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_pubsvrtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.PubSvrTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string Status = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_status();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.Status");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string ExchTime = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_exchtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.ExchTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional int64 PreClose = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_preclose(&has_bits);
          _impl_.preclose_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 HighLimit = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _Internal::set_has_highlimit(&has_bits);
          _impl_.highlimit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 LowLimit = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _Internal::set_has_lowlimit(&has_bits);
          _impl_.lowlimit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Open = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _Internal::set_has_open(&has_bits);
          _impl_.open_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 High = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _Internal::set_has_high(&has_bits);
          _impl_.high_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Low = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _Internal::set_has_low(&has_bits);
          _impl_.low_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Latest = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          _Internal::set_has_latest(&has_bits);
          _impl_.latest_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 Knock = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          _Internal::set_has_knock(&has_bits);
          _impl_.knock_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Volume = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _Internal::set_has_volume(&has_bits);
          _impl_.volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Value = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          _Internal::set_has_value(&has_bits);
          _impl_.value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 BA = 18 [packed = true];
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_ba(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 144) {
          _internal_add_ba(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 BP = 19 [packed = true];
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_bp(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 152) {
          _internal_add_bp(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 SA = 20 [packed = true];
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_sa(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 160) {
          _internal_add_sa(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 SP = 21 [packed = true];
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_sp(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 168) {
          _internal_add_sp(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 TotalBA = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 176)) {
          _Internal::set_has_totalba(&has_bits);
          _impl_.totalba_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 WeightedAvgBidPx = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 184)) {
          _Internal::set_has_weightedavgbidpx(&has_bits);
          _impl_.weightedavgbidpx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 TotalSA = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 192)) {
          _Internal::set_has_totalsa(&has_bits);
          _impl_.totalsa_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 WeightedAvgOfferPx = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 200)) {
          _Internal::set_has_weightedavgofferpx(&has_bits);
          _impl_.weightedavgofferpx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 IOPV = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 208)) {
          _Internal::set_has_iopv(&has_bits);
          _impl_.iopv_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 YieldToMaturity = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 216)) {
          _Internal::set_has_yieldtomaturity(&has_bits);
          _impl_.yieldtomaturity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 TotalWarrantExecQty = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 224)) {
          _Internal::set_has_totalwarrantexecqty(&has_bits);
          _impl_.totalwarrantexecqty_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 WarLowerPx = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 232)) {
          _Internal::set_has_warlowerpx(&has_bits);
          _impl_.warlowerpx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 WarUpperPx = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 240)) {
          _Internal::set_has_warupperpx(&has_bits);
          _impl_.warupperpx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string MdSource = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 250)) {
          auto str = _internal_mutable_mdsource();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.MarketData.MdSource");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional int32 WiDBuyNum = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 0)) {
          _Internal::set_has_widbuynum(&has_bits);
          _impl_.widbuynum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 WiDSellNum = 33;
      case 33:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_widsellnum(&has_bits);
          _impl_.widsellnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 WiDNum = 34;
      case 34:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_widnum(&has_bits);
          _impl_.widnum_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _impl_._has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MarketData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.MarketData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  // required string ExchId = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_exchid().data(), static_cast<int>(this->_internal_exchid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_exchid(), target);
  }

  // required string Category = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Category");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_category(), target);
  }

  // required string stkId = 3;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_stkid().data(), static_cast<int>(this->_internal_stkid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.stkId");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_stkid(), target);
  }

  // optional string RcvSvrTime = 4;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_rcvsvrtime().data(), static_cast<int>(this->_internal_rcvsvrtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.RcvSvrTime");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_rcvsvrtime(), target);
  }

  // optional string PubSvrTime = 5;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_pubsvrtime().data(), static_cast<int>(this->_internal_pubsvrtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.PubSvrTime");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_pubsvrtime(), target);
  }

  // optional string Status = 6;
  if (cached_has_bits & 0x00000020u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_status().data(), static_cast<int>(this->_internal_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.Status");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_status(), target);
  }

  // optional string ExchTime = 7;
  if (cached_has_bits & 0x00000040u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_exchtime().data(), static_cast<int>(this->_internal_exchtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.ExchTime");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_exchtime(), target);
  }

  // optional int64 PreClose = 8;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(8, this->_internal_preclose(), target);
  }

  // optional int64 HighLimit = 9;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(9, this->_internal_highlimit(), target);
  }

  // optional int64 LowLimit = 10;
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(10, this->_internal_lowlimit(), target);
  }

  // optional int64 Open = 11;
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(11, this->_internal_open(), target);
  }

  // optional int64 High = 12;
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(12, this->_internal_high(), target);
  }

  // optional int64 Low = 13;
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(13, this->_internal_low(), target);
  }

  // optional int64 Latest = 14;
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(14, this->_internal_latest(), target);
  }

  // optional int32 Knock = 15;
  if (cached_has_bits & 0x00020000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(15, this->_internal_knock(), target);
  }

  // optional int64 Volume = 16;
  if (cached_has_bits & 0x00008000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(16, this->_internal_volume(), target);
  }

  // optional int64 Value = 17;
  if (cached_has_bits & 0x00010000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(17, this->_internal_value(), target);
  }

  // repeated int64 BA = 18 [packed = true];
  {
    int byte_size = _impl_._ba_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          18, _internal_ba(), byte_size, target);
    }
  }

  // repeated int64 BP = 19 [packed = true];
  {
    int byte_size = _impl_._bp_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          19, _internal_bp(), byte_size, target);
    }
  }

  // repeated int64 SA = 20 [packed = true];
  {
    int byte_size = _impl_._sa_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          20, _internal_sa(), byte_size, target);
    }
  }

  // repeated int64 SP = 21 [packed = true];
  {
    int byte_size = _impl_._sp_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          21, _internal_sp(), byte_size, target);
    }
  }

  // optional int64 TotalBA = 22;
  if (cached_has_bits & 0x00080000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(22, this->_internal_totalba(), target);
  }

  // optional int64 WeightedAvgBidPx = 23;
  if (cached_has_bits & 0x00100000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(23, this->_internal_weightedavgbidpx(), target);
  }

  // optional int64 TotalSA = 24;
  if (cached_has_bits & 0x00200000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(24, this->_internal_totalsa(), target);
  }

  // optional int64 WeightedAvgOfferPx = 25;
  if (cached_has_bits & 0x00400000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(25, this->_internal_weightedavgofferpx(), target);
  }

  // optional int32 IOPV = 26;
  if (cached_has_bits & 0x00040000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(26, this->_internal_iopv(), target);
  }

  // optional int32 YieldToMaturity = 27;
  if (cached_has_bits & 0x02000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(27, this->_internal_yieldtomaturity(), target);
  }

  // optional int64 TotalWarrantExecQty = 28;
  if (cached_has_bits & 0x00800000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(28, this->_internal_totalwarrantexecqty(), target);
  }

  // optional int64 WarLowerPx = 29;
  if (cached_has_bits & 0x01000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(29, this->_internal_warlowerpx(), target);
  }

  // optional int64 WarUpperPx = 30;
  if (cached_has_bits & 0x08000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(30, this->_internal_warupperpx(), target);
  }

  // optional string MdSource = 31;
  if (cached_has_bits & 0x00000080u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_mdsource().data(), static_cast<int>(this->_internal_mdsource().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.MarketData.MdSource");
    target = stream->WriteStringMaybeAliased(
        31, this->_internal_mdsource(), target);
  }

  // optional int32 WiDBuyNum = 32;
  if (cached_has_bits & 0x04000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(32, this->_internal_widbuynum(), target);
  }

  // optional int32 WiDSellNum = 33;
  if (cached_has_bits & 0x10000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(33, this->_internal_widsellnum(), target);
  }

  // optional int32 WiDNum = 34;
  if (cached_has_bits & 0x20000000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(34, this->_internal_widnum(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.MarketData)
  return target;
}

size_t MarketData::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.MarketData)
  size_t total_size = 0;

  if (_internal_has_exchid()) {
    // required string ExchId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchid());
  }

  if (_internal_has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  if (_internal_has_stkid()) {
    // required string stkId = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stkid());
  }

  return total_size;
}
size_t MarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.MarketData)
  size_t total_size = 0;

  if (((_impl_._has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchid());

    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());

    // required string stkId = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stkid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 BA = 18 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.ba_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._ba_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 BP = 19 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.bp_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._bp_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 SA = 20 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.sa_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._sa_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 SP = 21 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.sp_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._sp_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000f8u) {
    // optional string RcvSvrTime = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_rcvsvrtime());
    }

    // optional string PubSvrTime = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_pubsvrtime());
    }

    // optional string Status = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_status());
    }

    // optional string ExchTime = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_exchtime());
    }

    // optional string MdSource = 31;
    if (cached_has_bits & 0x00000080u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_mdsource());
    }

  }
  if (cached_has_bits & 0x0000ff00u) {
    // optional int64 PreClose = 8;
    if (cached_has_bits & 0x00000100u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_preclose());
    }

    // optional int64 HighLimit = 9;
    if (cached_has_bits & 0x00000200u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_highlimit());
    }

    // optional int64 LowLimit = 10;
    if (cached_has_bits & 0x00000400u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_lowlimit());
    }

    // optional int64 Open = 11;
    if (cached_has_bits & 0x00000800u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_open());
    }

    // optional int64 High = 12;
    if (cached_has_bits & 0x00001000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_high());
    }

    // optional int64 Low = 13;
    if (cached_has_bits & 0x00002000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_low());
    }

    // optional int64 Latest = 14;
    if (cached_has_bits & 0x00004000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_latest());
    }

    // optional int64 Volume = 16;
    if (cached_has_bits & 0x00008000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_volume());
    }

  }
  if (cached_has_bits & 0x00ff0000u) {
    // optional int64 Value = 17;
    if (cached_has_bits & 0x00010000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_value());
    }

    // optional int32 Knock = 15;
    if (cached_has_bits & 0x00020000u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_knock());
    }

    // optional int32 IOPV = 26;
    if (cached_has_bits & 0x00040000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_iopv());
    }

    // optional int64 TotalBA = 22;
    if (cached_has_bits & 0x00080000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_totalba());
    }

    // optional int64 WeightedAvgBidPx = 23;
    if (cached_has_bits & 0x00100000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_weightedavgbidpx());
    }

    // optional int64 TotalSA = 24;
    if (cached_has_bits & 0x00200000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_totalsa());
    }

    // optional int64 WeightedAvgOfferPx = 25;
    if (cached_has_bits & 0x00400000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_weightedavgofferpx());
    }

    // optional int64 TotalWarrantExecQty = 28;
    if (cached_has_bits & 0x00800000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_totalwarrantexecqty());
    }

  }
  if (cached_has_bits & 0x3f000000u) {
    // optional int64 WarLowerPx = 29;
    if (cached_has_bits & 0x01000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_warlowerpx());
    }

    // optional int32 YieldToMaturity = 27;
    if (cached_has_bits & 0x02000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_yieldtomaturity());
    }

    // optional int32 WiDBuyNum = 32;
    if (cached_has_bits & 0x04000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_widbuynum());
    }

    // optional int64 WarUpperPx = 30;
    if (cached_has_bits & 0x08000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_warupperpx());
    }

    // optional int32 WiDSellNum = 33;
    if (cached_has_bits & 0x10000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_widsellnum());
    }

    // optional int32 WiDNum = 34;
    if (cached_has_bits & 0x20000000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_widnum());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MarketData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    MarketData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MarketData::GetClassData() const { return &_class_data_; }


void MarketData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<MarketData*>(&to_msg);
  auto& from = static_cast<const MarketData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.MarketData)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.ba_.MergeFrom(from._impl_.ba_);
  _this->_impl_.bp_.MergeFrom(from._impl_.bp_);
  _this->_impl_.sa_.MergeFrom(from._impl_.sa_);
  _this->_impl_.sp_.MergeFrom(from._impl_.sp_);
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_exchid(from._internal_exchid());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_category(from._internal_category());
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_internal_set_stkid(from._internal_stkid());
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_internal_set_rcvsvrtime(from._internal_rcvsvrtime());
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_internal_set_pubsvrtime(from._internal_pubsvrtime());
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_internal_set_status(from._internal_status());
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_internal_set_exchtime(from._internal_exchtime());
    }
    if (cached_has_bits & 0x00000080u) {
      _this->_internal_set_mdsource(from._internal_mdsource());
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    if (cached_has_bits & 0x00000100u) {
      _this->_impl_.preclose_ = from._impl_.preclose_;
    }
    if (cached_has_bits & 0x00000200u) {
      _this->_impl_.highlimit_ = from._impl_.highlimit_;
    }
    if (cached_has_bits & 0x00000400u) {
      _this->_impl_.lowlimit_ = from._impl_.lowlimit_;
    }
    if (cached_has_bits & 0x00000800u) {
      _this->_impl_.open_ = from._impl_.open_;
    }
    if (cached_has_bits & 0x00001000u) {
      _this->_impl_.high_ = from._impl_.high_;
    }
    if (cached_has_bits & 0x00002000u) {
      _this->_impl_.low_ = from._impl_.low_;
    }
    if (cached_has_bits & 0x00004000u) {
      _this->_impl_.latest_ = from._impl_.latest_;
    }
    if (cached_has_bits & 0x00008000u) {
      _this->_impl_.volume_ = from._impl_.volume_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00ff0000u) {
    if (cached_has_bits & 0x00010000u) {
      _this->_impl_.value_ = from._impl_.value_;
    }
    if (cached_has_bits & 0x00020000u) {
      _this->_impl_.knock_ = from._impl_.knock_;
    }
    if (cached_has_bits & 0x00040000u) {
      _this->_impl_.iopv_ = from._impl_.iopv_;
    }
    if (cached_has_bits & 0x00080000u) {
      _this->_impl_.totalba_ = from._impl_.totalba_;
    }
    if (cached_has_bits & 0x00100000u) {
      _this->_impl_.weightedavgbidpx_ = from._impl_.weightedavgbidpx_;
    }
    if (cached_has_bits & 0x00200000u) {
      _this->_impl_.totalsa_ = from._impl_.totalsa_;
    }
    if (cached_has_bits & 0x00400000u) {
      _this->_impl_.weightedavgofferpx_ = from._impl_.weightedavgofferpx_;
    }
    if (cached_has_bits & 0x00800000u) {
      _this->_impl_.totalwarrantexecqty_ = from._impl_.totalwarrantexecqty_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x3f000000u) {
    if (cached_has_bits & 0x01000000u) {
      _this->_impl_.warlowerpx_ = from._impl_.warlowerpx_;
    }
    if (cached_has_bits & 0x02000000u) {
      _this->_impl_.yieldtomaturity_ = from._impl_.yieldtomaturity_;
    }
    if (cached_has_bits & 0x04000000u) {
      _this->_impl_.widbuynum_ = from._impl_.widbuynum_;
    }
    if (cached_has_bits & 0x08000000u) {
      _this->_impl_.warupperpx_ = from._impl_.warupperpx_;
    }
    if (cached_has_bits & 0x10000000u) {
      _this->_impl_.widsellnum_ = from._impl_.widsellnum_;
    }
    if (cached_has_bits & 0x20000000u) {
      _this->_impl_.widnum_ = from._impl_.widnum_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MarketData::CopyFrom(const MarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.MarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarketData::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_impl_._has_bits_)) return false;
  return true;
}

void MarketData::InternalSwap(MarketData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.ba_.InternalSwap(&other->_impl_.ba_);
  _impl_.bp_.InternalSwap(&other->_impl_.bp_);
  _impl_.sa_.InternalSwap(&other->_impl_.sa_);
  _impl_.sp_.InternalSwap(&other->_impl_.sp_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.exchid_, lhs_arena,
      &other->_impl_.exchid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.category_, lhs_arena,
      &other->_impl_.category_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.stkid_, lhs_arena,
      &other->_impl_.stkid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rcvsvrtime_, lhs_arena,
      &other->_impl_.rcvsvrtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.pubsvrtime_, lhs_arena,
      &other->_impl_.pubsvrtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.status_, lhs_arena,
      &other->_impl_.status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.exchtime_, lhs_arena,
      &other->_impl_.exchtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.mdsource_, lhs_arena,
      &other->_impl_.mdsource_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MarketData, _impl_.widnum_)
      + sizeof(MarketData::_impl_.widnum_)
      - PROTOBUF_FIELD_OFFSET(MarketData, _impl_.preclose_)>(
          reinterpret_cast<char*>(&_impl_.preclose_),
          reinterpret_cast<char*>(&other->_impl_.preclose_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MarketData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_lyproto_2equota_2eproto_getter, &descriptor_table_lyproto_2equota_2eproto_once,
      file_level_metadata_lyproto_2equota_2eproto[0]);
}

// ===================================================================

class Transaction::_Internal {
 public:
  using HasBits = decltype(std::declval<Transaction>()._impl_._has_bits_);
  static void set_has_exchangeid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_category(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_securityid(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_pubsvrtime(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_tradeindex(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_tradetime(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_tradeprice(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_tradeqty(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_trademoney(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_side(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_tradetype(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_tradecode(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_offerindex(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_bidindex(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_reserve(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

Transaction::Transaction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:LYPROTO.QUOTA.Transaction)
}
Transaction::Transaction(const Transaction& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Transaction* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){from._impl_._has_bits_}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.exchangeid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.securityid_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.tradeindex_){}
    , decltype(_impl_.tradetime_){}
    , decltype(_impl_.side_){}
    , decltype(_impl_.tradetype_){}
    , decltype(_impl_.tradecode_){}
    , decltype(_impl_.offerindex_){}
    , decltype(_impl_.bidindex_){}
    , decltype(_impl_.reserve_){}
    , decltype(_impl_.tradeprice_){}
    , decltype(_impl_.trademoney_){}
    , decltype(_impl_.tradeqty_){}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.exchangeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchangeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_exchangeid()) {
    _this->_impl_.exchangeid_.Set(from._internal_exchangeid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_category()) {
    _this->_impl_.category_.Set(from._internal_category(), 
      _this->GetArenaForAllocation());
  }
  _impl_.securityid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.securityid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_securityid()) {
    _this->_impl_.securityid_.Set(from._internal_securityid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_pubsvrtime()) {
    _this->_impl_.pubsvrtime_.Set(from._internal_pubsvrtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.tradeindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradeindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_tradeindex()) {
    _this->_impl_.tradeindex_.Set(from._internal_tradeindex(), 
      _this->GetArenaForAllocation());
  }
  _impl_.tradetime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradetime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_tradetime()) {
    _this->_impl_.tradetime_.Set(from._internal_tradetime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.side_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.side_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_side()) {
    _this->_impl_.side_.Set(from._internal_side(), 
      _this->GetArenaForAllocation());
  }
  _impl_.tradetype_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradetype_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_tradetype()) {
    _this->_impl_.tradetype_.Set(from._internal_tradetype(), 
      _this->GetArenaForAllocation());
  }
  _impl_.tradecode_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradecode_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_tradecode()) {
    _this->_impl_.tradecode_.Set(from._internal_tradecode(), 
      _this->GetArenaForAllocation());
  }
  _impl_.offerindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.offerindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_offerindex()) {
    _this->_impl_.offerindex_.Set(from._internal_offerindex(), 
      _this->GetArenaForAllocation());
  }
  _impl_.bidindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.bidindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_bidindex()) {
    _this->_impl_.bidindex_.Set(from._internal_bidindex(), 
      _this->GetArenaForAllocation());
  }
  _impl_.reserve_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.reserve_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_reserve()) {
    _this->_impl_.reserve_.Set(from._internal_reserve(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.tradeprice_, &from._impl_.tradeprice_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.tradeqty_) -
    reinterpret_cast<char*>(&_impl_.tradeprice_)) + sizeof(_impl_.tradeqty_));
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.Transaction)
}

inline void Transaction::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.exchangeid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.securityid_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.tradeindex_){}
    , decltype(_impl_.tradetime_){}
    , decltype(_impl_.side_){}
    , decltype(_impl_.tradetype_){}
    , decltype(_impl_.tradecode_){}
    , decltype(_impl_.offerindex_){}
    , decltype(_impl_.bidindex_){}
    , decltype(_impl_.reserve_){}
    , decltype(_impl_.tradeprice_){int64_t{0}}
    , decltype(_impl_.trademoney_){int64_t{0}}
    , decltype(_impl_.tradeqty_){0}
  };
  _impl_.exchangeid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchangeid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.securityid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.securityid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.tradeindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradeindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.tradetime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradetime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.side_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.side_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.tradetype_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradetype_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.tradecode_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradecode_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.offerindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.offerindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.bidindex_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.bidindex_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.reserve_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.reserve_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Transaction::~Transaction() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.Transaction)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Transaction::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.exchangeid_.Destroy();
  _impl_.category_.Destroy();
  _impl_.securityid_.Destroy();
  _impl_.pubsvrtime_.Destroy();
  _impl_.tradeindex_.Destroy();
  _impl_.tradetime_.Destroy();
  _impl_.side_.Destroy();
  _impl_.tradetype_.Destroy();
  _impl_.tradecode_.Destroy();
  _impl_.offerindex_.Destroy();
  _impl_.bidindex_.Destroy();
  _impl_.reserve_.Destroy();
}

void Transaction::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Transaction::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.Transaction)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.exchangeid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.category_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      _impl_.securityid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      _impl_.pubsvrtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000010u) {
      _impl_.tradeindex_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000020u) {
      _impl_.tradetime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000040u) {
      _impl_.side_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000080u) {
      _impl_.tradetype_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x00000f00u) {
    if (cached_has_bits & 0x00000100u) {
      _impl_.tradecode_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000200u) {
      _impl_.offerindex_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000400u) {
      _impl_.bidindex_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000800u) {
      _impl_.reserve_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x00007000u) {
    ::memset(&_impl_.tradeprice_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.tradeqty_) -
        reinterpret_cast<char*>(&_impl_.tradeprice_)) + sizeof(_impl_.tradeqty_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Transaction::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string ExchangeID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_exchangeid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.ExchangeID");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string Category = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_category();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.Category");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string SecurityID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_securityid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.SecurityID");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string PubSvrTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_pubsvrtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.PubSvrTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string TradeIndex = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_tradeindex();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.TradeIndex");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string TradeTime = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_tradetime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.TradeTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional int64 TradePrice = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _Internal::set_has_tradeprice(&has_bits);
          _impl_.tradeprice_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 TradeQty = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_tradeqty(&has_bits);
          _impl_.tradeqty_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 TradeMoney = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _Internal::set_has_trademoney(&has_bits);
          _impl_.trademoney_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string Side = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_side();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.Side");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string TradeType = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_tradetype();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.TradeType");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string TradeCode = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_tradecode();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.TradeCode");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string OfferIndex = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_offerindex();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.OfferIndex");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string BidIndex = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          auto str = _internal_mutable_bidindex();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.BidIndex");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string reserve = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          auto str = _internal_mutable_reserve();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.Transaction.reserve");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _impl_._has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Transaction::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.Transaction)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  // required string ExchangeID = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_exchangeid().data(), static_cast<int>(this->_internal_exchangeid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.ExchangeID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_exchangeid(), target);
  }

  // required string Category = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Category");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_category(), target);
  }

  // required string SecurityID = 3;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_securityid().data(), static_cast<int>(this->_internal_securityid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.SecurityID");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_securityid(), target);
  }

  // optional string PubSvrTime = 4;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_pubsvrtime().data(), static_cast<int>(this->_internal_pubsvrtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.PubSvrTime");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_pubsvrtime(), target);
  }

  // optional string TradeIndex = 5;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tradeindex().data(), static_cast<int>(this->_internal_tradeindex().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeIndex");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_tradeindex(), target);
  }

  // optional string TradeTime = 6;
  if (cached_has_bits & 0x00000020u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tradetime().data(), static_cast<int>(this->_internal_tradetime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeTime");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_tradetime(), target);
  }

  // optional int64 TradePrice = 7;
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(7, this->_internal_tradeprice(), target);
  }

  // optional int32 TradeQty = 8;
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_tradeqty(), target);
  }

  // optional int64 TradeMoney = 9;
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(9, this->_internal_trademoney(), target);
  }

  // optional string Side = 10;
  if (cached_has_bits & 0x00000040u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_side().data(), static_cast<int>(this->_internal_side().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.Side");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_side(), target);
  }

  // optional string TradeType = 11;
  if (cached_has_bits & 0x00000080u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tradetype().data(), static_cast<int>(this->_internal_tradetype().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeType");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_tradetype(), target);
  }

  // optional string TradeCode = 12;
  if (cached_has_bits & 0x00000100u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tradecode().data(), static_cast<int>(this->_internal_tradecode().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.TradeCode");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_tradecode(), target);
  }

  // optional string OfferIndex = 13;
  if (cached_has_bits & 0x00000200u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_offerindex().data(), static_cast<int>(this->_internal_offerindex().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.OfferIndex");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_offerindex(), target);
  }

  // optional string BidIndex = 14;
  if (cached_has_bits & 0x00000400u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_bidindex().data(), static_cast<int>(this->_internal_bidindex().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.BidIndex");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_bidindex(), target);
  }

  // optional string reserve = 15;
  if (cached_has_bits & 0x00000800u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_reserve().data(), static_cast<int>(this->_internal_reserve().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.Transaction.reserve");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_reserve(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.Transaction)
  return target;
}

size_t Transaction::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.Transaction)
  size_t total_size = 0;

  if (_internal_has_exchangeid()) {
    // required string ExchangeID = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchangeid());
  }

  if (_internal_has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  if (_internal_has_securityid()) {
    // required string SecurityID = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_securityid());
  }

  return total_size;
}
size_t Transaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.Transaction)
  size_t total_size = 0;

  if (((_impl_._has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchangeID = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchangeid());

    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());

    // required string SecurityID = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_securityid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000f8u) {
    // optional string PubSvrTime = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_pubsvrtime());
    }

    // optional string TradeIndex = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_tradeindex());
    }

    // optional string TradeTime = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_tradetime());
    }

    // optional string Side = 10;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_side());
    }

    // optional string TradeType = 11;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_tradetype());
    }

  }
  if (cached_has_bits & 0x00007f00u) {
    // optional string TradeCode = 12;
    if (cached_has_bits & 0x00000100u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_tradecode());
    }

    // optional string OfferIndex = 13;
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_offerindex());
    }

    // optional string BidIndex = 14;
    if (cached_has_bits & 0x00000400u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_bidindex());
    }

    // optional string reserve = 15;
    if (cached_has_bits & 0x00000800u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_reserve());
    }

    // optional int64 TradePrice = 7;
    if (cached_has_bits & 0x00001000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_tradeprice());
    }

    // optional int64 TradeMoney = 9;
    if (cached_has_bits & 0x00002000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_trademoney());
    }

    // optional int32 TradeQty = 8;
    if (cached_has_bits & 0x00004000u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_tradeqty());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Transaction::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Transaction::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Transaction::GetClassData() const { return &_class_data_; }


void Transaction::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Transaction*>(&to_msg);
  auto& from = static_cast<const Transaction&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.Transaction)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_exchangeid(from._internal_exchangeid());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_category(from._internal_category());
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_internal_set_securityid(from._internal_securityid());
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_internal_set_pubsvrtime(from._internal_pubsvrtime());
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_internal_set_tradeindex(from._internal_tradeindex());
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_internal_set_tradetime(from._internal_tradetime());
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_internal_set_side(from._internal_side());
    }
    if (cached_has_bits & 0x00000080u) {
      _this->_internal_set_tradetype(from._internal_tradetype());
    }
  }
  if (cached_has_bits & 0x00007f00u) {
    if (cached_has_bits & 0x00000100u) {
      _this->_internal_set_tradecode(from._internal_tradecode());
    }
    if (cached_has_bits & 0x00000200u) {
      _this->_internal_set_offerindex(from._internal_offerindex());
    }
    if (cached_has_bits & 0x00000400u) {
      _this->_internal_set_bidindex(from._internal_bidindex());
    }
    if (cached_has_bits & 0x00000800u) {
      _this->_internal_set_reserve(from._internal_reserve());
    }
    if (cached_has_bits & 0x00001000u) {
      _this->_impl_.tradeprice_ = from._impl_.tradeprice_;
    }
    if (cached_has_bits & 0x00002000u) {
      _this->_impl_.trademoney_ = from._impl_.trademoney_;
    }
    if (cached_has_bits & 0x00004000u) {
      _this->_impl_.tradeqty_ = from._impl_.tradeqty_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Transaction::CopyFrom(const Transaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.Transaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transaction::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_impl_._has_bits_)) return false;
  return true;
}

void Transaction::InternalSwap(Transaction* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.exchangeid_, lhs_arena,
      &other->_impl_.exchangeid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.category_, lhs_arena,
      &other->_impl_.category_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.securityid_, lhs_arena,
      &other->_impl_.securityid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.pubsvrtime_, lhs_arena,
      &other->_impl_.pubsvrtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.tradeindex_, lhs_arena,
      &other->_impl_.tradeindex_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.tradetime_, lhs_arena,
      &other->_impl_.tradetime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.side_, lhs_arena,
      &other->_impl_.side_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.tradetype_, lhs_arena,
      &other->_impl_.tradetype_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.tradecode_, lhs_arena,
      &other->_impl_.tradecode_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.offerindex_, lhs_arena,
      &other->_impl_.offerindex_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.bidindex_, lhs_arena,
      &other->_impl_.bidindex_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.reserve_, lhs_arena,
      &other->_impl_.reserve_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Transaction, _impl_.tradeqty_)
      + sizeof(Transaction::_impl_.tradeqty_)
      - PROTOBUF_FIELD_OFFSET(Transaction, _impl_.tradeprice_)>(
          reinterpret_cast<char*>(&_impl_.tradeprice_),
          reinterpret_cast<char*>(&other->_impl_.tradeprice_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Transaction::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_lyproto_2equota_2eproto_getter, &descriptor_table_lyproto_2equota_2eproto_once,
      file_level_metadata_lyproto_2equota_2eproto[1]);
}

// ===================================================================

class FutureMarketData::_Internal {
 public:
  using HasBits = decltype(std::declval<FutureMarketData>()._impl_._has_bits_);
  static void set_has_exchid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_category(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_stkid(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_rcvsvrtime(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_pubsvrtime(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_status(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_exchtime(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_tradedate(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_preclose(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_presettle(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_preopenpos(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_highlimit(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_lowlimit(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_open(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_latest(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_high(HasBits* has_bits) {
    (*has_bits)[0] |= 65536u;
  }
  static void set_has_low(HasBits* has_bits) {
    (*has_bits)[0] |= 131072u;
  }
  static void set_has_settle(HasBits* has_bits) {
    (*has_bits)[0] |= 262144u;
  }
  static void set_has_latestvolume(HasBits* has_bits) {
    (*has_bits)[0] |= 32768u;
  }
  static void set_has_volume(HasBits* has_bits) {
    (*has_bits)[0] |= 524288u;
  }
  static void set_has_openpos(HasBits* has_bits) {
    (*has_bits)[0] |= 1048576u;
  }
  static void set_has_value(HasBits* has_bits) {
    (*has_bits)[0] |= 2097152u;
  }
  static void set_has_predelta(HasBits* has_bits) {
    (*has_bits)[0] |= 4194304u;
  }
  static void set_has_curdelta(HasBits* has_bits) {
    (*has_bits)[0] |= 8388608u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

FutureMarketData::FutureMarketData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:LYPROTO.QUOTA.FutureMarketData)
}
FutureMarketData::FutureMarketData(const FutureMarketData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  FutureMarketData* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){from._impl_._has_bits_}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.ba_){from._impl_.ba_}
    , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
    , decltype(_impl_.bp_){from._impl_.bp_}
    , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
    , decltype(_impl_.sa_){from._impl_.sa_}
    , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
    , decltype(_impl_.sp_){from._impl_.sp_}
    , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
    , decltype(_impl_.exchid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.stkid_){}
    , decltype(_impl_.rcvsvrtime_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.status_){}
    , decltype(_impl_.exchtime_){}
    , decltype(_impl_.tradedate_){}
    , decltype(_impl_.preclose_){}
    , decltype(_impl_.presettle_){}
    , decltype(_impl_.highlimit_){}
    , decltype(_impl_.lowlimit_){}
    , decltype(_impl_.open_){}
    , decltype(_impl_.latest_){}
    , decltype(_impl_.preopenpos_){}
    , decltype(_impl_.latestvolume_){}
    , decltype(_impl_.high_){}
    , decltype(_impl_.low_){}
    , decltype(_impl_.settle_){}
    , decltype(_impl_.volume_){}
    , decltype(_impl_.openpos_){}
    , decltype(_impl_.value_){}
    , decltype(_impl_.predelta_){}
    , decltype(_impl_.curdelta_){}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.exchid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_exchid()) {
    _this->_impl_.exchid_.Set(from._internal_exchid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_category()) {
    _this->_impl_.category_.Set(from._internal_category(), 
      _this->GetArenaForAllocation());
  }
  _impl_.stkid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.stkid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_stkid()) {
    _this->_impl_.stkid_.Set(from._internal_stkid(), 
      _this->GetArenaForAllocation());
  }
  _impl_.rcvsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_rcvsvrtime()) {
    _this->_impl_.rcvsvrtime_.Set(from._internal_rcvsvrtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_pubsvrtime()) {
    _this->_impl_.pubsvrtime_.Set(from._internal_pubsvrtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.status_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.status_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_status()) {
    _this->_impl_.status_.Set(from._internal_status(), 
      _this->GetArenaForAllocation());
  }
  _impl_.exchtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_exchtime()) {
    _this->_impl_.exchtime_.Set(from._internal_exchtime(), 
      _this->GetArenaForAllocation());
  }
  _impl_.tradedate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradedate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_tradedate()) {
    _this->_impl_.tradedate_.Set(from._internal_tradedate(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.preclose_, &from._impl_.preclose_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.curdelta_) -
    reinterpret_cast<char*>(&_impl_.preclose_)) + sizeof(_impl_.curdelta_));
  // @@protoc_insertion_point(copy_constructor:LYPROTO.QUOTA.FutureMarketData)
}

inline void FutureMarketData::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.ba_){arena}
    , /*decltype(_impl_._ba_cached_byte_size_)*/{0}
    , decltype(_impl_.bp_){arena}
    , /*decltype(_impl_._bp_cached_byte_size_)*/{0}
    , decltype(_impl_.sa_){arena}
    , /*decltype(_impl_._sa_cached_byte_size_)*/{0}
    , decltype(_impl_.sp_){arena}
    , /*decltype(_impl_._sp_cached_byte_size_)*/{0}
    , decltype(_impl_.exchid_){}
    , decltype(_impl_.category_){}
    , decltype(_impl_.stkid_){}
    , decltype(_impl_.rcvsvrtime_){}
    , decltype(_impl_.pubsvrtime_){}
    , decltype(_impl_.status_){}
    , decltype(_impl_.exchtime_){}
    , decltype(_impl_.tradedate_){}
    , decltype(_impl_.preclose_){int64_t{0}}
    , decltype(_impl_.presettle_){int64_t{0}}
    , decltype(_impl_.highlimit_){int64_t{0}}
    , decltype(_impl_.lowlimit_){int64_t{0}}
    , decltype(_impl_.open_){int64_t{0}}
    , decltype(_impl_.latest_){int64_t{0}}
    , decltype(_impl_.preopenpos_){0}
    , decltype(_impl_.latestvolume_){0}
    , decltype(_impl_.high_){int64_t{0}}
    , decltype(_impl_.low_){int64_t{0}}
    , decltype(_impl_.settle_){int64_t{0}}
    , decltype(_impl_.volume_){0}
    , decltype(_impl_.openpos_){0}
    , decltype(_impl_.value_){int64_t{0}}
    , decltype(_impl_.predelta_){0}
    , decltype(_impl_.curdelta_){0}
  };
  _impl_.exchid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.category_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.category_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.stkid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.stkid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.rcvsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.pubsvrtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.status_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.status_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.exchtime_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.tradedate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.tradedate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

FutureMarketData::~FutureMarketData() {
  // @@protoc_insertion_point(destructor:LYPROTO.QUOTA.FutureMarketData)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void FutureMarketData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.ba_.~RepeatedField();
  _impl_.bp_.~RepeatedField();
  _impl_.sa_.~RepeatedField();
  _impl_.sp_.~RepeatedField();
  _impl_.exchid_.Destroy();
  _impl_.category_.Destroy();
  _impl_.stkid_.Destroy();
  _impl_.rcvsvrtime_.Destroy();
  _impl_.pubsvrtime_.Destroy();
  _impl_.status_.Destroy();
  _impl_.exchtime_.Destroy();
  _impl_.tradedate_.Destroy();
}

void FutureMarketData::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void FutureMarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:LYPROTO.QUOTA.FutureMarketData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.ba_.Clear();
  _impl_.bp_.Clear();
  _impl_.sa_.Clear();
  _impl_.sp_.Clear();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.exchid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.category_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000004u) {
      _impl_.stkid_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000008u) {
      _impl_.rcvsvrtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000010u) {
      _impl_.pubsvrtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000020u) {
      _impl_.status_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000040u) {
      _impl_.exchtime_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000080u) {
      _impl_.tradedate_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    ::memset(&_impl_.preclose_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.latestvolume_) -
        reinterpret_cast<char*>(&_impl_.preclose_)) + sizeof(_impl_.latestvolume_));
  }
  if (cached_has_bits & 0x00ff0000u) {
    ::memset(&_impl_.high_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&_impl_.curdelta_) -
        reinterpret_cast<char*>(&_impl_.high_)) + sizeof(_impl_.curdelta_));
  }
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FutureMarketData::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string ExchId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_exchid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.ExchId");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string Category = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_category();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.Category");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // required string stkId = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_stkid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.stkId");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string RcvSvrTime = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_rcvsvrtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.RcvSvrTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string PubSvrTime = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_pubsvrtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.PubSvrTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string Status = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_status();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.Status");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string ExchTime = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_exchtime();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.ExchTime");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional string TradeDate = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_tradedate();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          #ifndef NDEBUG
          ::_pbi::VerifyUTF8(str, "LYPROTO.QUOTA.FutureMarketData.TradeDate");
          #endif  // !NDEBUG
        } else
          goto handle_unusual;
        continue;
      // optional int64 PreClose = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _Internal::set_has_preclose(&has_bits);
          _impl_.preclose_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 PreSettle = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _Internal::set_has_presettle(&has_bits);
          _impl_.presettle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 PreOpenPos = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _Internal::set_has_preopenpos(&has_bits);
          _impl_.preopenpos_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 HighLimit = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _Internal::set_has_highlimit(&has_bits);
          _impl_.highlimit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 LowLimit = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _Internal::set_has_lowlimit(&has_bits);
          _impl_.lowlimit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Open = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          _Internal::set_has_open(&has_bits);
          _impl_.open_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Latest = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          _Internal::set_has_latest(&has_bits);
          _impl_.latest_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 High = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _Internal::set_has_high(&has_bits);
          _impl_.high_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Low = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          _Internal::set_has_low(&has_bits);
          _impl_.low_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Settle = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          _Internal::set_has_settle(&has_bits);
          _impl_.settle_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 LatestVolume = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          _Internal::set_has_latestvolume(&has_bits);
          _impl_.latestvolume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 Volume = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 160)) {
          _Internal::set_has_volume(&has_bits);
          _impl_.volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 OpenPos = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 168)) {
          _Internal::set_has_openpos(&has_bits);
          _impl_.openpos_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 Value = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 176)) {
          _Internal::set_has_value(&has_bits);
          _impl_.value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int32 BA = 23 [packed = true];
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 186)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_ba(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 184) {
          _internal_add_ba(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 BP = 24 [packed = true];
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 194)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_bp(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 192) {
          _internal_add_bp(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int32 SA = 25 [packed = true];
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 202)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_sa(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 200) {
          _internal_add_sa(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 SP = 26 [packed = true];
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 210)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_sp(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 208) {
          _internal_add_sp(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 PreDelta = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 216)) {
          _Internal::set_has_predelta(&has_bits);
          _impl_.predelta_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 CurDelta = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 224)) {
          _Internal::set_has_curdelta(&has_bits);
          _impl_.curdelta_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _impl_._has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FutureMarketData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:LYPROTO.QUOTA.FutureMarketData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _impl_._has_bits_[0];
  // required string ExchId = 1;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_exchid().data(), static_cast<int>(this->_internal_exchid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_exchid(), target);
  }

  // required string Category = 2;
  if (cached_has_bits & 0x00000002u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Category");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_category(), target);
  }

  // required string stkId = 3;
  if (cached_has_bits & 0x00000004u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_stkid().data(), static_cast<int>(this->_internal_stkid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.stkId");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_stkid(), target);
  }

  // optional string RcvSvrTime = 4;
  if (cached_has_bits & 0x00000008u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_rcvsvrtime().data(), static_cast<int>(this->_internal_rcvsvrtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.RcvSvrTime");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_rcvsvrtime(), target);
  }

  // optional string PubSvrTime = 5;
  if (cached_has_bits & 0x00000010u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_pubsvrtime().data(), static_cast<int>(this->_internal_pubsvrtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.PubSvrTime");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_pubsvrtime(), target);
  }

  // optional string Status = 6;
  if (cached_has_bits & 0x00000020u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_status().data(), static_cast<int>(this->_internal_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.Status");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_status(), target);
  }

  // optional string ExchTime = 7;
  if (cached_has_bits & 0x00000040u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_exchtime().data(), static_cast<int>(this->_internal_exchtime().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.ExchTime");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_exchtime(), target);
  }

  // optional string TradeDate = 8;
  if (cached_has_bits & 0x00000080u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_tradedate().data(), static_cast<int>(this->_internal_tradedate().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "LYPROTO.QUOTA.FutureMarketData.TradeDate");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_tradedate(), target);
  }

  // optional int64 PreClose = 9;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(9, this->_internal_preclose(), target);
  }

  // optional int64 PreSettle = 10;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(10, this->_internal_presettle(), target);
  }

  // optional int32 PreOpenPos = 11;
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(11, this->_internal_preopenpos(), target);
  }

  // optional int64 HighLimit = 12;
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(12, this->_internal_highlimit(), target);
  }

  // optional int64 LowLimit = 13;
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(13, this->_internal_lowlimit(), target);
  }

  // optional int64 Open = 14;
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(14, this->_internal_open(), target);
  }

  // optional int64 Latest = 15;
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(15, this->_internal_latest(), target);
  }

  // optional int64 High = 16;
  if (cached_has_bits & 0x00010000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(16, this->_internal_high(), target);
  }

  // optional int64 Low = 17;
  if (cached_has_bits & 0x00020000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(17, this->_internal_low(), target);
  }

  // optional int64 Settle = 18;
  if (cached_has_bits & 0x00040000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(18, this->_internal_settle(), target);
  }

  // optional int32 LatestVolume = 19;
  if (cached_has_bits & 0x00008000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(19, this->_internal_latestvolume(), target);
  }

  // optional int32 Volume = 20;
  if (cached_has_bits & 0x00080000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(20, this->_internal_volume(), target);
  }

  // optional int32 OpenPos = 21;
  if (cached_has_bits & 0x00100000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(21, this->_internal_openpos(), target);
  }

  // optional int64 Value = 22;
  if (cached_has_bits & 0x00200000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(22, this->_internal_value(), target);
  }

  // repeated int32 BA = 23 [packed = true];
  {
    int byte_size = _impl_._ba_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          23, _internal_ba(), byte_size, target);
    }
  }

  // repeated int64 BP = 24 [packed = true];
  {
    int byte_size = _impl_._bp_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          24, _internal_bp(), byte_size, target);
    }
  }

  // repeated int32 SA = 25 [packed = true];
  {
    int byte_size = _impl_._sa_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          25, _internal_sa(), byte_size, target);
    }
  }

  // repeated int64 SP = 26 [packed = true];
  {
    int byte_size = _impl_._sp_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          26, _internal_sp(), byte_size, target);
    }
  }

  // optional int32 PreDelta = 27;
  if (cached_has_bits & 0x00400000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(27, this->_internal_predelta(), target);
  }

  // optional int32 CurDelta = 28;
  if (cached_has_bits & 0x00800000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(28, this->_internal_curdelta(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:LYPROTO.QUOTA.FutureMarketData)
  return target;
}

size_t FutureMarketData::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:LYPROTO.QUOTA.FutureMarketData)
  size_t total_size = 0;

  if (_internal_has_exchid()) {
    // required string ExchId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchid());
  }

  if (_internal_has_category()) {
    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  if (_internal_has_stkid()) {
    // required string stkId = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stkid());
  }

  return total_size;
}
size_t FutureMarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:LYPROTO.QUOTA.FutureMarketData)
  size_t total_size = 0;

  if (((_impl_._has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string ExchId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exchid());

    // required string Category = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());

    // required string stkId = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stkid());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 BA = 23 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int32Size(this->_impl_.ba_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._ba_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 BP = 24 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.bp_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._bp_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 SA = 25 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int32Size(this->_impl_.sa_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._sa_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 SP = 26 [packed = true];
  {
    size_t data_size = ::_pbi::WireFormatLite::
      Int64Size(this->_impl_.sp_);
    if (data_size > 0) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._sp_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x000000f8u) {
    // optional string RcvSvrTime = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_rcvsvrtime());
    }

    // optional string PubSvrTime = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_pubsvrtime());
    }

    // optional string Status = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_status());
    }

    // optional string ExchTime = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_exchtime());
    }

    // optional string TradeDate = 8;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_tradedate());
    }

  }
  if (cached_has_bits & 0x0000ff00u) {
    // optional int64 PreClose = 9;
    if (cached_has_bits & 0x00000100u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_preclose());
    }

    // optional int64 PreSettle = 10;
    if (cached_has_bits & 0x00000200u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_presettle());
    }

    // optional int64 HighLimit = 12;
    if (cached_has_bits & 0x00000400u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_highlimit());
    }

    // optional int64 LowLimit = 13;
    if (cached_has_bits & 0x00000800u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_lowlimit());
    }

    // optional int64 Open = 14;
    if (cached_has_bits & 0x00001000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_open());
    }

    // optional int64 Latest = 15;
    if (cached_has_bits & 0x00002000u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_latest());
    }

    // optional int32 PreOpenPos = 11;
    if (cached_has_bits & 0x00004000u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_preopenpos());
    }

    // optional int32 LatestVolume = 19;
    if (cached_has_bits & 0x00008000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_latestvolume());
    }

  }
  if (cached_has_bits & 0x00ff0000u) {
    // optional int64 High = 16;
    if (cached_has_bits & 0x00010000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_high());
    }

    // optional int64 Low = 17;
    if (cached_has_bits & 0x00020000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_low());
    }

    // optional int64 Settle = 18;
    if (cached_has_bits & 0x00040000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_settle());
    }

    // optional int32 Volume = 20;
    if (cached_has_bits & 0x00080000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_volume());
    }

    // optional int32 OpenPos = 21;
    if (cached_has_bits & 0x00100000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_openpos());
    }

    // optional int64 Value = 22;
    if (cached_has_bits & 0x00200000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int64Size(
          this->_internal_value());
    }

    // optional int32 PreDelta = 27;
    if (cached_has_bits & 0x00400000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_predelta());
    }

    // optional int32 CurDelta = 28;
    if (cached_has_bits & 0x00800000u) {
      total_size += 2 +
        ::_pbi::WireFormatLite::Int32Size(
          this->_internal_curdelta());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FutureMarketData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    FutureMarketData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FutureMarketData::GetClassData() const { return &_class_data_; }


void FutureMarketData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<FutureMarketData*>(&to_msg);
  auto& from = static_cast<const FutureMarketData&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:LYPROTO.QUOTA.FutureMarketData)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.ba_.MergeFrom(from._impl_.ba_);
  _this->_impl_.bp_.MergeFrom(from._impl_.bp_);
  _this->_impl_.sa_.MergeFrom(from._impl_.sa_);
  _this->_impl_.sp_.MergeFrom(from._impl_.sp_);
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_exchid(from._internal_exchid());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_category(from._internal_category());
    }
    if (cached_has_bits & 0x00000004u) {
      _this->_internal_set_stkid(from._internal_stkid());
    }
    if (cached_has_bits & 0x00000008u) {
      _this->_internal_set_rcvsvrtime(from._internal_rcvsvrtime());
    }
    if (cached_has_bits & 0x00000010u) {
      _this->_internal_set_pubsvrtime(from._internal_pubsvrtime());
    }
    if (cached_has_bits & 0x00000020u) {
      _this->_internal_set_status(from._internal_status());
    }
    if (cached_has_bits & 0x00000040u) {
      _this->_internal_set_exchtime(from._internal_exchtime());
    }
    if (cached_has_bits & 0x00000080u) {
      _this->_internal_set_tradedate(from._internal_tradedate());
    }
  }
  if (cached_has_bits & 0x0000ff00u) {
    if (cached_has_bits & 0x00000100u) {
      _this->_impl_.preclose_ = from._impl_.preclose_;
    }
    if (cached_has_bits & 0x00000200u) {
      _this->_impl_.presettle_ = from._impl_.presettle_;
    }
    if (cached_has_bits & 0x00000400u) {
      _this->_impl_.highlimit_ = from._impl_.highlimit_;
    }
    if (cached_has_bits & 0x00000800u) {
      _this->_impl_.lowlimit_ = from._impl_.lowlimit_;
    }
    if (cached_has_bits & 0x00001000u) {
      _this->_impl_.open_ = from._impl_.open_;
    }
    if (cached_has_bits & 0x00002000u) {
      _this->_impl_.latest_ = from._impl_.latest_;
    }
    if (cached_has_bits & 0x00004000u) {
      _this->_impl_.preopenpos_ = from._impl_.preopenpos_;
    }
    if (cached_has_bits & 0x00008000u) {
      _this->_impl_.latestvolume_ = from._impl_.latestvolume_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00ff0000u) {
    if (cached_has_bits & 0x00010000u) {
      _this->_impl_.high_ = from._impl_.high_;
    }
    if (cached_has_bits & 0x00020000u) {
      _this->_impl_.low_ = from._impl_.low_;
    }
    if (cached_has_bits & 0x00040000u) {
      _this->_impl_.settle_ = from._impl_.settle_;
    }
    if (cached_has_bits & 0x00080000u) {
      _this->_impl_.volume_ = from._impl_.volume_;
    }
    if (cached_has_bits & 0x00100000u) {
      _this->_impl_.openpos_ = from._impl_.openpos_;
    }
    if (cached_has_bits & 0x00200000u) {
      _this->_impl_.value_ = from._impl_.value_;
    }
    if (cached_has_bits & 0x00400000u) {
      _this->_impl_.predelta_ = from._impl_.predelta_;
    }
    if (cached_has_bits & 0x00800000u) {
      _this->_impl_.curdelta_ = from._impl_.curdelta_;
    }
    _this->_impl_._has_bits_[0] |= cached_has_bits;
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FutureMarketData::CopyFrom(const FutureMarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:LYPROTO.QUOTA.FutureMarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FutureMarketData::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_impl_._has_bits_)) return false;
  return true;
}

void FutureMarketData::InternalSwap(FutureMarketData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.ba_.InternalSwap(&other->_impl_.ba_);
  _impl_.bp_.InternalSwap(&other->_impl_.bp_);
  _impl_.sa_.InternalSwap(&other->_impl_.sa_);
  _impl_.sp_.InternalSwap(&other->_impl_.sp_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.exchid_, lhs_arena,
      &other->_impl_.exchid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.category_, lhs_arena,
      &other->_impl_.category_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.stkid_, lhs_arena,
      &other->_impl_.stkid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.rcvsvrtime_, lhs_arena,
      &other->_impl_.rcvsvrtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.pubsvrtime_, lhs_arena,
      &other->_impl_.pubsvrtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.status_, lhs_arena,
      &other->_impl_.status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.exchtime_, lhs_arena,
      &other->_impl_.exchtime_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.tradedate_, lhs_arena,
      &other->_impl_.tradedate_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FutureMarketData, _impl_.curdelta_)
      + sizeof(FutureMarketData::_impl_.curdelta_)
      - PROTOBUF_FIELD_OFFSET(FutureMarketData, _impl_.preclose_)>(
          reinterpret_cast<char*>(&_impl_.preclose_),
          reinterpret_cast<char*>(&other->_impl_.preclose_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FutureMarketData::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_lyproto_2equota_2eproto_getter, &descriptor_table_lyproto_2equota_2eproto_once,
      file_level_metadata_lyproto_2equota_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace QUOTA
}  // namespace LYPROTO
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::LYPROTO::QUOTA::MarketData*
Arena::CreateMaybeMessage< ::LYPROTO::QUOTA::MarketData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::LYPROTO::QUOTA::MarketData >(arena);
}
template<> PROTOBUF_NOINLINE ::LYPROTO::QUOTA::Transaction*
Arena::CreateMaybeMessage< ::LYPROTO::QUOTA::Transaction >(Arena* arena) {
  return Arena::CreateMessageInternal< ::LYPROTO::QUOTA::Transaction >(arena);
}
template<> PROTOBUF_NOINLINE ::LYPROTO::QUOTA::FutureMarketData*
Arena::CreateMaybeMessage< ::LYPROTO::QUOTA::FutureMarketData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::LYPROTO::QUOTA::FutureMarketData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
