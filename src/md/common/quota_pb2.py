# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lyproto.quota.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13lyproto.quota.proto\x12\rLYPROTO.QUOTA\"\x81\x05\n\nMarketData\x12\x0e\n\x06\x45xchId\x18\x01 \x02(\t\x12\x10\n\x08\x43\x61tegory\x18\x02 \x02(\t\x12\r\n\x05stkId\x18\x03 \x02(\t\x12\x12\n\nRcvSvrTime\x18\x04 \x01(\t\x12\x12\n\nPubSvrTime\x18\x05 \x01(\t\x12\x0e\n\x06Status\x18\x06 \x01(\t\x12\x10\n\x08\x45xchTime\x18\x07 \x01(\t\x12\x10\n\x08PreClose\x18\x08 \x01(\x03\x12\x11\n\tHighLimit\x18\t \x01(\x03\x12\x10\n\x08LowLimit\x18\n \x01(\x03\x12\x0c\n\x04Open\x18\x0b \x01(\x03\x12\x0c\n\x04High\x18\x0c \x01(\x03\x12\x0b\n\x03Low\x18\r \x01(\x03\x12\x0e\n\x06Latest\x18\x0e \x01(\x03\x12\r\n\x05Knock\x18\x0f \x01(\x05\x12\x0e\n\x06Volume\x18\x10 \x01(\x03\x12\r\n\x05Value\x18\x11 \x01(\x03\x12\x0e\n\x02\x42\x41\x18\x12 \x03(\x03\x42\x02\x10\x01\x12\x0e\n\x02\x42P\x18\x13 \x03(\x03\x42\x02\x10\x01\x12\x0e\n\x02SA\x18\x14 \x03(\x03\x42\x02\x10\x01\x12\x0e\n\x02SP\x18\x15 \x03(\x03\x42\x02\x10\x01\x12\x0f\n\x07TotalBA\x18\x16 \x01(\x03\x12\x18\n\x10WeightedAvgBidPx\x18\x17 \x01(\x03\x12\x0f\n\x07TotalSA\x18\x18 \x01(\x03\x12\x1a\n\x12WeightedAvgOfferPx\x18\x19 \x01(\x03\x12\x0c\n\x04IOPV\x18\x1a \x01(\x05\x12\x17\n\x0fYieldToMaturity\x18\x1b \x01(\x05\x12\x1b\n\x13TotalWarrantExecQty\x18\x1c \x01(\x03\x12\x12\n\nWarLowerPx\x18\x1d \x01(\x03\x12\x12\n\nWarUpperPx\x18\x1e \x01(\x03\x12\x10\n\x08MdSource\x18\x1f \x01(\t\x12\x11\n\tWiDBuyNum\x18  \x01(\x05\x12\x12\n\nWiDSellNum\x18! \x01(\x05\x12\x0e\n\x06WiDNum\x18\" \x01(\x05\x12\r\n\x05\x43lose\x18# \x01(\x03\"\xa7\x02\n\x0bTransaction\x12\x12\n\nExchangeID\x18\x01 \x02(\t\x12\x10\n\x08\x43\x61tegory\x18\x02 \x02(\t\x12\x12\n\nSecurityID\x18\x03 \x02(\t\x12\x12\n\nPubSvrTime\x18\x04 \x01(\t\x12\x12\n\nTradeIndex\x18\x05 \x01(\t\x12\x11\n\tTradeTime\x18\x06 \x01(\t\x12\x12\n\nTradePrice\x18\x07 \x01(\x03\x12\x10\n\x08TradeQty\x18\x08 \x01(\x05\x12\x12\n\nTradeMoney\x18\t \x01(\x03\x12\x0c\n\x04Side\x18\n \x01(\t\x12\x11\n\tTradeType\x18\x0b \x01(\t\x12\x11\n\tTradeCode\x18\x0c \x01(\t\x12\x12\n\nOfferIndex\x18\r \x01(\t\x12\x10\n\x08\x42idIndex\x18\x0e \x01(\t\x12\x0f\n\x07reserve\x18\x0f \x01(\t\"\xf1\x03\n\x10\x46utureMarketData\x12\x0e\n\x06\x45xchId\x18\x01 \x02(\t\x12\x10\n\x08\x43\x61tegory\x18\x02 \x02(\t\x12\r\n\x05stkId\x18\x03 \x02(\t\x12\x12\n\nRcvSvrTime\x18\x04 \x01(\t\x12\x12\n\nPubSvrTime\x18\x05 \x01(\t\x12\x0e\n\x06Status\x18\x06 \x01(\t\x12\x10\n\x08\x45xchTime\x18\x07 \x01(\t\x12\x11\n\tTradeDate\x18\x08 \x01(\t\x12\x10\n\x08PreClose\x18\t \x01(\x03\x12\x11\n\tPreSettle\x18\n \x01(\x03\x12\x12\n\nPreOpenPos\x18\x0b \x01(\x05\x12\x11\n\tHighLimit\x18\x0c \x01(\x03\x12\x10\n\x08LowLimit\x18\r \x01(\x03\x12\x0c\n\x04Open\x18\x0e \x01(\x03\x12\x0e\n\x06Latest\x18\x0f \x01(\x03\x12\x0c\n\x04High\x18\x10 \x01(\x03\x12\x0b\n\x03Low\x18\x11 \x01(\x03\x12\x0e\n\x06Settle\x18\x12 \x01(\x03\x12\x14\n\x0cLatestVolume\x18\x13 \x01(\x05\x12\x0e\n\x06Volume\x18\x14 \x01(\x05\x12\x0f\n\x07OpenPos\x18\x15 \x01(\x05\x12\r\n\x05Value\x18\x16 \x01(\x03\x12\x0e\n\x02\x42\x41\x18\x17 \x03(\x05\x42\x02\x10\x01\x12\x0e\n\x02\x42P\x18\x18 \x03(\x03\x42\x02\x10\x01\x12\x0e\n\x02SA\x18\x19 \x03(\x05\x42\x02\x10\x01\x12\x0e\n\x02SP\x18\x1a \x03(\x03\x42\x02\x10\x01\x12\x10\n\x08PreDelta\x18\x1b \x01(\x05\x12\x10\n\x08\x43urDelta\x18\x1c \x01(\x05')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lyproto.quota_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _MARKETDATA.fields_by_name['BA']._options = None
  _MARKETDATA.fields_by_name['BA']._serialized_options = b'\020\001'
  _MARKETDATA.fields_by_name['BP']._options = None
  _MARKETDATA.fields_by_name['BP']._serialized_options = b'\020\001'
  _MARKETDATA.fields_by_name['SA']._options = None
  _MARKETDATA.fields_by_name['SA']._serialized_options = b'\020\001'
  _MARKETDATA.fields_by_name['SP']._options = None
  _MARKETDATA.fields_by_name['SP']._serialized_options = b'\020\001'
  _FUTUREMARKETDATA.fields_by_name['BA']._options = None
  _FUTUREMARKETDATA.fields_by_name['BA']._serialized_options = b'\020\001'
  _FUTUREMARKETDATA.fields_by_name['BP']._options = None
  _FUTUREMARKETDATA.fields_by_name['BP']._serialized_options = b'\020\001'
  _FUTUREMARKETDATA.fields_by_name['SA']._options = None
  _FUTUREMARKETDATA.fields_by_name['SA']._serialized_options = b'\020\001'
  _FUTUREMARKETDATA.fields_by_name['SP']._options = None
  _FUTUREMARKETDATA.fields_by_name['SP']._serialized_options = b'\020\001'
  _MARKETDATA._serialized_start=39
  _MARKETDATA._serialized_end=680
  _TRANSACTION._serialized_start=683
  _TRANSACTION._serialized_end=978
  _FUTUREMARKETDATA._serialized_start=981
  _FUTUREMARKETDATA._serialized_end=1478
# @@protoc_insertion_point(module_scope)
