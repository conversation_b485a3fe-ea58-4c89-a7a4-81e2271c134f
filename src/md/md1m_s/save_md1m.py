#!/usr/bin/python3

import argparse
import zmq
from datetime import datetime
import struct

'''
typedef struct t_zmd1m
{
    char ty;                 // 类别siof
    char code[22];           // 代码，尾部填\0
    int64_t exchtime_pub;   // 行情发布时间, Unix 秒
    uint32_t v;              // volume
    double o, h, l, c, m, a; // open, high, low, close, money, avg
    double opos;
} zmd1m_t;
'''

def pb_parse_s(msg:bytes, f, recvtime):
    z = struct.unpack('=c22sQiddddddd', msg)
    ty, code_b, exchtime_pub, v, o, h, l, c, m, a, opos = z
    f.write(f"{code_b.decode('utf-8').rstrip('\x00')},{exchtime_pub},{recvtime},{v},{o},{h},{l},{c},{m},{a}\n")
    f.flush()

parser = argparse.ArgumentParser(description='保存分钟线行情到 csv 文件')
parser.add_argument('-f', '--file', type=str, default='md1m', help='分钟线行情保存文件名前缀')
parser.add_argument('-z', '--zmq', type=str, default='************:30007', help='ZeroMQ 服务器地址，如 localhost:30007')
parser.add_argument('-s', '--sub', type=str, default='', help='订阅行情主题，如 S600519，I3010*')

args = parser.parse_args()
f = open(args.file + ".csv", "w")
f.write("stkId,PubTime,RecvTime,Volume,Open,High,Low,Close,Money,Avg\n")
context = zmq.Context()
socket = context.socket(zmq.SUB)
socket.connect(f"tcp://{args.zmq}")
socket.setsockopt_string(zmq.SUBSCRIBE, args.sub)
socket.setsockopt(zmq.RCVBUF, 1024 * 1024 * 1024)
socket.setsockopt(zmq.RCVHWM, 2000)
#socket.setsockopt_string(zmq.SUBSCRIBE, "S510050")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S159922")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S600900")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S600519")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S002520")

while True:
    content = socket.recv()
    now = datetime.now()
    pb_parse_s(content, f, now.hour * 10000000 + now.minute * 100000 + now.second * 1000 + now.microsecond // 1000)

