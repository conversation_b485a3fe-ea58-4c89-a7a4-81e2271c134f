// 股票基金现货 L1/L2 行情实时生成分钟数据，以极简形式通过 ZMQ 发布

#include <getopt.h>
#include <iostream>
#include <fstream>
#include <format>
#include <string>
#include <unordered_map>
#include <vector>
#include <list>
#include <unordered_set>
#include <zmq.h>
#include <chrono>
#include <ctime>
#include <errno.h>
#include <time.h>

#include "fkYAML/node.hpp"
#include "lyproto.quota.pb.h"

#include "ut_zmd.h" // 极简行情数据结构
#include "md1m_s.h"

constexpr char VER_MD1M_S[] = "1.0.0";
constexpr char DEFAULT_CONFIG_FILE[] = "md1m_s.yaml"; // 缺省配置文件名
constexpr int DEFAULT_OUTPUT_PORT_UT = 30007; // 缺省极简行情输出端口
constexpr char DEFAULT_SUBSCRIPTION[] = "S"; // 缺省订阅所有股票、基金行情
constexpr int DEFAULT_HWM = 6000; // 缺省 ZeroMQ socket 的 high water mark，包括输入与输出，值过低在遍历时会造成丢失分钟线数据
constexpr int MAX_NUM_SECURITIES = 20000; // 最大证券数量，会用它来确定 SecurityInfo unordered_map 容器的大小，以免发生 rehashing 造成迭代器失效

std::tm today; // 今天日期
int32_t g_exchtime_latest = 0; // 所有证券中最新的行情交易所时间戳, 形如 HHMMSSmmm

// 对于不活跃证券，要依赖遍历来生成分钟线数据
// 由于深交所行情交易所时间戳的粒度为 3 秒，所以在只依赖行情来驱动的情况下，如果只订阅了深交所行情，则必须选择 3 倍数秒为遍历时间点，否则永远也触发不了遍历
int32_t g_exchtime_12_latest = 0; // 每分钟第 12 秒遍历的最新时间戳，开如 HHMM12mmm
int32_t g_exchtime_42_latest = 0; // 每分钟第 42 秒遍历的最新时间戳，开如 HHMM42mmm

// 证券信息字典，key 为证券代码
std::unordered_map<std::string, SecurityInfo> g_security_info_dict;

int g_num_securities = 0;                             // 加入生成分钟线数据的证券数量，当此数量超过 MAX_NUM_SECURITIES 时，不再缓存行情消息，以免因为 rehashing 导致迭代器失效
std::unordered_set<std::string> g_ignored_securities; // 因超过 MAX_NUM_SECURITIES 而被忽略的证券

// 待发布分钟线信息的证券队列, 每分钟一个，每支证券都在某个队列中
std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> g_min_queues(240);

// 所有正常结束分钟线数据生成的证券累计数
int g_num_securities_finished = 0;

// 所有被忽略的证券累计数
int g_num_ignored_securities = 0;

// 生成的分钟线数据总数
int g_num_md1m = 0;

struct ProgramOptions
{
    std::string conf; // 配置 YAML 文件路径
    std::vector<MdSource> sources; // 行情源
    std::vector<std::string> subscriptions; // 订阅的行情主题，必须以字符 'S' 开头，因为指数编码与股票、基金编码有重合，指数编码以 'I' 开头
    int output_port_ut; // 输出极简行情的 ZeroMQ XSUB 服务器端口
    int hwm; // ZeroMQ socket 的 high water mark，包括输入与输出
    bool help; // 是否打印帮助信息后即退出
    bool background; // 是否在后台运行
};

static void PrintUsage(const char *progname)
{
    std::cout << std::format("现货行情分钟线，版本: {}", VER_MD1M_S) << std::endl;
    std::cout << std::format("Usage: {} [OPTIONS]", progname) << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << std::format("  -c, --conf <file>   Configuration file path (default: {})", DEFAULT_CONFIG_FILE) << std::endl;
    std::cout << "  -d, --day [YYYYMMDD|YYMMDD|MMDD|MDD|DD|D]  Date of the trading day (default: 0, use today)" << std::endl;
    std::cout << "  -b, --background    Run in background" << std::endl;
    std::cout << "  -h, --help          Display this help and exit" << std::endl;
}

static bool ParseCommandLine(int argc, char *argv[], ProgramOptions &options)
{
    static struct option long_options[] = {
        {"conf", required_argument, NULL, 'c'},
        {"background", no_argument, NULL, 'b'},
        {"help", no_argument, NULL, 'h'},
        {NULL, 0, NULL, 0}};

    int c;
    while ((c = getopt_long(argc, argv, "c:bh", long_options, NULL)) != -1)
    {
        switch (c)
        {
        case 'c':
            options.conf = optarg;
            break;
        case 'b':
            options.background = true;
            break;
        case 'h':
            options.help = true;
            break;
        default:
            return false;
        }
    }
    return true;
}

// Load configuration from YAML file
static bool LoadConfigFromYaml(const char *config_file, ProgramOptions *options)
{
    if (config_file == nullptr)
    {
        return false;
    }
    std::ifstream ifs(config_file);
    if (!ifs.good())
    {
        DEBUG_LOG(std::format("Error opening configuration file: {}", config_file));
        return false;
    }
    fkyaml::node cfg;
    try
    {
        cfg = fkyaml::node::deserialize(ifs);
    }
    catch (const fkyaml::exception &e)
    {
        DEBUG_LOG(std::format("!!!Config file parse error: {}", e.what()));
        return false;
    }

    try
    {
        const auto &config = cfg.as_map();
        for (const auto &c : config)
        {
            auto kt = c.first.get_type();
            if (kt == fkyaml::node_type::STRING)
            { // key must be a string
                auto k = c.first.get_value<std::string>();
                if (k == "sources")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            MdSource source;
                            for (const auto &sc : s.as_map())
                            {
                                auto sk = sc.first.get_value<std::string>();
                                if (sk == "name")
                                {
                                    source.name = sc.second.get_value<std::string>();
                                }
                                else if (sk == "host")
                                {
                                    source.host = sc.second.get_value<std::string>();
                                }
                                else if (sk == "port")
                                {
                                    source.port = sc.second.get_value<int>();
                                }
                                else
                                {
                                    DEBUG_LOG(std::format("忽略 sources 中的未知配置项: {}", sk));
                                }
                            }
                            options->sources.push_back(source);
                        }
                    }
                    else
                    {
                        DEBUG_LOG("配置文件错误: sources 不是一个 sequence");
                        return false;
                    }
                }
                else if (k == "subscriptions")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            options->subscriptions.push_back(s.get_value<std::string>());
                        }
                    }
                    else
                    {
                        DEBUG_LOG("配置文件错误: subscriptions 不是一个 sequence");
                        return false;
                    }
                }
                else if (k == "high_water_mark")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::INTEGER)
                    {
                        options->hwm = c.second.get_value<int>();
                    }
                    else
                    {
                        DEBUG_LOG("配置文件错误: high_water_mark 不是一个整数");
                        return false;
                    }
                }
                else if (k == "output")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::MAPPING)
                    {
                        for (const auto &o : c.second.as_map())
                        {
                            auto ok = o.first.get_value<std::string>();
                            if (ok == "port_ut")
                            {
                                options->output_port_ut = o.second.get_value<int>();
                            }
                            else
                            {
                                DEBUG_LOG(std::format("忽略 output 中的未知配置项: {}", ok));
                            }
                        }
                    }
                    else
                    {
                        DEBUG_LOG("配置文件错误: output 不是一个 map");
                        return false;
                    }
                }
                else if (k == "background")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::BOOLEAN)
                    {
                        options->background = c.second.get_value<bool>();
                    }
                    else
                    {
                        DEBUG_LOG("配置文件错误: background 不是一个布尔值");
                        return false;
                    }
                }
                else
                {
                    DEBUG_LOG(std::format("忽略未知配置项: {}", k));
                }
            }
        }
    }
    catch (const fkyaml::exception &e)
    {
        DEBUG_LOG(std::format("!!!Config file parse error: {}", e.what()));
        return false;
    }
    return true;
}

static bool GetProgramOptions(int argc, char *argv[], ProgramOptions &options)
{
    if (!ParseCommandLine(argc, argv, options))
    {
        PrintUsage(argv[0]);
        BAD_THING("解析命令行参数错误");
    }
    if (options.help)
    {
        PrintUsage(argv[0]);
        return false;
    }
    if (options.conf.empty())
    {
        options.conf = DEFAULT_CONFIG_FILE;
    }
    if (!LoadConfigFromYaml(options.conf.c_str(), &options))
    {
        BAD_THING("加载配置文件错误");
    }
    if (options.sources.empty())
    {
        BAD_THING("没有配置行情源");
    }
    if (options.subscriptions.empty())
    {
        options.subscriptions.push_back(DEFAULT_SUBSCRIPTION);
    }
    for (const auto &s : options.subscriptions)
    {
        if (s[0] != 'S')
        {
            BAD_THING(std::format("订阅的行情主题必须以字母S开头，但有主题 {} 没有以S开头", s));
        }
    }
    return true;
}

// 遍历待发布分钟线信息的证券队列，如已过发布时间，则强制发布，并将其转移到下一待发布分钟线证券队列的尾部
// 调用者应该在当前分钟合适的秒数时调用，要考虑可能的时延，如当前时间为 9:31:12，则应调用 do_queue(0)
// 以全局变量 g_exchtime_latest 作为时间参考点（如以当前时间参考点，则回放测试时会出现偏差）
// 如果遍历的是最后一个队列，即 min_idx 为 239 的情况，则将证券从队列删除后，还要从 g_security_info_dict
// 转移到 g_ignored_securities 中
// 参数：
//  min_idx: 待发布分钟线信息的证券队列的索引
//  output_socket: 用于发布分钟线数据的 ZeroMQ socket
static void do_queue(int32_t min_idx, void *output_socket)
{
    DEBUG_LOG(std::format("遍历: min_idx {}, g_exchtime_latest {}, 证券数 {}, 生成的分钟线数 {}, 非空队列长度:",
        min_idx, g_exchtime_latest, g_security_info_dict.size(), g_num_md1m));
    // 对当前所有待发布分钟线信息的证券队列，检查其长度，如果其长度大于 0，则显示之
    for (int i = 0; i < 240; i++)
    {
        if (!g_min_queues[i].empty())
        {
            DEBUG_LOG(std::format("  min_idx = {}, size = {}", i, g_min_queues[i].size()));
        }
    }
    if (min_idx == hhmmssmmm2min_idx(g_exchtime_latest))
    {
        std::tm tm = today;
        tm.tm_hour = g_exchtime_latest / 10000000;
        tm.tm_min = g_exchtime_latest / 100000 % 100;
        int64_t exchtime_pub = mktime(&tm); // 分钟线时间戳对应的 Unix 秒
        int32_t next_pub = hhmmssmmm_add_min_whole(min_idx2hhmmssmmm(min_idx), 1);
        if (next_pub == 113100000) // 11:30 后面是 13:01
        {
            next_pub = 130100000;
        }
        for (auto it = g_min_queues[min_idx].begin(); it != g_min_queues[min_idx].end(); )
        {
            auto it_next = std::next(it);
            auto &s = (*it)->second;
            zmd1m_t z{0};
            z.ty = 'S';
            memcpy(z.code, (*it)->first.c_str(), (*it)->first.size());
            z.exchtime_pub = exchtime_pub;
            if (s.open > 0.0) // 有成交
            {
                z.v = s.vol_latest - s.vol_start;
                z.m = s.money_latest - s.money_start;
                z.o = s.open;
                z.h = s.high;
                z.l = s.low;
                z.c = s.latest;
                if (z.v > 0)
                {
                    z.a = z.m / z.v;
                }
                else
                {
                    DEBUG_LOG(std::format("!!!从 Open={} 非 0 看有成交，但成交量为 0: 证券 {}, min_idx={}", s.open, (*it)->first, min_idx));
                    z.a = 0.0;
                }
            }
            else // 没有成交，各项价格（包括均价）都按最新价或前收价处理，而成交量/金额则为 0
            {
                z.v = 0;
                z.m = 0;
                z.c = s.latest > 0.00001 ? s.latest : s.pre_close;
                z.o = z.c;
                z.h = z.c;
                z.l = z.c;
                z.a = z.c;
            }
            z.opos = g_num_md1m++;
            zmq_send(output_socket, &z, sizeof(z), 0);
            if (min_idx < 239) // 要转移到下一待发布分钟线证券队列的尾部
            {
                s.open = -1.0;
                s.high = -1.0;
                s.low = -1.0;
                s.vol_start = s.vol_latest; // 设置累计量的基数
                s.money_start = s.money_latest;
                s.exchtime_pub = next_pub;
                s.it_list = g_min_queues[min_idx + 1].insert(g_min_queues[min_idx + 1].end(), *it);
            }
            else // 最后一条分钟线数据，从待发布分钟线证券队列中删除，同时从证券信息字典中删除且加入忽略列表
            {
                g_ignored_securities.insert((*it)->first);
                g_num_securities_finished++;
                g_security_info_dict.erase(*it);
            }
            g_min_queues[min_idx].erase(it);
            it = it_next;
        }
    }
}

// 生成证券分钟线数据
static void gen_md1m_s(void *input_socket, void *output_socket)
{
    char buffer[1024]; // 对于行情来说 1024 足够了
    int more = 0;      // 用于接收 ZMQ 消息时检查是否还有下一个 frame
    size_t more_size = sizeof(more);

    while (true)
    {
        auto msg_len = zmq_recv(input_socket, buffer, sizeof(buffer), 0);
        if (msg_len == -1)
        {
            DEBUG_LOG(std::format("!!!接收行情失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(msg_len) > sizeof(buffer))
        {
            DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 超过缓冲区大小 {}, 忽略", msg_len, sizeof(buffer)));
            continue;
        }
        more_size = sizeof(more);
        if (zmq_getsockopt(input_socket, ZMQ_RCVMORE, &more, &more_size) != 0)
        {
            DEBUG_LOG(std::format("!!!获取 ZeroMQ 行情 ZMQ_RCVMORE 标志失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (more != 0) // 应该只有一个 frame
        {
            DEBUG_LOG(std::format("!!!接收行情后，ZMQ_RCVMORE 标志非 0，忽略"));
            continue;
        }
        if (msg_len < 16)
        {
            DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 小于 16， 忽略", msg_len));
            continue;
        }
        LYPROTO::QUOTA::MarketData o;
        if (!o.ParseFromArray(buffer + 15, msg_len - 15))
        {
            DEBUG_LOG(std::format("!!!解析行情失败 {}，忽略，msg_len = {}", std::string_view(buffer, 15), msg_len));
            continue;
        }
        std::string stkid = o.stkid();
        int32_t exchtime;
        try
        {
            exchtime = std::stoi(o.exchtime());
        }
        catch (const std::exception &e)
        {
            DEBUG_LOG(std::format("!!!Invalid exchtime: {} from {}, security: {}", o.exchtime(), o.mdsource(), stkid));
            continue;
        }
        if (exchtime > g_exchtime_latest)
        {
            g_exchtime_latest = exchtime;
        }
        ExchID exch_id = exch_id_from_string(o.exchid()); // 本条消息的交易所标识
        if (g_ignored_securities.contains(stkid)) // 被忽略的证券，直接忽略
        {
            continue;
        }
        if (exch_id != ExchID::SH && exch_id != ExchID::SZ) // 不是沪深交易所的证券，加入忽略表中
        {
            auto ignored = g_ignored_securities.insert(stkid); // 加入忽略列表
            if (ignored.second) // 插入成功
            {
                DEBUG_LOG(std::format("!!!Not SH/SZ security, ignore {} exchid={}\n{}", stkid, o.exchid(), o.DebugString()));
            }
            continue;
        }
        auto it = g_security_info_dict.find(stkid);
        if (it == g_security_info_dict.end()) // 此证券还未收录
        {
            if (g_num_securities == 0)
            {
                DEBUG_LOG(std::format("!!!First security: {}, 行情时间 {}, 全局行情时间 {}", stkid, exchtime, g_exchtime_latest));
            }
            if (g_num_securities >= MAX_NUM_SECURITIES) // 已经达到最大证券数量，不再收录新的证券
            {
                auto ignored = g_ignored_securities.insert(stkid); // 加入忽略列表
                if (ignored.second) // 插入成功
                {
                    g_num_ignored_securities++;
                    DEBUG_LOG(std::format("!!!Too many securities, ignore {} exchid={}\n{}", stkid, o.exchid(), o.DebugString()));
                }
                continue;
            }
            int32_t exchtime_pub; // 将要发布的分钟行情时间戳
            // int32_t exchtime_start; // 本分钟行情的起始时间戳（其数据作为累计值的初始值）
            if (exchtime / 1000 < 93000) // 收到连续竞价开始前的行情消息（可能是集合竞价过程中成交价未定，或集合竞价结束后成交价已公布），我们可以获得全天完整的分钟线数据
            {
                exchtime_pub = 93100000;
                // 第一条分钟线数据的获取比较特殊，我们用 vol_latest 和 money_latest 来记录最新的行情，不用在乎 vol_start 和 money_start
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, o.open() / 10000.0, o.high() / 10000.0, o.high() / 10000.0,
                    o.low() / 10000.0, o.low() / 10000.0, o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[0].insert(g_min_queues[0].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            if (exchtime / 1000 >= 93000 && exchtime / 1000 < 93057) // 连续竞价开始后，但还没有到第一条分钟线数据点
            {
                if (o.volume() == 0) // 当天到此时还没有成交，我们没有漏掉获得当天第一笔成交价的机会，可以得到全天完全的分钟线数据
                {
                    exchtime_pub = 93100000;
                    // 实际中累计成交量是 0 的时候，行情中的 open/high/low/close 值也都是 0，这里我们还是取一下行情中的值
                    it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, o.open() / 10000.0, o.high() / 10000.0, o.high() / 10000.0,
                        o.low() / 10000.0, o.low() / 10000.0, o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                    it->second.it_list = g_min_queues[0].insert(g_min_queues[0].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                    g_num_securities++;
                    continue;
                }
                // 此时已经有成交了，我们错过了前面的成交，所以只能获取到第二条 9:32（ XXX 这里我们忽略掉成交笔数为 1 的特殊情况，有空再补上）的数据
                // XXX 2026.1.28 注释：这里似乎我们不用在乎必须看到第一笔成交来确定 Open，因为行情中有 Open 的数据
                exchtime_pub = 93200000;
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0,
                    // vol_start 和 money_start 正常情况下要到分钟的最后 3 秒行情才确定，所以这里设置为 -1
                    o.latest() / 10000.0, -1, o.volume(), -1.0, double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[1].insert(g_min_queues[1].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            if (exchtime / 1000 >= 93057 && exchtime / 1000 < 93100) // 连续竞价第一分钟的末尾 3 秒，正好是当天第一个分钟线数据点 9:30:57/58/59
            {
                if (o.volume() == 0) // 当天到此时还没有成交，以前收价为第一条分钟线数据的各价格
                {
                    zmd1m_t z{0};
                    z.ty = 'S';
                    memcpy(z.code, stkid.c_str(), stkid.size());
                    std::tm tm = today;
                    tm.tm_hour = 9;
                    tm.tm_min = 31;
                    z.exchtime_pub = mktime(&tm); // 第一条分钟线时间戳是 9:31 对应的 Unix 秒
                    z.v = 0;
                    z.o = o.preclose() / 10000.0;
                    z.h = o.preclose() / 10000.0;
                    z.l = o.preclose() / 10000.0;
                    z.c = o.preclose() / 10000.0;
                    z.m = 0.0;
                    z.a = 0.0;
                    z.opos = g_num_md1m++;
                    zmq_send(output_socket, &z, sizeof(z), 0);
                }
                // 如果当天已经有了成交，我们缺少之前的集合竞价成交价或第一笔连续竞价成交价，所以不能生成完整的第一条分钟线数据，只能从第二条开始
                // XXX 2026.1.28 注释：这里似乎我们不用在乎必须看到第一笔成交来确定 Open，因为行情中有 Open 的数据
                exchtime_pub = 93200000;
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要下一分钟前 3 秒才开始计
                    // 确定了做为基数的 vol_start 和 money_start
                    o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[1].insert(g_min_queues[1].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            if (exchtime / 1000 >= 93100 && exchtime / 1000 < 112900) // 早盘 9:31:00 到 11:28:59（含）之间，可以计算 2 分钟后的分钟线数据
            {
                exchtime_pub = hhmmssmmm_add_min_whole(exchtime, 2); // 将要发布的分钟行情时间戳
                if ((exchtime % 100000) / 1000 >= 57) // 分钟的最后 3 秒的行情，可以确定得到 vol_start 和 money_start
                {
                    it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要下一分钟前 3 秒才开始计
                        // 确定了做为基数的 vol_start 和 money_start
                        o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                    it->second.it_list = g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].insert(g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                    g_num_securities++;
                    continue;
                }
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要下一分钟前 3 秒才开始计
                    // vol_start 和 money_start 要分钟的最后 3 秒才开始计
                    o.latest() / 10000.0, -1, o.volume(), -1.0, double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].insert(g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            if (exchtime / 1000 >= 112900 && exchtime / 1000 < 130000) // 11:29:00 到 13:00:00 之间，可以计算午盘第一分钟的分钟线数据
            {
                exchtime_pub = 130100000;
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要到 13:00 的前 3 秒才开始计
                    // 中午休盘时的行情都是重复
                    o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[120].insert(g_min_queues[120].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            if (exchtime / 1000 >= 130000 && exchtime / 1000 < 145900) // 午盘 13:00:00 到 14:58:59（含）之间，可以计算 2 分钟后的分钟线数据
            {
                exchtime_pub = hhmmssmmm_add_min_whole(exchtime, 2); // 将要发布的分钟行情时间戳
                if ((exchtime % 100000) / 1000 >= 57) // 分钟的最后 3 秒的行情，可以确定得到 vol_start 和 money_start
                {
                    it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要下一分钟前 3 秒才开始计
                        // 确定了做为基数的 vol_start 和 money_start
                        o.latest() / 10000.0, o.volume(), o.volume(), double(o.value()), double(o.value()), exchtime_pub, exchtime, 0}).first;
                    it->second.it_list = g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].insert(g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                    g_num_securities++;
                    continue;
                }
                it = g_security_info_dict.emplace(stkid, SecurityInfo{exch_id, o.preclose() / 10000.0, -1.0, -1.0, o.high() / 10000.0, -1.0, o.low() / 10000.0, // open/high/low 要下一分钟前 3 秒才开始计
                    // vol_start 和 money_start 要分钟的最后 3 秒才开始计
                    o.latest() / 10000.0, -1, o.volume(), -1.0, double(o.value()), exchtime_pub, exchtime, 0}).first;
                it->second.it_list = g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].insert(g_min_queues[hhmmssmmm2min_idx(exchtime_pub)].end(), it); // 追加到待发布分钟线信息的证券队列的尾部
                g_num_securities++;
                continue;
            }
            // 数据不足以计算任何一条分钟线数据，忽略之
            continue;
        }
        // 已收录证券的处理
        if (it->second.exchtime_pub == 93100000) // 全天第一条分钟线数据，需要特殊处理，它实际上包含了集合竞价及连续竞价一直到 9:31:00 之前的数据
        {
            // 对于上交所证券，有可能在 57 秒、59 秒都收到行情，所以在 09:57 的时候还不能确定是否为第一条分钟线数据点
            if ((exchtime / 1000 < 93057) || (exchtime / 1000 == 93057 && exch_id == ExchID::SH)) // 连续竞价第一分钟的末尾前收到的行情，还没有到第一条分钟线数据点
            {
                if (o.volume() > 0) // 已经有成交了，此时我们可以依赖行情的 Open/High/Low 来帮我们计算第一条分钟线数据值
                {
                    it->second.open = o.open() / 10000.0;
                    it->second.high = o.high() / 10000.0;
                    it->second.low = o.low() / 10000.0;
                } // 否则还是保持 open/high/low 为 -1.0
                it->second.latest = o.latest() / 10000.0;
                it->second.high_latest = o.high() / 10000.0;
                it->second.low_latest = o.low() / 10000.0;
                it->second.vol_latest = o.volume();
                it->second.money_latest = double(o.value());
                it->second.vol_start = -1;
                it->second.money_start = -1.0;
                it->second.exchtime_latest = exchtime;
                continue;
            }
            if (exchtime / 1000 >= 93057 && exchtime / 1000 < 93100) // 连续竞价第一分钟的末尾，正好是第一条分钟线数据点 9:57/58/59
            {
                zmd1m_t z{0};
                z.ty = 'S';
                memcpy(z.code, stkid.c_str(), stkid.size());
                std::tm tm = today;
                tm.tm_hour = 9;
                tm.tm_min = 31;
                z.exchtime_pub = mktime(&tm); // 第一条分钟线时间戳是 9:31 对应的 Unix 秒
                if (o.volume() == 0) // 当天到此时还没有成交，以前收价为第一条分钟线数据的各价格
                {
                    z.v = 0;
                    z.o = o.preclose() / 10000.0;
                    z.h = z.o;
                    z.l = z.o;
                    z.c = z.o;
                    z.m = 0.0;
                    z.a = 0.0;
                }
                else // 已经有成交了，以本条行情的数据作为第一条分钟线数据
                {
                    z.v = o.volume();
                    z.o = o.open() / 10000.0;
                    z.h = o.high() / 10000.0;
                    z.l = o.low() / 10000.0;
                    z.c = o.latest() / 10000.0;
                    z.m = double(o.value());
                    z.a = z.m / z.v;
                }
                z.opos = g_num_md1m++;
                zmq_send(output_socket, &z, sizeof(z), 0);
                it->second.open = -1.0;
                it->second.high = -1.0;
                it->second.low = -1.0;
                it->second.latest = o.latest() / 10000.0;
                it->second.high_latest = o.high() / 10000.0;
                it->second.low_latest = o.low() / 10000.0;
                it->second.vol_start = o.volume(); // 下一条分钟线数据中的成交量计算的基数
                it->second.vol_latest = it->second.vol_start;
                it->second.money_start = double(o.value()); // 下一条分钟线数据中的成交金额计算的基数
                it->second.money_latest = it->second.money_start;
                it->second.exchtime_latest = exchtime;
                it->second.exchtime_pub = 93200000;
                g_min_queues[0].erase(it->second.it_list); // 转移到下一分钟发布线队列尾部
                it->second.it_list = g_min_queues[1].insert(g_min_queues[1].end(), it);
                continue;
            }
            // 成交不活跃，此行情是 9:31:00 之后的首条行情，之前没有收到连续竞价第一分钟最后 3 秒的行情数据，要以其之前的数据进行填充
            zmd1m_t z{0};
            z.ty = 'S';
            memcpy(z.code, stkid.c_str(), stkid.size());
            std::tm tm = today;
            tm.tm_hour = 9;
            tm.tm_min = 31;
            z.exchtime_pub = mktime(&tm); // 第一条分钟线时间戳是 9:31 对应的 Unix 秒
            if (it->second.vol_latest == 0) // 此前最后一条行情还没有成交，用前收价作为第一条分钟线的各价格
            {
                z.v = 0;
                z.o = it->second.pre_close;
                z.h = z.o;
                z.l = z.o;
                z.c = z.o;
                z.m = 0.0;
                z.a = z.o;
            }
            else // 此前最后一条行情已经成交，用该成交数据作为第一条分钟线的各价格
            {
                z.v = it->second.vol_latest;
                z.o = it->second.open;
                z.h = it->second.high;
                z.l = it->second.low;
                z.c = it->second.latest;
                z.m = it->second.money_latest;
                z.a = z.m / z.v;
            }
            z.opos = g_num_md1m++;
            zmq_send(output_socket, &z, sizeof(z), 0);
            // 我们在这里假设本次行情不会超过 9:31:57，至少对于上交所 L2 行情保证每半分钟有一次行情，而且遍历是从 9:31:12 开始的
            if (exchtime / 1000 >= 93157)
            {
                DEBUG_LOG(std::format("!!!晚到 exchtime {} >= 93157, 93100 分钟线还未出, 最后行情时间 {}, 间隔 {} 秒, 证券 = {}", exchtime / 1000,
                    it->second.exchtime_latest / 1000, hhmmssmmm_diff(exchtime, it->second.exchtime_latest) / 1000, o.stkid()));
            }
            if (o.volume() > it->second.vol_latest) // 本次行情相对于第一分钟最后行情有新成交，则把本次行情作为新的分钟线的起始点
            {
                it->second.open = o.latest() / 10000.0; // 本次行情已经是新的分钟线的起始点了
                if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                {
                    it->second.high = o.high() / 10000.0;
                }
                else
                {
                    it->second.high = it->second.open;
                }
                if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                {
                    it->second.low = o.low() / 10000.0;
                }
                else
                {
                    it->second.low = it->second.open;
                }
                it->second.vol_latest = o.volume();
                it->second.money_latest = double(o.value());
                it->second.latest = it->second.open;
                it->second.exchtime_start = exchtime;
            }
            else // 本分钟（9:32 分钟线）行情还没有成交
            {
                it->second.open = -1.0; // 未确定状态
                it->second.high = -1.0;
                it->second.low = -1.0;
                it->second.latest = o.latest() / 10000.0;
            }
            it->second.vol_start = it->second.vol_latest; // 以之前最后一次的数据作为下一条分钟线数据中的成交量计算的基数
            it->second.money_start = it->second.money_latest; // 以之前最后一次的数据作为下一条分钟线数据中的成交量计算的基数
            it->second.high_latest = o.high() / 10000.0;
            it->second.low_latest = o.low() / 10000.0;
            it->second.exchtime_latest = exchtime;
            it->second.exchtime_pub = 93200000;
            g_min_queues[0].erase(it->second.it_list); // 转移到下一分钟发布线队列尾部
            it->second.it_list = g_min_queues[1].insert(g_min_queues[1].end(), it);
            continue;
        }

        // 非第一条分钟线数据的处理，其与 93100000 分钟线数据的处理逻辑不同点主要在于：1. 成交量、金额由行情值减去基数值获得；2. open/high/low/close 不能从行情中直接获取
        int32_t min_off = hhmmssmmm2min(it->second.exchtime_pub) - hhmmssmmm2min(exchtime); // 行情分钟数距离发布时间分钟数的差值

        // 应该是午盘休息时间，只需要更新数据即可
        if (min_off > 2)
        {
            it->second.latest = o.latest() / 10000.0;
            it->second.high_latest = o.high() / 10000.0;
            it->second.low_latest = o.low() / 10000.0;
            it->second.vol_latest = o.volume();
            it->second.money_latest = double(o.value());
            it->second.exchtime_latest = exchtime;
            continue;
        }

        // 是否应该进行遍历，由当前的 g_exchtime_latest 的秒数决定
        bool should_iter_12 = false;
        bool should_iter_42 = false;
        // 在 9:31 到 11:30 之间，以及 13:01 到 15:00 之间进行遍历
        if ((g_exchtime_latest > 93100000 && g_exchtime_latest < 113100000) || (g_exchtime_latest > 130100000 && g_exchtime_latest < 150100000))
        {
            // 秒数为 12，每分钟只遍历一次，遍历之后更新 g_exchtime_12_latest
            if (((g_exchtime_latest % 100000) / 1000) == 12 && g_exchtime_latest / 100000 > g_exchtime_12_latest / 100000)
            {
                should_iter_12 = true;
            }
            // 秒数为 42，每分钟只遍历一次，遍历之后更新 g_exchtime_42_latest
            else if ((((g_exchtime_latest % 100000) / 1000) == 42) && (g_exchtime_latest / 100000 > g_exchtime_42_latest / 100000))
            {
                should_iter_42 = true;
            }
        }

        // 由于程序启动晚造成的特殊情况
        if (min_off == 2)
        {
            // 对于上交所证券，有可能在 57 秒、59 秒都收到行情，所以在 57 秒的时候还不能确定它是否是分钟线的终点从而确定 vol_start 和 money_start
            // 所以对于上交所证券，在 57 秒收到行情时，大多数只能在下一分钟的开头才能确定 vol_start 和 money_start，少数会在 59 秒再收到一次行情就可直接确定了
            if (((exchtime % 100000) / 1000 < 57) || ((exchtime % 100000) / 1000 == 57 && exch_id == ExchID::SH)) // 还没到分钟的最后 3 秒，还不能确定 vol_start 和 money_start，只需要更新其最新值
            {
                it->second.latest = o.latest() / 10000.0;
                it->second.high_latest = o.high() / 10000.0;
                it->second.low_latest = o.low() / 10000.0;
                it->second.vol_latest = o.volume();
                it->second.money_latest = double(o.value());
                it->second.exchtime_latest = exchtime;
                if (should_iter_12 || should_iter_42)
                {
                    do_queue(hhmmssmmm2min_idx(g_exchtime_latest), output_socket);
                    if (should_iter_12)
                    {
                        g_exchtime_12_latest = g_exchtime_latest;
                    }
                    else
                    {
                        g_exchtime_42_latest = g_exchtime_latest;
                    }
                    // 这里应该不需要判断是否所有证券都已经生成完毕，因为此时 min_off == 2，应该还有证券没有生成完毕
                }
                continue;
            }
            // 分钟的最后 3 秒的行情，可以确定得到 vol_start 和 money_start
            it->second.latest = o.latest() / 10000.0;
            it->second.high_latest = o.high() / 10000.0;
            it->second.low_latest = o.low() / 10000.0;
            it->second.vol_start = o.volume(); // 分钟线数据累计量基数
            it->second.vol_latest = it->second.vol_start;
            it->second.money_start = double(o.value()); // 分钟线数据累计量基数
            it->second.money_latest = it->second.money_start;
            it->second.exchtime_latest = exchtime;
            continue;
        }

        // 分钟内数据更新及分钟末发布
        if (min_off == 1) // 已经进入计算分钟线数据的阶段
        {
            if (it->second.vol_start == -1) // 基数还没被设置上，用之前的最新值填充
            {
                // 正常情况下进到这里来的原因：
                //   1. 程序启动较晚，还没发布过行情，vol_start 初始化为 -1，同时该证券成交不活跃，在 (exchtime_pub - 2) 分钟的最后 3 秒无行情，
                //      导致 vol_start 和 money_start 没有被设置上：
                //          it->second.exchtime_latest < hhmmssmmm_add_min_whole(it->second.exchtime_pub, -2) + 57000
                //   2. 程序启动较晚，还没发布过行情，vol_start 初始化为 -1，上交所证券，在 57 秒收到了行情，但有可能 59 秒也会收到行情，所以当时
                //      还不能确定是否为前一条分钟线的最后一个数据点，但实际 59 秒没有收到行情，导致 vol_start 和 money_start 没有被设置上
                //          (exch_id == ExchID::SH && (it->second.exchtime_latest % 100000) / 1000 == 57)
                if (!(it->second.exchtime_latest < hhmmssmmm_add_min_whole(it->second.exchtime_pub, -2) + 57000 ||
                    (exch_id == ExchID::SH && (it->second.exchtime_latest % 100000) / 1000 == 57)))
                {
                    DEBUG_LOG(std::format("!!! 异常：vol_start 还没设置，只能用之前的最新值填充: 证券 {}, exchtime {}, 上次行情时间 {}, 下次发布时间 {} min_idx {}",
                        it->first, exchtime, it->second.exchtime_latest, it->second.exchtime_pub, hhmmssmmm2min_idx(it->second.exchtime_pub)));
                }
                it->second.vol_start = it->second.vol_latest;
                it->second.money_start = it->second.money_latest;
            }
            it->second.exchtime_latest = exchtime;
            if ((exchtime % 100000) / 1000 >= 57) // 分钟的最后 3 秒
            {
                // 对于非 11:30 及 15:00 且非上交所证券收到 57 秒的情况，可以计算出分钟线数据了
                if (it->second.exchtime_pub != 113000000 && it->second.exchtime_pub != 150000000 && ! (exch_id == ExchID::SH && (exchtime % 100000) / 1000 == 57))
                {
                    zmd1m_t z{0};
                    z.ty = 'S';
                    memcpy(z.code, stkid.c_str(), stkid.size());
                    std::tm tm = today;
                    tm.tm_hour = it->second.exchtime_pub / 10000000;
                    tm.tm_min = it->second.exchtime_pub / 100000 % 100;
                    z.exchtime_pub = mktime(&tm);  // 分钟线时间戳对应的 Unix 秒
                    z.v = o.volume() - it->second.vol_start;
                    z.m = o.value() - it->second.money_start;
                    if (z.v > 0) // 至少本次行情相对于基数有成交
                    {
                        if (it->second.open < 0.0) // 之前一直没有成交行情，导致 open 没有被设置上，意味着本次行情是本分钟线的首次同时也是最后一次有成交行情
                        {
                            z.o = o.latest() / 10000.0;
                            if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                            {
                                z.h = o.high() / 10000.0;
                            }
                            else
                            {
                                z.h = z.o;
                            }
                            if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                            {
                                z.l = o.low() / 10000.0;
                            }
                            else
                            {
                                z.l = z.o;
                            }
                            z.c = z.o;
                        }
                        else // 此前已经有本分钟线的成交行情
                        {
                            z.o = it->second.open;
                            if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                            {
                                z.h = o.high() / 10000.0;
                            }
                            else
                            {
                                z.h = std::max(it->second.high, o.latest() / 10000.0);
                            }
                            if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                            {
                                z.l = o.low() / 10000.0;
                            }
                            else
                            {
                                z.l = std::min(it->second.low, o.latest() / 10000.0);
                            }
                            z.c = o.latest() / 10000.0;
                        }
                        z.a = z.m / z.v;
                    }
                    else // 本分钟线没有成交
                    {
                        double p = (it->second.latest > -0.000001 && it->second.latest < 0.000001) ? it->second.pre_close : it->second.latest;
                        z.a = p;
                        z.o = p;
                        z.h = p;
                        z.l = p;
                        z.c = p;
                        z.v = 0;
                        z.m = 0.0;
                    }
                    z.opos = g_num_md1m++;
                    zmq_send(output_socket, &z, sizeof(z), 0);
                    // 准备下次分钟线发布
                    int32_t idx = hhmmssmmm2min_idx(it->second.exchtime_pub); // 当前所在的分钟线队列的索引
                    it->second.exchtime_pub = hhmmssmmm_add_min_whole(it->second.exchtime_pub, 1); // 非早盘或午盘的最后一条分钟线，所以加一分钟没有问题
                    it->second.open = -1.0;
                    it->second.high = -1.0;
                    it->second.high_latest = o.high() / 10000.0;
                    it->second.low = -1.0;
                    it->second.low_latest = o.low() / 10000.0;
                    it->second.latest = o.latest() / 10000.0;
                    it->second.vol_start = o.volume(); // 下一条分钟线数据中的成交量计算的基数
                    it->second.vol_latest = it->second.vol_start;
                    it->second.money_start = double(o.value()); // 下一条分钟线数据中的成交金额计算的基数
                    it->second.money_latest = it->second.money_start;
                    // 从当前分钟线队列中删除，并加到下一分钟线队列的尾部
                    g_min_queues[idx].erase(it->second.it_list);
                    it->second.it_list = g_min_queues[idx + 1].insert(g_min_queues[idx + 1].end(), it); // 放回队列尾部
                    continue; // 最后 3 秒，不用考虑遍历问题
                }

                // 早盘或午盘的最后 3 秒的行情，只需要更新数据即可，要等收到 11:30 后或 15:00 后的行情之后才能发布分钟线数据
                // 或者上交所 57 秒收到的行情，只能先更新数据，等待 59 秒的行情（少数情况）或下一秒的 00 秒行情来确定是否为分钟线的最后一个数据点
                if (o.volume() > it->second.vol_latest) // 本次行情相对于最近一次行情有成交，更新 high/low
                {
                    it->second.latest = o.latest() / 10000.0;
                    if (it->second.open < 0.0) // 之前一直没有成交行情，导致 open 没有被设置上，意味着本次行情是本分钟线的首次有成交行情，设置 open
                    {
                        it->second.open = it->second.latest; // 用本次行情来设置 open
                        if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                        {
                            it->second.high = o.high() / 10000.0;
                        }
                        else
                        {
                            it->second.high = it->second.open;
                        }
                        if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                        {
                            it->second.low = o.low() / 10000.0;
                        }
                        else
                        {
                            it->second.low = it->second.open;
                        }
                    }
                    else // 此前已经有本分钟线的成交行情
                    {
                        if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                        {
                            it->second.high = o.high() / 10000.0;
                        }
                        else
                        {
                            it->second.high = std::max(it->second.high, it->second.latest);
                        }
                        if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                        {
                            it->second.low = o.low() / 10000.0;
                        }
                        else
                        {
                            it->second.low = std::min(it->second.low, it->second.latest);
                        }
                    }
                    it->second.high_latest = o.high() / 10000.0;
                    it->second.low_latest = o.low() / 10000.0;
                    it->second.vol_latest = o.volume();
                    it->second.money_latest = double(o.value());
                }
                // 没有新的成交，无需更新
                continue; // 最后 3 秒，不用考虑遍历问题
            }

            // 这里开始是秒数小于 57 的情况

            if (o.volume() > it->second.vol_latest) // 本次行情相对于最近一次行情有新的成交
            {
                // 分钟线数据起点，这里我们要检查 open 确实还没定，因为对于上交所证券，有可能在 0 秒、2 秒都收到行情，我们不能把
                // 在 0 秒收到行情确定的 open 给冲掉了
                if ((exchtime % 100000) / 1000 < 3 && it->second.open < 0.0)
                {
                    it->second.open = o.latest() / 10000.0;
                    if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                    {
                        it->second.high = o.high() / 10000.0;
                    }
                    else
                    {
                        it->second.high = it->second.open;
                    }
                    if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                    {
                        it->second.low = o.low() / 10000.0;
                    }
                    else
                    {
                        it->second.low = it->second.open;
                    }
                    it->second.high_latest = o.high() / 10000.0;
                    it->second.low_latest = o.low() / 10000.0;
                    it->second.latest = it->second.open;
                    it->second.vol_latest = o.volume();
                    it->second.money_latest = double(o.value());
                    continue;
                }

                // 秒数为 3 到 56 之间，或者上交所证券已经在 0 秒确定 open 而当前收到行情的秒数为 2
                if (it->second.open < 0.0) // 之前一直没有成交行情，导致 open 没有被设置上，此次行情是首次有成交行情，设置 open
                {
                    it->second.open = o.latest() / 10000.0;
                    if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                    {
                        it->second.high = o.high() / 10000.0;
                    }
                    else
                    {
                        it->second.high = it->second.open;
                    }
                    if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                    {
                        it->second.low = o.low() / 10000.0;
                    }
                    else
                    {
                        it->second.low = it->second.open;
                    }
                }
                else // 此前已经有本分钟行情数据了，更新 high/low
                {
                    if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                    {
                        it->second.high = o.high() / 10000.0;
                    }
                    else
                    {
                        it->second.high = std::max(it->second.high, o.latest() / 10000.0);
                    }
                    if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                    {
                        it->second.low = o.low() / 10000.0;
                    }
                    else
                    {
                        it->second.low = std::min(it->second.low, o.latest() / 10000.0);
                    }
                }
                it->second.latest = o.latest() / 10000.0;
                it->second.vol_latest = o.volume();
                it->second.money_latest = double(o.value());
                it->second.high_latest = o.high() / 10000.0;
                it->second.low_latest = o.low() / 10000.0;
            } // 没有新的成交则只需更新 exchtime_latest（前面已经更新）

            if (should_iter_12 || should_iter_42)
            {
                do_queue(hhmmssmmm2min_idx(g_exchtime_latest), output_socket);
                if (should_iter_12)
                {
                    g_exchtime_12_latest = g_exchtime_latest;
                }
                else
                {
                    g_exchtime_42_latest = g_exchtime_latest;
                }
                // 如果当前 g_exchtime_latest 已经到了 15:00:12，而本次延迟的行情的时间戳在 14:59:03 到 15:00:00（不含）之间，则本次遍历可能会把所有股票都清除掉
                // 所以我们要检查一下是否已经没有股票了
                if (g_security_info_dict.empty())
                {
                    DEBUG_LOG(std::format("遍历后，所有分钟线数据生成完毕，共处理 {} 个证券，其中 {} 个证券获得最后一条数据，另有 {} 个被忽略", g_num_securities, g_num_securities_finished, g_num_ignored_securities));
                    return;
                }
            }
            continue;
        }

        // 1. 早盘最后一条分钟线的行情：进行发布，且放入 13:01 的待发布队列
        // 2. 午盘最后一条分钟线的行情：进行发布，且放入忽略列表，检查是否所有证券都已经生成完毕
        // 3. 至少缺失分钟线最后 3 秒至此时的行情，导致时间来到了发布分钟，进行发布，且放入下一分钟的待发布队列
        if (min_off == 0)
        {
            // XXX 这里我们先不考虑一直到此时还没有确定累计量基数，或者还没收到一次本分钟行情的情况
            zmd1m_t z{0};
            z.ty = 'S';
            memcpy(z.code, stkid.c_str(), stkid.size());
            std::tm tm = today;
            tm.tm_hour = it->second.exchtime_pub / 10000000;
            tm.tm_min = it->second.exchtime_pub / 100000 % 100;
            z.exchtime_pub = mktime(&tm);  // 分钟线时间戳对应的 Unix 秒

            // 对于深交所停牌证券要进行特殊处理，因为其行情的 close 会一直保持为 0.0
            if (it->second.exchtime_pub == 150000000 && it->second.exch_id == ExchID::SZ && o.status() == "8")
            {
                z.c = o.preclose() / 10000.0;
                z.o = z.c;
                z.h = z.c;
                z.l = z.c;
                z.a = z.c;
                z.v = 0;
                z.m = 0.0;
                z.opos = g_num_md1m++;
                zmq_send(output_socket, &z, sizeof(z), 0);
                // 放入忽略列表，并从待发布分钟线信息的证券队列中删除
                g_min_queues[239].erase(it->second.it_list);
                g_num_securities_finished++;
                g_ignored_securities.insert(stkid);
                g_security_info_dict.erase(it);
                if (g_security_info_dict.empty())
                {
                    DEBUG_LOG(std::format("深交所停牌证券处理后，所有证券处理完毕，共处理 {} 个证券，其中 {} 个证券获得最后一条数据，另有 {} 个被忽略，共生成 {} 条分钟线数据",
                        g_num_securities, g_num_securities_finished, g_num_ignored_securities, g_num_md1m));
                    return;
                }
                continue;
            }

            if (it->second.exchtime_pub == 150000000) // 我们需要等到收盘价出现才能发布 15:00 的分钟线数据
            {
                if (o.close() <= 0) // 收盘价未出现，不能发布 15:00 的分钟线数据
                {
                    DEBUG_LOG(std::format("!!! 收盘价未出现，还不能发布 15:00 的分钟线数据: 证券 {}, exchtime {}", stkid, exchtime));
                    it->second.latest = o.latest() / 10000.0;
                    it->second.exchtime_latest = exchtime;
                    continue;
                }
            }
            if (it->second.exchtime_pub == 113000000 || it->second.exchtime_pub == 150000000) // 如果本次发布时间是 11:30 或 15:00，则要进行发布
            {
                // 此行情是本分钟线的最后一个数据点
                z.v = o.volume() - it->second.vol_start;
                z.m = o.value() - it->second.money_start;
                if (z.v > 0) // 该分钟是有成交的
                {
                    z.a = z.m / z.v;
                    if (it->second.open < 0.0) // 之前一直没有成交行情，导致 open 没有被设置上，意味着本次行情是本分钟线的首次有成交行情，设置 open
                    {                          // 此种情况在下午收盘时是常态（除了上交所 ETF 没有收盘集合竞价阶段外），早盘收盘时也会有少量出现
                        z.o = o.latest() / 10000.0;
                        if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                        {
                            z.h = o.high() / 10000.0;
                        }
                        else
                        {
                            z.h = z.o;
                        }
                        if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                        {
                            z.l = o.low() / 10000.0;
                        }
                        else
                        {
                            z.l = z.o;
                        }
                        if (it->second.exchtime_pub == 150000000) // 15:00 的分钟线数据，收盘价已出现，用收盘价作为 close
                        {
                            z.c = o.close() / 10000.0;
                        }
                        else
                        {
                            z.c = z.o;
                        }
                    }
                    else // 此前已经有本分钟线的成交行情
                    {
                        z.o = it->second.open;
                        if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                        {
                            z.h = o.high() / 10000.0;
                        }
                        else
                        {
                            z.h = std::max(it->second.high, o.latest() / 10000.0);
                        }
                        if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                        {
                            z.l = o.low() / 10000.0;
                        }
                        else
                        {
                            z.l = std::min(it->second.low, o.latest() / 10000.0);
                        }
                        if (it->second.exchtime_pub == 150000000) // 15:00 的分钟线数据，收盘价已出现，用收盘价作为 close
                        {
                            z.c = o.close() / 10000.0;
                        }
                        else
                        {
                            z.c = o.latest() / 10000.0;
                        }
                    }
                }
                else // 本分钟线没有成交
                {
                    double p = (o.latest() > -0.000001 && o.latest() < 0.000001) ? it->second.pre_close : o.latest() / 10000.0;
                    z.a = p;
                    z.o = p;
                    z.h = p;
                    z.l = p;
                    if (it->second.exchtime_pub == 150000000) // 15:00 的分钟线数据，收盘价已出现，用收盘价作为 close
                    {
                        z.c = o.close() / 10000.0;
                    }
                    else
                    {
                        z.c = p;
                    }
                    z.v = 0;
                    z.m = 0.0;
                }
                z.opos = g_num_md1m++;
                zmq_send(output_socket, &z, sizeof(z), 0);
                if (it->second.exchtime_pub == 113000000) // 如果本次发布时间是 11:30，即早盘最后一次分钟线
                {
                    it->second.exchtime_pub = 130100000;
                    it->second.vol_start = o.volume();
                    it->second.money_start = double(o.value());
                    it->second.open = -1.0;
                    it->second.high = -1.0;
                    it->second.low = -1.0;
                    it->second.latest = o.latest() / 10000.0;
                    it->second.high_latest = o.high() / 10000.0;
                    it->second.low_latest = o.low() / 10000.0;
                    it->second.exchtime_latest = exchtime;
                    g_min_queues[119].erase(it->second.it_list);
                    it->second.it_list = g_min_queues[120].insert(g_min_queues[120].end(), it); // 放到下一分钟线队列尾部
                }
                else if (it->second.exchtime_pub == 150000000) // 如果本次发布时间是 15:00，则停止该证券的分钟线数据发布
                {
                    // 加入忽略列表，并从待发布分钟线信息的证券队列中删除
                    g_min_queues[239].erase(it->second.it_list);
                    g_num_securities_finished++;
                    g_ignored_securities.insert(stkid);
                    g_security_info_dict.erase(it);
                    if (g_security_info_dict.empty())
                    {
                        DEBUG_LOG(std::format("所有分钟线数据生成完毕，共处理 {} 个证券，其中 {} 个证券获得最后一条数据，另有 {} 个被忽略，共生成 {} 条分钟线数据",
                            g_num_securities, g_num_securities_finished, g_num_ignored_securities, g_num_md1m));
                        return;
                    }
                }
            }
            else // 非早盘或午盘最后一次分钟线
            {
                // 此行情是下一分钟线的起始点，而证券信息中的 latest 代表的是本分钟线中的最后一个数据点
                if (it->second.vol_start > 0) // 大于 0 才表示基数是已知的，相减才有意义
                {
                    z.v = it->second.vol_latest - it->second.vol_start; // 要用 vol_latest 而不是本行情里的 volume
                    z.m = it->second.money_latest - it->second.money_start;
                }
                else // 本分钟线基数未定且在分钟最后 3 秒无行情，所以本分钟线没有成交。一般只在刚开盘，还没开始遍历时出现，以后在遍历中会定基数
                {
                    z.v = 0;
                    z.m = 0.0;
                }
                if (z.v > 0) // 该分钟是有成交的
                {
                    z.a = z.m / z.v;
                    z.o = it->second.open;
                    z.h = it->second.high;
                    z.l = it->second.low;
                    z.c = it->second.latest;
                }
                else // 该分钟没有成交，用最新价或前收价作为各价格
                {
                    double p = (it->second.latest > -0.000001 && it->second.latest < 0.000001) ? it->second.pre_close : it->second.latest;
                    z.a = p;
                    z.o = p;
                    z.h = p;
                    z.l = p;
                    z.c = p;
                }
                z.opos = g_num_md1m++;
                zmq_send(output_socket, &z, sizeof(z), 0);

                // 我们不考虑本次行情的秒数是 57/58/59 的情况，因为在每分钟至少两次遍历的情况下，不可能拖到此时还没处理
                // 所以本次行情如果有新成交就应该是下一分钟线的起始行情，如果没有新成交，则起始行情仍应置 -1，
                // 下一分钟线的 vol_start 和 money_start 应该是当前最新的 volume 和 value
                int32_t idx = hhmmssmmm2min_idx(it->second.exchtime_pub); // 当前所在的分钟线队列的索引
                it->second.exchtime_pub = hhmmssmmm_add_min_whole(it->second.exchtime_pub, 1);
                it->second.latest = o.latest() / 10000.0;
                if (o.volume() > it->second.vol_latest) // 本次行情相对于最近一次行情有新的成交
                {
                    it->second.open = it->second.latest;
                    if (o.high() / 10000.0 > it->second.high_latest) // 本分钟内创新高
                    {
                        it->second.high = o.high() / 10000.0;
                    }
                    else
                    {
                        it->second.high = it->second.open;
                    }
                    if (o.low() / 10000.0 < it->second.low_latest) // 本分钟内创新低
                    {
                        it->second.low = o.low() / 10000.0;
                    }
                    else
                    {
                        it->second.low = it->second.open;
                    }
                }
                else // 本次行情相对于最近一次行情没有新的成交
                {
                    it->second.open = -1.0;
                    it->second.high = -1.0;
                    it->second.low = -1.0;
                }
                it->second.high_latest = o.high() / 10000.0;
                it->second.low_latest = o.low() / 10000.0;
                it->second.vol_start = it->second.vol_latest;
                it->second.vol_latest = o.volume();
                it->second.money_start = it->second.money_latest;
                it->second.money_latest = double(o.value());
                it->second.exchtime_latest = exchtime;
                // 从当前分钟线队列中删除，并加到下一分钟线队列的尾部
                g_min_queues[idx].erase(it->second.it_list);
                it->second.it_list = g_min_queues[idx + 1].insert(g_min_queues[idx + 1].end(), it); // 放回队列尾部
            }
            if (should_iter_12 || should_iter_42)
            {
                do_queue(hhmmssmmm2min_idx(g_exchtime_latest), output_socket);
                if (should_iter_12)
                {
                    g_exchtime_12_latest = g_exchtime_latest;
                }
                else
                {
                    g_exchtime_42_latest = g_exchtime_latest;
                }
                // 如果当前 g_exchtime_latest 已经到了 15:00:12，而本次行情的时间戳在 15:00:00 到 15:00:12 之间，则本次遍历可能会把所有股票都清除掉
                // 所以我们要检查一下是否已经没有股票了
                if (g_security_info_dict.empty())
                {
                    DEBUG_LOG(std::format("遍历后，所有分钟线数据生成完毕，共处理 {} 个证券，其中 {} 个证券获得最后一条数据，另有 {} 个被忽略，共生成 {} 条分钟线数据",
                        g_num_securities, g_num_securities_finished, g_num_ignored_securities, g_num_md1m));
                    return;
                }
            }
            continue;
        }

        // 发布分钟都过了才来行情，特殊情况是深交所停牌证券
        if (it->second.exchtime_pub == 150000000 && it->second.exch_id == ExchID::SZ && o.status() == "8")
        {
            zmd1m_t z{0};
            z.ty = 'S';
            memcpy(z.code, stkid.c_str(), stkid.size());
            std::tm tm = today;
            tm.tm_hour = it->second.exchtime_pub / 10000000;
            tm.tm_min = it->second.exchtime_pub / 100000 % 100;
            z.exchtime_pub = mktime(&tm); // 分钟线时间戳对应的 Unix 秒
            z.c = o.preclose() / 10000.0;
            z.o = z.c;
            z.h = z.c;
            z.l = z.c;
            z.a = z.c;
            z.v = 0;
            z.m = 0.0;
            z.opos = g_num_md1m++;
            zmq_send(output_socket, &z, sizeof(z), 0);
            DEBUG_LOG(std::format("!!!深交所停牌证券，发布分钟线数据: 证券 {}, 状态 {}, 全局时间 {}, 行情时间 {}, 最后行情时间 {}, min_off {}",
                o.stkid(), o.status(), g_exchtime_latest, exchtime, it->second.exchtime_latest, min_off));
            // 放入忽略列表，并从待发布分钟线信息的证券队列中删除
            g_min_queues[239].erase(it->second.it_list);
            g_num_securities_finished++;
            g_ignored_securities.insert(stkid);
            g_security_info_dict.erase(it);
            if (g_security_info_dict.empty())
            {
                DEBUG_LOG(std::format("深交所停牌证券处理后，所有证券处理完毕，共 {} 个证券，其中 {} 个证券获得最后一条数据，另有 {} 个被忽略，共生成 {} 条分钟线数据",
                                      g_num_securities, g_num_securities_finished, g_num_ignored_securities, g_num_md1m));
                return;
            }
            continue;
        }

        // 发布分钟都过了才来行情，如果假设每分钟至少有两次行情，则不应该出现此种情况
        DEBUG_LOG(std::format("!!!晚到 exchtime {}, 预定发布 {}, 最后行情 {}, 间隔 {} 秒, 证券 {}, 状态 {}, min_off {}, 全局时间 {}, 放弃治疗，放入忽略表中",
            exchtime / 1000, it->second.exchtime_pub / 1000, it->second.exchtime_latest / 1000, hhmmssmmm_diff(exchtime, it->second.exchtime_latest) / 1000,
            o.stkid(), o.status(), min_off, g_exchtime_latest));
        g_security_info_dict.erase(it);
        if (g_security_info_dict.empty())
        {
            DEBUG_LOG(std::format("忽略后，没有剩下的证券了，共生成 {} 条分钟线数据，当前全局时间 {}", g_num_md1m, g_exchtime_latest));
            return;
        }
        g_ignored_securities.insert(stkid); // 加入忽略列表
    }
}

int main(int argc, char *argv[])
{
    ProgramOptions options = {
        DEFAULT_CONFIG_FILE,
        {},
        {},
        DEFAULT_OUTPUT_PORT_UT,
        DEFAULT_HWM,
        false,
        false
    };

    if (!GetProgramOptions(argc, argv, options))
    {
        return -1;
    }

    g_security_info_dict.reserve(MAX_NUM_SECURITIES); // 确保不会发生 rehashing 导致迭代器失效

    // 设置时区为东八区
    setenv("TZ", "/usr/share/zoneinfo/Asia/Shanghai", 1);
    std::time_t result = std::time(nullptr);
    today = *std::localtime(&result); // 本程序单线程，不考虑 localtime() 是线程不安全的
    today.tm_sec = 0; // 秒数清零，方便发布分钟线时不用再清零了

    // 打开 ZeroMQ 的 Publish socket
    auto zmq_ctx = zmq_ctx_new();
    auto output_socket = zmq_socket(zmq_ctx, ZMQ_PUB);  // 输出极简行情
    zmq_setsockopt(output_socket, ZMQ_SNDHWM, &options.hwm, sizeof(options.hwm));
    std::string endpoint = std::format("tcp://*:{}", options.output_port_ut);
    int r = zmq_bind(output_socket, endpoint.c_str());
    if (r != 0)
    {
        BAD_THING(std::format("绑定 ZeroMQ 输出端口 {} 失败: {}", endpoint, zmq_strerror(errno)));
    }
    else
    {
        DEBUG_LOG(std::format("绑定 ZeroMQ 输出端口成功: {}", endpoint));
    }

    // 订阅 ZeroMQ 的行情源
    auto input_socket = zmq_socket(zmq_ctx, ZMQ_SUB);  // 输入行情
    zmq_setsockopt(input_socket, ZMQ_RCVHWM, &options.hwm, sizeof(options.hwm));
    endpoint = std::format("tcp://{}:{}", options.sources[0].host, options.sources[0].port);
    r = zmq_connect(input_socket, endpoint.c_str());
    if (r != 0)
    {
        BAD_THING(std::format("连接 ZeroMQ 行情源失败: {}", zmq_strerror(errno)));
    }
    for (const auto &s : options.subscriptions)
    {
        zmq_setsockopt(input_socket, ZMQ_SUBSCRIBE, s.c_str(), s.size());
    }
    gen_md1m_s(input_socket, output_socket);
    DEBUG_LOG("分钟线数据生成函数退出，程序结束");
    zmq_close(input_socket);
    zmq_close(output_socket);
    zmq_ctx_term(zmq_ctx);
    return 0;
}
