# 实时现货行情分钟线数据生成程序 md1m_s 配置文件
# 

# 现阶段只支持一个 L1 或 L2 行情源，ZMQ 上的 PB 编码格式
sources:
  - name: DYL2     # 通联 L2 行情
    host: ***********
    port: 9869
  # - name: DYOT     # 通联逐笔全速行情，包含上交所与深交所
  #   host: ***********
  #   # host: 127.0.0.1
  #   port: 30002
  # - name: HTL1     # 华泰 L1 行情
  #   host: ************
  #   port: 9869
  # - name: GXSZ     # 国信中畅 L1 行情，东莞 VPN
  #   host: ***********
  #   port: 9869
  # - name: GXSH_BIG     # 国信中畅 L1 行情，上海 VPN
  #   host: ************
  #   port: 9869
  # - name: GXSH_SMALL   # 国信中畅 L1 行情，上海 VPN
  #   host: ************
  #   port: 9870

# 订阅的行情主题，必须以字符 'S' 开头，因为指数编码与股票、基金编码有重合，指数编码以 'I' 开头
subscriptions:
  # - S              # 全部股票、基金行情
  # - S159             # 深交所 ETF
  # 根据 /A/md/1m/s 下的 pickle 文件名总结，并参考
  #  《深圳证券交易所证券代码区间表(2024 年 12 月修订)》
  #  《上海证券交易所证券交易业务指南第 4 号——证券代码段分配指南（2025 年第 5 次修订）》
  #（以下肯定多于文件名的 6900 个）

  # 深市主板 A 股
  - S000
  # 为了避开 001001-001199 主板存托凭证
  - S0012
  - S0013
  - S0014
  - S0015
  - S0016
  - S0017
  - S0018
  - S0019
  - S002
  - S003
  - S004

  # 深市 ETF
  - S159

  # 深市创业版股票，目前只用到了 302
  - S300
  - S301
  - S302

  # 上交所 ETF
  - S510
  - S511
  - S512
  - S513
  - S515
  - S516
  - S517
  - S518
  - S520
  - S530
  - S551
  - S560
  - S561
  - S562
  - S563
  - S588
  - S589

  # 上交所主板 A 股
  - S60  # 指南中只分配了 600 601 603 605，这里简化一下

  # 上交所 A 股科创板
  - S688

  # 有趣的上交所九号公司存托凭证
  - S689009

high_water_mark: 6000  # ZeroMQ socket 的 high water mark，包括输入与输出，值太低时可能在遍历处理大量不活跃证券时丢失分钟线数据

output:
  port_ut: 30007      # 输出极简行情的 ZeroMQ PUB 服务器端口，缺省 30007

background: false   # 是否在后台运行
