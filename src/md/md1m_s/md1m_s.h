#pragma once
#include <list>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <cstdint>
#include <string>
#include <chrono>
#include <format>
#include <iostream>
#include <ctime>

// 行情源类型
enum class MDSourceType
{
    L1,     // Level 1 行情
    L2,     // Level 2 行情
    OT,     // 逐笔全速行情
    UNKOWN, // 未知，不应该出现
};

// 行情源
struct MdSource
{
    std::string name; // 行情源名称
    std::string host; // 行情源 ZMQ 服务器地址
    int port;         // 行情源 ZMQ 服务器端口
};

// 交易所标识
enum class ExchID
{
    SH,     // 上海
    SZ,     // 深圳
    UNKOWN, // 未知
};

// 行情
struct Md
{
    std::string stkid;  // 证券代码
    ExchID exch_id;     // 交易所标识
    int32_t exchtime;   // 行情交易所时间戳, 形如 HHMMSSmmm
    double preclose;    // 昨收盘价
    double open;        // 开盘价
    double high;        // 最高价
    double low;         // 最低价
    double latest;      // 最新价
    double close;       // 收盘价
    int64_t volume;     // 成交量
    int64_t value;      // 成交金额
    std::string status; // 证券状态
};

// 证券信息
struct SecurityInfo
{
    ExchID exch_id;                                                                       // 交易所标识
    double pre_close;                                                                     // 昨收盘价，用于集合竞价没有成交、且第一分钟也没有成交时填充开高收低价
    double open;                                                                          // 本分钟的开盘价，正常情况下为每分钟开始 3 秒的行情数据，为 -1.0 表示还没有得到该数据
    double high;                                                                          // 本分钟内的最高价
    double high_latest;                                                                   // 开盘以来的最高价
    double low;                                                                           // 本分钟内的最低价
    double low_latest;                                                                    // 开盘以来的最低价
    double latest;                                                                        // 最新价，主要用于缺失行情时以之前的价格填充
    int64_t vol_start;                                                                    // 起始累计成交量，用于计算的基数，正常情况下是每分钟最后 3 秒的行情数据，为 -1 表示还没有得到该数据
    int64_t vol_latest;                                                                   // 最新的累计成交量
    double money_start;                                                                   // 起始累计成交金额，用于计算的基数，正常情况是每分钟最后 3 秒的行情数据，为 -1.0 表示还没有得到该数据
    double money_latest;                                                                  // 最新的累计成交金额
    int32_t exchtime_pub;                                                                 // 将要发布的分钟行情时间戳，形如 HHMMSSmmm
    int32_t exchtime_latest;                                                              // 最新的行情交易所时间戳, 形如 HHMMSSmmm，用于缺失行情时以之前的价格填充的判断
    int32_t exchtime_start;                                                               // 起始行情交易所时间戳, 形如 HHMMSSmmm
    std::list<std::unordered_map<std::string, SecurityInfo>::iterator>::iterator it_list; // 指向
};

#define BAD_THING(msg) bad_thing(__func__, __LINE__, msg)
#define DEBUG_LOG(msg) debug_log(__func__, __LINE__, msg)

inline void bad_thing(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cerr << std::format("[{}][ERROR][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
    exit(1);
}

inline void debug_log(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cout << std::format("[{}][DEBUG][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
}

inline MDSourceType md_source_type_from_string(const std::string &type)  // 行情源类型字符串转换为 MDSourceType
{
    if (type == "L1")
    {
        return MDSourceType::L1;
    }
    else if (type == "L2")
    {
        return MDSourceType::L2;
    }
    else if (type == "OT")
    {
        return MDSourceType::OT;
    }
    else
    {
        return MDSourceType::UNKOWN;
    }
}

inline ExchID exch_id_from_string(const std::string &id)  // 交易所标识字符串转换为 ExchID
{
    if (id == "1")
    {
        return ExchID::SH;
    }
    else if (id == "0")
    {
        return ExchID::SZ;
    }
    else
    {
        return ExchID::UNKOWN;
    }
}

// 把形如 HHMMSSmmm 的时间戳转换为从午夜开始的分钟数
inline int32_t hhmmssmmm2min(int32_t t)
{
    return (t / 10000000) * 60 + (t % 10000000) / 100000;
}

// 把自午夜开始的分钟数转换成形如 HHMM00000 的时间戳
inline int32_t min2hhmmssmmm(int32_t min)
{
    return (min / 60) * 10000000 + (min % 60) * 100000;
}

// 把形如 HHMMSSmmm 的时间戳加上指定分钟数并返回 HHMM00000 的整分钟格式
inline int32_t hhmmssmmm_add_min_whole(int32_t t, int32_t min)
{
    return min2hhmmssmmm(hhmmssmmm2min(t) + min) / 100000 * 100000;
}

// 把形如 HHMMSSmmm 的时间戳转换为从午夜开始的毫秒数
inline int32_t hhmmssmmm2ms(int32_t t)
{
    return (t / 10000000) * 3600000 + (t % 10000000) / 100000 * 60000 + t % 100000;
}

// 计算两个形如 HHMMSSmmm 的时间戳之间的毫秒差
inline int32_t hhmmssmmm_diff(int32_t t1, int32_t t2)
{
    return hhmmssmmm2ms(t1) - hhmmssmmm2ms(t2);
}

// 把形如 HHMM00000 的时间戳转成分钟线序号
// 93100000 为 0，113000000 为 119，130100000 为 120，150000000 为 239
// 非交易时间:
//   93000000 及以前 为 -1，午间休盘时间为 -2，下午收盘后为 -3
inline int32_t hhmmssmmm2min_idx(int32_t t)
{
    t = t / 100000 * 100000; // 取整到分钟
    if (t < 93100000)
    {
        return -1;
    }
    if (t <= 113000000)
    {
        return hhmmssmmm2min(t) - 9 * 60 - 31;
    }
    if (t < 130100000)
    {
        return -2;
    }
    if (t <= 150000000)
    {
        return hhmmssmmm2min(t) - 13 * 60 + 119;
    }
    return -3;
}

// 把分钟线序号转换成形如 HHMM00000 的时间戳
// 合法序号为 0 - 239，对于非法的序号返回 -1
inline int32_t min_idx2hhmmssmmm(int32_t idx)
{
    if (idx < 0 || idx >= 240)
    {
        return -1;
    }
    if (idx <= 119)
    {
        return min2hhmmssmmm(idx + 9 * 60 + 31);
    }
    return min2hhmmssmmm(idx - 119 + 13 * 60);
}

class md1m_s
{
private:
    std::tm m_today; // 今天日期
    int m_max_num_securities;  // 最大证券数量，会用它来确定 SecurityInfo unordered_map 容器的大小，以免发生 rehashing 造成迭代器失效
    int32_t m_exchtime_latest = 0; // 所有证券中最新的行情交易所时间戳, 形如 HHMMSSmmm

    // 对于不活跃证券，要依赖遍历来生成分钟线数据
    // 由于深交所行情交易所时间戳的粒度为 3 秒，所以在只依赖行情来驱动的情况下，如果只订阅了深交所行情，则必须选择 3 倍数秒为遍历时间点，否则永远也触发不了遍历
    int32_t m_exchtime_12_latest = 0; // 每分钟第 12 秒遍历的最新时间戳，开如 HHMM12mmm
    int32_t m_exchtime_42_latest = 0; // 每分钟第 42 秒遍历的最新时间戳，开如 HHMM42mmm

    // 证券信息字典，key 为证券代码
    std::unordered_map<std::string, SecurityInfo> m_security_info_dict;

    int m_num_securities = 0;  // 加入生成分钟线数据的证券数量，当此数量超过 MAX_NUM_SECURITIES 时，不再缓存行情消息，以免因为 rehashing 导致迭代器失效
    std::unordered_set<std::string> m_ignored_securities; // 因超过 MAX_NUM_SECURITIES 而被忽略的证券

    // 待发布分钟线信息的证券队列, 每分钟一个，每支证券都在某个队列中
    std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> m_min_queues(240);

    // 所有正常结束分钟线数据生成的证券累计数
    int m_num_securities_finished = 0;

    // 所有被忽略的证券累计数
    int m_num_ignored_securities = 0;

    // 生成的分钟线数据总数
    int m_num_md1m = 0;

public:
    md1m_s(/* args */);
    ~md1m_s();
};

md1m_s::md1m_s(/* args */)
{
}

md1m_s::~md1m_s()
{
}
