cmake_minimum_required(VERSION 3.20)
project(md1m_s)

set(CMAKE_INSTALL_MESSAGE ALWAYS)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(
    .
    ../include
)

# Compiler flags
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O2 -Wall")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZMQ REQUIRED libzmq)

# 使用系统的静态 Protobuf 库
set(Protobuf_ROOT "/usr")
find_package(Protobuf 3.21.0 REQUIRED)

# Executables
# 股票基金现货 L1/L2 行情实时生成分钟数据，以极简形式通过 ZMQ 发布
add_executable(md1m_s ../common/lyproto.quota.pb.cc md1m_s.cc)

# Link libraries for 通联 L2 行情转 ZMQ 并保存 CSV 文件的程序
target_link_libraries(md1m_s PRIVATE  dl pthread rt ${ZMQ_LIBRARIES} protobuf::libprotobuf m)

# Installation
install(TARGETS md1m_s
    DESTINATION /usr/local/sbin
)
