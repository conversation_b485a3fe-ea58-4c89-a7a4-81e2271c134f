#pragma once
#include <cstdint>
#include <string>
#include <set>
#include <map>
#include <unordered_map>
#include <vector>

// 上交所发布的逐笔信息
struct order_raw_sh
{
    std::string recvTime;  // 接收程序收到逐笔数据的时间戳
    int64_t bizIndex; // 逐笔序号，每个 channel 从 1 开始单独连续编号
    int64_t channel;
    int64_t securityID;
    std::string tickTime;  // 上交所原始逐笔数据中的时间戳，据观察精确到百分之一秒
    std::string type;  // A 新增委托订单，D 撤销委托订单，T 成交，S 产品状态订单
    int64_t buyOrderNo;
    int64_t sellOrderNo;
    double price;
    int64_t qty;
    double tradeMoney;  // 成交信息时：成交金额（三位小数），新增委托订单时：已成交的委托数量（次生委托订单），0.0（原始委托订单）
    std::string tickBSFlag;  // 成交信息时：B 买方委托单成交，S 卖方委托单成交，N 集合竞价成交；新增或删除委托订单时：B 买方委托单，S 卖方委托单
                             // 产品状态订单：START 启动，OCALL 开市集合竞价，TRADE 连续自动撮合，SUSP 停牌，CCALL 收盘集合竞价，CLOSE 闭市，ENDTR 交易结束
    int64_t delay;  // 接收程序计算的时延，单位微秒
};

// 买卖方向
enum class TradeType
{
    BUY = 0,
    SELL = 1
};

// 深交所发布的逐笔委托消息，mdl.6.33
struct order_raw_sz
{
    int64_t recvTime;  // 接收程序收到逐笔数据的时间戳，自 epoch 以来的微秒数
    int channelNo;  // 通道号
    int64_t applSeqNum;  // 消息记录号，每个通道从 1 开始编号，委托与成交共同使用
    std::string steamID;  // 行情类别，011 现货（股票，基金，债券等）集中竞价交易逐笔行情，021 质押式回购交易逐笔行情，041 期权集中竞价交易逐笔行情
    std::string securityID;  // 证券代码
    std::string securityIDSource; // 证券代码源
    double price;  // 委托价格
    int64_t orderQty;  // 委托数量
    char side;  // 买卖方向，'1' 49 买，'2' 50 卖，'G' 71 借入，'F' 70 出借
    std::string transactTime; // 委托时间，如 "09:15:00.360"
    char ordType;  // 订单类别，'1' 49 市价委托，'2' 50 限价委托，'U' 85 本方最优
    int delay;  // 接收程序计算的时延，单位微秒

    bool operator==(const order_raw_sz &other) const
    {
        return recvTime == other.recvTime &&
               channelNo == other.channelNo &&
               applSeqNum == other.applSeqNum &&
               steamID == other.steamID &&
               securityID == other.securityID &&
               securityIDSource == other.securityIDSource &&
               price == other.price &&
               orderQty == other.orderQty &&
               side == other.side &&
               transactTime == other.transactTime &&
               ordType == other.ordType &&
               delay == other.delay;
    }
};

// 深交所发布的逐笔成交消息，mdl.6.36
struct transaction_raw_sz
{
    int64_t recvTime;  // 接收程序收到逐笔数据的时间戳，自 epoch 以来的微秒数
    int channelNo;  // 通道号
    int64_t applSeqNum;  // 消息记录号，每个通道从 1 开始编号，委托与成交共同使用
    std::string steamID;  // 行情类别，011 现货（股票，基金，债券等）集中竞价交易逐笔行情，021 质押式回购交易逐笔行情，041 期权集中竞价交易逐笔行情
    std::string securityID;  // 证券代码
    std::string securityIDSource; // 证券代码源
    int64_t bidApplSeqNum;  // 买方委托索引，0表示无对应委托
    int64_t offerApplSeqNum;  // 卖方委托索引，0表示无对应委托
    double lastPx;  // 成交价格，撤单价格为 0
    int64_t lastQty;  // 成交数量，撤单为撤单数量
    char execType;  // 成交类别，'4' 52 撤单，'F' 70 正常成交
    std::string transactTime;  // 成交时间，如 "09:15:00.360"
    int delay;  // 接收程序计算的时延，单位微秒

    bool operator==(const transaction_raw_sz &other) const
    {
        return recvTime == other.recvTime &&
               channelNo == other.channelNo &&
               applSeqNum == other.applSeqNum &&
               steamID == other.steamID &&
               securityID == other.securityID &&
               securityIDSource == other.securityIDSource &&
               bidApplSeqNum == other.bidApplSeqNum &&
               offerApplSeqNum == other.offerApplSeqNum &&
               lastPx == other.lastPx &&
               lastQty == other.lastQty &&
               execType == other.execType &&
               transactTime == other.transactTime &&
               delay == other.delay;
    }
};

enum class TickType
{
    ORDER = 0,  // 新增委托订单
    CANCEL = 1,  // 撤销委托订单，即撤单
    TRANSACTION = 2,  // 成交
};

#define TICK_TYPE_ORDER 0
#define TICK_TYPE_CANCEL 1
#define TICK_TYPE_TRANSACTION 2

#define TICK_DIRECT_BUY 1
#define TICK_DIRECT_SELL 2
#define TICK_DIRECT_AUCTION 0

// 连续竞价期间发布的行情信息，每个委托、成交或撤单，都对应生成一个Tick_info
struct Tick_info
{
    char time[24];             // 交易所行情时间，如 "2025-08-04 14:56:59.470", 长度正好为 23
    int8_t tick_type;          // 消息类型：委托 TICK_TYPE_ORDER 0、撤单 TICK_TYPE_CANCEL 1、成交 TICK_TYPE_TRANSACTION 2
    int8_t tick_direc;         // 消息方向：买 TICK_DIRECT_BUY 1、卖 TICK_DIRECT_SELL 2、集合竞价 TICK_DIRECT_AUCTION 3
    int bid20prices[20];       // 20档买价
    int64_t bid20qty[20];      // 20档买量
    int ask20prices[20];       // 20档卖价
    int64_t ask20qty[20];      // 20档卖量
    int bid20num[20];          // 20档买单笔数
    int ask20num[20];          // 20档卖单笔数
    int64_t bid20lgqty[20];    // 20档买中大单买量
    int64_t ask20lgqty[20];    // 20档卖中大单卖量
    std::map<int, int> orderbook_tier;  // 价格档位信息，key 是以厘为单位的报价，value 是相对于前收价的涨跌千分点
    int prev_close;            // 昨收价
    int latest;                // 最新价
    int mid_price;             // 买一、卖一中间价
    int spread;                // 卖一、买一价差
    int64_t tick_vol;
    double tick_money;
    int order_aggr;
    int64_t total_buy_qty;     // 当前总有效申买量
    int64_t total_sell_qty;    // 当前总有效申卖量
    int64_t sm_order_cum_vol;  // 5万以下小单的净流入金额，单位元
    int64_t mid_order_cum_vol; // 5万至30万中单的净流入金额，单位元
    int64_t lg_order_cum_vol;  // 30万至100万大单的净流入金额，单位元
    int64_t slg_order_cum_vol; // 100万以上特大单的净流入金额，单位元
    int64_t buy_order_index;   // 当前买单编号
    int64_t sell_order_index;  // 当前卖单编号

    Tick_info(const char* t, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier)
    {
        memcpy(time, t, 23);  // 如 "2025-08-04 14:56:59.470", 长度正好为 23
        time[23] = 0;
        this->tick_type = tick_type;
        this->tick_direc = tick_direc;
        this->orderbook_tier = tier;
    }
};

enum class Market
{
    SH,
    SZ,
    BJ
};

// 上交所市场状态
enum class SHMarketState
{
    START, // 启动
    OCALL, // 开市集合竞价
    TRADE, // 连续自动撮合
    SUSP,  // 停牌
    CCALL, // 收盘集合竞价
    CLOSE, // 闭市
    ENDTR, // 交易结束
};

// 上交所连续交易阶段，主动交易方订单信息
// 在连续交易阶段
//   - 如果主动交易订单被一次性撮合完成（含市价单部分成交剩余自动撤回），则不会发布该订单的原始委托订单
//   - 如果主动交易订单只是部分完成且剩余部分要继续撮合，则会发布该订单的原始委托订单的修正订单
//   - 修正订单
//       * 在成交信息后紧接着发布（实际验证了至少5个修正订单）
//       * 订单号沿用原始委托订单的订单号
//       * 报价修正为新的挂单价（一般为本方最新成交价，也就是本订单成交的最高价）
struct sh_driver {
    int qty;    // 本方累积交易量
    int money;  // 本方累积交易金额
    TradeType direct;   // 本方是买方还是卖方
    std::string time;   // 本次订单首次出现在成交信息中的时间戳
    double counterpart_sm;  // 对手方小单累计成交金额
    double counterpart_mid;  // 对手方中单累计成交金额
    double counterpart_lg;   // 对手方大单累计成交金额
    double counterpart_slg;  // 对手方特大单累计成交金额
};

enum class LOG_LEVEL
{
    DEBUG = 0,
    INFO,
    WARNING,
    ERROR
};

// 价位上的订单信息
struct price_order
{
    std::unordered_map<int64_t, int64_t> orders[2];  // 买单和卖单的[委托单号, 数量]映射
    int64_t total_qty[2];  // 买单和卖单的总数量，输出20档
};

class OrderBook
{
public:
    OrderBook(int upper_limit, int lower_limit, int prev_close, bool is_etf, Market market, LOG_LEVEL log_level = LOG_LEVEL::WARNING);
    ~OrderBook();
    bool put_instruction(const order_raw_sh& order, int &index);
    void print_tier_dict(void);
    void print_orderbook_tier(void);
    void print_orderbook(void);
    void print_orderbook(int price);
    void print_orderpool(void);
    void print_tick_info_snapshots(void);
    void print_money_balance(void);
    void print_vol_stat(void);
    void print_vol_lg_stat(void);
    void log(LOG_LEVEL level, std::string msg);
    static void log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg);
private:
    int upper_limit_;
    int lower_limit_;
    int prev_close_;
    double mid_threshold_;  // 以昨收价计算，5万元订单对应的股数
    double lg_threshold_;   // 以昨收价计算，30万元订单对应的股数
    double slg_threshold_;  // 以昨收价计算，100万元订单对应的股数
    float disc_coef_; // 从跌停 -10% 到涨停 10% 之间，一共 20 个百分点，根据前收盘价计算分档粒度
    bool is_etf_;  // 是否是 ETF，如果是 ETF，则最小价格变动单位为 0.001 元，一般股票则是 0.01 元
    int increment_;  // 最小价格变动单位，ETF 为 0.001 元，一般股票为 0.01 元，要乘以 1000，所以分别是 1 和 10
    bool whether_snapshot_ = false;  // 是否计算行情快照
    int64_t total_buy_qty_ = 0;  // 买单总数量
    int64_t total_sell_qty_ = 0;  // 卖单总数量
    std::map<int, int> tier_dict_;  // key 是所有可能价格，value 是相对于前收价的涨跌千分点，构造函数中根据涨跌停价、是否ETF来计算，key 数量是不变的
    std::map<int, int> orderbook_tier_;  // key 是相对于前收价的涨跌千分点，value 是该价位的挂单量，构造函数中会把所有可能千分点的值都初始化为0，key 数量是不变的，全量输出
    std::map<int, struct price_order> orderbook_;  // key 是以厘为单位的报价，value 是买单和卖单的[委托单号, 数量]映射以及总数量，key 数量是动态增减的
    std::map<int, struct price_order>::iterator it_bid1_;  // 买一
    std::map<int, struct price_order>::iterator it_ask1_;  // 卖一
    std::unordered_map<int64_t, int64_t [2]> orderpool_;  // 订单池，key 是委托单编号，value 是委托单的报价（厘）和数量，这里没有买或卖的信息，key 数量是动态增减的
    std::set<int64_t> orderpool_sm_;  // 小单池，内容是委托单编号
    std::set<int64_t> orderpool_mid_;  // 中单池，内容是委托单编号
    std::set<int64_t> orderpool_lg_;  // 大单池，内容是委托单编号
    std::set<int64_t> orderpool_slg_;  // 特大单池，内容是委托单编号
    std::map<int, int64_t [2]> orderbook_vol_lg_;  // 报价为索引的30万以上大单（含特大单）总量，key 是以厘为单位的报价，value 是大单中买单、卖单各自的当前有效总数量，
                                                   // key 数量是动态增减的，输出20档，输出档位与 orderbook_ 一致，空值输出为 0
    Market market_;
    SHMarketState market_state_ = SHMarketState::START;  // 市场状态，适用于沪市
    std::vector<Tick_info> tick_info_snapshots_;  // 行情信息快照序列
    double sm_order_cum_vol_ = 0.0;  // 5万以下小单的净流入金额，单位元
    double mid_order_cum_vol_ = 0.0;  // 5万至30万中单的净流入金额，单位元
    double lg_order_cum_vol_ = 0.0;  // 30万至100万大单的净流入金额，单位元
    double slg_order_cum_vol_ = 0.0;  // 100万以上特大单的净流入金额，单位元
    int bid1_ = 0;  // 当前买一价
    int ask1_ = 0;  // 当前卖一价

    // 上交所连续竞价阶段，任一时刻只会有一个当前主动订单，主动买或者主动卖
    int64_t pending_buy_no_ = 0;  // 上交所连续竞价阶段，当前主动买订单编号，在新主动买成交信息中设置，后续的逻辑：
                                  //   - 主动买成交信息，进行买单号比较，相同说明主动买吃掉了更多的卖单，不同说明是新的主动买订单，原主动买订单已完结，更新本变量
                                  //   - 其它类型信息，说明该主动买订单的主动成交已完结（但可能产生次生订单），本变量置为 0
                                  //   - 本变量如果要变化（更新或置0），则根据累积变量 pending_buy_qty_ 和 pending_buy_money_ 进行统计处理
                                  //   - 本变量如果要持续保持不变，则更新累积变量 pending_buy_qty_ 和 pending_buy_money_
    int64_t pending_buy_qty_ = 0;  // 上交所连续竞价阶段，当前主动买订单累积成交数量
    double pending_buy_money_ = 0.0;  // 上交所连续竞价阶段，当前主动买订单累积成交金额
    int64_t pending_sell_no_ = 0;  // 上交所连续竞价阶段，当前主动卖订单编号，在新主动卖成交信息中设置，后续的逻辑：
                                   //   - 主动卖成交信息，进行卖单号比较，相同说明主动卖吃掉了更多的买单，不同说明是新的主动卖订单，原主动卖订单已完结，更新本变量
                                   //   - 其它类型信息，说明该主动买订单的主动成交已完结（但可能产生次生订单），本变量置为 0
                                   //   - 本变量如果要变化（更新或置0），则根据累积变量 pending_sell_qty_ 和 pending_sell_money_ 进行统计处理
                                   //   - 本变量如果要持续保持不变，则更新累积变量 pending_sell_qty_ 和 pending_sell_money_
    int64_t pending_sell_qty_ = 0;  // 上交所连续竞价阶段，当前主动卖订单累积成交数量
    double pending_sell_money_ = 0.0;  // 上交所连续竞价阶段，当前主动卖订单累积成交金额
    int latest_ = 0;     // 最新成交价
    int latest_buy_price_ = 0;       // 最新成交买单报价
    int latest_sell_price_ = 0;      // 最新成交卖单报价
    LOG_LEVEL log_level_;
    int total_inst_trans_ = 0;   // 总成交订单数
    int total_inst_order_ = 0;   // 总委托订单数
    int total_inst_cancel_ = 0;  // 总撤单订单数
    int total_inst_state_ = 0;   // 总指令订单数
    int64_t buy_order_index_ = 0;  // 买单编号
    int64_t sell_order_index_ = 0; // 卖单编号
    std::map<int, int> xxx_fake_orderbook_tier_;  // 临时变量，用于验证 orderbook_tier_ 的正确性

    void export_tick_info_(Tick_info &tick_info); // 导出一些不同情形下（委托、撤单、成交、状态变化等）共同的 tick 信息
};
