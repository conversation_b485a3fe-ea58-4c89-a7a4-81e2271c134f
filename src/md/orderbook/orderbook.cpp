/*
 * 通过逐笔数据重建订单簿
 */

#include <arrow/api.h>
#include <arrow/io/api.h>
#include <parquet/arrow/reader.h>
#include <parquet/arrow/writer.h>

#include <vector>
#include <iostream>
#include <chrono>
#include <ctime>
#include <cstdio>
#include <format>
#include <string>
#include <string_view>
#include <sstream>
#include <iomanip>
#include <cmath>
#include <map>
#include <iterator>
#include <set>

// https://github.com/ben-strasser/fast-cpp-csv-parser
#include "3rdparty/fast-cpp-csv-parser/csv.h"

#include "orderbook.h"

LOG_LEVEL sys_log_level = LOG_LEVEL::DEBUG;

void bad_thing(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cerr << std::format("[{}] {}:{} {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
    exit(1);
}

// 解析时间字符串如 "2025-09-04 09:15:02.201720" 转换为自 epoch 以来的微秒数，本地时间
int64_t parse_time_to_microseconds(const std::string &time_str)
{
    std::istringstream iss{time_str};

    // 解析日期和时间部分
    tm tm = {};
    char dot;
    int ms;

    iss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S") >> dot >> ms;

    if (iss.fail())
    {
        throw std::runtime_error("Failed to parse time string");
    }

    // 将 tm 转换为 time_point
    auto tp = std::chrono::system_clock::from_time_t(std::mktime(&tm));

    // 加上微秒部分
    auto duration_since_epoch = tp.time_since_epoch() + std::chrono::microseconds(ms);

    // 转换为微秒数
    return duration_cast<std::chrono::microseconds>(duration_since_epoch).count();
}

// 解析时间字符串如 "2025-09-04 09:15:02.201720" 转换为自 epoch 以来的微秒数，本地时间
int64_t parse_time_to_microseconds(const char *time_str)
{
    tm tm = {};
    int year, month, day, hour, minute, second;
    int microsecond = 0;

    if (sscanf(time_str, "%d-%d-%d %d:%d:%d.%d", &year, &month, &day, &hour, &minute, &second, &microsecond) != 7)
    {
        throw std::runtime_error("Failed to parse time string");
    }
    tm.tm_year = year - 1900;
    tm.tm_mon = month - 1;
    tm.tm_mday = day;
    tm.tm_hour = hour;
    tm.tm_min = minute;
    tm.tm_sec = second;
    time_t t = std::mktime(&tm);

    // 转换为微秒数
    int64_t t_ms = (int64_t)t * 1000000 + microsecond;
    return t_ms;
}

OrderBook::OrderBook(int upper_limit, int lower_limit, int prev_close, bool is_etf, Market market, LOG_LEVEL log_level)
    : upper_limit_(upper_limit), lower_limit_(lower_limit), prev_close_(prev_close), is_etf_(is_etf), market_(market), log_level_(log_level)
{
    mid_threshold_ = 50000 / ((lower_limit + upper_limit) * 0.001 / 2);
    lg_threshold_ = 300000 / ((lower_limit + upper_limit) * 0.001 / 2);
    slg_threshold_ = 1000000 / ((lower_limit + upper_limit) * 0.001 / 2);

    if (prev_close > 10000)
    {
        disc_coef_ = 0.001; // 10元以上股票，按 0.1% 分档，一共有 201 个档
    }
    else if (prev_close > 5000 && prev_close <= 10000)
    {
        disc_coef_ = 0.002; // 5元以上到10元及以下的股票，按 0.2% 分档，一共有 101 个档
    }
    else if (prev_close > 2000 && prev_close <= 5000)
    {
        disc_coef_ = 0.005; // 2元以上到5元及以下的股票，按 0.5% 分档，一共有 21 个档
    }
    else
    {
        disc_coef_ = 0.01; // 5元以下的股票，按 1% 分档，一共有 21 个档(如果是 ST 股票，涨跌幅限制为 5%，则只有 11 个档)
    }

    if (is_etf_) // 最小价格变动单位，ETF 为 0.001 元，一般股票为 0.01 元，要乘以 1000，所以分别是 1 和 10
    {
        increment_ = 1;
    }
    else
    {
        increment_ = 10;
    }

    for (int p = lower_limit_; p < upper_limit_ + increment_; p += increment_)
    {
        tier_dict_[p] = std::lround((double)std::lround(((double)p / (double)prev_close_ - 1.0L) / disc_coef_) * disc_coef_ * 1000.0L);
    }
    std::set<int> tier_set;
    for (const auto &[key, value] : tier_dict_)
    {
        tier_set.insert(value);
    }
    for (auto p : tier_set)
    {
        orderbook_tier_[p] = 0;
    }
    // tick_info_snapshots_.reserve(300000); // 行情信息快照序列，似乎 reserve 没啥用
}

OrderBook::~OrderBook() {}

void OrderBook::print_orderbook_tier(void)
{
    std::cout << std::format("orderbook_tier[{}]:", orderbook_tier_.size()) << std::endl;
    for (auto p : orderbook_tier_)
    {
        std::cout << p.first << " " << p.second << std::endl;
    }
}

// 打印全部价位的统计信息，如有不一致则退出
void OrderBook::print_orderbook(void)
{
    std::cout << std::format("orderbook[{}]:", orderbook_.size()) << std::endl;
    for (auto p : orderbook_)
    {
        if (p.second.orders[0].size() == 0 && p.second.orders[1].size() == 0) // 该价位的买卖订单都已删除，但该价位的 key 却没删除
        {
            std::cout << std::format("价位 {} 的买卖订单都已删除，但该价位的 key 却没删除", p.first) << std::endl;
        }
        int64_t sum = 0;
        for (auto p1 : p.second.orders[0])  // 用剩余买单计算总买量
        {
            sum += p1.second;
        }
        std::string buy_qty;
        if (sum != p.second.total_qty[0])
        {
            buy_qty = " 不等！";
        }
        int64_t sum1 = 0;
        for (auto p1 : p.second.orders[1])  // 用剩余卖单计算总卖量
        {
            sum1 += p1.second;
        }
        std::string sell_qty;
        if (sum1 != p.second.total_qty[1])
        {
            sell_qty = " 不等！";
        }

        if (buy_qty != "" || sell_qty != "")
        {
            std::cout << std::format("price: {}, 买单数量: {}, 总申买量: {}, 卖单数量: {}, 总申卖量: {}, 计算总申买量: {}{}, 计算总申卖量: {}{}", p.first,
                p.second.orders[0].size(), p.second.total_qty[0], p.second.orders[1].size(), p.second.total_qty[1], sum, buy_qty, sum1, sell_qty)
                 << std::endl;
            bad_thing(__func__, __LINE__, std::format("orderbook 中 price = {} 的价位，总申买量和总申卖量不等于订单数量之和", p.first));
        }
        else
        {
            std::cout << std::format("price: {}, 买单数量: {}, 总申买量: {}, 卖单数量: {}, 总申卖量: {}", p.first,
                                     p.second.orders[0].size(), p.second.total_qty[0], p.second.orders[1].size(), p.second.total_qty[1])
                      << std::endl;
        }
    }
}

// 打印指定价位的详细订单信息，如有不一致则退出（没有指定价位信息正常返回）
void OrderBook::print_orderbook(int price)
{
    if (!orderbook_.contains(price))
    {
        std::cout << std::format("orderbook 中未找到 price = {} 的价位", price) << std::endl;
        return;
    }
    std::cout << std::format("price: {}, 买单数量: {}, 卖单数量: {}", price, orderbook_[price].orders[0].size(), orderbook_[price].orders[1].size()) << std::endl;
    std::cout << "买单: " << orderbook_[price].total_qty[0] << std::endl << "{";
    int64_t buy_qty = 0;
    for (auto o : orderbook_[price].orders[0])
    {
        std::cout << std::format("{}: {}, ", o.first, o.second);
        buy_qty += o.second;
    }
    std::cout << "} 计算总量: " << buy_qty << std::endl;
    std::cout << "卖单: " << orderbook_[price].total_qty[1] << std::endl << "{";
    int64_t sell_qty = 0;
    for (auto o : orderbook_[price].orders[1])
    {
        std::cout << std::format("{}: {}, ", o.first, o.second);
        sell_qty += o.second;
    }
    std::cout << "} 计算总量: " << sell_qty << std::endl;
    if (buy_qty != orderbook_[price].total_qty[0] || sell_qty != orderbook_[price].total_qty[1])
    {
        bad_thing(__func__, __LINE__, std::format("orderbook 中 price = {} 的价位，总申买量和总申卖量不等于订单数量之和", price));
    }
}

void OrderBook::print_orderpool(void)
{
    std::cout << std::format("orderpool[{}]:", orderpool_.size()) << std::endl << "{";
    for (auto p : orderpool_)
    {
        if (p.second[1] == 0)
        {
            std::cout << std::format("未删除的空残余订单 order no {}: price {}, qty {}", p.first, p.second[0], p.second[1]) << std::endl;
        }
        // std::cout << std::format("order no {}: price {}, qty {}", p.first, p.second[0], p.second[1]) << std::endl;
        std::cout << std::format("{}: [{}, {}], ", p.first, p.second[0], p.second[1]);
    }
    std::cout << "}" << std::endl;
}

void OrderBook::print_tick_info_snapshots(void)
{
    std::cout << std::format("tick_info[{}]:", tick_info_snapshots_.size()) << std::endl;
    for (auto p : tick_info_snapshots_)
    {
        std::cout << std::format("{}: type {}, vol {}, money {}, direction {}, tier0 {}, bid1 {} {}, ask1 {} {}", p.time, p.tick_type, p.tick_vol,
            p.tick_money, p.tick_direc, p.orderbook_tier[0], p.bid20prices[0], p.bid20qty[0], p.ask20prices[0], p.ask20qty[0]) << std::endl;
    }
}

void OrderBook::print_money_balance(void)
{
    double total = sm_order_cum_vol_ + mid_order_cum_vol_ + lg_order_cum_vol_ + slg_order_cum_vol_;
    double total_abs = std::abs(sm_order_cum_vol_) + std::abs(mid_order_cum_vol_) + std::abs(lg_order_cum_vol_) + std::abs(slg_order_cum_vol_);
    std::cout << std::format("small: {:.2f} {:.2f}%, mid: {:.2f} {:.2f}%, large: {:.2f} {:.2f}%, super large: {:.2f} {:.2f}%, total {}, total_abs {}",
        sm_order_cum_vol_, sm_order_cum_vol_ / total_abs * 100.0, mid_order_cum_vol_, mid_order_cum_vol_ / total_abs * 100.0,
        lg_order_cum_vol_, lg_order_cum_vol_ / total_abs * 100.0, slg_order_cum_vol_, slg_order_cum_vol_ / total_abs * 100.0, total, total_abs) << std::endl;
}

void OrderBook::print_vol_stat(void)
{
    std::cout << std::format("总申买量: {}, 总申卖量: {}", total_buy_qty_, total_sell_qty_) << std::endl;
    for (auto o : orderbook_)
    {
        std::cout << std::format("价格: {:.2f}, 申买量: {}, 申卖量: {}", o.first / 1000.0, o.second.total_qty[0], o.second.total_qty[1]) << std::endl;
    }
}

void OrderBook::print_vol_lg_stat(void)
{
    std::cout << "按价格统计大单的申买、申卖量: " << std::endl;
    for (auto o : orderbook_vol_lg_)
    {
        std::cout << std::format("价格: {:.2f}, 大单申买量: {}, 大单申卖量: {}", o.first / 1000.0, o.second[0], o.second[1]) << std::endl;
    }
}

void OrderBook::log(LOG_LEVEL level, std::string msg)
{
    if (level >= log_level_)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cerr << std::format("[{}] {}", std::chrono::current_zone()->to_local(tp_utc), msg) << std::endl;
    }
}

void OrderBook::log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg)
{
    if (level >= sys_level)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cerr << std::format("[{}] {}", std::chrono::current_zone()->to_local(tp_utc), msg) << std::endl;
    }
}

void OrderBook::export_tick_info_(Tick_info &tick_info)
{
    int i = 0;
    auto it = it_bid1_;
    while (i < 20 && it != orderbook_.end())   // 20档买价
    {
        // 1. 从 orderbook_ 中取出 20 个价位
        // 2. 将这些价位的订单量、订单笔数、大单订单量等信息填入 tick_info 中
        tick_info.bid20prices[i] = it->first;
        tick_info.bid20qty[i] = it->second.total_qty[0];
        tick_info.bid20num[i] = it->second.orders[0].size();
        tick_info.bid20lgqty[i] = orderbook_vol_lg_[it->first][0];
        it--;
        i++;
    }
    i = 0;
    it = it_ask1_;
    while (i < 20 && it != orderbook_.end())   // 20档卖价
    {
        tick_info.ask20prices[i] = it->first;
        tick_info.ask20qty[i] = it->second.total_qty[1];
        tick_info.ask20num[i] = it->second.orders[1].size();
        tick_info.ask20lgqty[i] = orderbook_vol_lg_[it->first][1];
        it++;
        i++;
    }
    tick_info.total_buy_qty = total_buy_qty_;
    tick_info.total_sell_qty = total_sell_qty_;
    tick_info.prev_close = prev_close_;
    tick_info.latest = latest_;
    tick_info.mid_price = (latest_buy_price_ + latest_sell_price_) / 2;
    tick_info.spread = ask1_ - bid1_;
    tick_info.sm_order_cum_vol = sm_order_cum_vol_;
    tick_info.mid_order_cum_vol = mid_order_cum_vol_;
    tick_info.lg_order_cum_vol = lg_order_cum_vol_;
    tick_info.slg_order_cum_vol = slg_order_cum_vol_;
    tick_info.buy_order_index = buy_order_index_;
    tick_info.sell_order_index = sell_order_index_;
}

void OrderBook::print_tier_dict(void)
{
    std::cout << std::format("disc_coef: {}, increment: {}, tier_dict[{}]:", disc_coef_, increment_, tier_dict_.size()) << std::endl;
    for (auto p : tier_dict_)
    {
        std::cout << p.first << " " << p.second << std::endl;
    }
}

// 上交所竞价逐笔合并行情
bool OrderBook::put_instruction(const order_raw_sh &order, int &index)
{
    if (market_state_ == SHMarketState::START) // 此时只能由指令订单'开市集合竞价'来启动市场，或者'停牌'
    {
        if (order.type == "S" && order.tickBSFlag == "OCALL")
        {
            market_state_ = SHMarketState::OCALL;
            whether_snapshot_ = false;
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::OCALL, 进入开盘集合竞价"));
            index = -1;
            total_inst_state_++;  // 总指令订单数
            return true;
        }
        else if (order.type == "S" && order.tickBSFlag == "SUSP")
        {
            market_state_ = SHMarketState::SUSP;
            whether_snapshot_ = false;
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::SUSP, 进入停牌"));
            index = -1;
            total_inst_state_++;  // 总指令订单数
            return true;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("market_state_ == SHMarketState::START, order.type = {}, order.tickBSFlag = {}", order.type, order.tickBSFlag));
            return false;
        }
    }
    if (order.type == "S") // 指令订单
    {
        total_inst_state_++;
        if (order.tickBSFlag == "TRADE") // 开始连续自动撮合
        {
            market_state_ = SHMarketState::TRADE;
            whether_snapshot_ = true;
            // log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::TRADE, 进入连续自动撮合, 准备计算买一、卖一价"));
            // 计算买一价和卖一价，根据集合竞价期间是否有成交、是否有剩余买单、卖单等条件分为 11 种情况，case 0 - 10
            // 集合竞价有成交: case 0 - 6
            //   0: 成交价有卖单，无买单
            //   1: 成交价有卖单，更低价位上有买单
            //   2: 成交价有买单，更高价位上有卖单
            //   3: 成交价无买单、无卖单，其它价位有买单和卖单
            //   4: 成交价无买单、无卖单，其它价位只有卖单
            //   5: 成交价无买单、无卖单，其它价位只有买单
            //   6: 完全无剩余买单和剩余卖单，应极为罕见
            // 集合竞价无成交: case 7 - 10
            //   7: 无成交，有买单，有卖单
            //   8: 无成交，有买单，无卖单
            //   9: 无成交，无买单，有卖单
            //   10: 无成交，无买单，无卖单
            bid1_ = lower_limit_;
            it_bid1_ = orderbook_.end();
            ask1_ = upper_limit_;
            it_ask1_ = orderbook_.end();
            if (latest_ > 0)  // 集合竞价期间有成交，case 0 - 6
            {
                // print_orderbook(latest_);
                auto it = orderbook_.lower_bound(latest_);
                if (it != orderbook_.end())  // 成交价或某个低于成交价的价位是有买单或卖单的，case 0, 1, 2, 3, 5
                {
                    if (!it->second.orders[0].empty())   // 有买单，case 2, 3, 5
                    {
                        bid1_ = it->first;
                        it_bid1_ = it;
                        it++;  // 如果还有卖单，一定在更高价位上
                        if (it != orderbook_.end())  // 更高价位上有卖单，case 2, 3
                        {
                            ask1_ = it->first;
                            it_ask1_ = it;
                        }   // else ask1_ = upper_limit_ 完全无卖单，卖一为涨停价，case 5
                    }
                    else    // 没有买单，case 0, 1
                    {
                        ask1_ = it->first;  // 本价位肯定有卖单
                        it_ask1_ = it;
                        it++;   // 如果还有买单，一定在更低价位上
                        if (it != orderbook_.end())   // 更低价位上有买单，case 1
                        {
                            bid1_ = it->first;
                            it_bid1_ = it;
                        }   // else bid1_ = lower_limit_ 完全无买单，买一为跌停价, case 0
                    }
                }
                else  // 成交价或低于成交价的价位没有买单和卖单，case 4, 6
                {
                    it++;  // 如果还有卖单，一定在更高价位上
                    if (it != orderbook_.end())  // 更高价位上有卖单，case 4
                    {
                        ask1_ = it->first;
                        it_ask1_ = it;
                    }   // else ask1_ = upper_limit_ 完全无剩余买单和卖单，应极为罕见，卖一为涨停价，case 6
                    // bid1_ = lower_limit_;  没有剩余买单，买一为跌停价
                }
            }
            else   // 集合竞价期间没有成交，case 7 - 10
            {
                if (total_buy_qty_ > 0)  // 有买单, case 7, 8
                {
                    auto it = orderbook_.begin();
                    while (!it->second.orders[0].empty() && it != orderbook_.end())   // 从最低价开始找，找到第一个没有买单的价位
                    {
                        it++;
                    }
                    if (it != orderbook_.end())  // 有卖单，case 7
                    {
                        ask1_ = it->first;
                        it_ask1_ = it;
                        it--;   // 回退一个价位是最高买价
                        bid1_ = it->first;
                        it_bid1_ = it;
                    }
                    else // 全是买单，没有卖单，case 8
                    {
                        it_bid1_ = std::prev(orderbook_.end());
                        bid1_ = it_bid1_->first; // ask1_ 保持 upper_limit_
                    }
                }
                else  // 没有买单，case 9, 10
                {
                    if (total_sell_qty_ > 0)  // 有卖单，case 9
                    {
                        ask1_ = orderbook_.begin()->first;  // bid1_ = lower_limit_;  没有买单，买一为跌停价
                        it_ask1_ = orderbook_.begin();
                    }  // 完全无买单和卖单，应极为罕见，case 10
                }
            }
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::TRADE, 进入连续自动撮合, 集合竞价期间成交价为 {}, 当前买一价为 {} {}, 卖一价为 {} {}",
                latest_, bid1_, it_bid1_->first, ask1_, it_ask1_->first));

            // 生成的第一条 tick 信息
            auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_);
            buy_order_index_ = sell_order_index_ = 0;
            export_tick_info_(tick_info);
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, xxx_fake_orderbook_tier_);
            index = tick_info_snapshots_.size() - 1;
            return true;
        }
        else if (order.tickBSFlag == "CCALL") // 收盘集合竞价  XXX 似乎应该也要处理未完结订单的资金流向
        {
            market_state_ = SHMarketState::CCALL;
            whether_snapshot_ = false;
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::CCALL, 进入收盘集合竞价"));
            index = -1;
            return true;
        }
        else if (order.tickBSFlag == "CLOSE") // 闭市
        {
            market_state_ = SHMarketState::CLOSE;
            whether_snapshot_ = false;
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::CLOSE, 进入闭市"));
            // 生成的最后一条 tick 信息
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, xxx_fake_orderbook_tier_);
            auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_);
            buy_order_index_ = sell_order_index_ = 0;
            export_tick_info_(tick_info);
            index = tick_info_snapshots_.size() - 1;
            return true;
        }
        else if (order.tickBSFlag == "ENDTR") // 交易结束
        {
            market_state_ = SHMarketState::ENDTR;
            whether_snapshot_ = false;
            log(LOG_LEVEL::DEBUG, std::format("market_state_ = SHMarketState::ENDTR, 进入交易结束, 处理委托 {}, 撤单 {}, 成交 {}, 指令 {}, 共 {} 条消息",
                total_inst_order_, total_inst_cancel_, total_inst_trans_, total_inst_state_,
                total_inst_order_ + total_inst_cancel_ + total_inst_trans_ + total_inst_state_));
            index = -1;
            return true;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("订单指令, 未知的 order.tickBSFlag = {}", order.tickBSFlag));
            index = -1;
            return false;
        }
    }
    if (order.type != "A" && order.type != "D" && order.type != "T") // 非法的订单类型
    {
        bad_thing(__func__, __LINE__, std::format("非法的订单类型, order.type = {}", order.type));
        return false;
    }

    if (order.type == "A") // 新增委托订单：1、集合竞价期间原始订单；2、连续竞价期间原始订单或次生订单
    {
        TradeType direction = (order.tickBSFlag == "B") ? TradeType::BUY : TradeType::SELL;
        int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标
        int order_price = std::lround(order.price * 1000.0);      // 申报价格，以厘为单位
        int64_t order_no = (direction == TradeType::BUY) ? order.buyOrderNo : order.sellOrderNo;
        int64_t order_qty = order.qty;
        double qty_origin = order_qty + order.tradeMoney; // 原始申报数量，对于原始订单 TradeMoney 为0，对于次生订单，TraceMoney 为已成交数量
        bool is_derived_order = (order.tradeMoney > 0.0);  // 是否为次生订单

        total_inst_order_++;   // 总委托订单数
        orderpool_[order_no][0] = order_price; // 订单池以订单编号为索引，值为[价格, 数量]
        orderpool_[order_no][1] = order_qty;

        // 处理可能的完结的订单
        if (pending_buy_no_ != 0 && pending_sell_no_ != 0)
        {
            bad_thing(__func__, __LINE__, std::format("同时存在未完结买订单 {} 和卖订单 {}", pending_buy_no_, pending_sell_no_));
        }
        if (is_derived_order)  // 本订单为次生订单
        {
            if (direction == TradeType::BUY) // 本条订单是次生买订单
            {
                if (order_no != pending_buy_no_)
                {
                    bad_thing(__func__, __LINE__, std::format("本条次生买订单不是当前未完结主动买订单的后续订单, order_no = {}, pending_buy_no_ = {}",
                        order_no, pending_buy_no_));
                    return false;
                }
                if (pending_buy_qty_ != static_cast<int64_t>(order.tradeMoney))
                {
                    bad_thing(__func__, __LINE__, std::format("次生委托买订单 {} 的已成交量不等于累积成交量, pending_buy_qty_ = {}, tradeMoney = {}",
                        order_no, pending_buy_qty_, order.tradeMoney));
                    return false;
                }
                if (qty_origin < mid_threshold_)        // 原始订单是5万以下小买单
                {
                    sm_order_cum_vol_ += pending_buy_money_;
                }
                else if (qty_origin <= lg_threshold_)   // 原始订单是5万至30万中买单
                {
                    mid_order_cum_vol_ += pending_buy_money_;
                }
                else if (qty_origin <= slg_threshold_)  // 原始订单是30万至100万大买单
                {
                    lg_order_cum_vol_ += pending_buy_money_;
                }
                else  // 原始订单是100万以上超大买单
                {
                    slg_order_cum_vol_ += pending_buy_money_;
                }
                pending_buy_no_ = 0;
                pending_buy_money_ = 0.0;
                pending_buy_qty_ = 0;
            }
            else // 本条订单是次生卖订单
            {
                if (order_no != pending_sell_no_)
                {
                    bad_thing(__func__, __LINE__, std::format("本条次生卖订单不是当前未完结主动卖订单的后续订单, order_no = {}, pending_sell_no_ = {}",
                        order_no, pending_sell_no_));
                    return false;
                }
                if (pending_sell_qty_ != static_cast<int64_t>(order.tradeMoney))
                {
                    bad_thing(__func__, __LINE__, std::format("次生委托卖订单 {} 的已成交量不等于累积成交量, pending_sell_qty_ = {}, tradeMoney = {}",
                        order_no, pending_sell_qty_, order.tradeMoney));
                    return false;
                }
                if (qty_origin < mid_threshold_)        // 原始订单是5万以下小卖单
                {
                    sm_order_cum_vol_ -= pending_sell_money_;
                }
                else if (qty_origin <= lg_threshold_)   // 原始订单是5万至30万中卖单
                {
                    mid_order_cum_vol_ -= pending_sell_money_;
                }
                else if (qty_origin <= slg_threshold_)  // 原始订单是30万至100万大卖单
                {
                    lg_order_cum_vol_ -= pending_sell_money_;
                }
                else  // 原始订单是100万以上超大卖单
                {
                    slg_order_cum_vol_ -= pending_sell_money_;
                }
                pending_sell_no_ = 0;
                pending_sell_money_ = 0.0;
                pending_sell_qty_ = 0;
            }
        }
        else  // 本次订单为原始订单
        {
            if (pending_buy_no_ != 0) // 终结未完结的主动买订单，无法确定是全部成交还是未成交部分自动撤
            {
                if (pending_buy_qty_ < mid_threshold_)       // 已成交部分为小单
                {
                    sm_order_cum_vol_ += pending_buy_money_;
                }
                else if (pending_buy_qty_ <= lg_threshold_) // 已成交部分为中单
                {
                    mid_order_cum_vol_ += pending_buy_money_;
                }
                else if (pending_buy_qty_ <= slg_threshold_) // 已成交部分为大单
                {
                    lg_order_cum_vol_ += pending_buy_money_;
                }
                else // 已成交部分为超大单
                {
                    slg_order_cum_vol_ += pending_buy_money_;
                }
                pending_buy_no_ = 0;
                pending_buy_money_ = 0.0;
                pending_buy_qty_ = 0;
            }
            else if (pending_sell_no_ != 0) // 终结未完结的主动卖订单，无法确定是全部成交还是未成交部分自动撤
            {
                if (pending_sell_qty_ < mid_threshold_)       // 已成交部分为小单
                {
                    sm_order_cum_vol_ -= pending_sell_money_;
                }
                else if (pending_sell_qty_ <= lg_threshold_) // 已成交部分为中单
                {
                    mid_order_cum_vol_ -= pending_sell_money_;
                }
                else if (pending_sell_qty_ <= slg_threshold_) // 已成交部分为大单
                {
                    lg_order_cum_vol_ -= pending_sell_money_;
                }
                else // 已成交部分为超大单
                {
                    slg_order_cum_vol_ -= pending_sell_money_;
                }
                pending_sell_no_ = 0;
                pending_sell_money_ = 0.0;
                pending_sell_qty_ = 0;
            }
        }

        if (qty_origin <= mid_threshold_) // 将本订单按原始数量加入不同大小订单编号集
        {
            orderpool_sm_.insert(order_no);
        }
        else if (qty_origin <= lg_threshold_)
        {
            orderpool_mid_.insert(order_no);
        }
        else if (qty_origin <= slg_threshold_)
        {
            orderpool_lg_.insert(order_no);
        }
        else
        {
            orderpool_slg_.insert(order_no);
        }

        if (qty_origin > lg_threshold_) // 以价格为索引的30万以上大单当前有效总量统计，不区分大单和超大单了
        {
            if (orderbook_vol_lg_.contains(order_price))
            {
                orderbook_vol_lg_[order_price][direct_index] += order_qty;
            }
            else
            {
                orderbook_vol_lg_[order_price][direct_index] = order_qty;
            }
        }

        orderbook_[order_price].orders[direct_index][order_no] = order_qty; // 订单薄以价格为索引，值为[买单, 卖单]，买单和卖单都是以订单编号为索引，值为数量
        orderbook_[order_price].total_qty[direct_index] += order_qty;

        // 连续竞价阶段在新增委托时要重新计算买一、卖一并生成 tick 信息
        if (market_state_ == SHMarketState::TRADE)
        {
            if (direction == TradeType::BUY) // 本新增订单是一个买单
            {
                buy_order_index_ = order_no;
                sell_order_index_ = 0;
                if (order_price > bid1_)
                {
                    bid1_ = order_price;
                    if (total_buy_qty_ > 0) // 原来就有买单
                    {
                        it_bid1_++;  // 本买单价在原来买一价更高的相邻价位
                    }
                    else
                    {
                        it_bid1_ = orderbook_.begin();  // 本买单是当前唯一买单，肯定是最底价位
                    }
                }
            }
            else  // 本新增订单是一个卖单
            {
                buy_order_index_ = 0;
                sell_order_index_ = order_no;
                if (order_price < ask1_)
                {
                    ask1_ = order_price;
                    if (total_sell_qty_ > 0) // 原来就有卖单
                    {
                        it_ask1_--;  // 本卖单价在原来卖一价更低的相邻价位
                    }
                    else
                    {
                        it_ask1_ = std::prev(orderbook_.end());  // 本卖单是当前唯一卖单，肯定是最高价位
                    }
                }
            }
        }

        // XXX 待处理，计算 order_aggr

        if (direction == TradeType::BUY) // 当前总申买量和总申卖量
        {
            total_buy_qty_ += order_qty;
        }
        else
        {
            total_sell_qty_ += order_qty;
        }

        if (!tier_dict_.contains(order_price))
        {
            bad_thing(__func__, __LINE__, std::format("新增委托订单: 在涨跌千分点字典中未找到订单对应价位, order_price = {}, 不在涨跌停价之间？", order_price));
            return false;
        }
        int tier = tier_dict_[order_price]; // 根据申报价格查找应该属于哪一档
        orderbook_tier_[tier] += order.qty;

        if (!whether_snapshot_) // 只在连续竞价期间或集合竞价结束时生成Tick行情
        {
            index = -1;
            return true;
        }
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_ORDER,
        //     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, xxx_fake_orderbook_tier_);
        auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_ORDER,
            direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        export_tick_info_(tick_info);
        index = tick_info_snapshots_.size() - 1;
        tick_info.tick_vol = order_qty;
        tick_info.tick_money = order_qty * order.price;
    }
    else if (order.type == "D") // 撤销委托订单，即撤单
    {
        int64_t cancel_qty = order.qty;
        TradeType direction = (order.tickBSFlag == "B") ? TradeType::BUY : TradeType::SELL;
        int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标
        int cancel_price = std::lround(order.price * 1000.0);
        int64_t cancel_index = (direction == TradeType::BUY) ? order.buyOrderNo : order.sellOrderNo;

        total_inst_cancel_++;   // 总撤单订单数
        // 处理可能的完结的订单（上一条消息是成交信息）
        if (pending_buy_no_ != 0 && pending_sell_no_ != 0)
        {
            bad_thing(__func__, __LINE__, std::format("撤单消息，但同时存在未完结买订单 {} 和卖订单 {}", pending_buy_no_, pending_sell_no_));
        }
        if (pending_buy_no_ != 0)   // 终结未完结的买订单（刚好完全成交或自动撤的市价单）
        {
            if (pending_buy_qty_ < mid_threshold_)       // 已成交部分为小单
            {
                sm_order_cum_vol_ += pending_buy_money_;
            }
            else if (pending_buy_qty_ <= lg_threshold_) // 已成交部分为中单
            {
                mid_order_cum_vol_ += pending_buy_money_;
            }
            else if (pending_buy_qty_ <= slg_threshold_) // 已成交部分为大单
            {
                lg_order_cum_vol_ += pending_buy_money_;
            }
            else // 已成交部分为超大单
            {
                slg_order_cum_vol_ += pending_buy_money_;
            }
            pending_buy_no_ = 0;
            pending_buy_money_ = 0.0;
            pending_buy_qty_ = 0;
        }
        else if (pending_sell_no_ != 0) // 终结未完结的卖订单（刚好完全成交或自动撤的市价单）
        {
            if (pending_sell_qty_ < mid_threshold_)       // 已成交部分为小单
            {
                sm_order_cum_vol_ -= pending_sell_money_;
            }
            else if (pending_sell_qty_ <= lg_threshold_) // 已成交部分为中单
            {
                mid_order_cum_vol_ -= pending_sell_money_;
            }
            else if (pending_sell_qty_ <= slg_threshold_) // 已成交部分为大单
            {
                lg_order_cum_vol_ -= pending_sell_money_;
            }
            else // 已成交部分为超大单
            {
                slg_order_cum_vol_ -= pending_sell_money_;
            }
            pending_sell_no_ = 0;
            pending_sell_money_ = 0.0;
            pending_sell_qty_ = 0;
        }

        if (direction == TradeType::BUY) // 当前总申买量和总申卖量，先冲减，后面更新买一、卖一时要用于一些判断
        {
            if (total_buy_qty_ < cancel_qty)
            {
                bad_thing(__func__, __LINE__, std::format("撤单 {} 当前总申买量小于撤单量, total_buy_qty_ = {}, cancel_qty = {}", cancel_index, total_buy_qty_, cancel_qty));
                return false;
            }
            total_buy_qty_ -= cancel_qty;
        }
        else
        {
            if (total_sell_qty_ < cancel_qty)
            {
                bad_thing(__func__, __LINE__, std::format("撤单 {} 当前总申卖量小于撤单量, total_sell_qty_ = {}, cancel_qty = {}", cancel_index, total_sell_qty_, cancel_qty));
                return false;
            }
            total_sell_qty_ -= cancel_qty;
        }

        int r = orderbook_[cancel_price].orders[direct_index].erase(cancel_index); // 从订单薄中删除订单
        if (r == 0)                                                         // 未找到要删除的订单
        {
            bad_thing(__func__, __LINE__, std::format("在主订单薄中未找到要删除的订单, cancel_index = {}", cancel_index));
            return false;
        }
        if (market_state_ == SHMarketState::TRADE)  // 连续竞价阶段要更新买一、卖一价
        {
            if (cancel_price == bid1_) // 撤单价为买一价，说明被撤的订单是一个买单
            {
                buy_order_index_ = cancel_index;
                sell_order_index_ = 0;
                if (orderbook_[cancel_price].orders[0].empty()) // 该价位的买单都已删除
                {
                    if (total_buy_qty_ == 0) // 所有买单都已删除
                    {
                        bid1_ = lower_limit_;
                        it_bid1_ = orderbook_.end();
                    }
                    else  // 仍有买单，但已删除买一价
                    {
                        it_bid1_--;  // 新买一价为更低的相邻价位
                        bid1_ = it_bid1_->first;
                    }
                }
            }
            else if (cancel_price == ask1_)  // 撤单价为卖一价，说明被撤的订单是一个卖单
            {
                buy_order_index_ = 0;
                sell_order_index_ = cancel_index;
                if (orderbook_[cancel_price].orders[1].empty()) // 该价位的卖单都已删除
                {
                    if (total_sell_qty_ == 0) // 所有卖单都已删除
                    {
                        ask1_ = upper_limit_;
                        it_ask1_ = orderbook_.end();
                    }
                    else  // 仍有卖单，但已删除卖一价
                    {
                        it_ask1_++;  // 新卖一价为更高的相邻价位
                        ask1_ = it_ask1_->first;
                    }
                }
            }
        }
        //log(LOG_LEVEL::DEBUG, std::format("after delete: bid1 {} {} ask1 {} {}", bid1_, it_bid1_->first, ask1_, it_ask1_->first));

        if (orderbook_[cancel_price].orders[0].empty() && orderbook_[cancel_price].orders[1].empty()) // 该价位的订单都已删除
        {
            orderbook_.erase(cancel_price);
        }
        if (orderbook_.contains(cancel_price)) // 该价位上还有订单，要调减该价位的该方向的订单量总量
        {
            if (orderbook_[cancel_price].total_qty[direct_index] < cancel_qty) // 该价位的订单量小于要删除的数量
            {
                bad_thing(__func__, __LINE__, std::format("在订单量总量统计中当前量小于要删除的数量, cancel_price = {}, direct_index = {}, cancel_qty = {}", cancel_price, direct_index, cancel_qty));
                return false;
            }
            orderbook_[cancel_price].total_qty[direct_index] -= cancel_qty;
        }

        r = orderpool_.erase(cancel_index); // 从订单池中删除订单
        if (r == 0)                         // 未找到要删除的订单
        {
            bad_thing(__func__, __LINE__, std::format("在订单池中未找到要删除的订单, cancel_index = {}", cancel_index));
            return false;
        }

        bool was_lg = false;  // 删除前曾属于大单或超大单（之前加入时是以原始订单量来计算的，现在原始订单量的信息已经没有了）
        if (orderpool_sm_.contains(cancel_index)) // 属于小单
        {
            orderpool_sm_.erase(cancel_index);
        }
        else if (orderpool_mid_.contains(cancel_index)) // 属于中单
        {
            orderpool_mid_.erase(cancel_index);
        }
        else if (orderpool_lg_.contains(cancel_index)) // 属于大单
        {
            was_lg = true;
            orderpool_lg_.erase(cancel_index);
        }
        else if (orderpool_slg_.contains(cancel_index)) // 属于超大单
        {
            was_lg = true;
            orderpool_slg_.erase(cancel_index);
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("在4种订单池子中都未找到要删除的订单, cancel_index = {}", cancel_index));
            return false;
        }

        if (was_lg)
        {
            if (!orderbook_vol_lg_.contains(cancel_price)) // 以价格为索引的大单量总量统计，该价位未曾有大单或超大单
            {
                bad_thing(__func__, __LINE__, std::format("在大单量总量统计中未找到要减量的价位, cancel_price = {}, direct_index = {}, cancel_qty = {}",
                    cancel_price, direct_index, cancel_qty));
                return false;
            }
            else if (orderbook_vol_lg_[cancel_price][direct_index] < cancel_qty)  // 该价位的大单量小于要删除的数量
            {
                bad_thing(__func__, __LINE__, std::format("在大单量总量统计中当前量小于要删除的数量, cancel_price = {}, direct_index = {}, cancel_qty = {}, vol = {}, "
                                                          "RecvTime = {}, cancel_index = {}",
                                                          cancel_price, direct_index, cancel_qty, orderbook_vol_lg_[cancel_price][direct_index], order.recvTime, cancel_index));
                return false;
            }
            orderbook_vol_lg_[cancel_price][direct_index] -= cancel_qty;
            if (orderbook_vol_lg_[cancel_price][0] == 0 && orderbook_vol_lg_[cancel_price][1] == 0) // 该价位的大单都已删除
            {
                orderbook_vol_lg_.erase(cancel_price);
            }
        }

        if (!tier_dict_.contains(cancel_price))
        {
            bad_thing(__func__, __LINE__, std::format("撤单: 在涨跌千分点字典中未找到订单对应价位, cancel_price = {}, 不在涨跌停价之间？", cancel_price));
            return false;
        }
        int tier = tier_dict_[cancel_price];
        if (orderbook_tier_[tier] < order.qty)
        {
            bad_thing(__func__, __LINE__, std::format("撤单：在涨跌千分点挂单量统计中当前量小于要删除的数量, tier = {}, order.qty = {}", tier, order.qty));
            return false;
        }
        orderbook_tier_[tier] -= order.qty;

        if (!whether_snapshot_) // 只在连续竞价期间或集合竞价结束时生成Tick行情
        {
            index = -1;
            return true;
        }
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_CANCEL,
        //     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, xxx_fake_orderbook_tier_);
        auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_CANCEL,
            direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        export_tick_info_(tick_info);
        index = tick_info_snapshots_.size() - 1;
        tick_info.tick_vol = cancel_qty;
        tick_info.tick_money = cancel_qty * order.price;
    }
    else if (order.type == "T") // 成交信息
    {
        int64_t trade_qty = order.qty;
        int64_t buy_order = buy_order_index_ = order.buyOrderNo;
        int64_t sell_order = sell_order_index_ = order.sellOrderNo;

        total_inst_trans_++;   // 总成交订单数
        latest_ = std::lround(order.price * 1000.0);  // 以厘为单位的成交价格

        if (market_state_ == SHMarketState::OCALL || market_state_ == SHMarketState::CCALL) // 开市集合竞价和收盘集合竞价，成交双方订单肯定都在订单池里
        {
            if (!orderpool_.contains(buy_order)) // 买单不在订单池中
            {
                bad_thing(__func__, __LINE__, std::format("集合竞价期间在订单池中未找到成交买单, buy_order = {}", buy_order));
                return false;
            }
            if (!orderpool_.contains(sell_order)) // 卖单不在订单池中
            {
                bad_thing(__func__, __LINE__, std::format("集合竞价期间在订单池中未找到成交卖单, sell_order = {}", sell_order));
                return false;
            }
            // 集合竞价阶段的买、卖、成交价可能都不相等
            int buy_order_price = orderpool_[buy_order][0];   // 原始买单报价，单位厘
            int sell_order_price = orderpool_[sell_order][0]; // 原始卖单报价，单位厘

            // 不同大小订单的净流入资金统计，注意这里没有把订单从数量大小分类池中去除，因为订单总数不多，所以性能影响可以接受
            // 撤单时删除是因为不必检查是否完全成交，这里要检查是完全成交才能删除，所以删除的代价稍大
            bool was_lg_buy = false;
            if (orderpool_sm_.contains(buy_order)) // 买单属于小单
                sm_order_cum_vol_ += order.tradeMoney;
            else if (orderpool_mid_.contains(buy_order)) // 买单属于中单
                mid_order_cum_vol_ += order.tradeMoney;
            else if (orderpool_lg_.contains(buy_order)) // 买单属于大单
            {
                lg_order_cum_vol_ += order.tradeMoney;
                was_lg_buy = true;
            }
            else if (orderpool_slg_.contains(buy_order)) // 买单属于超大单
            {
                slg_order_cum_vol_ += order.tradeMoney;
                was_lg_buy = true;
            }
            else
            {
                bad_thing(__func__, __LINE__, std::format("集合竞价阶段成交信息: 在4种订单池子中都未找到买单, buy_order = {}", buy_order));
                return false;
            }
            bool was_lg_sell = false;
            if (orderpool_sm_.contains(sell_order)) // 卖单属于小单
                sm_order_cum_vol_ -= order.tradeMoney;
            else if (orderpool_mid_.contains(sell_order)) // 卖单属于中单
                mid_order_cum_vol_ -= order.tradeMoney;
            else if (orderpool_lg_.contains(sell_order)) // 卖单属于大单
            {
                lg_order_cum_vol_ -= order.tradeMoney;
                was_lg_sell = true;
            }
            else if (orderpool_slg_.contains(sell_order)) // 卖单属于超大单
            {
                slg_order_cum_vol_ -= order.tradeMoney;
                was_lg_sell = true;
            }
            else
            {
                bad_thing(__func__, __LINE__, std::format("集合竞价阶段成交信息: 在4种订单池子中都未找到卖单, sell_order = {}", sell_order));
                return false;
            }

            orderpool_[buy_order][1] -= trade_qty;                  // 修改订单池中成交买订单的数量，减掉成交部分
            orderbook_[buy_order_price].orders[0][buy_order] -= trade_qty; // 修改主订单薄中买单表中该订单的数量，减掉成交部分
            orderbook_[buy_order_price].total_qty[0] -= trade_qty;
            if (orderpool_[buy_order][1] == 0)                      // 该买单已全部成交
            {
                orderpool_.erase(buy_order);
                orderbook_[buy_order_price].orders[0].erase(buy_order);
                if (orderbook_[buy_order_price].orders[0].empty() && orderbook_[buy_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    orderbook_.erase(buy_order_price);
                }
            }

            orderpool_[sell_order][1] -= trade_qty;                   // 修改订单池中成交卖订单的数量，减掉成交部分
            orderbook_[sell_order_price].orders[1][sell_order] -= trade_qty; // 修改主订单薄中卖单表中该订单的数量，减掉成交部分
            orderbook_[sell_order_price].total_qty[1] -= trade_qty;
            if (orderpool_[sell_order][1] == 0)                       // 该卖单已全部成交
            {
                orderpool_.erase(sell_order);
                orderbook_[sell_order_price].orders[1].erase(sell_order);
                if (orderbook_[sell_order_price].orders[0].empty() && orderbook_[sell_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    orderbook_.erase(sell_order_price);
                }
            }

            total_buy_qty_ -= trade_qty;                      // 调整当前总申买量
            total_sell_qty_ -= trade_qty;                     // 调整当前总申卖量

            if (was_lg_buy) // 买单属于大单
            {
                if (!orderbook_vol_lg_.contains(buy_order_price)) // 买单对应价位未曾有大单
                {
                    bad_thing(__func__, __LINE__, std::format("成交信息: 在大单量总量统计中未找到买单对应价位, buy_order_price = {}", buy_order_price));
                    return false;
                }
                else if (orderbook_vol_lg_[buy_order_price][0] < trade_qty) // 买单对应价位的大单量小于要删除的数量
                {
                    bad_thing(__func__, __LINE__, std::format("成交信息: 在大单量总量统计中当前量小于本次成交的数量, buy_order_price = {}, trade_qty = {}, vol = {}",
                        buy_order_price, trade_qty, orderbook_vol_lg_[buy_order_price][0]));
                    return false;
                }
                orderbook_vol_lg_[buy_order_price][0] -= trade_qty; // 调整大单统计薄中买单对应价位的总申买量
                if (orderbook_vol_lg_[buy_order_price][0] == 0 && orderbook_vol_lg_[buy_order_price][1] == 0) // 买单对应价位的大单都已删除
                {
                    orderbook_vol_lg_.erase(buy_order_price);
                }
            }
            if (was_lg_sell) // 卖单属于大单
            {
                if (!orderbook_vol_lg_.contains(sell_order_price)) // 卖单对应价位未曾有大单
                {
                    bad_thing(__func__, __LINE__, std::format("成交信息: 在大单量总量统计中未找到卖单对应价位, sell_order_price = {}", sell_order_price));
                    return false;
                }
                else if (orderbook_vol_lg_[sell_order_price][1] < trade_qty) // 卖单对应价位的大单量小于要删除的数量
                {
                    bad_thing(__func__, __LINE__, std::format("成交信息: 在大单量总量统计中当前量小于本次成交的数量, sell_order_price = {}, trade_qty = {}, vol = {}",
                        sell_order_price, trade_qty, orderbook_vol_lg_[sell_order_price][1]));
                    return false;
                }
                orderbook_vol_lg_[sell_order_price][1] -= trade_qty; // 调整大单统计薄中卖单对应价位的总申卖量
                if (orderbook_vol_lg_[sell_order_price][0] == 0 && orderbook_vol_lg_[sell_order_price][1] == 0) // 卖单对应价位的大单都已删除
                {
                    orderbook_vol_lg_.erase(sell_order_price);
                }
            }

            // XXX 需要吗？待处理，集合竞价期间更新买一、卖一
            if ((bid1_ > 0) && (ask1_ > 0) && whether_snapshot_)
            {
            }
            if (!tier_dict_.contains(buy_order_price)) // 买单对应价位不在涨跌停价内
            {
                bad_thing(__func__, __LINE__, std::format("成交信息: 在涨跌千分点字典中未找到买单对应价位, buy_order_price = {}, 不在涨跌停价内？", buy_order_price));
                return false;
            }
            int tier = tier_dict_[buy_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                bad_thing(__func__, __LINE__, std::format("成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}",
                    orderbook_tier_[tier], trade_qty));
                return false;
            }
            orderbook_tier_[tier] -= order.qty;
            if (!tier_dict_.contains(sell_order_price)) // 卖单对应价位不在涨跌停价内
            {
                bad_thing(__func__, __LINE__, std::format("成交信息: 在涨跌千分点字典中未找到卖单对应价位, sell_order_price = {}, 不在涨跌停价内？", sell_order_price));
                return false;
            }
            tier = tier_dict_[sell_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                bad_thing(__func__, __LINE__, std::format("成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}",
                    orderbook_tier_[tier], trade_qty));
                return false;
            }
            orderbook_tier_[tier] -= order.qty;
        }
        else if (market_state_ == SHMarketState::TRADE) // 连续竞价阶段的成交信息
        {
            // 1. 主动订单委托信息肯定尚未发布（如果它不产生次生订单的话则永远不会发布），但本条成交可能是同一笔主动订单的后续成交
            // 2. 被动订单委托信息肯定已经发布（它也可能是个次生订单）
            // 3. 同一笔主动订单成交信息连续发布
            // 4. 如果有次生订单，则在连续发布完成交信息后立即发布
            // 5. 如果不产生次生订单，则下一条信息可能是新的委托订单，也可能是下一笔成交信息，也可能是指令订单
            double trade_money = order.tradeMoney;
            int direct_index, other_index;
            TradeType direction;
            int multiplier = 1;
            int64_t active_order, passive_order;
            bool new_active_order = false;   // 是否是一次新的主动订单的第一条成交信息

            // 设置一些变量如 direct_index 等，处理可能的未完结订单
            if (order.tickBSFlag == "B") // 本次成交是主动买订单
            {
                direction = TradeType::BUY;
                active_order = buy_order;
                passive_order = sell_order;
                direct_index = 0;
                other_index = 1;
                if (pending_buy_no_ == 0) // 还没有未完结主动买订单，因此本次成交的主动买订单肯定是一个新的主动买单
                {
                    if (pending_sell_no_ != 0) // 有未完结主动卖订单，说明原主动卖订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                    {
                        if (pending_sell_qty_ <= mid_threshold_)
                            sm_order_cum_vol_ -= pending_sell_money_;
                        else if (pending_sell_qty_ <= lg_threshold_)
                            mid_order_cum_vol_ -= pending_sell_money_;
                        else if (pending_sell_qty_ <= slg_threshold_)
                            lg_order_cum_vol_ -= pending_sell_money_;
                        else
                            slg_order_cum_vol_ -= pending_sell_money_;
                        pending_sell_no_ = 0;      // 终结未完结的主动卖订单
                        pending_sell_qty_ = 0;
                        pending_sell_money_ = 0.0;
                    }
                    new_active_order = true;     // 本次成交是新未完结主动买订单的第一条成交信息
                    pending_buy_no_ = active_order;
                    pending_buy_qty_ = trade_qty;
                    pending_buy_money_ = trade_money;
                }
                else if (pending_buy_no_ != active_order) // 有未终结主动买订单，但不是当前订单，原主动买订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                {
                    if (pending_buy_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ += pending_buy_money_;
                    else
                        slg_order_cum_vol_ += pending_buy_money_;
                    new_active_order = true;     // 本次成交是新未完结主动买订单的第一条成交信息
                    pending_buy_no_ = active_order;
                    pending_buy_qty_ = trade_qty;
                    pending_buy_money_ = trade_money;
                }
                else // 已有未完结主动买订单，且是当前订单
                {
                    pending_buy_qty_ += trade_qty;
                    pending_buy_money_ += trade_money;
                }
                total_sell_qty_ -= trade_qty; // 被动的卖方订单之前是被计入总申卖量的，现在成交了要调整当前总申卖量
            }
            else if (order.tickBSFlag == "S") // 本次成交是主动卖订单
            {
                direction = TradeType::SELL;
                active_order = sell_order;
                passive_order = buy_order;
                direct_index = 1;
                other_index = 0;
                multiplier = -1;
                if (pending_sell_no_ == 0) // 还没有未完结主动卖订单，因此本次成交的主动卖订单肯定是一个新的主动卖单
                {
                    if (pending_buy_no_ != 0) // 有未完结主动买订单，说明原主动买订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                    {
                        if (pending_buy_qty_ <= mid_threshold_)
                            sm_order_cum_vol_ += pending_buy_money_;
                        else if (pending_buy_qty_ <= lg_threshold_)
                            mid_order_cum_vol_ += pending_buy_money_;
                        else if (pending_buy_qty_ <= slg_threshold_)
                            lg_order_cum_vol_ += pending_buy_money_;
                        else
                            slg_order_cum_vol_ += pending_buy_money_;
                        pending_buy_no_ = 0;      // 终结未完结的主动买订单
                        pending_buy_qty_ = 0;
                        pending_buy_money_ = 0.0;
                    }
                    new_active_order = true;     // 本次成交是新未完结主动卖订单的第一条成交信息
                    pending_sell_no_ = active_order;
                    pending_sell_qty_ = trade_qty;
                    pending_sell_money_ = trade_money;
                }
                else if (pending_sell_no_ != active_order) // 已有未完结主动卖订单，但不是当前订单，原主动卖订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                {
                    if (pending_sell_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ -= pending_sell_money_;
                    else
                        slg_order_cum_vol_ -= pending_sell_money_;
                    new_active_order = true;     // 本次成交是新未完结主动卖订单的第一条成交信息
                    pending_sell_no_ = active_order;
                    pending_sell_qty_ = trade_qty;
                    pending_sell_money_ = trade_money;
                }
                else // 已有未完结主动卖订单，且是当前订单
                {
                    pending_sell_qty_ += trade_qty;
                    pending_sell_money_ += trade_money;
                }
                total_buy_qty_ -= trade_qty; // 被动的买方订单之前是被计入总申买量的，现在成交了要调整当前总申买量
            }
            else
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 未知的tickBSFlag, tickBSFlag = {}", order.tickBSFlag));
                return false;
            }

            if (!orderpool_.contains(passive_order)) // 被动订单不在订单池中
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 被动订单不在订单池中, passive_order = {}, direction = {}", passive_order, order.tickBSFlag));
                return false;
            }
            if (orderpool_.contains(active_order)) // 主动订单出现在订单池中，也不合理
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 主动订单出现在订单池中, active_order = {}, direction = {}",
                    active_order, order.tickBSFlag));
                return false;
            }

            // 取出被动订单的申报价格
            int64_t passive_order_price = orderpool_[passive_order][0];
            if (!orderbook_.contains(passive_order_price)) // 被动订单对应价位不在订单薄中
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在订单薄中未找到被动订单对应价位, passive_order_price = {}", passive_order_price));
                return false;
            }
            if (!orderbook_[passive_order_price].orders[other_index].contains(passive_order)) // 被动订单不在订单薄中
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在订单薄中未找到被动订单, passive_order = {}", passive_order));
                return false;
            }

            // 被动方造成的资金净流向
            bool passive_is_lg = false;
            if (orderpool_sm_.contains(passive_order))
            {
                sm_order_cum_vol_ -= multiplier * trade_money;
            }
            else if (orderpool_mid_.contains(passive_order))
            {
                mid_order_cum_vol_ -= multiplier * trade_money;
            }
            else if (orderpool_lg_.contains(passive_order))
            {
                lg_order_cum_vol_ -= multiplier * trade_money;
                passive_is_lg = true;
            }
            else if (orderpool_slg_.contains(passive_order))
            {
                slg_order_cum_vol_ -= multiplier * trade_money;
                passive_is_lg = true;
            }
            // 主动方原始订单量还未知，所以要等到可能的次生委托单再作判断

            // 更新 orderpool_ 中的被动订单的未成交数量
            bool passive_fullfilled = false;
            orderpool_[passive_order][1] -= trade_qty;
            if (orderpool_[passive_order][1] == 0) // 该订单已全部成交
            {
                orderpool_.erase(passive_order);
                passive_fullfilled = true;
            }

            if (direction == TradeType::BUY && passive_order_price != ask1_)
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价成交: 买单成交，但被动订单价位 {} 不是卖一价 {}, 被动订单号 {}", passive_order_price, ask1_, passive_order));
                return false;
            }
            else if (direction == TradeType::SELL && passive_order_price != bid1_)
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价成交: 卖单成交，但被动订单价位 {} 不是买一价 {}, 被动订单号 {}", passive_order_price, bid1_, passive_order));
                return false;
            }
            // 更新 orderbook_ 中的被动订单的未成交数量
            orderbook_[passive_order_price].orders[other_index][passive_order] -= trade_qty; // 被动订单在订单薄中，成交了要从里边减去
            orderbook_[passive_order_price].total_qty[other_index] -= trade_qty;
            if (orderbook_[passive_order_price].orders[other_index][passive_order] == 0)     // 被动订单已全部成交
            {
                // log(LOG_LEVEL::DEBUG, std::format("连续竞价成交: 被动订单已全部成交, passive_order = {}", passive_order));
                orderbook_[passive_order_price].orders[other_index].erase(passive_order);

                // 连续竞价期间有成交时，要更新买一、卖一
                if (direction == TradeType::BUY) // 本次成交是主动买
                {
                    // log(LOG_LEVEL::DEBUG, std::format("连续竞价主动买成交：被动订单全部成交，卖一价 {} {} 剩余订单数：{}", ask1_, it_ask1_->first, it_ask1_->second.orders[1].size()));
                    if (it_ask1_->second.orders[1].empty())  // 本价位的卖单已全部成交
                    {
                        if (total_sell_qty_ > 0)  // 仍有卖单，但已删除卖一价，说明卖一价更高的相邻价位上有卖单
                        {
                            it_ask1_++;  // 新卖一价为更高的相邻价位
                            ask1_ = it_ask1_->first;
                        }
                        else
                        {
                            ask1_ = upper_limit_;
                            it_ask1_ = orderbook_.end();
                        }
                    }
                }
                else  // 本次成交是主动卖
                {
                    if (it_bid1_->second.orders[0].empty())  // 本价位的买单已全部成交
                    {
                        if (total_buy_qty_ > 0)  // 仍有买单，但已删除买一价，说明买一价更低的相邻价位上有买单
                        {
                            it_bid1_--;  // 新买一价为更低的相邻价位
                            bid1_ = it_bid1_->first;
                        }
                        else
                        {
                            bid1_ = lower_limit_;
                            it_bid1_ = orderbook_.end();
                        }
                    }
                }
                if (orderbook_[passive_order_price].orders[0].empty() && orderbook_[passive_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    if (orderbook_[passive_order_price].total_qty[other_index] != 0)
                    {
                        bad_thing(__func__, __LINE__, std::format("连续竞价成交: 订单薄价位 {} 中所有订单都已删除, 但被动订单量总量不为0, 被动订单号 {}, other_index {}",
                            passive_order_price, passive_order, other_index));
                        return false;
                    }
                    orderbook_.erase(passive_order_price);
                }
            }
            else  // 被动订单未全部成交
            {
                // log(LOG_LEVEL::DEBUG, std::format("连续竞价成交: 被动订单未全部成交, passive_order = {}", passive_order));
            }

            if (passive_is_lg)  // 被动订单是大单或超大单
            {
                if (!orderbook_vol_lg_.contains(passive_order_price)) // 以价格为索引的30万以上大单当前有效总量统计
                {
                    bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在大单量总量统计中未找到被动订单对应价位, passive_order_price = {}", passive_order_price));
                    return false;
                }
                if (orderbook_vol_lg_[passive_order_price][other_index] < trade_qty) // 该价位的大单量小于要删除的数量
                {
                    bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在大单量总量统计中当前量小于本次成交的数量, passive_order_price = {}, trade_qty = {}, vol = {}",
                        passive_order_price, trade_qty, orderbook_vol_lg_[passive_order_price][other_index]));
                    return false;
                }
                orderbook_vol_lg_[passive_order_price][other_index] -= trade_qty;
                if (orderbook_vol_lg_[passive_order_price][0] == 0 && orderbook_vol_lg_[passive_order_price][1] == 0) // 该价位的大单量总量都已删除
                {
                    orderbook_vol_lg_.erase(passive_order_price);
                }
            }
            if (!tier_dict_.contains(passive_order_price)) // 被动订单对应价位不在涨跌停价内
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在涨跌千分点字典中未找到被动订单对应价位, passive_order_price = {}, 不在涨跌停价内？", passive_order_price));
                return false;
            }
            int tier = tier_dict_[passive_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                bad_thing(__func__, __LINE__, std::format("连续竞价阶段成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}",
                    orderbook_tier_[tier], trade_qty));
                return false;
            }
            orderbook_tier_[tier] -= trade_qty;

            // 连续竞价期间要生成 tick 信息
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION,
            //     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, xxx_fake_orderbook_tier_);
            auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION,
                direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
            export_tick_info_(tick_info);
            index = tick_info_snapshots_.size() - 1;
            tick_info.tick_vol = trade_qty;
            tick_info.tick_money = trade_qty * order.price;
        }
    }

    return true;
}

arrow::Result<std::vector<order_raw_sh>> read_parquet_file(const std::string &filename)
{
    std::shared_ptr<arrow::io::ReadableFile> infile;
    ARROW_ASSIGN_OR_RAISE(infile, arrow::io::ReadableFile::Open(filename));
    std::unique_ptr<parquet::arrow::FileReader> reader;
    PARQUET_ASSIGN_OR_THROW(reader, parquet::arrow::OpenFile(infile, arrow::default_memory_pool()));
    std::shared_ptr<arrow::Table> parquet_table;
    PARQUET_THROW_NOT_OK(reader->ReadTable(&parquet_table));

    auto schemap = parquet_table->schema();
    std::cout << schemap->ToString() << std::endl;
    std::cout << std::format("parquet_table->num_columns(): {}, parquet_table->num_rows(): {}", parquet_table->num_columns(), parquet_table->num_rows()) << std::endl;

    std::vector<order_raw_sh> order_raw_sh_vec;
    auto recvTimes = std::static_pointer_cast<arrow::StringArray>(parquet_table->column(0)->chunk(0));
    auto bizIndexs = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(1)->chunk(0));
    auto channels = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(2)->chunk(0));
    auto securityIDs = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(3)->chunk(0));
    auto tickTimes = std::static_pointer_cast<arrow::StringArray>(parquet_table->column(4)->chunk(0));
    auto types = std::static_pointer_cast<arrow::StringArray>(parquet_table->column(5)->chunk(0));
    auto buyOrderNos = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(6)->chunk(0));
    auto sellOrderNos = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(7)->chunk(0));
    auto prices = std::static_pointer_cast<arrow::DoubleArray>(parquet_table->column(8)->chunk(0));
    auto qtys = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(9)->chunk(0));
    auto tradeMoneys = std::static_pointer_cast<arrow::DoubleArray>(parquet_table->column(10)->chunk(0));
    auto tickBSFlags = std::static_pointer_cast<arrow::StringArray>(parquet_table->column(11)->chunk(0));
    auto delays = std::static_pointer_cast<arrow::Int64Array>(parquet_table->column(12)->chunk(0));

    for (int i = 0; i < parquet_table->num_rows(); i++)
    {
        order_raw_sh_vec.push_back({std::string(recvTimes->Value(i)),
                                    bizIndexs->Value(i),
                                    channels->Value(i),
                                    securityIDs->Value(i),
                                    std::string(tickTimes->Value(i)),
                                    std::string(types->Value(i)),
                                    buyOrderNos->Value(i),
                                    sellOrderNos->Value(i),
                                    prices->Value(i),
                                    qtys->Value(i),
                                    tradeMoneys->Value(i),
                                    std::string(tickBSFlags->Value(i)),
                                    delays->Value(i)});
    }
    return order_raw_sh_vec;
}

arrow::Status RunSH(const std::string &filename, const std::string &upper_limit, const std::string &lower_limit, const std::string &prev_close)
{
    std::vector<order_raw_sh> order_raw_sh_vec;
    auto res = read_parquet_file(filename);
    if (res.ok())
    {
        order_raw_sh_vec = res.ValueOrDie();
    }
    else
    {
        std::cerr << res.status() << std::endl;
        return res.status();
    }
    std::cout << std::format("order_raw_sh_vec.size(): {}", order_raw_sh_vec.size()) << std::endl;
    OrderBook o1{atoi(upper_limit.c_str()), atoi(lower_limit.c_str()), atoi(prev_close.c_str()), false, Market::SH, LOG_LEVEL::DEBUG};
    // o1.print_tier_dict();
    for (auto &order : order_raw_sh_vec)
    {
        int index = -1;
        o1.put_instruction(order, index);
    }
    // o1.print_orderbook_tier();
    o1.print_tick_info_snapshots();
    // o1.print_money_balance();
    // o1.print_vol_stat();
    // o1.print_vol_lg_stat();
    // o1.print_orderbook();
    // o1.print_orderpool();
    // o1.print_orderbook(28140);
    return arrow::Status::OK();
}

// RecvTime,ChannelNo,ApplSeqNum,StreamID,SecurityID,SecurityIDSource,Price,OrderQty,Side,TransactTime,OrdType,Delay
// 对字符串和时间戳使用 char *，也许快一点
arrow::Status read_sz_order_csv(const std::string &filename, std::vector<order_raw_sz> &order_raw_sz_vec)
{
    io::CSVReader<12> in(filename);
    in.read_header(io::ignore_extra_column, "RecvTime", "ChannelNo", "ApplSeqNum", "StreamID", "SecurityID", "SecurityIDSource",
                   "Price", "OrderQty", "Side", "TransactTime", "OrdType", "Delay");
    const char *recvTime, *streamID, *securityID, *securityIDSource, *transactTime;
    int64_t applSeqNum, orderQty;
    int channelNo, delay, side, ordType;
    double price;
    while (in.read_row(recvTime, channelNo, applSeqNum, streamID, securityID, securityIDSource, price, orderQty, side,
                       transactTime, ordType, delay))
    {
        order_raw_sz_vec.push_back({parse_time_to_microseconds(recvTime), channelNo, applSeqNum, streamID, securityID, securityIDSource, price,
                                    orderQty, static_cast<char>(side), transactTime, static_cast<char>(ordType), delay});
    }
    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("read_sz_order_csv_cstr: {} records read", order_raw_sz_vec.size()));
    return arrow::Status::OK();
}

// RecvTime,ChannelNo,ApplSeqNum,StreamID,BidApplSeqNum,OfferApplSeqNum,SecurityID,SecurityIDSource,LastPx,LastQty,ExecType,TransactTime,Delay
// 对字符串和时间戳使用 char *，也许快一点
arrow::Status read_sz_transaction_csv(const std::string &filename, std::vector<transaction_raw_sz> &transaction_raw_sz_vec)
{
    io::CSVReader<13> in(filename);
    in.read_header(io::ignore_extra_column, "RecvTime", "ChannelNo", "ApplSeqNum", "StreamID", "BidApplSeqNum", "OfferApplSeqNum",
                   "SecurityID", "SecurityIDSource", "LastPx", "LastQty", "ExecType", "TransactTime", "Delay");
    const char *recvTime, *streamID, *securityID, *securityIDSource, *transactTime;
    int64_t applSeqNum, bidApplSeqNum, offerApplSeqNum, lastQty;
    int channelNo, delay, execType;
    double lastPx;
    while (in.read_row(recvTime, channelNo, applSeqNum, streamID, bidApplSeqNum, offerApplSeqNum, securityID, securityIDSource, lastPx,
                       lastQty, execType, transactTime, delay))
    {
        transaction_raw_sz_vec.push_back({parse_time_to_microseconds(recvTime), channelNo, applSeqNum, streamID, securityID, securityIDSource,
                                          bidApplSeqNum, offerApplSeqNum, lastPx, lastQty, static_cast<char>(execType), transactTime, delay});
    }
    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("read_sz_transaction_csv_cstr: {} records read", transaction_raw_sz_vec.size()));
    return arrow::Status::OK();
}

// RecvTime,ChannelNo,ApplSeqNum,StreamID,SecurityID,SecurityIDSource,Price,OrderQty,Side,TransactTime,OrdType,Delay
arrow::Status read_sz_order_csv_string(const std::string &filename, std::vector<order_raw_sz> &order_raw_sz_vec)
{
    io::CSVReader<12> in(filename);
    in.read_header(io::ignore_extra_column, "RecvTime", "ChannelNo", "ApplSeqNum", "StreamID", "SecurityID", "SecurityIDSource",
        "Price", "OrderQty", "Side", "TransactTime", "OrdType", "Delay");
    std::string recvTime, streamID, securityID, securityIDSource, transactTime;
    int64_t applSeqNum, orderQty;
    int channelNo, delay, side, ordType;
    double price;
    while (in.read_row(recvTime, channelNo, applSeqNum, streamID, securityID, securityIDSource, price, orderQty, side,
        transactTime, ordType, delay))
    {
        order_raw_sz_vec.push_back({parse_time_to_microseconds(recvTime), channelNo, applSeqNum, streamID, securityID, securityIDSource, price,
            orderQty, static_cast<char>(side), transactTime, static_cast<char>(ordType), delay});
    }
    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("read_sz_order_csv: {} records read", order_raw_sz_vec.size()));
    return arrow::Status::OK();
}

// RecvTime,ChannelNo,ApplSeqNum,StreamID,BidApplSeqNum,OfferApplSeqNum,SecurityID,SecurityIDSource,LastPx,LastQty,ExecType,TransactTime,Delay
arrow::Status read_sz_transaction_csv_string(const std::string &filename, std::vector<transaction_raw_sz> &transaction_raw_sz_vec)
{
    io::CSVReader<13> in(filename);
    in.read_header(io::ignore_extra_column, "RecvTime", "ChannelNo", "ApplSeqNum", "StreamID", "BidApplSeqNum", "OfferApplSeqNum",
        "SecurityID", "SecurityIDSource", "LastPx", "LastQty", "ExecType", "TransactTime", "Delay");
    std::string recvTime, streamID, securityID, securityIDSource, transactTime;
    int64_t applSeqNum, bidApplSeqNum, offerApplSeqNum, lastQty;
    int channelNo, delay, execType;
    double lastPx;
    while (in.read_row(recvTime, channelNo, applSeqNum, streamID, bidApplSeqNum, offerApplSeqNum, securityID, securityIDSource, lastPx,
        lastQty, execType, transactTime, delay))
    {
        transaction_raw_sz_vec.push_back({parse_time_to_microseconds(recvTime), channelNo, applSeqNum, streamID, securityID, securityIDSource,
            bidApplSeqNum, offerApplSeqNum, lastPx, lastQty, static_cast<char>(execType), transactTime, delay});
    }
    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("read_sz_transaction_csv: {} records read", transaction_raw_sz_vec.size()));
    return arrow::Status::OK();
}

arrow::Status RunSZ(const std::string &file_order, const std::string &file_transaction, const std::string &upper_limit,
    const std::string &lower_limit, const std::string &prev_close)
{
    // std::vector<order_raw_sz> order_raw_sz_vec_string;
    std::vector<order_raw_sz> order_raw_sz_vec;

    auto res = read_sz_order_csv(file_order, order_raw_sz_vec);
    if (!res.ok())
    {
        std::cerr << res << std::endl;
        return res;
    }
    // res = read_sz_order_csv_string(file_order, order_raw_sz_vec_string);
    // if (!res.ok())
    // {
    //     std::cerr << res << std::endl;
    //     return res;
    // }

    // std::vector<transaction_raw_sz> transaction_raw_sz_vec_string;
    std::vector<transaction_raw_sz> transaction_raw_sz_vec;
    res = read_sz_transaction_csv(file_transaction, transaction_raw_sz_vec);
    if (!res.ok())
    {
        std::cerr << res << std::endl;
        return res;
    }
    // res = read_sz_transaction_csv_string(file_transaction, transaction_raw_sz_vec_string);
    // if (!res.ok())
    // {
    //     std::cerr << res << std::endl;
    //     return res;
    // }

    OrderBook o1{atoi(upper_limit.c_str()), atoi(lower_limit.c_str()), atoi(prev_close.c_str()), false, Market::SZ, LOG_LEVEL::DEBUG};
    // o1.print_tier_dict();
    return arrow::Status::OK();
}

arrow::Status RunMain(int argc, char **argv)
{
    if (argc != 5 && argc != 6)
    {
        std::cerr << "usage: " << std::endl << argv[0] << " <sh parquet file> <upper limit> <lower limit> <prev close>" << std::endl;
        std::cerr << argv[0] << " <sz order csv> <sz transaction csv> <upper limit> <lower limit> <prev close>" << std::endl;
        return arrow::Status::Invalid("没有指定原始逐笔交易记录文件");
    }
    if (argc == 5)
    {
        OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, "RunMain: SH");
        return RunSH(argv[1], argv[2], argv[3], argv[4]);
    }
    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, "RunMain: SZ");
    return RunSZ(argv[1], argv[2], argv[3], argv[4], argv[5]);
}

int main(int argc, char **argv)
{
    setenv("TZ", "Asia/Shanghai", 1);
    arrow::Status st = RunMain(argc, argv);
    sys_log_level = LOG_LEVEL::DEBUG;

    OrderBook::log(sys_log_level, LOG_LEVEL::DEBUG, "完全处理完毕");
    if (!st.ok())
    {
        std::cerr << st << std::endl;
        return 1;
    }

    return 0;
}
