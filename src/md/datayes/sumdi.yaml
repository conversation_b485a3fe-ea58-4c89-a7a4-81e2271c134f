# 超级指数行情 sumdi 配置文件
# 
sources:
  - name: dyl2     # 通联 L2 行情
    host: ***********
    port: 9869
  - name: htl1     # 华泰 L1 行情
    host: ************
    port: 9869
  - name: gxl1     # 国信中畅 L1 行情
    host: ***********
    port: 9869
  - name: gxl1sh     # 国信中畅 L1 行情，上海 VPN
    host: ************
    port: 9870

subscriptions:     # 订阅的行情主题列表，每个订阅都必须以字母I开头，如果没有订阅，则订阅所有指数行情
  - I              # 所有指数行情

high_water_mark: 100  # ZeroMQ socket 的 high water mark，包括输入与输出，高值没有意义，可能反而有害，堆积的数据都是过时的数据

output:
  host: 127.0.0.1  # 输出行情的 ZeroMQ XSUB 服务器地址
  port: 30003      # 输出行情的 ZeroMQ XSUB 服务器端口

background: false   # 是否在后台运行
