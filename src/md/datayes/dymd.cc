// Based on mdl_api_demo.cpp, manual connect/subscribe to both markets (Shenzhen and Shanghai) at the same time.
// Based on md2.cpp

#include <iostream>
#include <sstream>
#include <stdio.h>
#include <pthread.h>
#include <fstream>
#include <getopt.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <signal.h>
#include <math.h>
#include <cctype>
#include <unordered_map>
#include <string>
#include <mutex>
#include <condition_variable>
#include <zmq.h>
#include <unistd.h>
#include <errno.h>
#include <queue>
#include <thread>
#include <chrono>
#include <hiredis/hiredis.h>
#include <algorithm>
#include <iomanip>
#include <filesystem>
#include <format>

#include "fkYAML/node.hpp"

#include "mdl_api.h"
#include "mdl_shl1_msg.h"
#include "mdl_shl2_msg.h"
#include "mdl_szl2_msg.h"
#include "mdl_szl1_msg.h"
#include "mdl_cffex_msg.h"
#include "mdl_czce_msg.h"
#include "mdl_dce_msg.h"
#include "mdl_shfe_msg.h"
#include "mdl_hkex_msg.h"
#include "mdl_bar_msg.h"

#include "lyproto.quota.pb.h"

#include "dymd.h"

#define MARKET_SZ 1  // Shenzhen
#define MARKET_SH 2  // Shanghai

#define SERVER_SZ "mdl-cloud-bj.datayes.com:19012;mdl-cloud-sz.datayes.com:19012;mdl-cloud-sh.datayes.com:19012"
//#define SERVER_SZ "mdl-cloud-sz.datayes.com:19012"
#define SERVER_SH "mdl-cloud-bj.datayes.com:19014;mdl-cloud-sz.datayes.com:19014;mdl-cloud-sh.datayes.com:19014"
//#define SERVER_SH "mdl-cloud-sh.datayes.com:19014"

void *g_zmq_ctx = NULL;
void *g_zmq = NULL;
std::mutex mtx;   // 用于保护 ZeroMQ 的发布

using namespace datayes::mdl;

// ZMQ 的 PUB socket 非线程安全，所以这里设置成 1 可能最安全
int num_work_threads = 1;
IOManagerPtr g_IOManager;

int g_data_type;  // DATA_TICK 或 DATA_TRANSORD
int g_do_redis = 0;  // 是否通过 redis 发布静态信息

static char pub111_stock_buf[200000];

class TimeFormatter {
public:
	static std::string format(const std::string& fmt) {
		auto now = std::chrono::system_clock::now();
		std::time_t now_time = std::chrono::system_clock::to_time_t(now);
		std::tm tm_buf;

		localtime_r(&now_time, &tm_buf);
		std::ostringstream oss;
		oss << std::put_time(&tm_buf, fmt.c_str());
		return oss.str();
	}
};

static bool prepare_dir(const std::string& filep) {
	namespace fs = std::filesystem;
	fs::path p(filep + "XXXX");
	fs::path dir = p.parent_path();

	if (dir.empty()) {  // current dir
		return true;
	}

	try {
		fs::create_directories(dir);
		return true;
	} catch (const fs::filesystem_error& e) {
		std::cout << e.what() << std::endl;
		return false; // something wrong
	}
}

// 静态信息，从深交所 6.51 消息或上交所 4.23 消息中获取，放入队列中，然后再通过 redis 发布线程写入到 redis 服务器中
// 另外有一些信息使用 Python 程序通过 HTTP 接口获取并补充写入 Redis
// 深交所的申报数量限制是在本程序中根据深交所交易规则自行填写的，不一定准确
struct static_info {
	std::string SecurityID;        // 证券代码，涨跌停API表中字段名为 ticker
	std::string SecurityName;      // 证券名称，深交所 6.51 消息中字段名相同，涨跌停API表中字段名为 secShortName
	std::string szSecurityType;    // 深交所证券类型，深交所 6.51 消息中字段名为 SecurityType
	std::string PreClosePx;        // 昨收价 x 10000，深交所 6.51 消息中字段名为 PrevClosePx
	std::string TickSize;          // 上交所 4.23 消息中字段名为 DeltaPriceUnit，深交所消息中无此字段，涨跌停API表中无此字段，因此深交所证券要根据其类型规则填写
	std::string ListDate;          // 上市日期，格式为 YYYYMMDD
	std::string Currency;          // 币种，如 CNY，HKD
	std::string MDDate;            // 行情日期，上交所、深交所消息中都没有，涨跌停API表中字段名为 tradeDate
	std::string MaxPx;             // 涨停价 x 10000，上交所 4.23 消息中字段名为 HighLimitPrice，深交所无此字段，涨跌停API表中字段名为 limitUpPrice
	std::string MinPx;             // 跌停价 x 10000，上交所 4.23 消息中字段名为 LowLimitPrice，深交所无此字段，涨跌停API表中字段名为 limitDownPrice
	std::string BuyQtyUnit;        // 买数量单位（每笔买委托的委托数量必须是买数量单位的整数倍），上交所 4.23 消息中字段名为 BidUnit，深交所消息中无此字段，涨跌停API表中没有
	std::string SellQtyUnit;       // 卖数量单位（每笔卖委托的委托数量必须是卖数量单位的整数倍），上交所 4.23 消息中字段名为 AskUnit，深交所消息中无此字段，涨跌停API表中没有
	std::string LLimitNum;         // 限价申报数量下限，上交所 4.23 消息中字段名为 LLimitNum，涨跌停API表中没有，深交无此字段，要根据深交所规则按是否为创业板填写常数
	std::string ULimitNum;         // 限价申报数量上限，上交所 4.23 消息中字段名为 ULimitNum，涨跌停API表中没有，深交无此字段，要根据深交所规则按是否为创业板填写常数
	std::string LMktPNum;          // 市价申报数量下限，上交所 4.23 消息中字段名为 LMktPNum，涨跌停API表中没有，深交无此字段，要根据深交所规则按是否为创业板填写常数
	std::string UMktNum;           // 市价申报数量上限，上交所 4.23 消息中字段名为 UMktNum，涨跌停API表中没有，深交无此字段，要根据深交所规则按是否为创业板填写常数
	std::string FType;             // 融资标的标志('T' 是'F'不是)，上交所 4.23 消息中字段名为 FType，深交所消息字段名为 CrdBuyUnderlying ('Y' 是'N'不是)，涨跌停API表中没有
	std::string SType;             // 融券标的标志('T' 是'F'不是)，上交所 4.23 消息中字段名为 SType，深交所消息字段名为 CrdSellUnderlying ('Y' 是'N'不是)，涨跌停API表中没有
};

std::queue<static_info> g_static_info;   // 用于在 handler 与 redis_pub 线程间通信
std::mutex mtx_static_info;
std::condition_variable cv_static_info;

static bool dymd_init(int data_type, const char *logfp, int zmq_port, int hwm)
{
	if ((data_type == DATA_TICK) && (zmq_port > 0)) {   // 只通过 ZeroMQ 发布 tick 数据
		g_zmq_ctx = zmq_ctx_new();
		g_zmq = zmq_socket(g_zmq_ctx, ZMQ_PUB);
		if (hwm > 0) {
			zmq_setsockopt(g_zmq, ZMQ_SNDHWM, &hwm, sizeof(hwm));
		}
		int r = zmq_bind(g_zmq, std::format("tcp://*:{}", zmq_port).c_str());
		if (r != 0) {
			printf("Failed to bind ZeroMQ socket: %s\n", zmq_strerror(errno));
			return false;
		}
	}

	g_IOManager = CreateIOManager(num_work_threads);
	if (g_IOManager.IsNull()) {
		printf("Incompatible API lib version.\n");
		if (g_zmq) {
			zmq_close(g_zmq);
			g_zmq = NULL;
		}
		if (g_zmq_ctx) {
			zmq_ctx_destroy(g_zmq_ctx);
			g_zmq_ctx = NULL;
		}
		return false;
	}
	if ((logfp != NULL) && (strlen(logfp) != 0)) {
		if (std::string(logfp) == ".") logfp = "./";
		g_IOManager->EnableLog(logfp);
	}

	return true;
}

static void dymd_fini()
{
	if (!g_IOManager.IsNull()) {
		g_IOManager->Shutdown();
	}
	if (g_zmq) {
		zmq_close(g_zmq);
		g_zmq = NULL;
	}
	if (g_zmq_ctx) {
		zmq_ctx_destroy(g_zmq_ctx);
		g_zmq_ctx = NULL;
	}
}

// 通联沪市股票、基金L2行情中的交易阶段代码，统计到的有 ['START', 'SUSP', 'OCALL', 'TRADE', 'CCALL', 'CLOSE', 'ENDTR']
// 通联文档中的状态数量比较多，把一些华泰没有的状态按自己的理解映射
// 也有两个华泰独有的状态，随意用不存在的 XXX 和 YYY 来映射
std::unordered_map<std::string, std::string> shInstruStatus2status = {
	{"START", "0"},   // 启动；开盘前，启动
	{"OCALL", "1"},   // 开市集合竞价；开盘集合竞价
	{"XXX", "2"},   // XXX；开盘集合竞价阶段结束到连续竞价阶段开始之前
	{"TRADE", "3"},   // 连续自动撮合；连续竞价
	{"BREAK", "4"},   // 休市，例如：午餐休市；中午休市
	{"CCALL", "5"},   //  闭市集合竞价；收盘集合竞价
	{"CLOSE", "6"},   // 闭市，自动计算闭市价格；已闭市
	{"POSTR", "7"},   // 盘后处理；盘后交易
	{"SUSP", "8"},   // 停牌（与HALT的区别在于可撤单）；临时停牌
	{"HALT", "8"},   // 暂停，除了自有订单和交易的查询外，任何交易活动都被禁止；临时停牌
	{"VOLA", "9"},   // 连续交易和集合竞价交易的波动性中断；波动性中断
	{"YYY", "10"},  // YYY；竞价交易收盘至盘后固定价格交易之前
	{"ENDTR", "6"},  // 交易结束；已闭市
	{"ADD", "3"},  // 新产品；连续竞价
	{"BETW", "4"},  // 交易间，禁止任何交易活动；中午休市
	{"DEL", "8"},  // 产品待删除；临时停牌
	{"FCALL", "11"},  // 固定价格集合竞价；盘后固定交易交易
	{"ICALL", "5"},  // 盘中集合竞价；收盘集合竞价
	{"IOBB", "5"},  // 盘中集合竞价订单簿平衡（OBB）；收盘集合竞价
	{"IPOBB", "5"},  // 盘中集合竞价 PreOBB；收盘集合竞价
	{"OOBB", "1"},  // 开市集合竞价 OBB；开盘集合竞价
	{"OPOBB", "1"},  // 开市集合竞价订单簿平衡（OBB）前期时段；开盘集合竞价
	{"NOTRD", "6"},  // 非交易支持非交易服务；已闭市
	{"PRETR", "0"}  // 盘前处理；开盘前，启动
};

// 通联深市股票、基金L2行情中的交易阶段代码，统计到的有 13 种：
//   ['S0      ', 'S1      ', 'O0      ', 'O1      ', 'H0      ',
//    'B0      ', 'B1      ', 'T0      ', 'T1      ', 'C0      ',
//    'C1      ', 'E0      ', 'E1      ']
// 通联文档：产品所处的交易阶段代码
// 第 0 位：
// S=启动（开市前）
// O=开盘集合竞价
// T=连续竞价
// B=休市
// C=收盘集合竞价
// E=已闭市
// H=临时停牌
// A=盘后交易
// V=波动性中断
// 第 1 位：
// 0=正常状态
// 1=全天停牌
std::unordered_map<std::string, std::string> szTradingPhaseCode2status = {
	{"C1      ", "8"},   // 似乎是14:57开始发送的停牌的股票；临时停牌
	{"O1      ", "8"},   // 似乎是9:30开盘前发送的停牌的股票；临时停牌
	{"S1      ", "8"},   // 似乎是9:15前发送的停牌的股票；临时停牌
	{"H0      ", "8"},   // 似乎全天都可能发布的停牌的股票；临时停牌
	{"E1      ", "8"},   // 似乎是15:00后发送的已停牌的股票；临时停牌
	{"B1      ", "8"},   // 似乎是在非交易时间发送的已停牌的股票；临时停牌
	{"T1      ", "8"},   // 似乎是在交易时间改善的已停牌的股票；临时停牌
	{"S0      ", "0"},   // 似乎是在9:15前发送的正常股票行情；开盘前，启动
	{"O0      ", "1"},   // 似乎是在9:15-9:24:57之间发送的开盘集合竞价行情；开盘集合竞价
	{"E0      ", "6"},   // 似乎是从15:00后开始一直到很晚发送的正常股票行情；已闭市
	{"B0      ", "4"},   // 似乎是9:25-9:29:57以及11:30-12:59:57之间发送的正常股票行情；中午休市
	{"T0      ", "3"},   // 9:30-11:30及13:00-14:57之间发送的正常股票行情；连续竞价
	{"C0      ", "5"},   // 14:57-15:00收盘集合竞价；收盘集合竞价
	{"A0      ", "7"},   // 没统计到；盘后交易
	{"V0      ", "9"},   // 没统计到；波动性中断
};

// 沪市涨跌停价是在盘前由 MarketInfo 消息（沪市静态文件）提供的
struct SHL2LimitPrice {
	float high_limit_price;
	float low_limit_price;
};
std::unordered_map<std::string, SHL2LimitPrice> g_sh_limit_price;

// 针对通联的 MDLDoubleT 和 MDLFloatT 返回 10000 倍的价格
template <typename T>
long long get1w(T f) {
        if (f.IsNull()) return 0;
        if (f.GetDecimalPlace() <= 4) {
                return (long long)f.m_Value * 10000 / f.GetDecimalShift();
        }
        auto k = f.GetDecimalShift() / 10000;
        auto r = div(f.m_Value, k);
        if (r.rem > (k / 2)) return r.quot + 1;
        return r.quot;
}

class MyMessageHandler : public MessageHandler {
	private:
    std::ofstream m_file_szl2_order;        // 深圳L2逐笔委托文件
    std::ofstream m_file_szl2_trans;        // 深圳L2逐笔成交文件
    std::ofstream m_file_shl2_trans_order;  // 上海L2竞价逐笔合并行情文件
	std::ofstream m_file_szl2_tick;         // 深圳L2 tick文件
	std::ofstream m_file_szl2_index;        // 深圳L2 index文件
	std::ofstream m_file_shl2_tick;         // 上海L2 tick文件
	std::ofstream m_file_shl2_index;        // 上海L2 index文件
	std::ofstream m_file_shl2_minfo;        // 上海L2 market info静态文件
	std::ofstream m_file_szl2_minfo;        // 深圳L2 market info静态文件
	
	pthread_mutex_t m_lock_szl2_order;
	pthread_mutex_t m_lock_szl2_trans;
	pthread_mutex_t m_lock_shl2_trans_order;
	pthread_mutex_t m_lock_szl2_tick;
	pthread_mutex_t m_lock_szl2_index;
	pthread_mutex_t m_lock_shl2_tick;
	pthread_mutex_t m_lock_shl2_index;
	pthread_mutex_t m_lock_shl2_minfo;
	pthread_mutex_t m_lock_szl2_minfo;
	
	int m_market;    // MARKET_SZ 或 MARKET_SH
	int m_data_type; // DATA_TICK 或 DATA_TRANSORD
	std::string m_md_source; // 行情源标签
	
	public:
	std::string server_sz{SERVER_SZ};
	std::string server_sh{SERVER_SH};

	MyMessageHandler() {
	}

	MyMessageHandler(std::string server_sz, std::string server_sh) : server_sz(server_sz), server_sh(server_sh) {
	}

	bool Open(int market, int data_type, const char * filep, const char * token, const std::string & md_source) {
		m_market = market;
		m_data_type = data_type;
		m_md_source = md_source;

		// 初始化互斥锁，实际上每次 open 只会用到其中的一部分
		// market = MARKET_SZ 时，data_type = DATA_TICK 时，用到 m_lock_szl2_tick 和 m_lock_szl2_index
		pthread_mutex_init(&m_lock_szl2_tick, NULL);
		pthread_mutex_init(&m_lock_szl2_index, NULL);

		// market = MARKET_SZ 时，data_type = DATA_TRANSORD 时，用到 m_lock_szl2_order 和 m_lock_szl2_trans
		pthread_mutex_init(&m_lock_szl2_order, NULL);
		pthread_mutex_init(&m_lock_szl2_trans, NULL);

		// market = MARKET_SH 时，data_type = DATA_TRANSORD 时，用到 m_lock_shl2_trans_order
		pthread_mutex_init(&m_lock_shl2_trans_order, NULL);

		// market = MARKET_SH 时，data_type = DATA_TICK 时，用到 m_lock_shl2_tick 和 m_lock_shl2_index
		pthread_mutex_init(&m_lock_shl2_tick, NULL);
		pthread_mutex_init(&m_lock_shl2_index, NULL);
		pthread_mutex_init(&m_lock_shl2_minfo, NULL);
		pthread_mutex_init(&m_lock_szl2_minfo, NULL);
				
		// 用于保存文件名
		std::string filename;

		bool multi_thread_callback = false; // 在各 OnXXXMessage() 回调中会向 ZMQ 发布数据，所以不能多线程
		SubscriberPtr sub = g_IOManager->CreateSubscriber(this, multi_thread_callback);
		if (filep != NULL && std::string(filep) == ".") filep = "./";
		if (market == MARKET_SZ) {    // 深圳
			sub->SetServerAddress(server_sz.c_str());
			if (data_type == DATA_TICK) {    // tick 数据包含股票、基金和指数以及静态数据
				sub->SubcribeMessage<mdl_szl2_msg::Snapshot300111_v2>();  // subscribe Shenzhen L2 tick data for stock and fund
				sub->SubcribeMessage<mdl_szl2_msg::Snapshot309011_v2>();  // subscribe Shenzhen L2 tick data for index
				sub->SubcribeMessage<mdl_szl2_msg::MarketInfo>();  // subscribe Shenzhen L2 market info
				if (filep != NULL) {    // 保存深市tick和index数据
					filename = std::string(filep) + "sz_tick.csv";
					printf("Saving Shenzhen L2 tick data to %s\n", filename.c_str());
					m_file_szl2_tick.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_szl2_tick.is_open()) {
						printf("Failed to open file %s for saving Shenzhen L2 tick data\n", filename.c_str());
						return false;
					}
					// 写入CSV文件头
					m_file_szl2_tick << "RecvTime,UpdateTime,ChannelNo,StreamID,SecurityID,SecurityIDSource,TradingPhase,PreCloPrice,TurnNum,Volume,Turnover,LastPrice,"
						"OpenPrice,HighPrice,LowPrice,DifPrice1,DifPrice2,PE1,PE2,PreCloseIOPV,IOPV,TotalOfferQty,WeightedAvgOfferPx,TotalBidQty,WeightedAvgBidPx,HighLimitPrice,"
						"LowLimitPrice,OpenInt,OptPremiumRatio,BuyPrice1,BuyVolume1,BuyNumOrders1,BuyPrice2,BuyVolume2,BuyNumOrders2,BuyPrice3,BuyVolume3,BuyNumOrders3,"
						"BuyPrice4,BuyVolume4,BuyNumOrders4,BuyPrice5,BuyVolume5,BuyNumOrders5,BuyPrice6,BuyVolume6,BuyNumOrders6,BuyPrice7,BuyVolume7,BuyNumOrders7,"
						"BuyPrice8,BuyVolume8,BuyNumOrders8,BuyPrice9,BuyVolume9,BuyNumOrders9,BuyPrice10,BuyVolume10,BuyNumOrders10,"
						"SellPrice1,SellVolume1,SellNumOrders1,SellPrice2,SellVolume2,SellNumOrders2,SellPrice3,SellVolume3,SellNumOrders3,SellPrice4,SellVolume4,SellNumOrders4,"
						"SellPrice5,SellVolume5,SellNumOrders5,SellPrice6,SellVolume6,SellNumOrders6,SellPrice7,SellVolume7,SellNumOrders7,SellPrice8,SellVolume8,SellNumOrders8,"
						"SellPrice9,SellVolume9,SellNumOrders9,SellPrice10,SellVolume10,SellNumOrders10,Delay,TLTime" << std::endl;

					filename = std::string(filep) + "sz_index.csv";
					printf("Saving Shenzhen L2 index data to %s\n", filename.c_str());
					m_file_szl2_index.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_szl2_index.is_open()) {
						printf("Failed to open file %s for saving Shenzhen L2 index data\n", filename.c_str());
						m_file_szl2_tick.close();
						return false;
					}
					// 写入CSV文件头
					m_file_szl2_index << "RecvTime,UpdateTime,ChannelNo,StreamID,SecurityID,SecurityIDSource,TradingPhase,TurnNum,Volume,Turnover,LastIndex,"
						"PreCloIndex,OpenIndex,HighIndex,LowIndex,Delay,TLTime" << std::endl;

					filename = std::string(filep) + "sz_minfo.csv";
					printf("Saving Shenzhen L2 market info to %s\n", filename.c_str());
					m_file_szl2_minfo.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_szl2_minfo.is_open()) {
						printf("Failed to open file %s for saving Shenzhen L2 market info\n", filename.c_str());
						m_file_szl2_tick.close();
						m_file_szl2_index.close();
						return false;
					}
					// 写入CSV文件头
					m_file_szl2_minfo << "RecvTime,SecurityID,SecurityName,UpdateTime,UnderlyingSecurityID,ListDate,SecurityType,Currency,QtyUnit,DayTrading,PrevClosePx,SecurityStatus,"
						"OutstandingShare,PublicFloatShareQuantity,ParValue,GageFlag,GageRatio,CrdBuyUnderlying,CrdSellUnderlying,PriceCheckMode,PledgeFlag,ContractMultiplier,"
						"QualificationFlag,QualificationClass,RegularShare,IndustryClassification,PreviousYearProfitPerShare,CurrentYearProfitPerShare,Attribute,NoProfit,"
						"WeightedVotingRights,IsRegistration,IsVIE,NAV,CouponRate,IssuePrice,Interest,InterestAccrualDate,MaturityDate,OfferingFlag,SwapFlag,PutbackFlag,"
						"PutbackCancelFlag,PutbackResellFlag,PricingMethod,ExpirationDays,CallOrPut,ListType,DeliveryDay,DeliveryMonth,DeliveryType,ExerciseBeginDate,ExerciseEndDate,"
						"ExercisePrice,ExerciseType,LastTradeDay,AdjustTimes,ContractUnit,PrevClearingPrice,ContractPosition,TLTime" << std::endl;
				}
			} else if (data_type == DATA_TRANSORD) {    // 逐笔交易数据
				// subscribe Shenzhen order and transaction data
				sub->SubcribeMessage<mdl_szl2_msg::Order300192_v2>();
				sub->SubcribeMessage<mdl_szl2_msg::Transaction300191_v2>();
				if (filep != NULL) {    // 保存深市逐笔委托和逐笔成交数据
					filename = std::string(filep) + "sz_order.csv";
					printf("Saving Shenzhen L2 order data to %s\n", filename.c_str());
					m_file_szl2_order.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_szl2_order.is_open()) {
						printf("Failed to open file %s for saving Shenzhen L2 order data\n", filename.c_str());
						return false;
					}
					// 写入CSV文件头
					m_file_szl2_order << "RecvTime,ChannelNo,ApplSeqNum,StreamID,SecurityID,SecurityIDSource,Price,OrderQty,Side,TransactTime,OrdType,Delay,TLTime" << std::endl;

					filename = std::string(filep) + "sz_trans.csv";
					printf("Saving Shenzhen L2 transaction data to %s\n", filename.c_str());
					m_file_szl2_trans.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_szl2_trans.is_open()) {
						printf("Failed to open file %s for saving Shenzhen L2 transaction data\n", filename.c_str());
						m_file_szl2_order.close();
						return false;
					}
					// 写入CSV文件头
					m_file_szl2_trans << "RecvTime,ChannelNo,ApplSeqNum,StreamID,BidApplSeqNum,OfferApplSeqNum,SecurityID,SecurityIDSource,LastPx,LastQty,ExecType,TransactTime,Delay,TLTime" << std::endl;
				}
			} else {
				printf("Invalid data type\n");
				return false;
			}
		} else if (market == MARKET_SH) {    // 上海
			sub->SetServerAddress(server_sh.c_str());
			if (data_type == DATA_TICK) {    // tick 数据包含股票、基金和指数，另外还要从盘前静态文件 MarketInfo 中读取涨停价信息
				sub->SubcribeMessage<mdl_shl2_msg::SHL2MarketData>();  // subscribe Shanghai L2 tick data for stock and fund
				sub->SubcribeMessage<mdl_shl2_msg::SHL2Index>();  // subscribe Shanghai L2 tick data for index
				sub->SubcribeMessage<mdl_shl2_msg::MarketInfo>();  // subscribe Shanghai L2 market info
				if (filep != NULL) {    // 保存沪市tick和index数据
					filename = std::string(filep) + "sh_tick.csv";
					printf("Saving Shanghai L2 tick data to %s\n", filename.c_str());
					m_file_shl2_tick.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_shl2_tick.is_open()) {
						printf("Failed to open file %s for saving Shanghai L2 tick data\n", filename.c_str());
						return false;
					}
					// 写入CSV文件头
					m_file_shl2_tick << "RecvTime,UpdateTime,SecurityID,ImageStatus,PreCloPrice,OpenPrice,HighPrice,LowPrice,LastPrice,ClosePrice,InstruStatus,TradNumber,TradVolume,Turnover,"
						"TotalBidVol,WAvgBidPri,AltWAvgBidPri,TotalAskVol,WAvgAskPri,AltWAvgAskPri,EtfBuyNumber,EtfBuyVolume,EtfBuyMoney,EtfSellNumber,EtfSellVolume,ETFSellMoney,YieldToMatu,"
						"TotWarExNum,WarLowerPri,WarUpperPri,WiDBuyNum,WiDBuyVol,WiDBuyMon,WiDSellNum,WiDSellVol,WiDSellMon,TotBidNum,TotSellNum,MaxBidDur,MaxSellDur,BidNum,SellNum,IOPV,"
						"BuyPrice1,BuyVolume1,BuyNumOrders1,BuyPrice2,BuyVolume2,BuyNumOrders2,BuyPrice3,BuyVolume3,BuyNumOrders3,BuyPrice4,BuyVolume4,BuyNumOrders4,BuyPrice5,BuyVolume5,BuyNumOrders5,"
						"BuyPrice6,BuyVolume6,BuyNumOrders6,BuyPrice7,BuyVolume7,BuyNumOrders7,BuyPrice8,BuyVolume8,BuyNumOrders8,BuyPrice9,BuyVolume9,BuyNumOrders9,BuyPrice10,BuyVolume10,BuyNumOrders10,"
						"SellPrice1,SellVolume1,SellNumOrders1,SellPrice2,SellVolume2,SellNumOrders2,SellPrice3,SellVolume3,SellNumOrders3,SellPrice4,SellVolume4,SellNumOrders4,"
						"SellPrice5,SellVolume5,SellNumOrders5,SellPrice6,SellVolume6,SellNumOrders6,SellPrice7,SellVolume7,SellNumOrders7,SellPrice8,SellVolume8,SellNumOrders8,"
						"SellPrice9,SellVolume9,SellNumOrders9,SellPrice10,SellVolume10,SellNumOrders10,Delay,TLTime" << std::endl;

					filename = std::string(filep) + "sh_index.csv";
					printf("Saving Shanghai L2 index data to %s\n", filename.c_str());
					m_file_shl2_index.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_shl2_index.is_open()) {
						printf("Failed to open file %s for saving Shanghai L2 index data\n", filename.c_str());
						m_file_shl2_tick.close();
						return false;
					}
					// 写入CSV文件头
					m_file_shl2_index << "RecvTime,UpdateTime,DataStatus,SecurityID,PreCloseIndex,OpenIndex,Turnover,HighIndex,LowIndex,LastIndex,TradTime,TradVolume,CloseIndex,Delay,TLTime" << std::endl;

					filename = std::string(filep) + "sh_minfo.csv";
					printf("Saving Shanghai L2 market info to %s\n", filename.c_str());
					m_file_shl2_minfo.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_shl2_minfo.is_open()) {
						printf("Failed to open file %s for saving Shanghai L2 market info\n", filename.c_str());
						m_file_shl2_tick.close();
						m_file_shl2_index.close();
						return false;
					}
					// 写入CSV文件头
					m_file_shl2_minfo << "RecvTime,UpdateTime,SecurityID,SecurityName,ObjectID,MarketClass,AssetClass,SubAssetClass,Currency,FaceValue,LastTradeDate,ListDate,SetID,AskUnit,BidUnit,"
						"LLimitNum,ULimitNum,PreClosePrice,DeltaPriceUnit,LimitType,HighLimitPrice,LowLimitPrice,XRPercentage,XDMoney,FType,SType,Status,LMktPNum,UMktNum,Note,TLTime" << std::endl;
				}
			} else if (data_type == DATA_TRANSORD) {
				// subscribe Shanghai order and transaction data
				sub->SubcribeMessage<mdl_shl2_msg::NGTSTick>();
				if (filep != NULL) {    // 保存沪市逐笔委托和逐笔成交数据
					filename = std::string(filep) + "sh_transorder.csv";
					printf("Saving Shanghai L2 transaction and order data to %s\n", filename.c_str());
					m_file_shl2_trans_order.open(filename, std::ios::out | std::ios::trunc);
					if (!m_file_shl2_trans_order.is_open()) {
						printf("Failed to open file %s for saving Shanghai L2 transaction and order data\n", filename.c_str());
						m_file_shl2_tick.close();
						return false;
					}
					// 写入CSV文件头
					m_file_shl2_trans_order << "RecvTime,BizIndex,Channel,SecurityID,TickTime,Type,BuyOrderNO,SellOrderNO,Price,Qty,TradeMoney,TickBSFlag,Delay,TLTime" << std::endl;
				}
			} else {
				printf("Invalid data type\n");
				return false;
			}
		} else {
			printf("Wrong market\n");
			return false;
		}
		sub->SetUserName(token); // Set to YOUR TOKEN!!!
		// MDLEID_MKTPRO (7) 应该比 MDLEID_BINARY (1) 快
		sub->SetMessageEncoding(MDLEID_MKTPRO);
		//sub->SetMessageEncoding(MDLEID_BINARY);
		printf("Heartbeat interval is %d, heartbeat timeout is %d\n", sub->GetHeartbeatInterval(), sub->GetHeartbeatTimeout());
		
		// connect to server
		std::string err = sub->Connect();
		if (err.empty()) {
			printf("Connect to server successfully.\n");
			return true;
		}
		printf("Connect to server failed: %s.\n", err.c_str());
		return false; 
	}
 
	void Close() {
		// 关闭文件
		if (m_file_szl2_order.is_open()) {
			m_file_szl2_order.close();
		}
		if (m_file_szl2_trans.is_open()) {
			m_file_szl2_trans.close();
		}
		if (m_file_shl2_trans_order.is_open()) {
			m_file_shl2_trans_order.close();
		}
		if (m_file_szl2_tick.is_open()) {
			m_file_szl2_tick.close();
		}
		if (m_file_szl2_index.is_open()) {
			m_file_szl2_index.close();
		}
		if (m_file_shl2_tick.is_open()) {
			m_file_shl2_tick.close();
		}
		if (m_file_shl2_index.is_open()) {
			m_file_shl2_index.close();
		}
		if (m_file_shl2_minfo.is_open()) {
			m_file_shl2_minfo.close();
		}
		if (m_file_szl2_minfo.is_open()) {
			m_file_szl2_minfo.close();
		}
		// 销毁互斥锁
		pthread_mutex_destroy(&m_lock_szl2_order);
		pthread_mutex_destroy(&m_lock_szl2_trans);
		pthread_mutex_destroy(&m_lock_shl2_trans_order);
		pthread_mutex_destroy(&m_lock_szl2_tick);
		pthread_mutex_destroy(&m_lock_szl2_index);
		pthread_mutex_destroy(&m_lock_shl2_tick);
		pthread_mutex_destroy(&m_lock_shl2_index);
		pthread_mutex_destroy(&m_lock_shl2_minfo);
		pthread_mutex_destroy(&m_lock_szl2_minfo);
	}

	// handle network failure
	virtual void OnMDLAPIMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_api_msg::ConnectingEvent::MessageID) {
			mdl_api_msg::ConnectingEvent* resp = (mdl_api_msg::ConnectingEvent*)msg->GetBody();
			printf("Connect to %s ...\n", resp->Address.c_str());
		}
		else if (head->MessageID == mdl_api_msg::ConnectErrorEvent::MessageID) {
			mdl_api_msg::ConnectErrorEvent* resp = (mdl_api_msg::ConnectErrorEvent*)msg->GetBody();
			printf("Connect to %s failed %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
		else if (head->MessageID == mdl_api_msg::DisconnectedEvent::MessageID) {
			mdl_api_msg::DisconnectedEvent* resp = (mdl_api_msg::DisconnectedEvent*)msg->GetBody();
			printf("Disconnected from %s: %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
		}
	}

	// handle server response
	virtual void OnMDLSysMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID) {
			mdl_sys_msg::LogonResponse* resp = (mdl_sys_msg::LogonResponse*)msg->GetBody();
			if (resp->ReturnCode != MDLEC_OK) {
				printf("Logon failed: return code %d.\n", resp->ReturnCode);
			}
			for (uint32_t i = 0; i < resp->Services.Length; ++i) {
				for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j) {
					if (resp->Services[i]->Messages[j]->MessageStatus != MDLEC_OK) {
						printf("The server doesn't publish message (service id %d message id %d)\n", 
							resp->Services[i]->ServiceID,
							resp->Services[i]->Messages[j]->MessageID);
					}
				}
			}
		}
	}

	// print shfe future message
	virtual void OnMDLSHFEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shfe_msg::CTPFuture::MessageID) {
			mdl_shfe_msg::CTPFuture* body = (mdl_shfe_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print czce future message
	virtual void OnMDLCZCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_czce_msg::CTPFuture::MessageID) {
			mdl_czce_msg::CTPFuture* body = (mdl_czce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print cffex future message
	virtual void OnMDLCFFEXMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_cffex_msg::CTPFuture::MessageID) {
			mdl_cffex_msg::CTPFuture* body = (mdl_cffex_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print dce future message
	virtual void OnMDLDCEMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_dce_msg::CTPFuture::MessageID) {
			mdl_dce_msg::CTPFuture* body = (mdl_dce_msg::CTPFuture*)msg->GetBody();
			PrintFutureMessage(body);
		}
	}

	// print shanghai level1 message
	virtual void OnMDLSHL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_shl1_msg::Equity2::MessageID) {
			mdl_shl1_msg::Equity2* body = (mdl_shl1_msg::Equity2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Indexes::MessageID) {
			mdl_shl1_msg::Indexes* body = (mdl_shl1_msg::Indexes*)msg->GetBody(); 
			PrintIndexMessage(body); 
		}
		else if (head->MessageID == mdl_shl1_msg::Bond2::MessageID) {
			mdl_shl1_msg::Bond2* body = (mdl_shl1_msg::Bond2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
		else if (head->MessageID == mdl_shl1_msg::Fund2::MessageID) {
			mdl_shl1_msg::Fund2* body = (mdl_shl1_msg::Fund2*)msg->GetBody(); 
			PrintNewStockMessage(body);
		}
	}

	// print shenzhen level1 message
	virtual void OnMDLSZL1Message(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl1_msg::Index2::MessageID) {
			mdl_szl1_msg::Index2* body = (mdl_szl1_msg::Index2*)msg->GetBody();
			PrintIndexMessage(body); 
		}
		else if (head->MessageID == mdl_szl1_msg::SZL1Stock::MessageID) {
			mdl_szl1_msg::SZL1Stock* body = (mdl_szl1_msg::SZL1Stock*)msg->GetBody();
			PrintStockMessage(body); 
		}
	}

	template <class T> 
	void PrintIndexMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighIndex:%s LowIndex:%s LastIndex:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->IndexID.std_str().c_str(), msgBody->IndexName.std_str().c_str(), 
			DoubleToString(msgBody->HighIndex).c_str(), 
			DoubleToString(msgBody->LowIndex).c_str(), 
			DoubleToString(msgBody->LastIndex).c_str());
	}
	template <class T> 
	void PrintNewStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			DoubleToString(msgBody->HighPrice).c_str(), 
			DoubleToString(msgBody->LowPrice).c_str(), 
			DoubleToString(msgBody->LastPrice).c_str());
	}
	template <class T> 
	void PrintStockMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->SecurityID.std_str().c_str(), msgBody->SecurityName.std_str().c_str(), 
			FloatToString(msgBody->HighPrice).c_str(), 
			FloatToString(msgBody->LowPrice).c_str(), 
			FloatToString(msgBody->LastPrice).c_str());
	}

	template <class T> 
	void PrintFutureMessage(const T* msgBody) {
		printf("%d:%02d:%02d %s HighPrice:%s LowPrice:%s LastPrice:%s\n",
			msgBody->UpdateTime.GetHour(), msgBody->UpdateTime.GetMinute(), msgBody->UpdateTime.GetSecond(),
			msgBody->InstruID.std_str().c_str(),
			DoubleToString(msgBody->HighPrice).c_str(),
			DoubleToString(msgBody->LowPrice).c_str(),
			DoubleToString(msgBody->LastPrice).c_str());
	}

	template <class T>
	std::string DoubleToString(const T& mdlDouble) {
		if (mdlDouble.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlDouble.GetDouble());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlDouble.GetDouble());
#endif
		return std::string(strBuf);
	}

	template <class T>
	std::string FloatToString(const T& mdlFloat) {
		if (mdlFloat.IsNull()) {
			return std::string("null");
		}
		char strBuf[100];
#if defined(__linux__)
        sprintf(strBuf, "%.2f", mdlFloat.GetFloat());
#else
		sprintf_s(strBuf, sizeof(strBuf), "%.2f", mdlFloat.GetFloat());
#endif
		return std::string(strBuf);
	}

	// print hkex message
	virtual void OnMDLHKExMessage(const MDLMessage* msg) {
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_hkex_msg::OMDMarketData::MessageID) {
			mdl_hkex_msg::OMDMarketData* body = (mdl_hkex_msg::OMDMarketData*)msg->GetBody();
			printf("[%d:%02d:%02d.%d] %s ",
				body->TradTime.GetHour(), body->TradTime.GetMinute(), body->TradTime.GetSecond(), body->TradTime.GetMilliSec(),
				body->TickerSymbol.c_str());
			if (body->LastPrice.IsNull()) {
				printf("LastPrice:null");
			}
			else {
				printf("LastPrice:%.2f", body->LastPrice.GetFloat());
			}	
			if (body->ChangePct.IsNull()) {
				printf("(),");
			}
			else {
				printf("(%.3f%%),", body->ChangePct.GetFloat() * 100);
			}	
			printf("Volume:%.3fM,", (float)body->Quantity / 1000000.0);
			if (body->Turnover.IsNull()) {
				printf("Turnover:null\n");
			}
			else {
				printf("Turnover:%.3fM\n", body->Turnover.GetDouble() / 1000000.0);
			}	

			if (body->AskBook.Length > 0) {
				if (body->AskBook[0]->Price.IsNull()) {
					printf("	Ask1:null,0,");
				}
				else {
					printf("	Ask1:%.3f,%ld,", body->AskBook[0]->Price.GetFloat(), body->AskBook[0]->Volume);
				}
			}
			if (body->BidBook.Length > 0) {
				if (body->BidBook[0]->Price.IsNull()) {
					printf("	Bid1:null,0");
				}
				else {
					printf("	Bid1:%.3f,%ld", body->BidBook[0]->Price.GetFloat(), body->BidBook[0]->Volume);
				}
			} 
			printf("\n");
		}
	}

	// handle shenzhen level2 message
	void OnMDLSZL2Message(const MDLMessage* msg) {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		struct tm timeinfo;
		localtime_r(&tv.tv_sec, &timeinfo);
		long long recvtime = tv.tv_sec * 1000000 + tv.tv_usec;
		
		MDLMessageHead* head = msg->GetHead();
		if (head->MessageID == mdl_szl2_msg::Snapshot300111_v2::MessageID) {  // 6.28 市场行情
			mdl_szl2_msg::Snapshot300111_v2 * resp = (mdl_szl2_msg::Snapshot300111_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;

			const char *csid = resp->SecurityID.std_str().c_str(); // "510050"
			char stkid[8];
			memcpy(stkid, csid, 6);
			stkid[6] = 0;

			LYPROTO::QUOTA::MarketData o;

			o.set_exchid("0");    // 深市
			o.set_category("S");   // 股票、基金
			o.set_stkid(stkid);
			o.set_mdsource(m_md_source);

			char tmpbuf[32];
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", resp->UpdateTime.m_Value);  // 交易所时间戳
			o.set_exchtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", head->LocalTime.m_Value);  // 通联接收时间戳
			o.set_rcvsvrtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%ld", timeinfo.tm_hour * 10000000 + timeinfo.tm_min * 100000 + timeinfo.tm_sec * 1000 + tv.tv_usec / 1000);  // 本地接收时间戳
			o.set_pubsvrtime(tmpbuf);

			std::string status_str = szTradingPhaseCode2status[resp->TradingPhaseCode.std_str()];
			o.set_status(status_str);   // 交易阶段代码，把通联的字符串转成华泰的数字字符串
			if (status_str == "6") {  // 已闭市, 可以设置收盘价
				o.set_close(get1w(resp->LastPrice)); // 通联深圳行情没有专门的收盘价字段，按深交所规则，当收盘时，最新价即为收盘价
			}
			else if (status_str == "8" && resp->UpdateTime.m_Value >= 150000000) {  // 临时停牌, 可以设置涨跌停价
				o.set_close(get1w(resp->LastPrice)); // 通联深圳行情没有专门的收盘价字段，根据实测，停牌的股票在收盘后其 LastPrice 会变为前收价
			}

			o.set_preclose(get1w(resp->PreCloPrice));
			o.set_highlimit(get1w(resp->HighLimitPrice));
			o.set_lowlimit(get1w(resp->LowLimitPrice));

			o.set_open(get1w(resp->OpenPrice));
			o.set_high(get1w(resp->HighPrice));
			o.set_low(get1w(resp->LowPrice));
			o.set_latest(get1w(resp->LastPrice));

			o.set_knock(resp->TurnNum);    // 成交笔数
			o.set_volume(resp->Volume);    // 成交总量
			o.set_value(lrint(resp->Turnover.GetDouble()));  // 成交总金额

			unsigned int i;
			for (i = 0; i < resp->BidPriceLevel.Length; ++i) {
				o.add_bp(get1w(resp->BidPriceLevel[i]->Price));
				o.add_ba(resp->BidPriceLevel[i]->Volume);
			}
			if (i < 3) {  // 至少 3 个买盘，不然老的程序可能会崩
				for (; i < 3; ++i) {
					o.add_bp(0);
					o.add_ba(0);
				}
			}
			for (i = 0; i < resp->AskPriceLevel.Length; ++i) {
				o.add_sp(get1w(resp->AskPriceLevel[i]->Price));
				o.add_sa(resp->AskPriceLevel[i]->Volume);
			}
			if (i < 3) {  // 至少 3 个卖盘，不然老的程序可能会崩
				for (; i < 3; ++i) {
					o.add_sp(0);
					o.add_sa(0);
				}
			}

			o.set_totalba(resp->TotalBidQty);   // 委托买单股数或手数
			o.set_totalsa(resp->TotalOfferQty);    // 委托卖单股数或手数
			o.set_weightedavgbidpx(get1w(resp->WeightedAvgBidPx));    // 买入加权平均价 × 10000
			o.set_weightedavgofferpx(get1w(resp->WeightedAvgOfferPx));    // 卖出加权平均价 × 10000

			o.set_iopv(get1w(resp->IOPV));  // IOPV × 10000，沪深 ETF 提供

			auto pbsize = o.ByteSizeLong();
			size_t pktsize = 15 + pbsize;
			if (pktsize > sizeof(pub111_stock_buf)) return;
			char *pb = pub111_stock_buf;
			if (pb) {
				std::lock_guard<std::mutex> lck (mtx);

				snprintf(pb, pktsize, "S%s%08ld:", stkid, pbsize);
				o.SerializeToArray(pb + 15, pbsize);
				int r = zmq_send(g_zmq, pb, pktsize, 0);
				if (r < 0) {
					printf("zmq_send Shenzhen tick data error\n");
				}
			}
			
			if (m_file_szl2_tick.is_open()) {
				(void)pthread_mutex_lock(&m_lock_szl2_tick);
				m_file_szl2_tick << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
					"{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},{},"
					"{},{},{},{},"
					"{},{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
					resp->ChannelNo, resp->MDStreamID.std_str().c_str(), resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(),
					resp->TradingPhaseCode.std_str().c_str(), resp->PreCloPrice.GetDouble(), resp->TurnNum, resp->Volume, resp->Turnover.GetDouble(),
					resp->LastPrice.GetDouble(), resp->OpenPrice.GetDouble(), resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->DifPrice1.GetDouble(),
					resp->DifPrice2.GetDouble(), resp->PE1.GetDouble(), resp->PE2.GetDouble(), resp->PreCloseIOPV.GetDouble(), resp->IOPV.GetDouble(), resp->TotalOfferQty,
					resp->WeightedAvgOfferPx.GetDouble(), resp->TotalBidQty, resp->WeightedAvgBidPx.GetDouble(), resp->HighLimitPrice.GetDouble(),
					resp->LowLimitPrice.GetDouble(), resp->OpenInt, resp->OptPremiumRatio.GetDouble());
					for (unsigned int i = 0; i < 10; ++i) {
						if (i < resp->BidPriceLevel.Length) {
							m_file_szl2_tick << std::format(",{},{},{}", resp->BidPriceLevel[i]->Price.GetDouble(), resp->BidPriceLevel[i]->Volume,resp->BidPriceLevel[i]->NumOrders);
						} else {
							m_file_szl2_tick << ",,,";
						}
						if (i < resp->AskPriceLevel.Length) {
							m_file_szl2_tick << std::format(",{},{},{}", resp->AskPriceLevel[i]->Price.GetDouble(), resp->AskPriceLevel[i]->Volume,resp->AskPriceLevel[i]->NumOrders);
						} else {
							m_file_szl2_tick << ",,,";
						}
					}
					m_file_szl2_tick << "," << recvtime - ticktime << "," << head->LocalTime.m_Value << std::endl;
					(void)pthread_mutex_unlock(&m_lock_szl2_tick);
			}
		} else if (head->MessageID == mdl_szl2_msg::Order300192_v2::MessageID) {  // 6.33 逐笔委托
			mdl_szl2_msg::Order300192_v2 * resp = (mdl_szl2_msg::Order300192_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TransactTime.GetHour();
			tm1.tm_min = resp->TransactTime.GetMinute();
			tm1.tm_sec = resp->TransactTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TransactTime.GetMilliSec() * 1000;

			if (m_file_szl2_order.is_open()) {
				(void)pthread_mutex_lock(&m_lock_szl2_order);
				m_file_szl2_order << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{},{}", 
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
					timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
					resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->Price.GetDouble(), resp->OrderQty, resp->Side,
					resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec(), resp->OrdType, recvtime - ticktime, head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_szl2_order);
			}
		} else if (head->MessageID == mdl_szl2_msg::Transaction300191_v2::MessageID) {  // 6.36 逐笔成交
			mdl_szl2_msg::Transaction300191_v2 * resp = (mdl_szl2_msg::Transaction300191_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TransactTime.GetHour();
			tm1.tm_min = resp->TransactTime.GetMinute();
			tm1.tm_sec = resp->TransactTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TransactTime.GetMilliSec() * 1000;

			if (m_file_szl2_trans.is_open()) {
				(void)pthread_mutex_lock(&m_lock_szl2_order);
				m_file_szl2_trans << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{},{},{},{},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{}",
				timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
				resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.std_str().c_str(),
				resp->BidApplSeqNum, resp->OfferApplSeqNum, resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(), resp->LastPx.GetDouble(), resp->LastQty, resp->ExecType,
				resp->TransactTime.GetHour(), resp->TransactTime.GetMinute(), resp->TransactTime.GetSecond(), resp->TransactTime.GetMilliSec(), recvtime - ticktime, head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_szl2_order);
			}
		} else if (head->MessageID == mdl_szl2_msg::Snapshot309011_v2::MessageID) {  // 6.29 指数行情
			mdl_szl2_msg::Snapshot309011_v2 * resp = (mdl_szl2_msg::Snapshot309011_v2*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;

			const char *csid = resp->SecurityID.std_str().c_str();
			char stkid[8];
			memcpy(stkid, csid, 6);
			stkid[6] = 0;

			LYPROTO::QUOTA::MarketData o;

			o.set_exchid("0");    // 深市
			o.set_category("I");   // 指数
			o.set_stkid(stkid);
			o.set_mdsource(m_md_source);

			char tmpbuf[32];
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", resp->UpdateTime.m_Value);  // 交易所时间戳
			o.set_exchtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", head->LocalTime.m_Value);  // 通联接收时间戳
			o.set_rcvsvrtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%ld", timeinfo.tm_hour * 10000000 + timeinfo.tm_min * 100000 + timeinfo.tm_sec * 1000 + tv.tv_usec / 1000);  // 本地接收时间戳
			o.set_pubsvrtime(tmpbuf);

			// o.set_status(resp->TradingPhaseCode.std_str().c_str());   // 原 Insight 的指数也没有交易阶段代码
			
			o.set_high(get1w(resp->HighIndex));
			o.set_latest(get1w(resp->LastIndex));
			o.set_low(get1w(resp->LowIndex));
			o.set_open(get1w(resp->OpenIndex));
			o.set_preclose(get1w(resp->PreCloIndex));

			o.set_value(lrint(resp->Turnover.GetDouble()));  // 成交总金额
			o.set_volume(resp->Volume);    // 成交总量

			// 即使对于指数，也需要至少 3 个买卖盘，不然老的程序可能会崩
			for (int i = 0; i < 3; ++i) {
				o.add_bp(0);
				o.add_ba(0);
				o.add_sp(0);
				o.add_sa(0);
			}

			auto pbsize = o.ByteSizeLong();
			size_t pktsize = 15 + pbsize;
			if (pktsize > sizeof(pub111_stock_buf)) return;
			char *pb = pub111_stock_buf;
			if (pb) {
				std::lock_guard<std::mutex> lck (mtx);

				snprintf(pb, pktsize, "I%s%08ld:", stkid, pbsize);
				o.SerializeToArray(pb + 15, pbsize);
				int r = zmq_send(g_zmq, pb, pktsize, 0);
				if (r < 0) {
					printf("zmq_send Shenzhen index data error\n");
				}
			}

			if (m_file_szl2_index.is_open()) {
				(void)pthread_mutex_lock(&m_lock_szl2_index);
				m_file_szl2_index << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
					"{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},"
					"{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
					resp->ChannelNo, resp->MDStreamID.std_str().c_str(), resp->SecurityID.std_str().c_str(), resp->SecurityIDSource.std_str().c_str(),
					resp->TradingPhaseCode.std_str().c_str(), resp->TurnNum, resp->Volume, resp->Turnover.GetDouble(), resp->LastIndex.GetDouble(),
					resp->PreCloIndex.GetDouble(), resp->OpenIndex.GetDouble(), resp->HighIndex.GetDouble(), resp->LowIndex.GetDouble(),
					recvtime - ticktime, head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_szl2_index);
			}
		} else if (head->MessageID == mdl_szl2_msg::MarketInfo::MessageID) {  // 6.51 深交所静态文件
			mdl_szl2_msg::MarketInfo * resp = (mdl_szl2_msg::MarketInfo*)msg->GetBody();
			if (m_file_szl2_minfo.is_open()) {
				std::string status_str;
				for (unsigned int i = 0; i < resp->SecurityStatus.Length; ++i) {
					status_str += "|" + std::to_string(resp->SecurityStatus[i]->Status) + "|";
				}
				(void)pthread_mutex_lock(&m_lock_szl2_minfo);
				m_file_szl2_minfo << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},"
					"{},{},{:09d},{},"
					"{:08d},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},"
					"{},{},{},{},"
					"{},{},{},{},"
					"{},{},{},{},{},{:08d},"
					"{:08d},{},{},{},"
					"{},{},{},{},"
					"{},{},{:08d},{},{},"
					"{:08d},{:08d},{},{},{:08d},"
					"{},{},{},{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					resp->SecurityID.std_str().c_str(), resp->SecurityName.ToAnsi().c_str(), resp->UpdateTime.m_Value, resp->UnderlyingSecurityID.std_str().c_str(),
					resp->ListDate.m_Value, resp->SecurityType, resp->Currency.std_str().c_str(), resp->QtyUnit.GetDouble(), resp->DayTrading.std_str().c_str(),
					resp->PrevClosePx.GetDouble(), status_str.c_str(), resp->OutstandingShare.GetDouble(), resp->PublicFloatShareQuantity.GetDouble(), resp->ParValue.GetDouble(),
					resp->GageFlag.std_str().c_str(), resp->GageRatio.GetFloat(), resp->CrdBuyUnderlying.std_str().c_str(), resp->CrdSellUnderlying.std_str().c_str(), resp->PriceCheckMode,
					resp->PledgeFlag.std_str().c_str(), resp->ContractMultiplier.GetFloat(), resp->QualificationFlag.std_str().c_str(), resp->QualificationClass,
					resp->RegularShare.std_str().c_str(), resp->IndustryClassification.std_str().c_str(), resp->PreviousYearProfitPerShare.GetDouble(), resp->CurrentYearProfitPerShare.GetDouble(),
					resp->Attribute, resp->NoProfit.std_str().c_str(), resp->WeightedVotingRights.std_str().c_str(), resp->IsRegistration.std_str().c_str(),
					resp->IsVIE.std_str().c_str(), resp->NAV.GetDouble(), resp->CouponRate.GetFloat(), resp->IssuePrice.GetDouble(), resp->Interest.GetDouble(), resp->InterestAccrualDate.m_Value,
					resp->MaturityDate.m_Value, resp->OfferingFlag.std_str().c_str(), resp->SwapFlag.std_str().c_str(), resp->PutbackFlag.std_str().c_str(),
					resp->PutbackCancelFlag.std_str().c_str(), resp->PutbackResellFlag.std_str().c_str(), resp->PricingMethod, resp->ExpirationDays,
					resp->CallOrPut.std_str().c_str(), resp->ListType, resp->DeliveryDay.m_Value, resp->DeliveryMonth.std_str().c_str(), resp->DeliveryType.std_str().c_str(),
					resp->ExerciseBeginDate.m_Value, resp->ExerciseEndDate.m_Value, resp->ExercisePrice.GetDouble(), resp->ExerciseType.std_str().c_str(), resp->LastTradeDay.m_Value,
					resp->AdjustTimes, resp->ContractUnit.GetDouble(), resp->PrevClearingPrice.GetDouble(), resp->ContractPosition.GetDouble(), head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_szl2_minfo);
			}
			if (g_do_redis) {
				static_info si;
				// XXX 注意 si 的每个字段都要赋值且非空（也不能带空格），不然 redis 命令格式就不对了
				si.SecurityID = resp->SecurityID.std_str();
				if (resp->SecurityName.std_str().empty()) {
					si.SecurityName = "Unknown";
				} else {
					std::string name = resp->SecurityName.ToAnsi();
					// Remove spaces from the name，否则可能引起奇怪的问题：其它的值也带上了引号
					name.erase(std::remove_if(name.begin(), name.end(),
						[](unsigned char c) { return std::isspace(c); }), name.end());
					si.SecurityName = name;
				}
				si.szSecurityType = std::to_string(resp->SecurityType);
				si.PreClosePx = std::to_string(get1w(resp->PrevClosePx));
				switch (resp->SecurityType) {  // XXX
				case 1:  // 主板A股
				case 3:  // 创业版股票
				case 4:  // 主板S股
					si.TickSize = "0.01";
					break;
				case 5:  // 国债（含地方债）
				case 6:  // 企业债
				case 7:  // 公司债
				case 8:  // 可转换债券
				case 9:  // 私募债
				case 10: // 可交换债券
				case 11: // 证券公司次级债
					si.TickSize = "0.001";
					break;
				case 12: // 质押式回购
					si.TickSize = "0.005";
					break;
				case 13: // 资产支持证券
				case 14: // 本市场股票ETF
				case 15: // 跨市场股票ETF
				case 16: // 跨境ETF
				case 17: // 本市场实物债券ETF
				case 18: // 现金债券ETF
				case 19: // 黄金ETF
				case 20: // 货币ETF
				case 21: // （预留）杠杆ETF
				case 22: // 商品期货ETF
				case 23: // 标准LOF
				case 24: // 分级子基金（似乎还没有）
				case 25: // 封闭式基金（似乎仅剩下 1 只）
				case 26: // 仅申赎基金
					si.TickSize = "0.001";
					break;
				case 28: // 权证（目前似乎没有了）
				case 29: // 个股期权（目前似乎没有）
				case 30: // ETF期权
					si.TickSize = "0.0001";
					break;
				case 33: // 优先股
					si.TickSize = "0.01";
					break;
				case 34: // 证券公司短期债（目前似乎没有）
				case 35: // 可交换公司债（目前似乎没有）
				case 36: // 主板存托凭证（目前似乎没有）
				case 37: // 创业板存托凭证（目前似乎没有）
					si.TickSize = "0";
					break;
				case 38: // 基础设施基金
					si.TickSize = "0.001";
					break;
				case 39: // 定向可转债（目前有 1 只）
					si.TickSize = "0.001";
					break;
				case 40: // 跨银行间实物债券ETF（目前似乎没有）
					si.TickSize = "0.001";
					break;
				default:  // 期权
					si.TickSize = "0";
					break;
				}
				si.ListDate = std::to_string(resp->ListDate.m_Value);
				if (resp->Currency.std_str().empty()) si.Currency = "CNY"; else si.Currency = resp->Currency.std_str();
				si.MDDate = std::to_string((timeinfo.tm_year + 1900) * 10000 + (timeinfo.tm_mon + 1) * 100 + timeinfo.tm_mday);
				si.MaxPx = "0";   // XXX 要通过 HTTP API 来更新
				si.MinPx = "0";   // XXX 要通过 HTTP API 来更新
				si.BuyQtyUnit = "100";   // XXX 通联各 API 中都没有，只能写死
				si.SellQtyUnit = "1";    // XXX 通联各 API 中都没有，只能写死
				si.LLimitNum = "1";    // XXX 通联各 API 中都没有，只能写死
				si.LMktPNum = "1";     // XXX 通联各 API 中都没有，只能写死
				// XXX 通联各 API 中都没有，根据深交所规则写死，
				if (resp->SecurityType == 3) {  // 创业版股票
					si.ULimitNum = "300000";
					si.UMktNum = "150000";
				} else {
					si.ULimitNum = "1000000";
					si.UMktNum = "1000000";
				}
				// 融资融券标准，按沪市的标准转换成：T表示是，F表示不是
				if ((resp->CrdBuyUnderlying.std_str() == "N") || resp->CrdBuyUnderlying.std_str().empty()) si.FType = "F"; else si.FType = "T";
				if ((resp->CrdSellUnderlying.std_str() == "N") || resp->CrdSellUnderlying.std_str().empty()) si.SType = "F"; else si.SType = "T";

				{
					std::lock_guard<std::mutex> lck (mtx_static_info);
					g_static_info.push(si);
				}
				cv_static_info.notify_one();
			}
		}
	}


	// handle shanghai level2 message
	void OnMDLSHL2Message(const MDLMessage* msg) {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		struct tm timeinfo;
		localtime_r(&tv.tv_sec, &timeinfo);
		long long recvtime = tv.tv_sec * 1000000 + tv.tv_usec;
		auto head = msg->GetHead();

		if (head->MessageID == mdl_shl2_msg::SHL2Index::MessageID) {  // 4.6 指数行情
			mdl_shl2_msg::SHL2Index * resp = (mdl_shl2_msg::SHL2Index*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;
			
			const char *csid = resp->SecurityID.std_str().c_str();
			char stkid[8];
			memcpy(stkid, csid, 6);
			stkid[6] = 0;

			LYPROTO::QUOTA::MarketData o;

			o.set_exchid("1");    // 沪市
			o.set_category("I");   // 指数
			o.set_stkid(stkid);
			o.set_mdsource(m_md_source);

			char tmpbuf[32];
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", resp->UpdateTime.m_Value);  // 交易所时间戳
			o.set_exchtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", head->LocalTime.m_Value);  // 通联接收时间戳
			o.set_rcvsvrtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%ld", timeinfo.tm_hour * 10000000 + timeinfo.tm_min * 100000 + timeinfo.tm_sec * 1000 + tv.tv_usec / 1000);  // 本地接收时间戳
			o.set_pubsvrtime(tmpbuf);

			o.set_high(get1w(resp->HighIndex));
			o.set_latest(get1w(resp->LastIndex));
			o.set_low(get1w(resp->LowIndex));
			o.set_open(get1w(resp->OpenIndex));
			o.set_preclose(get1w(resp->PreCloseIndex));

			o.set_value(lrint(resp->Turnover.GetDouble()));
			o.set_volume(lrint(resp->TradVolume.GetDouble() * 100));   // 为了跟华泰的一致，转成股数

			// 即使对于指数，也需要至少 3 个买卖盘，不然老的程序可能会崩
			for (int i = 0; i < 3; ++i) {
				o.add_bp(0);
				o.add_ba(0);
				o.add_sp(0);
				o.add_sa(0);
			}

			auto pbsize = o.ByteSizeLong();
			size_t pktsize = 15 + pbsize;
			if (pktsize > sizeof(pub111_stock_buf)) return;
			char *pb = pub111_stock_buf;
			if (pb) {
				std::lock_guard<std::mutex> lck (mtx);

				snprintf(pb, pktsize, "I%s%08ld:", stkid, pbsize);
				o.SerializeToArray(pb + 15, pbsize);
				int r = zmq_send(g_zmq, pb, pktsize, 0);
				if (r < 0) {
					printf("zmq_send Shanghai index data error\n");
				}
			}

			if (m_file_shl2_index.is_open()) {
				(void)pthread_mutex_lock(&m_lock_shl2_index);
				m_file_shl2_index << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
					"{},{},{},{},"
					"{},{},{},{},"
					"{},{},{},{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
					resp->DataStatus, resp->SecurityID.std_str().c_str(), resp->PreCloseIndex.GetDouble(), resp->OpenIndex.GetDouble(),
					resp->Turnover.GetDouble(), resp->HighIndex.GetDouble(), resp->LowIndex.GetDouble(), resp->LastIndex.GetDouble(),
					resp->TradTime.m_Value, resp->TradVolume.GetDouble(), resp->CloseIndex.GetDouble(), recvtime - ticktime, head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_shl2_index);
			}
		} else if (head->MessageID == mdl_shl2_msg::SHL2MarketData::MessageID) {  // 4.4 个股行情
			mdl_shl2_msg::SHL2MarketData * resp = (mdl_shl2_msg::SHL2MarketData*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->UpdateTime.GetHour();
			tm1.tm_min = resp->UpdateTime.GetMinute();
			tm1.tm_sec = resp->UpdateTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->UpdateTime.GetMilliSec() * 1000;

			const char *csid = resp->SecurityID.std_str().c_str(); // "510050"
			char stkid[8];
			memcpy(stkid, csid, 6);
			stkid[6] = 0;

			LYPROTO::QUOTA::MarketData o;

			o.set_exchid("1");    // 沪市
			o.set_category("S");   // 股票、基金
			o.set_stkid(stkid);
			o.set_mdsource(m_md_source);

			char tmpbuf[32];
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", resp->UpdateTime.m_Value);  // 交易所时间戳
			o.set_exchtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%d", head->LocalTime.m_Value);  // 通联接收时间戳
			o.set_rcvsvrtime(tmpbuf);
			snprintf(tmpbuf, sizeof(tmpbuf), "%ld", timeinfo.tm_hour * 10000000 + timeinfo.tm_min * 100000 + timeinfo.tm_sec * 1000 + tv.tv_usec / 1000);  // 本地接收时间戳
			o.set_pubsvrtime(tmpbuf);

			std::string status_str = shInstruStatus2status[resp->InstruStatus.std_str()];
			o.set_status(status_str);   // 交易阶段代码，把通联的字符串转成华泰的数字字符串
			if (resp->ClosePrice.m_Value > 0) // ClosePrice 字段在收盘后某个时刻之后才会有大于 0 的值
			{
				o.set_close(get1w(resp->ClosePrice)); // 通联沪市行情没有专门的收盘价字段，按上交所规则，当收盘时，最新价即为收盘价
			}

			o.set_preclose(get1w(resp->PreCloPrice));
			// 通联沪市行情没有涨停价字段，要从静态数据生成的 map 里读取
			o.set_highlimit(lrintf(g_sh_limit_price[resp->SecurityID.std_str()].high_limit_price * 10000.0));
			// 通联沪市行情没有跌停价字段，要从静态数据生成的 map 里读取
			o.set_lowlimit(lrintf(g_sh_limit_price[resp->SecurityID.std_str()].low_limit_price * 10000.0));

			o.set_open(get1w(resp->OpenPrice));
			o.set_high(get1w(resp->HighPrice));
			o.set_low(get1w(resp->LowPrice));
			o.set_latest(get1w(resp->LastPrice));

			o.set_knock(resp->TradNumber);    // 成交笔数
			o.set_volume(lrint(resp->TradVolume.GetDouble()));    // 成交总量
			o.set_value(lrint(resp->Turnover.GetDouble()));  // 成交总金额
			o.set_widnum(resp->WiDBuyNum + resp->WiDSellNum);  // 累计买、卖撤单笔数，主要用于比较上交所 L2 行情和逐笔合成行情的先后

			unsigned int i;
			for (i = 0; i < resp->BidLevels.Length; ++i) {
				o.add_bp(get1w(resp->BidLevels[i]->OrderPrice));
				o.add_ba(lrint(resp->BidLevels[i]->OrderVol.GetDouble()));
			}
			if (i < 3) {  // 至少 3 个买盘，不然老的程序可能会崩
				for (; i < 3; ++i) {
					o.add_bp(0);
					o.add_ba(0);
				}
			}
			for (i = 0; i < resp->SellLevels.Length; ++i) {
				o.add_sp(get1w(resp->SellLevels[i]->OrderPrice));
				o.add_sa(lrintf(resp->SellLevels[i]->OrderVol.GetDouble()));
			}
			if (i < 3) {  // 至少 3 个卖盘，不然老的程序可能会崩
				for (; i < 3; ++i) {
					o.add_sp(0);
					o.add_sa(0);
				}
			}
			o.set_totalba(lrint(resp->TotalBidVol.GetDouble()));
			o.set_totalsa(lrint(resp->TotalAskVol.GetDouble()));
			o.set_weightedavgbidpx(get1w(resp->WAvgBidPri));
			o.set_weightedavgofferpx(get1w(resp->WAvgAskPri));
			// o.set_iopv(lrint(resp->IOPV.GetFloat() * 10000));
			o.set_iopv(get1w(resp->WarUpperPri)); // 用 warrant upper price 代替 IOPV

			auto pbsize = o.ByteSizeLong();
			size_t pktsize = 15 + pbsize;
			if (pktsize > sizeof(pub111_stock_buf)) return;
			char *pb = pub111_stock_buf;
			if (pb) {
				std::lock_guard<std::mutex> lck (mtx);

				snprintf(pb, pktsize, "S%s%08ld:", stkid, pbsize);
				o.SerializeToArray(pb + 15, pbsize);
				int r = zmq_send(g_zmq, pb, pktsize, 0);
				if (r < 0) {
					printf("zmq_send Shanghai tick data error\n");
				}
			}

			if (m_file_shl2_tick.is_open()) {
				(void)pthread_mutex_lock(&m_lock_shl2_tick);
				m_file_shl2_tick << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:03d},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},{},"
					"{},{},{},{},{},{},{},"
					"{},{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					tm1.tm_year + 1900, tm1.tm_mon + 1, tm1.tm_mday, tm1.tm_hour, tm1.tm_min, tm1.tm_sec, resp->UpdateTime.GetMilliSec(),
					resp->SecurityID.std_str().c_str(), resp->ImageStatus, resp->PreCloPrice.GetFloat(), resp->OpenPrice.GetFloat(), resp->HighPrice.GetFloat(), 
					resp->LowPrice.GetFloat(), resp->LastPrice.GetFloat(), resp->ClosePrice.GetFloat(), resp->InstruStatus.c_str(), resp->TradNumber, 
					resp->TradVolume.GetDouble(), resp->Turnover.GetDouble(), resp->TotalBidVol.GetDouble(), resp->WAvgBidPri.GetFloat(), resp->AltWAvgBidPri.GetFloat(),
					resp->TotalAskVol.GetDouble(), resp->WAvgAskPri.GetFloat(), resp->AltWAvgAskPri.GetFloat(), resp->EtfBuyNumber, resp->EtfBuyVolume.GetDouble(),
					resp->EtfBuyMoney.GetDouble(), resp->EtfSellNumber, resp->EtfSellVolume.GetDouble(), resp->ETFSellMoney.GetDouble(), resp->YieldToMatu.GetFloat(),
					resp->TotWarExNum.GetDouble(), resp->WarLowerPri.GetDouble(), resp->WarUpperPri.GetDouble(), resp->WiDBuyNum, resp->WiDBuyVol.GetDouble(), resp->WiDBuyMon.GetDouble(),
					resp->WiDSellNum, resp->WiDSellVol.GetDouble(), resp->WiDSellMon.GetDouble(), resp->TotBidNum, resp->TotSellNum, resp->MaxBidDur, resp->MaxSellDur,
					resp->BidNum, resp->SellNum, resp->IOPV.GetFloat());
					for (unsigned int i = 0; i < 10; ++i) {
						if (i < resp->BidLevels.Length) {
							m_file_shl2_tick << std::format(",{},{},{}", resp->BidLevels[i]->OrderPrice.GetFloat(), resp->BidLevels[i]->OrderVol.GetDouble(), resp->BidLevels[i]->OrderNum);
						}
						else {
							m_file_shl2_tick << ",,,";
						}
						if (i < resp->SellLevels.Length) {
							m_file_shl2_tick << std::format(",{},{},{}", resp->SellLevels[i]->OrderPrice.GetFloat(), resp->SellLevels[i]->OrderVol.GetDouble(), resp->SellLevels[i]->OrderNum);
						}
						else {
							m_file_shl2_tick << ",,,";
						}
					}
					m_file_shl2_tick << "," << recvtime - ticktime << "," << head->LocalTime.m_Value << std::endl;
					(void)pthread_mutex_unlock(&m_lock_shl2_tick);
			}
		} else if (head->MessageID == mdl_shl2_msg::NGTSTick::MessageID) {  // 4.24 竞价逐笔合并行情
			mdl_shl2_msg::NGTSTick * resp = (mdl_shl2_msg::NGTSTick*)msg->GetBody();
			struct tm tm1;
			tm1.tm_year = timeinfo.tm_year;
			tm1.tm_mon = timeinfo.tm_mon;
			tm1.tm_mday = timeinfo.tm_mday;
			tm1.tm_hour = resp->TickTime.GetHour();
			tm1.tm_min = resp->TickTime.GetMinute();
			tm1.tm_sec = resp->TickTime.GetSecond();
			tm1.tm_isdst = 0;
			long long ticktime = mktime(&tm1) * 1000000 + resp->TickTime.GetMilliSec() * 1000;
			if (m_file_shl2_trans_order.is_open()) {
				(void)pthread_mutex_lock(&m_lock_shl2_trans_order);
				m_file_shl2_trans_order << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{},{},{},{},{},{},{},{}", 
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
					timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
					resp->BizIndex, resp->Channel, resp->SecurityID.std_str().c_str(),
					resp->TickTime.GetHour(), resp->TickTime.GetMinute(), resp->TickTime.GetSecond(), resp->TickTime.GetMilliSec(),
					resp->Type.c_str(), resp->BuyOrderNO, resp->SellOrderNO, resp->Price.GetFloat(), resp->Qty, resp->TradeMoney.GetDouble(), resp->TickBSFlag.c_str(), recvtime - ticktime, head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_shl2_trans_order);
			}
		} else if (head->MessageID == mdl_shl2_msg::MarketInfo::MessageID) {  // 4.23 上交所静态文件
			mdl_shl2_msg::MarketInfo * resp = (mdl_shl2_msg::MarketInfo*)msg->GetBody();

			g_sh_limit_price[resp->SecurityID.std_str()] = {resp->HighLimitPrice.GetFloat(), resp->LowLimitPrice.GetFloat()};
			if (m_file_shl2_minfo.is_open()) {
				(void)pthread_mutex_lock(&m_lock_shl2_minfo);
				m_file_shl2_minfo << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{:09d},"
					"{},{},{},{},{},"
					"{},{},{},{:08d},{:08d},"
					"{},{},{},{},{},{},{},"
					"{},{},{},{},{},"
					"{},{},{},{},{},{},{}",
					timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday, timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec, resp->UpdateTime.m_Value,
					resp->SecurityID.std_str().c_str(), resp->SecurityName.ToAnsi().c_str(), resp->ObjectID.std_str().c_str(), resp->MarketClass.c_str(), resp->AssetClass.c_str(),
					resp->SubAssetClass.c_str(), resp->Currency.c_str(), resp->FaceValue.GetDouble(), resp->LastTradeDate.m_Value, resp->ListDate.m_Value,
					resp->SetID, resp->AskUnit, resp->BidUnit, resp->LLimitNum, resp->ULimitNum, resp->PreClosePrice.GetFloat(), resp->DeltaPriceUnit.GetFloat(),
					resp->LimitType.c_str(), resp->HighLimitPrice.GetFloat(), resp->LowLimitPrice.GetFloat(), resp->XRPercentage.GetFloat(), resp->XDMoney.GetFloat(),
					resp->FType.c_str(), resp->SType.c_str(), resp->Status.c_str(), resp->LMktPNum, resp->UMktNum, resp->Note.std_str().c_str(), head->LocalTime.m_Value) << std::endl;
				(void)pthread_mutex_unlock(&m_lock_shl2_minfo);
			}
			if (g_do_redis) {
				static_info si;
				// XXX 注意 si 的每个字段都要赋值且非空（也不能带空格），不然 redis 命令格式就不对了
				si.SecurityID = resp->SecurityID.std_str();
				if (resp->SecurityName.std_str().empty()) {
					si.SecurityName = "Unknown";
				} else {
					std::string name = resp->SecurityName.ToAnsi();
					// Remove spaces from the name，否则可能引起奇怪的问题：其它的值也带上了引号
					name.erase(std::remove_if(name.begin(), name.end(),
						[](unsigned char c) { return std::isspace(c); }), name.end());
					si.SecurityName = name;
				}
				si.szSecurityType = std::to_string(0);
				si.PreClosePx = std::to_string(get1w(resp->PreClosePrice));
				si.TickSize = std::to_string(resp->DeltaPriceUnit.GetFloat());
				si.ListDate = std::to_string(resp->ListDate.m_Value);
				if (resp->Currency.std_str().empty()) si.Currency = "CNY"; else si.Currency = resp->Currency.std_str();
				si.MDDate = std::to_string((timeinfo.tm_year + 1900) * 10000 + (timeinfo.tm_mon + 1) * 100 + timeinfo.tm_mday);
				si.MaxPx = std::to_string(get1w(resp->HighLimitPrice));
				si.MinPx = std::to_string(get1w(resp->LowLimitPrice));
				si.BuyQtyUnit = std::to_string(resp->BidUnit);
				si.SellQtyUnit = std::to_string(resp->AskUnit);
				si.LLimitNum = std::to_string(resp->LLimitNum);
				si.ULimitNum = std::to_string(resp->ULimitNum);
				si.LMktPNum = std::to_string(resp->LMktPNum);
				si.UMktNum = std::to_string(resp->UMktNum);
				si.FType = resp->FType.std_str();  // 融资标志，统一按沪市的标准，T表示是，F表示不是
				if (si.FType.empty()) si.FType = "F";
				si.SType = resp->SType.std_str();  // 融券标志，统一按沪市的标准，T表示是，F表示不是
				if (si.SType.empty()) si.SType = "F";

				{
					std::lock_guard<std::mutex> lck (mtx_static_info);
					g_static_info.push(si);
				}
				cv_static_info.notify_one();
			}
		} else if (head->MessageID == mdl_shl2_msg::SHL2Transaction::MessageID) {
			// mdl_shl2_msg::SHL2Transaction * resp = (mdl_shl2_msg::SHL2Transaction*)msg->GetBody();
		} else if (head->MessageID == mdl_shl2_msg::SHL2VirtualAuctionPrice::MessageID) {
			// mdl_shl2_msg::SHL2VirtualAuctionPrice * resp = (mdl_shl2_msg::SHL2VirtualAuctionPrice*)msg->GetBody();
		} else if (head->MessageID == mdl_shl2_msg::OPTLevel1::MessageID) {
			mdl_shl2_msg::OPTLevel1 * resp = (mdl_shl2_msg::OPTLevel1*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->UpdateTime.GetHour(), resp->UpdateTime.GetMinute(), resp->UpdateTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPx.GetDouble(),	resp->LowPx.GetDouble(), resp->LastPx.GetDouble());
		}
	}

	virtual void OnMDLBARMessage(const MDLMessage* msg) {
		if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGStockMinuteBar::MessageID) {
			mdl_bar_msg::XSHGStockMinuteBar * resp = (mdl_bar_msg::XSHGStockMinuteBar*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s Hi:%.2f Lo:%.2f Last:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->BarTime.GetHour(), resp->BarTime.GetMinute(), resp->BarTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->HighPrice.GetDouble(), resp->LowPrice.GetDouble(), resp->LowPrice.GetDouble());
		} else if (msg->GetHead()->MessageID == mdl_bar_msg::XSHGCapitalFlow::MessageID) {
			mdl_bar_msg::XSHGCapitalFlow * resp = (mdl_bar_msg::XSHGCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s Px:%.2f Vol:%ld NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->BSFlag == 1 ? "B" : (resp->BSFlag == 2 ? "S" : "NA"),
				resp->Price.GetDouble(), resp->Volume, resp->NetCapitalInflow.GetDouble());
		} else if (msg->GetHead()->MessageID == mdl_bar_msg::IndustryCapitalFlow::MessageID) {
			mdl_bar_msg::IndustryCapitalFlow * resp = (mdl_bar_msg::IndustryCapitalFlow*)msg->GetBody();
 			printf("#%ld [%d:%02d:%02d] %s %s In:%.2f OutIn:%.2f NetIn:%.2f\n",
				msg->GetHead()->SequenceID,
				resp->TradeTime.GetHour(), resp->TradeTime.GetMinute(), resp->TradeTime.GetSecond(),
				resp->SecurityID.std_str().c_str(), 
				resp->SecurityName.std_str().c_str(),  
				resp->CapitalInFlow.GetDouble(),
				resp->CapitalOutFlow.GetDouble(),
				resp->NetCapitalInflow.GetDouble());
		}
	}

};

///////////////////////////////////////////////////////////////////////////////////////////////////////////

// Define program options structure
struct ProgramOptions {
    const char* filep;  // data file name prefix
    const char* logfp;  // log file name prefix
    const char* token;
    const char* redis_host;
    int redis_port;
    const char* redis_key;
    int zmq_port;
    const char* conf;   // Added config file option
    bool help;
    bool background;    // Added background option
	const char* md_source;  // 行情源
	const char* server_sz; // 深市行情服务器地址
	const char* server_sh; // 沪市行情服务器地址
	int hwm;    // ZMQ socket high water mark
};

// Parse command line arguments
#define DEFAULT_TOKEN "4EEB86544CA6F15C44F63118F55224CC"
#define DEFAULT_REDIS_PORT 6379
//#define DEFAULT_REDIS_HOST "************"
#define DEFAULT_REDIS_HOST NULL
#define DEFAULT_REDIS_KEY "STKLST2"
#define DEFAULT_ZMQ_PORT 9869
#define DEFAULT_MD_SOURCE "DYL2"
#define DEFAULT_HWM 0   // 使用系统的缺省值，如果要为分钟线计算程序提供行情，应该在配置文件中设置为更大的值如 6000 以免丢失数据

static ProgramOptions
ParseCommandLine(int argc, char* argv[]) {
    ProgramOptions options = {
        NULL,          // default data file name prefix
        NULL,          // default log file name prefix
        DEFAULT_TOKEN, // default token
        DEFAULT_REDIS_HOST,
        DEFAULT_REDIS_PORT,
        DEFAULT_REDIS_KEY,
        DEFAULT_ZMQ_PORT,
        NULL,          // default config file
        false,         // don't show help by default
        false,         // don't run in background by default
		DEFAULT_MD_SOURCE,
		SERVER_SZ,
		SERVER_SH,
		DEFAULT_HWM
    };

    static struct option long_options[] = {
        {"filep", required_argument, 0, 'f'},
        {"logfp", required_argument, 0, 'l'},  // Added log file prefix option
        {"token", required_argument, 0, 't'},  // Added token option
        {"redis_host", required_argument, 0, 'r'},
        {"redis_port", required_argument, 0, 'p'},
        {"redis_key", required_argument, 0, 'k'},
        {"zmq_port", required_argument, 0, 'z'},
        {"conf", required_argument, 0, 'c'},  // Added config file option
        {"background", no_argument,  0, 'b'},  // Added background option
		{"md_source", required_argument, 0, 's'}, // 行情源
		{"server_sz", required_argument, 0, 'Z'}, // 深市行情服务器地址
		{"server_sh", required_argument, 0, 'H'}, // 沪市行情服务器地址
		{"hwm", required_argument, 0, 'w'}, // ZeroMQ socket high water mark
        {"help",  no_argument,       0, 'h'},
        {0, 0, 0, 0}
    };

    int opt;
    int option_index = 0;

    while ((opt = getopt_long(argc, argv, "f:k:l:p:r:s:t:c:z:bZ:H:w:h", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'f':
                options.filep = optarg;
				if (strlen(options.filep) != 0) {
					options.filep = strdup(TimeFormatter::format(options.filep).c_str());
				}
                break;
            case 'l':
                options.logfp = optarg;
				if (strlen(options.logfp) != 0) {
					options.logfp = strdup(TimeFormatter::format(options.logfp).c_str());
				}
                break;
			case 's':
				options.md_source = optarg;
				break;
            case 't':
                options.token = optarg;
                break;
            case 'r':
                options.redis_host = optarg;
                break;
            case 'p':
                options.redis_port = std::stoi(optarg);
                break;
            case 'k':
                options.redis_key = optarg;
                break;
            case 'z':
                options.zmq_port = std::stoi(optarg);
                break;
            case 'c':
                options.conf = optarg;
                break;
            case 'b':
                options.background = true;
                break;
			case 'Z':
				options.server_sz = optarg;
				break;
			case 'H':
				options.server_sh = optarg;
				break;
			case 'w':
				options.hwm = std::stoi(optarg);
				break;
            case 'h':
                options.help = true;
                break;
            default:
                break;
        }
    }

    return options;
}

// Display help information
static void
PrintUsage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("Options:\n");
    printf("  -f, --filep=FILEP   File prefix for saving data (default: NULL)\n");
	printf("                      If you don't want to save data and yaml config\n");
	printf("                      has filep set, set this to an empty parameter\n");
    printf("  -l, --logfp=LOGFP   Log file prefix for saving log (default: NULL)\n");
	printf("                      If you don't want to save log and yaml config\n");
	printf("                      has logfp set, set this to an empty parameter\n");
    printf("  -t, --token=TOKEN   Token for authentication (default: %s)\n", DEFAULT_TOKEN);
    printf("  -r, --redis_host=HOST   Redis host (default: NULL)\n");
	printf("                      If you don't want to save data to redis and yaml config\n");
	printf("                      has redis_host set, set this to an empty parameter\n");
    printf("  -p, --redis_port=PORT   Redis port (default: %d)\n", DEFAULT_REDIS_PORT);
    printf("  -k, --redis_key=KEY   Redis key (default: %s)\n", DEFAULT_REDIS_KEY);
    printf("  -z, --zmq_port=PORT   ZeroMQ port (default: %d)\n", DEFAULT_ZMQ_PORT);
    printf("  -c, --conf=CONF     Config file in YAML format\n");
	std::cout << std::format("  -s, --md_source=SOURCE   Market data source (default: {})", DEFAULT_MD_SOURCE) << std::endl;
    printf("  -b, --background    Run in background as a daemon (default: false)\n");
	printf("  -Z, --server_sz=SERVER_SZ   Server address for Shenzhen market (default: %s)\n", SERVER_SZ);
	printf("  -H, --server_sh=SERVER_SH   Server address for Shanghai market (default: %s)\n", SERVER_SH);
	printf("  -w, --hwm=HWM   ZeroMQ socket high water mark (default: %d)\n", DEFAULT_HWM);
    printf("  -h, --help          Display this help and exit\n");
    printf("\nNote: This program connects to both Shenzhen and Shanghai markets simultaneously.\n");
}

// Load configuration from YAML file
// XXX 用到 strdup，会有点内存泄漏
static void LoadConfigFromYaml(const char* config_file, ProgramOptions* options) {
	if (config_file == NULL) {
		return;
	}
	std::ifstream ifs(config_file);
	if (!ifs.good()) {
		fprintf(stderr, "Error opening configuration file: %s\n", config_file);
		return;
	}
	fkyaml::node cfg;
	try {
		cfg = fkyaml::node::deserialize(ifs);
	} catch (const fkyaml::exception& e) {
		std::cout << "!!!Config file parse error: " << e.what() << std::endl;
		return;
	}
	try {
		const auto& config = cfg.as_map();
		for (const auto& c : config) {
			auto kt = c.first.get_type();
			if (kt == fkyaml::node_type::STRING) {  // key must be a string
				auto k = c.first.get_value<std::string>();
				if ((k == "filep") && (options->filep == NULL)) {  // command line option can overwrite yaml option
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {  // file prefix must be a string
						std::string s = c.second.get_value<std::string>();
						if (s.empty()) {
							options->filep = NULL;
						} else {
							options->filep = strdup(TimeFormatter::format(s).c_str());
						}
					} else {
						std::cout << "Config error: filep is not a string" << std::endl;
					}
				} else if ((k == "logfp") && (options->logfp == NULL)) {  // command line option can overwrite yaml option
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						std::string s = c.second.get_value<std::string>();
						if (s.empty()) {
							options->logfp = NULL;
						} else {
							options->logfp = strdup(TimeFormatter::format(s).c_str());
						}
					} else {
						std::cout << "Config error: logfp is not a string" << std::endl;
					}
				} else if (k == "token") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->token = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: token is not a string" << std::endl;
					}
				} else if (k == "md_source") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->md_source = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: md_source is not a string" << std::endl;
					}
				} else if (k == "redis_host") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->redis_host = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: redis_host is not a string" << std::endl;
					}
				} else if (k == "redis_port") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::INTEGER) {
						options->redis_port = c.second.get_value<int>();
					} else {
						std::cout << "Config error: redis_port is not an int" << std::endl;
					}
				} else if (k == "redis_key") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->redis_key = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: redis_key is not a string" << std::endl;
					}
				} else if (k == "zmq_port") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::INTEGER) {
						options->zmq_port = c.second.get_value<int>();
					} else {
						std::cout << "Config error: zmq_port is not an int" << std::endl;
					}
				} else if (k == "background") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::BOOLEAN) {
						options->background = c.second.get_value<bool>();
					} else {
						std::cout << "Config error: background is not a bool" << std::endl;
					}
				} else if (k == "server_sz") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->server_sz = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: server_sz is not a string" << std::endl;
					}
				} else if (k == "server_sh") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING) {
						options->server_sh = strdup(c.second.get_value<std::string>().c_str());
					} else {
						std::cout << "Config error: server_sh is not a string" << std::endl;
					}
				} else if (k == "high_water_mark") {
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::INTEGER) {
						options->hwm = c.second.get_value<int>();
					} else {
						std::cout << "Config error: high_water_mark is not an int" << std::endl;
					}
				} else {
					std::cout << "unkown config: " << k << std::endl;
				}
			}
		}
        printf("Loaded configuration from %s\n", config_file);
    } catch (const fkyaml::exception& e) {
		std::cout << "!!!Config file parse error: " << e.what() << std::endl;
		return;
    }
}

// Global variables for signal handler
static MyMessageHandler* g_szHandler = nullptr;
static MyMessageHandler* g_shHandler = nullptr;
static bool g_running = true;

// Signal handler function
static void signal_handler(int signum) {
    if (signum == SIGTERM || signum == SIGINT) {
        printf("\nReceived termination signal. Closing connections...\n");
        g_running = false;
		if (g_data_type == DATA_TICK) {
			cv_static_info.notify_one();   // 通知 redis_pub 线程退出
		}
        
        // Close connections
        if (g_szHandler != nullptr) {
            g_szHandler->Close();
            printf("Shenzhen market connection closed.\n");
        }
        
        if (g_shHandler != nullptr) {
            g_shHandler->Close();
            printf("Shanghai market connection closed.\n");
        }
        
		dymd_fini();

        printf("Exiting...\n");
        exit(0);
    }
}

static void redis_pub(const char *redis_host, int redis_port, const char *redis_key) {
	redisContext *c = redisConnect(redis_host, redis_port);
	while (c == NULL || c->err) {
		if (c) {
			std::cout << "Redis connection error: " << c->errstr << std::endl;
			std::this_thread::sleep_for(std::chrono::seconds(1));
			redisReconnect(c);
		} else {
			std::cout << "Connection error: can't allocate redis context" << std::endl;
			std::cout << "Retry in 1 second..." << std::endl;
			std::this_thread::sleep_for(std::chrono::seconds(1));
			c = redisConnect(redis_host, redis_port);
		}
	}
	std::cout << "Connected to Redis server " << redis_host << ":" << redis_port << std::endl;

	while (g_running) {
		static_info si;
		{
			std::unique_lock<std::mutex> lock(mtx_static_info);
			cv_static_info.wait(lock, []{ return !g_static_info.empty() || !g_running; });
			if (!g_running) {
				std::cout << "Exiting redis_pub thread..." << std::endl;
				redisFree(c);
				return;
			}
			si = g_static_info.front();
			g_static_info.pop();
		}

		// XXX 注意这里要保证每个字段都有非空值（也不能带空格），不然 redis 命令格式就不对了
		std::string cmd = std::format("HSET {}:{} SecurityID {} SecurityName {} szSecurityType {} "
			"PreClosePx {} TickSize {} ListDate {} Currency {} MDDate {} MaxPx {} MinPx {} "
			"BuyQtyUnit {} SellQtyUnit {} LLimitNum {} ULimitNum {} LMktPNum {} UMktNum {} FType {} SType {}",
			redis_key, si.SecurityID, si.SecurityID, si.SecurityName, si.szSecurityType,
			si.PreClosePx, si.TickSize, si.ListDate, si.Currency, si.MDDate, si.MaxPx, si.MinPx,
			si.BuyQtyUnit, si.SellQtyUnit, si.LLimitNum, si.ULimitNum, si.LMktPNum, si.UMktNum,
			si.FType, si.SType);
		// std::cout << "Will do redis cmd: " << cmd << std::endl;
		// continue;
		redisReply *reply = (redisReply*)redisCommand(c, cmd.c_str());
		if (reply == NULL) {
			std::cout << "Redis command error: " << c->errstr << " for cmd: " << cmd << std::endl;  // XXX should reconnect
		} else {
			freeReplyObject(reply);
		}
	}
	std::cout << "Exiting redis_pub thread..." << std::endl;

	redisFree(c);
}

int dymain(int argc, char* argv[], int data_type) {
	g_data_type = data_type;

    // Parse command line arguments
    ProgramOptions options = ParseCommandLine(argc, argv);

    // If help requested, show usage and exit
    if (options.help) {
        PrintUsage(argv[0]);
        return 0;
    }

    // Load configuration from YAML file if specified
    if (options.conf != NULL) {
        LoadConfigFromYaml(options.conf, &options);
    }
	if ((options.logfp != NULL) && (strlen(options.logfp) != 0)) {
		if (!prepare_dir(options.logfp)) {  // 如果 logfp 不是空，则创建目录
			fprintf(stderr, "Failed to create log file dir for %s\n", options.logfp);
			return 1;
		}
	}
	if ((options.filep != NULL) && (strlen(options.filep) != 0)) {
		if (!prepare_dir(options.filep)) {  // 如果 filep 不是空，则创建目录
			fprintf(stderr, "Failed to create data file dir for %s\n", options.filep);
			return 1;
		}
	}

    // Run as daemon if background option is set
    if (options.background) {
        printf("Starting in background mode...\n");
        
        // Fork the process
        pid_t pid = fork();
        
        if (pid < 0) {
            // Fork failed
            fprintf(stderr, "Failed to fork process: %s\n", strerror(errno));
            return 1;
        }
        
        if (pid > 0) {
            // Parent process exits
            printf("Daemon started with PID %d\n", pid);
            return 0;
        }
        
        // Child process continues
        
        // Create a new session and process group
        if (setsid() < 0) {
            fprintf(stderr, "Failed to create new session: %s\n", strerror(errno));
            return 1;
        }
        
        // Redirect standard file descriptors to /dev/null or log file
		std::string stdlogf = "/dev/null";
		if ((options.logfp != NULL) && (strlen(options.logfp) != 0)) {
			stdlogf = std::string(options.logfp) + ".std.log";
		}
        int fd = open(stdlogf.c_str(), O_WRONLY | O_CREAT | O_APPEND, 0666);
        if (fd < 0) {
			fprintf(stderr, "Failed to open log file %s: %s\n", stdlogf.c_str(), strerror(errno));
            return 1;
        }
        
        // Close standard file descriptors
        close(STDIN_FILENO);
        close(STDOUT_FILENO);
        close(STDERR_FILENO);
        
        dup2(fd, STDIN_FILENO);
        dup2(fd, STDOUT_FILENO);
        dup2(fd, STDERR_FILENO);
        
        if (fd > STDERR_FILENO) {
            close(fd);
        }
    }

    // Set up signal handler
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = signal_handler;
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGINT, &sa, NULL);

    // Create handlers for both markets
    MyMessageHandler szHandler(options.server_sz, options.server_sh);
    MyMessageHandler shHandler(options.server_sz, options.server_sh);
    
    // Set global pointers for message handler
    g_szHandler = &szHandler;
    g_shHandler = &shHandler;
    
    bool szConnected = false;
    bool shConnected = false;

	if ((data_type == DATA_TICK) && (options.redis_host != NULL) && (strlen(options.redis_host) != 0)) {  // 接收 tick 数据并发布到 redis
		g_do_redis = 1;
		std::thread redis_thread(redis_pub, options.redis_host, options.redis_port, options.redis_key);
		redis_thread.detach();
	}

    if (!dymd_init(data_type, options.logfp, options.zmq_port, options.hwm)) {
        printf("Failed to initialize dymd.\n");
        return 1;
    }

    // Connect to Shanghai market
    printf("Connecting to Shanghai market...\n");
    if (shHandler.Open(MARKET_SH, data_type, options.filep, options.token, options.md_source)) {
        printf("Connected to Shanghai market server: %s\n", shHandler.server_sh.c_str());
        printf("Data type: %s\n", data_type == DATA_TICK ? "Tick data" : "Transaction and Order data");
        printf("File prefix: %s\n", options.filep);
        shConnected = true;
    } else {
        printf("Failed to connect to Shanghai market.\n");
    }

    // Connect to Shenzhen market
    printf("Connecting to Shenzhen market...\n");
    if (szHandler.Open(MARKET_SZ, data_type, options.filep, options.token, options.md_source)) {
        printf("Connected to Shenzhen market server: %s\n", szHandler.server_sz.c_str());
        printf("Data type: %s\n", data_type == DATA_TICK ? "Tick data" : "Transaction and Order data");
        printf("File prefix: %s\n", options.filep);
        szConnected = true;
    } else {
        printf("Failed to connect to Shenzhen market.\n");
    }
    
    // Check if at least one connection was successful
    if (szConnected || shConnected) {
        printf("Receiving %s messages from %s%s markets.\n",
               data_type == DATA_TICK ? "tick" : "transaction and order",
               szConnected ? "Shenzhen" : "", 
               (szConnected && shConnected) ? " and Shanghai" : (shConnected ? "Shanghai" : ""));
        printf("Or send SIGTERM to gracefully terminate the program.\n");
        
        // Wait for user input or signal
        (void)pause();
    } else {
        printf("Failed to connect to any market.\n");
        return 1;
    }

    // Reset global pointers
    g_szHandler = nullptr;
    g_shHandler = nullptr;

    // Close connections
    if (szConnected) {
        szHandler.Close();
    }
    if (shConnected) {
        shHandler.Close();
    }
    
    return 0;
}

