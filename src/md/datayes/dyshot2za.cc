// 上交所逐笔行情接收及发布程序
//   - 从性能考虑，本程序只通过 ZMQ 进行发布，不进行存盘操作
//   - 所有 Channel 数据都发布到同一个端口
//   - 采用 ZMQ multipart 消息，其中第一个 frame 是 SecurityID (不带C字符串结尾的 0x00 字符)，第二个 frame 是 mdl_shl2_msg::NGTSTick 结构体
//     第三个 frame 是 LocalTime

#include <iostream>
#include <sstream>
#include <fstream>
#include <getopt.h>
#include <fcntl.h>
#include <signal.h>
#include <string>
#include <zmq.h>
#include <unistd.h>
#include <errno.h>
#include <chrono>
#include <format>
#include <filesystem>

#include "fkYAML/node.hpp"

#include "mdl_api.h"
#include "mdl_shl2_msg.h"

constexpr char VER_DYSHOT2ZA[] = "1.0.0";

void *g_zmq_ctx = nullptr;
void *g_zmq_sockets = nullptr;

using namespace datayes::mdl;

// ZMQ 的 PUB socket 非线程安全，所以这里设置成 1 可能最安全
int num_work_threads = 1;
IOManagerPtr g_IOManager;

static bool prepare_dir(const std::string &filep)
{
	namespace fs = std::filesystem;
	fs::path p(filep + "XXXX");
	fs::path dir = p.parent_path();

	if (dir.empty())
	{ // current dir
		return true;
	}

	try
	{
		fs::create_directories(dir);
		return true;
	}
	catch (const fs::filesystem_error &e)
	{
		std::cout << e.what() << std::endl;
		return false; // something wrong
	}
}

class TimeFormatter
{
public:
	static std::string format(const std::string &fmt)
	{
		auto now = std::chrono::system_clock::now();
		std::time_t now_time = std::chrono::system_clock::to_time_t(now);
		std::tm tm_buf;

		localtime_r(&now_time, &tm_buf);
		std::ostringstream oss;
		oss << std::put_time(&tm_buf, fmt.c_str());
		return oss.str();
	}
};

static bool dymd_init(const char *logfp, int zmq_port, int hwm)
{
	g_zmq_ctx = zmq_ctx_new();
	g_zmq_sockets = zmq_socket(g_zmq_ctx, ZMQ_PUB);
	zmq_setsockopt(g_zmq_sockets, ZMQ_SNDHWM, &hwm, sizeof(hwm));
	int r = zmq_bind(g_zmq_sockets, std::format("tcp://*:{}", zmq_port).c_str());
	if (r != 0)
	{
		std::cout << std::format("Failed to bind ZeroMQ socket ({}) : {}", zmq_strerror(errno), errno) << std::endl;
		return false;
	}

	g_IOManager = CreateIOManager(num_work_threads);
	if (g_IOManager.IsNull())
	{
		std::cout << "Incompatible API lib version." << std::endl;
		zmq_close(g_zmq_sockets);
		g_zmq_sockets = nullptr;
		if (g_zmq_ctx)
		{
			zmq_ctx_destroy(g_zmq_ctx);
			g_zmq_ctx = nullptr;
		}
		return false;
	}
	if ((logfp != nullptr) && (strlen(logfp) != 0))
	{
		if (std::string(logfp) == ".")
			logfp = "./";
		g_IOManager->EnableLog(logfp);
	}

	return true;
}

static void dymd_fini()
{
	if (!g_IOManager.IsNull())
	{
		g_IOManager->Shutdown();
	}
	if (g_zmq_sockets)
	{
		zmq_close(g_zmq_sockets);
		g_zmq_sockets = nullptr;
	}
	if (g_zmq_ctx)
	{
		zmq_ctx_destroy(g_zmq_ctx);
		g_zmq_ctx = nullptr;
	}
}

class SHMessageHandler : public MessageHandler
{
private:
	uint64_t seqno = 0;

public:
	bool Open(const char *token, const char *server)
	{
		bool multi_thread_callback = false; // 在各 OnXXXMessage() 回调中会向 ZMQ 发布数据，所以不能多线程
		SubscriberPtr sub = g_IOManager->CreateSubscriber(this, multi_thread_callback);
		// 上海
		sub->SetServerAddress(server);
		// subscribe Shanghai order and transaction data
		sub->SubcribeMessage<mdl_shl2_msg::NGTSTick>();
		sub->SetUserName(token); // Set to YOUR TOKEN!!!
		// MDLEID_MKTPRO (7) 应该比 MDLEID_BINARY (1) 快
		sub->SetMessageEncoding(MDLEID_MKTPRO);
		// sub->SetMessageEncoding(MDLEID_BINARY);
		std::cout << std::format("Heartbeat interval is {}, heartbeat timeout is {}", sub->GetHeartbeatInterval(), sub->GetHeartbeatTimeout()) << std::endl;

		// connect to server
		std::string err = sub->Connect();
		if (err.empty())
		{
			std::cout << "Connect to server successfully." << std::endl;
			return true;
		}
		std::cout << std::format("Connect to server failed: {}.", err) << std::endl;
		return false;
	}

	void Close()
	{
	}

	// handle network failure
	virtual void OnMDLAPIMessage(const MDLMessage *msg)
	{
		MDLMessageHead *head = msg->GetHead();
		if (head->MessageID == mdl_api_msg::ConnectingEvent::MessageID)
		{
			mdl_api_msg::ConnectingEvent *resp = (mdl_api_msg::ConnectingEvent *)msg->GetBody();
			std::cout << std::format("Connect to {} ...", resp->Address.c_str()) << std::endl;
		}
		else if (head->MessageID == mdl_api_msg::ConnectErrorEvent::MessageID)
		{
			mdl_api_msg::ConnectErrorEvent *resp = (mdl_api_msg::ConnectErrorEvent *)msg->GetBody();
			std::cout << std::format("Connect to {} failed {}.", resp->Address.c_str(), resp->ErrorMessage.c_str()) << std::endl;
		}
		else if (head->MessageID == mdl_api_msg::DisconnectedEvent::MessageID)
		{
			mdl_api_msg::DisconnectedEvent *resp = (mdl_api_msg::DisconnectedEvent *)msg->GetBody();
			std::cout << std::format("Disconnected from {}: {}.", resp->Address.c_str(), resp->ErrorMessage.c_str()) << std::endl;
		}
	}

	// handle server response
	virtual void OnMDLSysMessage(const MDLMessage *msg)
	{
		MDLMessageHead *head = msg->GetHead();
		if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID)
		{
			mdl_sys_msg::LogonResponse *resp = (mdl_sys_msg::LogonResponse *)msg->GetBody();
			if (resp->ReturnCode != MDLEC_OK)
			{
				std::cout << std::format("Logon failed: return code {}.", resp->ReturnCode) << std::endl;
			}
			for (uint32_t i = 0; i < resp->Services.Length; ++i)
			{
				for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j)
				{
					if (resp->Services[i]->Messages[j]->MessageStatus != MDLEC_OK)
					{
						std::cout << std::format("The server doesn't publish message (service id {} message id {})",
							   resp->Services[i]->ServiceID,
							   resp->Services[i]->Messages[j]->MessageID) << std::endl;
					}
				}
			}
		}
	}

	// handle shanghai level2 message
	void OnMDLSHL2Message(const MDLMessage *msg)
	{
		MDLMessageHead *head = msg->GetHead();
		if (head->MessageID == mdl_shl2_msg::NGTSTick::MessageID) // 4.24 竞价逐笔合并行情
		{
			if (head->SequenceID != (seqno + 1))
			{
				std::cout << std::format("seqno error: 当前消息号 {} 对不上上次消息号 {}", head->SequenceID, seqno) << std::endl;
			}
			seqno = head->SequenceID;
			mdl_shl2_msg::NGTSTick *resp = (mdl_shl2_msg::NGTSTick *)msg->GetBody();
			// std::cout << std::format("tickTime={}, security={}, type={}, tickbsflags={}", resp->TickTime.m_Value, resp->SecurityID.c_str(), resp->Type.c_str(), resp->TickBSFlag.c_str()) << std::endl;
			int r = zmq_send(g_zmq_sockets, (char *)resp->SecurityID.c_str(), resp->SecurityID.Length, ZMQ_SNDMORE);
			if (r == -1)
			{
				std::cerr << std::format("Error sending frame 1: {}", zmq_strerror(errno)) << std::endl;
			}
			r = zmq_send(g_zmq_sockets, (char *)resp, msg->GetBodySize(), ZMQ_SNDMORE);
			if (r == -1)
			{
				std::cerr << std::format("Error sending frame 2: {}", zmq_strerror(errno)) << std::endl;
			}
			r = zmq_send(g_zmq_sockets, (char *)&head->LocalTime, sizeof(head->LocalTime), 0);
			if (r == -1)
			{
				std::cerr << std::format("Error sending frame 3: {}", zmq_strerror(errno)) << std::endl;
			}
		}
	}
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////

// Define program options structure
struct ProgramOptions
{
	const char *logfp; // log file name prefix
	const char *server; // 行情服务器地址，如 "mdl-cloud-bj.datayes.com:19014;mdl-cloud-sz.datayes.com:19014;mdl-cloud-sh.datayes.com:19014"
	const char *token;
	int zmq_port; // ZMQ port
	int hwm;    // ZMQ socket high water mark
	const char *conf; // Added config file option
	bool help;
	bool background; // Added background option
};

// Parse command line arguments
constexpr char DEFAULT_TOKEN[] = "4EEB86544CA6F15C44F63118F55224CC";
constexpr int DEFAULT_ZMQ_PORT = 22011;
constexpr char SERVER_SH[] = "mdl-cloud-bj.datayes.com:19014;mdl-cloud-sz.datayes.com:19014;mdl-cloud-sh.datayes.com:19014";
constexpr int DEFAULT_HWM = 10000;

static void set_default_options(ProgramOptions &options)
{
	// 如果没在命令行或配置文件中配置，则设置缺省行情服务器地址
	if (options.server == NULL || std::strlen(options.server) == 0)
	{
		options.server = SERVER_SH;
	}

	if (options.token == NULL || std::strlen(options.token) == 0)
	{
		options.token = DEFAULT_TOKEN;
	}

	if (options.zmq_port == 0)
	{
		options.zmq_port = DEFAULT_ZMQ_PORT;
	}

	if (options.hwm == 0)
	{
		options.hwm = DEFAULT_HWM;
	}
}

static ProgramOptions
ParseCommandLine(int argc, char *argv[])
{
	ProgramOptions options = {
		NULL,    // 日志文件名前缀
		NULL,    // 行情服务器地址
		NULL,    // token
		0,       // 发布逐笔数据的 ZeroMQ 端口
		0,       // ZMQ socket high water mark
		NULL,    // YAML 配置文件名
		false,   // 是否显示帮助
		false    // 是否在后台运行
	};

	static struct option long_options[] = {
		{"logfp", required_argument, 0, 'l'}, // Added log file prefix option
		{"server", required_argument, 0, 's'}, // 行情服务器地址
		{"token", required_argument, 0, 't'}, // Added token option
		{"zmq_port", required_argument, 0, 'z'},
		{"hwm", required_argument, 0, 'h'},
		{"conf", required_argument, 0, 'c'}, // Added config file option
		{"background", no_argument, 0, 'b'}, // Added background option
		{"help", no_argument, 0, 'h'},
		{0, 0, 0, 0}};

	int opt;
	int option_index = 0;

	while ((opt = getopt_long(argc, argv, "l:s:t:c:w:z:bh", long_options, &option_index)) != -1)
	{
		switch (opt)
		{
		case 'l':
			options.logfp = optarg;
			if (strlen(options.logfp) != 0)
			{
				options.logfp = strdup(TimeFormatter::format(options.logfp).c_str());
			}
			break;
		case 's':
			options.server = optarg;
			break;
		case 't':
			options.token = optarg;
			break;
		case 'z':
			options.zmq_port = std::stoi(optarg);
			break;
		case 'w':
			options.hwm = std::stoi(optarg);
			break;
		case 'c':
			options.conf = optarg;
			break;
		case 'b':
			options.background = true;
			break;
		case 'h':
			options.help = true;
			break;
		default:
			break;
		}
	}

	return options;
}

// Display help information
static void
PrintUsage(const char *program_name)
{
	std::cout << std::format("上交所逐笔行情接收及发布程序 Version: {}", VER_DYSHOT2ZA) << std::endl;
	printf("Usage: %s [OPTIONS]\n", program_name);
	printf("Options:\n");
	printf("  -l, --logfp=LOGFP   Log file prefix for saving log (default: NULL)\n");
	printf("                      If you don't want to save log and yaml config\n");
	printf("                      has logfp set, set this to an empty parameter\n");
	printf("  -s, --server=SERVER   行情服务器地址 (default: %s)\n", SERVER_SH);
	printf("  -t, --token=TOKEN   Token for authentication (default: %s)\n", DEFAULT_TOKEN);
	printf("  -z, --zmq_port=PORT   ZeroMQ port (default: %d)\n", DEFAULT_ZMQ_PORT);
	printf("  -w, --hwm=HWM       ZMQ socket high water mark (default: %d)\n", DEFAULT_HWM);
	printf("  -c, --conf=CONF     Config file in YAML format\n");
	printf("  -b, --background    Run in background as a daemon (default: false)\n");
	printf("  -h, --help          Display this help and exit\n");
	printf("\nNote: This program connects to Shanghai market.\n");
}

// Load configuration from YAML file
// XXX 用到 strdup，会有点内存泄漏
static void LoadConfigFromYaml(const char *config_file, ProgramOptions *options)
{
	if (config_file == NULL)
	{
		return;
	}
	std::ifstream ifs(config_file);
	if (!ifs.good())
	{
		fprintf(stderr, "Error opening configuration file: %s\n", config_file);
		return;
	}
	fkyaml::node cfg;
	try
	{
		cfg = fkyaml::node::deserialize(ifs);
	}
	catch (const fkyaml::exception &e)
	{
		std::cout << "!!!Config file parse error: " << e.what() << std::endl;
		return;
	}
	try
	{
		const auto &config = cfg.as_map();
		for (const auto &c : config)
		{
			auto kt = c.first.get_type();
			if (kt == fkyaml::node_type::STRING)
			{ // key must be a string
				auto k = c.first.get_value<std::string>();
				if ((k == "logfp") && (options->logfp == NULL))
				{ // command line option can overwrite yaml option
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING)
					{
						std::string s = c.second.get_value<std::string>();
						if (s.empty())
						{
							options->logfp = NULL;
						}
						else
						{
							options->logfp = strdup(TimeFormatter::format(s).c_str());
						}
					}
					else
					{
						std::cout << "Config error: logfp is not a string" << std::endl;
					}
				}
				else if (k == "server")
				{
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING)
					{
						options->server = strdup(c.second.get_value<std::string>().c_str());
					}
					else
					{
						std::cout << "Config error: server is not a string" << std::endl;
					}
				}
				else if (k == "token")
				{
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::STRING)
					{
						options->token = strdup(c.second.get_value<std::string>().c_str());
					}
					else
					{
						std::cout << "Config error: token is not a string" << std::endl;
					}
				}
				else if (k == "zmq_port")
				{
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::INTEGER)
					{
						options->zmq_port = c.second.get_value<int>();
					}
					else
					{
						std::cout << "Config error: zmq_port is not an int" << std::endl;
					}
				}
				else if (k == "hwm")
				{
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::INTEGER)
					{
						options->hwm = c.second.get_value<int>();
					}
					else
					{
						std::cout << "Config error: hwm is not an int" << std::endl;
					}
				}
				else if (k == "background")
				{
					auto vt = c.second.get_type();
					if (vt == fkyaml::node_type::BOOLEAN)
					{
						options->background = c.second.get_value<bool>();
					}
					else
					{
						std::cout << "Config error: background is not a bool" << std::endl;
					}
				}
				else
				{
					std::cout << "unkown config: " << k << std::endl;
				}
			}
		}
		std::cout << std::format("Loaded configuration from {}", config_file) << std::endl;
	}
	catch (const fkyaml::exception &e)
	{
		std::cout << "!!!Config file parse error: " << e.what() << std::endl;
		return;
	}
}

// Global variables for signal handler
static SHMessageHandler *g_shHandler = nullptr;
static bool g_running = true;

// Signal handler function
static void signal_handler(int signum)
{
	if (signum == SIGTERM || signum == SIGINT)
	{
		std::cout << std::format("Received termination signal. Closing connections...") << std::endl;
		g_running = false;

		// Close connections
		if (g_shHandler != nullptr)
		{
			g_shHandler->Close();
			std::cout << "Shanghai market connection closed." << std::endl;
		}

		dymd_fini();

		std::cout << "Exiting..." << std::endl;
		exit(0);
	}
}

int main(int argc, char *argv[])
{
	// Parse command line arguments
	ProgramOptions options = ParseCommandLine(argc, argv);

	// If help requested, show usage and exit
	if (options.help)
	{
		PrintUsage(argv[0]);
		return 0;
	}

	// Load configuration from YAML file if specified
	if (options.conf != NULL)
	{
		LoadConfigFromYaml(options.conf, &options);
	}

	set_default_options(options);
	if ((options.logfp != NULL) && (strlen(options.logfp) != 0))
	{
		if (!prepare_dir(options.logfp))
		{
			fprintf(stderr, "Failed to create log file dir for %s\n", options.logfp);
			return 1;
		}
	}

	// Run as daemon if background option is set
	if (options.background)
	{
		std::cout << "Starting in background mode..." << std::endl;

		// Fork the process
		pid_t pid = fork();

		if (pid < 0)
		{
			// Fork failed
			std::cerr << std::format("Failed to fork process: {}", strerror(errno)) << std::endl;
			return 1;
		}

		if (pid > 0)
		{
			// Parent process exits
			std::cout << std::format("Daemon started with PID {}", pid) << std::endl;
			return 0;
		}

		// Child process continues

		// Create a new session and process group
		if (setsid() < 0)
		{
			std::cerr << std::format("Failed to create new session: {}", strerror(errno)) << std::endl;
			return 1;
		}

		// Redirect standard file descriptors to /dev/null or log file
		std::string stdlogf = "/dev/null";
		if ((options.logfp != NULL) && (strlen(options.logfp) != 0))
		{
			stdlogf = std::string(options.logfp) + ".std.log";
		}
		int fd = open(stdlogf.c_str(), O_WRONLY | O_CREAT | O_APPEND, 0666);
		if (fd < 0)
		{
			std::cerr << std::format("Failed to open log file {}: {}", stdlogf, strerror(errno)) << std::endl;
			return 1;
		}

		// Close standard file descriptors
		close(STDIN_FILENO);
		close(STDOUT_FILENO);
		close(STDERR_FILENO);

		dup2(fd, STDIN_FILENO);
		dup2(fd, STDOUT_FILENO);
		dup2(fd, STDERR_FILENO);

		if (fd > STDERR_FILENO)
		{
			close(fd);
		}
	}

	// Set up signal handler
	struct sigaction sa;
	memset(&sa, 0, sizeof(sa));
	sa.sa_handler = signal_handler;
	sigaction(SIGTERM, &sa, NULL);
	sigaction(SIGINT, &sa, NULL);

	SHMessageHandler shHandler;

	// Set global pointers for message handler
	g_shHandler = &shHandler;

	bool shConnected = false;

	if (!dymd_init(options.logfp, options.zmq_port, options.hwm))
	{
		std::cout << "Failed to initialize dymd." << std::endl;
		return 1;
	}

	// Connect to Shanghai market
	std::cout << "Connecting to Shanghai market..." << std::endl;
	if (shHandler.Open(options.token, options.server))
	{
		std::cout << std::format("Connected to Shanghai market server: {}", options.server) << std::endl;
		shConnected = true;
	}
	else
	{
		std::cout << "Failed to connect to Shanghai market." << std::endl;
	}

	if (shConnected)
	{
		std::cout << "Receiving messages from Shanghai market." << std::endl;
		std::cout << "Send SIGTERM to gracefully terminate the program." << std::endl;

		// Wait for user input or signal
		(void)pause();
	}
	else
	{
		std::cout << "Failed to connect to Shanghai market, quit." << std::endl;
		return 1;
	}

	// Reset global pointers
	g_shHandler = nullptr;

	// Close connections
	if (shConnected)
	{
		shHandler.Close();
	}

	return 0;
}
