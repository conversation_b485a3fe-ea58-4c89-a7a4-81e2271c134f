// 上交所逐笔数据转换成标准 protobuf 编码的行情信息并通过 ZeroMQ 发布极速行情快照
//   - 缺省从 Redis 获取涨跌停价、昨收价、最小价格变动单位
//   - 缺省从 ZeroMQ 获取逐笔数据

#include <iostream>
#include <fstream>
#include <format>
#include <cstring>
#include <sys/time.h>
#include <iomanip>
#include <getopt.h>
#include <errno.h>
#include <chrono>
#include <set>

#include <arrow/api.h>
#include <arrow/io/api.h>
#include <parquet/arrow/reader.h>
#include <parquet/arrow/writer.h>

#include <hiredis/hiredis.h>
#include <zmq.h>

#include "fkYAML/node.hpp"

#include "mdl_api.h"
#include "mdl_shl2_msg.h"

#include "lyproto.quota.pb.h"
#include "dyshot2t.h"

constexpr char VER_DYSHOTSNAP[] = "1.0.0";

using namespace datayes::mdl;

LOG_LEVEL sys_log_level = LOG_LEVEL::DEBUG;
std::set<std::string> g_etf_sh = {"510050", "510300", "510500", "510900", "512000", "512100", "512600", "513050", "513100", "513500", "518800", "519000"};

class TimeFormatter
{
public:
    static std::string format(const std::string &fmt)
    {
        auto now = std::chrono::system_clock::now();
        std::time_t now_time = std::chrono::system_clock::to_time_t(now);
        std::tm tm_buf;

        localtime_r(&now_time, &tm_buf);
        std::ostringstream oss;
        oss << std::put_time(&tm_buf, fmt.c_str());
        return oss.str();
    }
};

// 通过 ZeroMQ 发布 protobuf 编码的行情快照，如果 socket 为 nullptr，则不发布
// 不变的字段应在调用本函数前设置好，如：exchid, category, stkid, highlimit, lowlimit, preclose
// 成功返回发送的字节数，失败返回 -1
int send_tick_zmq(void *socket, LYPROTO::QUOTA::MarketData &o, const std::string &security_id, const Tick_info &tick_info)
{
    if (socket == nullptr)
        return 0;

    char tmpbuf[32];
    snprintf(tmpbuf, sizeof(tmpbuf), "%d", tick_info.exchtime);
    o.set_exchtime(tmpbuf);  // 交易时间，形如 HHMMSSmmm

    // rcvsvrtime 打算用来保存通联服务端的时间戳，目前 CSV/Parquet 文件中还没有，准备其其中添加这个字段
    // // 获取当前时间（UTC 或本地时间）
    // auto now = std::chrono::system_clock::now();
    // auto now_time_t = std::chrono::system_clock::to_time_t(now);
    // auto now_tm = *std::localtime(&now_time_t); // 转换为本地时间
    // // 提取毫秒部分
    // auto since_epoch = now.time_since_epoch();
    // auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(since_epoch).count() % 1000;
    // o.set_rcvsvrtime(std::format("{:d}{:02d}{:02d}{:03d}", now_tm.tm_hour, now_tm.tm_min, now_tm.tm_sec, ms));

    // o.set_pubsvrtime(tmpbuf);   // 真正发布时再填写

    // o.set_open(tick_info.open * 10);      // 转成万倍
    o.set_latest(tick_info.latest * 10);  // 转成万倍
    // o.set_high(tick_info.high * 10);  // 转成万倍
    // o.set_low(tick_info.low * 10);  // 转成万倍
    o.set_knock(tick_info.trade_num);  // 成交总笔数
    o.set_volume(tick_info.trade_qty);  // 成交总量
    o.set_value(tick_info.trade_money);  // 成交总金额，单位元
    o.set_widnum(tick_info.withdraw_num);  // 累计撤单笔数
    o.clear_ba();
    o.clear_bp();
    o.clear_sa();
    o.clear_sp();
    int i = 0;
    for (i = 0; i < 20; i++)
    {
        if (tick_info.bid20prices[i] == 0)
            break;
        o.add_bp(tick_info.bid20prices[i] * 10);  // 买盘价格，转成万倍
        o.add_ba(tick_info.bid20qty[i]);          // 买量
    }
    while (i < 3) // 至少 3 个买盘，不然老的程序可能会崩
    {
        o.add_bp(0);
        o.add_ba(0);
        i++;
    }
    for (i = 0; i < 20; i ++)
    {
        if (tick_info.ask20prices[i] == 0)
            break;
        o.add_sp(tick_info.ask20prices[i] * 10);  // 卖盘价格，转成万倍
        o.add_sa(tick_info.ask20qty[i]);          // 卖量
    }
    while (i < 3) // 至少 3 个卖盘，不然老的程序可能会崩
    {
        o.add_sp(0);
        o.add_sa(0);
        i++;
    }
    o.set_totalba(tick_info.total_buy_qty);  // 总委买量
    o.set_totalsa(tick_info.total_sell_qty);  // 总委卖量
    // 这两个量没有计算
    // o.set_weightedavgbidpx(tick_info.w_avg_bid_price * 10);  // 加权平均委买价，转成万倍
    // o.set_weightedavgofferpx(tick_info.w_avg_ask_price * 10);  // 加权平均委卖价，转成万倍

    char buf[1024];
    auto pbsize = o.ByteSizeLong();
    if (pbsize > 1024 - 15)
    {
        bad_thing(__func__, __LINE__, std::format("行情快照长度超过 1024: {}", pbsize));
        return -1;
    }
    auto pktsize = pbsize + 15;
    snprintf(buf, pktsize, "S%s%08ld:", security_id.c_str(), pbsize);
    o.SerializeToArray(buf + 15, pbsize);
    // Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("发送行情快照长度: {}", o.ByteSizeLong()));
    return zmq_send(socket, buf, pktsize, 0);
}

// 从 Redis 获取证券的涨跌停价、昨收价、最小价格变动单位
int get_security_info(redisContext *redis, const std::string &redis_key, const std::string &security_id, int &upper_limit, int &lower_limit, int &prev_close, int &increment)
{
    std::string key = std::format("{}:{}", redis_key, security_id);
    redisReply *reply = (redisReply *)redisCommand(redis, "HGETALL %s", key.c_str());
    if (reply == NULL)
    {
        std::cerr << "redisCommand failed: " << redis->errstr << std::endl;
        return 1;
    }
    if (reply->type != REDIS_REPLY_ARRAY)
    {
        std::cerr << "redisCommand failed: reply->type != REDIS_REPLY_ARRAY" << std::endl;
        freeReplyObject(reply);
        return 1;
    }
    for (int i = 0; i < static_cast<int>(reply->elements); i += 2)
    {
        if (std::string(reply->element[i]->str) == "MaxPx")
        {
            upper_limit = atoi(reply->element[i + 1]->str) / 10; // Redis 里是按万倍保存的
        }
        if (std::string(reply->element[i]->str) == "MinPx")
        {
            lower_limit = atoi(reply->element[i + 1]->str) / 10; // Redis 里是按万倍保存的
        }
        if (std::string(reply->element[i]->str) == "PreClosePx")
        {
            prev_close = atoi(reply->element[i + 1]->str) / 10; // Redis 里是按万倍保存的
        }
        if (std::string(reply->element[i]->str) == "TickSize")
        {
            increment = std::lround(atof(reply->element[i + 1]->str) * 1000); // TickSize 在 Redis 里是以元为单位保存的，要乘以 1000
        }
    }
    freeReplyObject(reply);
    return 0;
}

arrow::Result<std::vector<order_raw_sh>> read_parquet_file(const std::string &filename)
{
    std::shared_ptr<arrow::io::ReadableFile> infile;
    ARROW_ASSIGN_OR_RAISE(infile, arrow::io::ReadableFile::Open(filename));
    std::unique_ptr<parquet::arrow::FileReader> reader;
    PARQUET_ASSIGN_OR_THROW(reader, parquet::arrow::OpenFile(infile, arrow::default_memory_pool()));
    std::shared_ptr<arrow::Table> parquet_table;
    PARQUET_THROW_NOT_OK(reader->ReadTable(&parquet_table));

    auto schemap = parquet_table->schema();
    // std::cout << schemap->ToString() << std::endl;
    // std::cout << std::format("parquet_table->num_columns(): {}, parquet_table->num_rows(): {}", parquet_table->num_columns(), parquet_table->num_rows()) << std::endl;

    bool has_tltime = schemap->GetFieldByName("TLTime") != nullptr;  // 判断是否存在 TLTime 字段，这个字段在 2025年10月23日 起的 CSV/Parquet 文件中添加
    std::shared_ptr<arrow::Int64Array> tltimes;
    std::vector<order_raw_sh> order_raw_sh_vec;
    auto recvTimes = std::static_pointer_cast<arrow::StringArray>(parquet_table->GetColumnByName("RecvTime")->chunk(0));
    auto bizIndexs = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("BizIndex")->chunk(0));
    auto channels = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("Channel")->chunk(0));
    auto securityIDs = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("SecurityID")->chunk(0));
    auto tickTimes = std::static_pointer_cast<arrow::StringArray>(parquet_table->GetColumnByName("TickTime")->chunk(0));
    auto types = std::static_pointer_cast<arrow::StringArray>(parquet_table->GetColumnByName("Type")->chunk(0));
    auto buyOrderNos = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("BuyOrderNO")->chunk(0));
    auto sellOrderNos = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("SellOrderNO")->chunk(0));
    auto prices = std::static_pointer_cast<arrow::DoubleArray>(parquet_table->GetColumnByName("Price")->chunk(0));
    auto qtys = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("Qty")->chunk(0));
    auto tradeMoneys = std::static_pointer_cast<arrow::DoubleArray>(parquet_table->GetColumnByName("TradeMoney")->chunk(0));
    auto tickBSFlags = std::static_pointer_cast<arrow::StringArray>(parquet_table->GetColumnByName("TickBSFlag")->chunk(0));
    auto delays = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("Delay")->chunk(0));
    if (has_tltime)
    {
        tltimes = std::static_pointer_cast<arrow::Int64Array>(parquet_table->GetColumnByName("TLTime")->chunk(0));
    }

    for (int i = 0; i < parquet_table->num_rows(); i++)
    {
        order_raw_sh_vec.push_back({std::string(recvTimes->Value(i)),
                                    bizIndexs->Value(i),
                                    channels->Value(i),
                                    securityIDs->Value(i),
                                    std::string(tickTimes->Value(i)),
                                    HHMMSSmmm2uint32_t(tickTimes->Value(i)),
                                    std::string(types->Value(i)),
                                    buyOrderNos->Value(i),
                                    sellOrderNos->Value(i),
                                    prices->Value(i),
                                    qtys->Value(i),
                                    tradeMoneys->Value(i),
                                    std::string(tickBSFlags->Value(i)),
                                    delays->Value(i),
                                    has_tltime ? static_cast<uint32_t>(tltimes->Value(i)) : 0});
    }
    return order_raw_sh_vec;
}



int g_xxx_ = 0;
// 从 parquet 文件中读取逐笔数据并处理
arrow::Status RunSH(const std::string &filename, const std::string &security, void *socket_snap, int upper_limit, int lower_limit, int prev_close, int increment, const char *md_source)
{
    std::vector<order_raw_sh> order_raw_sh_vec;
    auto res = read_parquet_file(filename);
    if (res.ok())
    {
        order_raw_sh_vec = res.ValueOrDie();
    }
    else
    {
        DEBUG_LOGS(std::format("读取逐笔数据文件 {} 失败: {}", filename, res.status().message()));
        return res.status();
    }
    DEBUG_LOGS(std::format("order_raw_sh_vec.size(): {}", order_raw_sh_vec.size()));
    Shot2t o1{upper_limit, lower_limit, prev_close, increment, LOG_LEVEL::DEBUG};
    int r, i = 0;
    Tick_info *t = nullptr;
    Tick_info *t1 = nullptr;
    LYPROTO::QUOTA::MarketData o;
    o.set_exchid("1");             // 沪市
    o.set_category("S");           // 股票、基金
    o.set_mdsource(md_source);     // 快照行情源标签
    o.set_stkid(security.c_str()); // 证券代码
    o.set_highlimit(upper_limit * 10);  // 涨停价，万倍
    o.set_lowlimit(lower_limit * 10);  // 跌停价，万倍
    o.set_preclose(prev_close * 10);  // 昨收价，万倍
    int count = 0;
    for (auto &order : order_raw_sh_vec)
    {
        g_xxx_ = count;
        // std::cout << std::format("处理第 {} 条记录", count++) << std::endl;
        t = o1.put_instruction(order);
        if (t != nullptr)
        {
            if (order.tltime != 0)  // 如果有通联行情时间，则设置之，没有则不设置
            {
                o.set_rcvsvrtime(std::to_string(order.tltime).c_str());
            }
            r = send_tick_zmq(socket_snap, o, security, *t);
            i++;
            t1 = t;
            if (r < 0)
            {
                DEBUG_LOGS(std::format("send_tick_zmq 失败: {}", zmq_strerror(errno)));
                break;
            }
        }
    }
    if (i > 0)
    {
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("共生成 {} 条日志", i));
        print_tick_info(*t1);
    }
    else
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, "共生成 0 条日志");
    return arrow::Status::OK();
}

// Define program options structure
struct ProgramOptions
{
    const char *security;    // 证券代码
    const char *logfp;       // 日志文件名前缀
    const char *redis_host;  // 用于取涨跌停价等信息的 Redis 服务器
    int redis_port;          // 用于取涨跌停价等信息的 Redis 服务器端口
    const char *redis_key;   // 用于取涨跌停价等信息的 Redis 关键字
    int zmq_port_snap;       // 发布全速行情快照的 ZMQ 端口
    bool cmdl_zmq_port_snap; // 已通过命令行指定发布极速行情快照的 ZMQ 端口
    bool use_pub;            // 是否使用本地 PUB 而非向远程 XSUB 发布 ZMQ 全速行情
    int zmq_xsub_port;       // 发布全速行情快照的 XSUB 端口
    bool cmdl_zmq_xsub_port; // 已通过命令行指定发布全速行情快照的 XSUB 端口
    const char *zmq_xsub_host; // 发布全速行情快照的远程 XSUB 服务器地址
    int hwm_snap;            // 发布全速行情快照的 ZMQ socket 的 high water mark
    const char *zmq_host_ot; // 接收逐笔数据的 ZMQ 服务器
    int zmq_port_ot;         // 接收逐笔数据的 ZMQ 服务器端口
    int hwm_ot;              // 接收逐笔数据的 ZMQ socket 的 high water mark
    const char *conf;        // YAML 配置文件
    bool help;               // 显示帮助信息
    bool background;         // 在后台运行
    bool cmdl_background;    // 已通过命令行指定是否后台运行
    int upper_limit;         // 涨停价，单位厘，用于测试时直接指定而非从 Redis 取
    int lower_limit;         // 跌停价，单位厘，用于测试时直接指定而非从 Redis 取
    int prev_close;          // 昨收价，单位厘，用于测试时直接指定而非从 Redis 取
    int increment;           // 最小价格变动单位，单位厘，用于测试时直接指定而非从 Redis 取
    const char *parquet;     // 读取 parquet 文件进行测试
    const char *md_source;   // 行情数据源标签
};

constexpr char DEFAULT_SECURITY[] = "600900";
constexpr char DEFAULT_REDIS_HOST[] = "************";
constexpr char DEFAULT_REDIS_KEY[] = "STKLST2";
constexpr int DEFAULT_REDIS_PORT = 6379;
constexpr int DEFAULT_ZMQ_PORT_SNAP = 30002;
constexpr int DEFAULT_ZMQ_XSUB_PORT = 30001;
constexpr char DEFAULT_ZMQ_XSUB_HOST[] = "127.0.0.1";
constexpr int DEFAULT_ZMQ_PORT_OT = 22011;  // 输入逐笔数据的 ZeroMQ 端口
constexpr char DEFAULT_ZMQ_HOST_OT[] = "127.0.0.1";
constexpr int DEFAULT_HWM_SNAP = 10000;
constexpr int DEFAULT_HWM_OT = 10000;
constexpr char DEFAULT_MD_SOURCE[] = "DYSHOT";

static ProgramOptions
ParseCommandLine(int argc, char *argv[])
{
    ProgramOptions options = {
        NULL,  // 证券代码
        NULL,  // 日志文件名前缀
        NULL,  // Redis 服务器地址，用于获取涨跌停价等信息
        0,     // Redis 服务器端口，用于获取涨跌停价等信息
        NULL,  // Redis 关键字前缀，用于获取涨跌停价等信息
        0,     // 输出盘口的 ZeroMQ 端口
        false, // 未通过命令行指定输出盘口的 ZeroMQ 端口
        false, // 不使用本地 PUB 而向远程 XSUB 发布 ZMQ 全速行情
        0,     // 发布全速行情快照的 XSUB 端口
        false, // 未通过命令行指定发布全速行情快照的 XSUB 端口
        NULL,  // 发布全速行情快照的远程 XSUB 服务器地址
        0,     // 发布全速行情快照的 ZMQ socket 的 high water mark
        NULL,  // 获取逐笔数据的 ZeroMQ 服务器地址
        0,     // 获取逐笔数据的 ZeroMQ 端口
        0,     // 接收逐笔数据的 ZMQ socket 的 high water mark
        NULL,  // YAML 配置文件名
        false, // 不显示帮助信息
        false, // 不后台运行
        false, // 未通过命令行指定是否后台运行
        0,     // 涨停价
        0,     // 跌停价
        0,     // 昨收价
        0,     // 最小价格变动单位
        NULL,  // parquet 文件名
        DEFAULT_MD_SOURCE // 行情数据源标签
    };

    static struct option long_options[] = {
        {"security", required_argument, 0, 's'},
        {"logfp", required_argument, 0, 'l'},
        {"redis_host", required_argument, 0, 'r'},
        {"redis_port", required_argument, 0, 'p'},
        {"redis_key", required_argument, 0, 'k'},
        {"zmq_port_snap", required_argument, 0, 'z'},
        {"use_pub", no_argument, 0, 'm'},
        {"zmq_xsub_port", required_argument, 0, 'x'},
        {"zmq_xsub_host", required_argument, 0, 'y'},
        {"hwm_snap", required_argument, 0, 'W'},
        {"zmq_host_ot", required_argument, 0, 'o'},
        {"zmq_port_ot", required_argument, 0, 't'},
        {"hwm_ot", required_argument, 0, 'n'},
        {"conf", required_argument, 0, 'c'},
        {"help", no_argument, 0, 'h'},
        {"background", no_argument, 0, 'b'},
        {"upper_limit", required_argument, 0, 'u'},
        {"lower_limit", required_argument, 0, 'w'},
        {"prev_close", required_argument, 0, 'v'},
        {"increment", required_argument, 0, 'i'},
        {"parquet", required_argument, 0, 'q'},
        {"md_source", required_argument, 0, 'M'},
        {0, 0, 0, 0}};

    int opt;
    int option_index = 0;

    while ((opt = getopt_long(argc, argv, "l:r:s:p:k:z:o:t:c:mM:n:u:w:v:W:x:y:i:q:bh", long_options, &option_index)) != -1)
    {
        switch (opt)
        {
        case 's':
            options.security = optarg;
            break;
        case 'l':
            options.logfp = optarg;
            if (std::strlen(options.logfp) != 0)
            {
                options.logfp = strdup(TimeFormatter::format(options.logfp).c_str());
            }
            break;
        case 'r':
            options.redis_host = optarg;
            break;
        case 'p':
            options.redis_port = std::stoi(optarg);
            break;
        case 'k':
            options.redis_key = optarg;
            break;
        case 'z':
            options.zmq_port_snap = std::stoi(optarg);
            options.cmdl_zmq_port_snap = true;
            break;
        case 'm':
            options.use_pub = true;
            break;
        case 'M':
            options.md_source = optarg;
            break;
        case 'x':
            options.zmq_xsub_port = std::stoi(optarg);
            options.cmdl_zmq_xsub_port = true; // 已通过命令行指定通过远程 XSUB 方式发布全速行情的端口
            break;
        case 'y':
            options.zmq_xsub_host = optarg;
            break;
        case 'W':
            options.hwm_snap = std::stoi(optarg);
            break;
        case 'o':
            options.zmq_host_ot = optarg;
            break;
        case 't':
            options.zmq_port_ot = std::stoi(optarg);
            break;
        case 'n':
            options.hwm_ot = std::stoi(optarg);
            break;
        case 'c':
            options.conf = optarg;
            break;
        case 'b':
            options.background = true;
            options.cmdl_background = true;
            break;
        case 'h':
            options.help = true;
            break;
        case 'u':
            options.upper_limit = std::stoi(optarg);
            break;
        case 'w':
            options.lower_limit = std::stoi(optarg);
            break;
        case 'v':
            options.prev_close = std::stoi(optarg);
            break;
        case 'i':
            options.increment = std::stoi(optarg);
            break;
        case 'q':
            options.parquet = optarg;
            break;
        default:
            break;
        }
    }

    return options;
}

// 帮助信息
static void
PrintUsage(const char *program_name)
{
    std::cout << std::format("上交所逐笔行情转全速行情快照程序 Version: {}", VER_DYSHOTSNAP) << std::endl;
    std::cout << std::format("Usage: {} [OPTIONS]", program_name) << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << std::format("  -s, --security=SECURITY   证券代码 (default: {})", DEFAULT_SECURITY) << std::endl;
    std::cout << "  -l, --logfp=LOGFP   日志文件前缀 (default: NULL)" << std::endl;
    std::cout << "                      如果不想将日志保存在文件中而 yaml 配置文件中已经设置了" << std::endl;
    std::cout << "                      logfp, 则可在命令行将此参数设置成空字符串来实现" << std::endl;
    std::cout << std::format("  -r, --redis_host=HOST   Redis 服务器地址 (default: {})", DEFAULT_REDIS_HOST) << std::endl;
    std::cout << "                          涨跌停价、前收价及最小价格变动单位要么全部通过命令行或 yaml 指定，" << std::endl;
    std::cout << "                          要么通过 Redis 获取" << std::endl;
    std::cout << std::format("  -p, --redis_port=PORT   Redis 端口 (default: {})", DEFAULT_REDIS_PORT) << std::endl;
    std::cout << std::format("  -k, --redis_key=KEY     Redis 关键字前缀 (default: {})", DEFAULT_REDIS_KEY) << std::endl;
    std::cout << std::format("  -z, --zmq_port_snap=PORT   输出盘口的本地 ZeroMQ 端口 (default: {})", DEFAULT_ZMQ_PORT_SNAP) << std::endl;
    std::cout << "                             如果不需要通过 ZeroMQ 输出盘口数据，则可在命令行将此参数设置成0来实现" << std::endl;
    std::cout << "  -m, --use_pub       通过 ZeroMQ 发布全速行情时，使用 PUB 模式而非缺省的 XSUB 模式" << std::endl;
    std::cout << std::format("  -x, --zmq_xsub_port=PORT   发布全速行情快照的远程 XSUB 端口 (default: {})", DEFAULT_ZMQ_XSUB_PORT) << std::endl;
    std::cout << "                             如果不需要通过 ZeroMQ 输出盘口数据，则可在命令行将此参数设置成0来实现" << std::endl;
    std::cout << std::format("  -y, --zmq_xsub_host=HOST   发布全速行情快照的远程 XSUB 服务器地址 (default: {})", DEFAULT_ZMQ_XSUB_HOST) << std::endl;
    std::cout << std::format("  -W, --hwm_snap=HWM_SNAP    发布全速行情快照的 ZMQ socket 的 high water mark (default: {})", DEFAULT_HWM_SNAP) << std::endl;
    std::cout << std::format("  -o, --zmq_host_ot=HOST     获取逐笔数据的 ZeroMQ 服务器地址 (default: {})", DEFAULT_ZMQ_HOST_OT) << std::endl;
    std::cout << "                             如果不想从 ZeroMQ 获取逐笔数据而 yaml 配置文件中已经设置了" << std::endl;
    std::cout << "                             zmq_host_ot，则可在命令行将此参数设置成空字符串来实现，" << std::endl;
    std::cout << "                             此配置优先级低于使用 parquet 文件进行测试" << std::endl;
    std::cout << std::format("  -t, --zmq_port_ot=PORT   获取逐笔数据的 ZeroMQ 端口 (default: {})", DEFAULT_ZMQ_PORT_OT) << std::endl;
    std::cout << std::format("  -n, --hwm_ot=HWM_OT      接收逐笔数据的 ZMQ socket 的 high water mark (default: {})", DEFAULT_HWM_OT) << std::endl;
    std::cout << "  -u, --upper_limit=UPPER_LIMIT   涨停价（厘），缺省为 0 从 Redis 获取" << std::endl;
    std::cout << "  -w, --lower_limit=LOWER_LIMIT   跌停价（厘），缺省为 0 从 Redis 获取" << std::endl;
    std::cout << "  -v, --prev_close=PREV_CLOSE     昨收价（厘），缺省为 0 从 Redis 获取" << std::endl;
    std::cout << "  -i, --increment=INCREMENT       最小价格变动单位（厘），缺省为 0 从 Redis 获取" << std::endl;
    std::cout << "  -q, --parquet=PARQUET   读取 parquet 文件进行测试" << std::endl;
    std::cout << "                          此配置优先级高于使用 ZeroMQ 获取逐笔数据" << std::endl;
    std::cout << "  -c, --conf=CONF     YAML 配置文件" << std::endl;
    std::cout << "  -b, --background    后台运行" << std::endl;
    std::cout << std::format("  -M, --md_source=SOURCE    行情数据源标签 (default: {})", DEFAULT_MD_SOURCE) << std::endl;
    std::cout << "  -h, --help          显示帮助信息并退出" << std::endl;
}

// Load configuration from YAML file
// XXX 用到 strdup，会有点内存泄漏
static void LoadConfigFromYaml(const char *config_file, ProgramOptions *options)
{
    if (config_file == NULL)
    {
        return;
    }
    std::ifstream ifs(config_file);
    if (!ifs.good())
    {
        std::cerr << std::format("Error opening configuration file: {}", config_file) << std::endl;
        return;
    }
    fkyaml::node cfg;
    try
    {
        cfg = fkyaml::node::deserialize(ifs);
    }
    catch (const fkyaml::exception &e)
    {
        std::cout << "!!!Config file parse error: " << e.what() << std::endl;
        return;
    }

    for (const auto &c : cfg.as_map())
    {
        auto kt = c.first.get_type();
        if (kt == fkyaml::node_type::STRING)
        { // key must be a string
            auto k = c.first.get_value<std::string>();
            if (k == "security")
            {
                if (options->security != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->security = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: security is not a string" << std::endl;
                }
            }
            else if (k == "logfp")
            {
                if (options->logfp != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->logfp = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: logfp is not a string" << std::endl;
                }
            }
            else if (k == "redis_host")
            {
                if (options->redis_host != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->redis_host = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: redis_host is not a string" << std::endl;
                }
            }
            else if (k == "redis_port")
            {
                if (options->redis_port != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->redis_port = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: redis_port is not an int" << std::endl;
                }
            }
            else if (k == "redis_key")
            {
                if (options->redis_key != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->redis_key = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: redis_key is not a string" << std::endl;
                }
            }
            else if (k == "zmq_port_snap")
            {
                if (options->cmdl_zmq_port_snap) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->zmq_port_snap = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: zmq_port_snap is not an int" << std::endl;
                }
            }
            else if (k == "use_pub")
            {
                if (options->use_pub) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::BOOLEAN)
                {
                    options->use_pub = c.second.get_value<bool>();
                }
                else
                {
                    std::cout << "Config error: use_pub is not a bool" << std::endl;
                }
            }
            else if (k == "zmq_xsub_port")
            {
                if (options->cmdl_zmq_xsub_port) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->zmq_xsub_port = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: zmq_xsub_port is not an int" << std::endl;
                }
            }
            else if (k == "zmq_xsub_host")
            {
                if (options->zmq_xsub_host != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->zmq_xsub_host = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: zmq_xsub_host is not a string" << std::endl;
                }
            }
            else if (k == "hwm_snap")
            {
                if (options->hwm_snap != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->hwm_snap = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: hwm_snap is not an int" << std::endl;
                }
            }
            else if (k == "zmq_host_ot")
            {
                if (options->zmq_host_ot != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->zmq_host_ot = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: zmq_host_ot is not a string" << std::endl;
                }
            }
            else if (k == "zmq_port_ot")
            {
                if (options->zmq_port_ot != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->zmq_port_ot = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: zmq_port_ot is not an int" << std::endl;
                }
            }
            else if (k == "hwm_ot")
            {
                if (options->hwm_ot != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->hwm_ot = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: hwm_ot is not an int" << std::endl;
                }
            }
            else if (k == "background")
            {
                if (options->cmdl_background) // 已通过命令行指定，以命令行为准
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::BOOLEAN)
                {
                    options->background = c.second.get_value<bool>();
                }
                else
                {
                    std::cout << "Config error: background is not a bool" << std::endl;
                }
            }
            else if (k == "upper_limit")
            {
                if (options->upper_limit != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->upper_limit = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: upper_limit is not an int" << std::endl;
                }
            }
            else if (k == "lower_limit")
            {
                if (options->lower_limit != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->lower_limit = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: lower_limit is not an int" << std::endl;
                }
            }
            else if (k == "prev_close")
            {
                if (options->prev_close != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->prev_close = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: prev_close is not an int" << std::endl;
                }
            }
            else if (k == "increment")
            {
                if (options->increment != 0) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::INTEGER)
                {
                    options->increment = c.second.get_value<int>();
                }
                else
                {
                    std::cout << "Config error: increment is not an int" << std::endl;
                }
            }
            else if (k == "parquet")
            {
                if (options->parquet != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->parquet = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: parquet is not a string" << std::endl;
                }
            }
            else if (k == "md_source")
            {
                if (options->md_source != NULL) // 已通过命令行指定
                {
                    continue;
                }
                auto vt = c.second.get_type();
                if (vt == fkyaml::node_type::STRING)
                {
                    options->md_source = strdup(c.second.get_value<std::string>().c_str());
                }
                else
                {
                    std::cout << "Config error: md_source is not a string" << std::endl;
                }
            }
            else
            {
                std::cout << "unkown config: " << k << std::endl;
            }
        }
    }
    printf("Loaded configuration from %s\n", config_file);
}

int main(int argc, char *argv[])
{
    ProgramOptions options = ParseCommandLine(argc, argv);
    if (options.help)
    {
        PrintUsage(argv[0]);
        return 0;
    }
    if (options.conf != NULL)
    {
        LoadConfigFromYaml(options.conf, &options);
    }
    if (options.security == NULL) // 命令行与配置文件都没有指定证券代码
    {
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("证券代码缺省设置为 {}", DEFAULT_SECURITY));
        options.security = DEFAULT_SECURITY;
    }

    if (options.upper_limit == 0 && options.lower_limit == 0 && options.prev_close == 0 && options.increment == 0) // 没有通过命令行或配置文件设置任何一项价格信息
    {
        // 从 Redis 获取涨跌停价、昨收价、最小价格变动单位
        if (options.redis_host == NULL)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("Redis 服务器地址缺省设置为 {}", DEFAULT_REDIS_HOST));
            options.redis_host = DEFAULT_REDIS_HOST;
        }
        if (options.redis_port == 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("Redis 服务器端口缺省设置为 {}", DEFAULT_REDIS_PORT));
            options.redis_port = DEFAULT_REDIS_PORT;
        }
        if (options.redis_key == NULL)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("Redis key 缺省设置为 {}", DEFAULT_REDIS_KEY));
            options.redis_key = DEFAULT_REDIS_KEY;
        }
        redisContext *redis = redisConnect(options.redis_host, options.redis_port);
        if (redis == NULL || redis->err)
        {
            if (redis)
            {
                Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("Redis 连接错误: {}", redis->errstr));
            }
            else
            {
                Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, "Redis 连接错误: 无法分配 redis context");
            }
            return 1;
        }
        int r = get_security_info(redis, options.redis_key, options.security, options.upper_limit, options.lower_limit, options.prev_close, options.increment);
        redisFree(redis);
        if (r != 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("get_security_info 失败: {}", r));
            return 1;
        }
        if (options.upper_limit == 0 || options.lower_limit == 0 || options.prev_close == 0 || options.increment == 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("从 Redis 获取涨停价 {}、跌停价 {}、昨收价 {}、最小变动单位 {} 有一个为 0",
                options.upper_limit, options.lower_limit, options.prev_close, options.increment));
            return 1;
        }
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("从 Redis 获取涨停价 {}、跌停价 {}、昨收价 {}、最小变动单位 {} 成功",
            options.upper_limit, options.lower_limit, options.prev_close, options.increment));
    }
    else if (options.upper_limit == 0 || options.lower_limit == 0 || options.prev_close == 0 || options.increment == 0)
    {
        Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, "涨跌停价、前收价及最小变动单位必须同时设置或者同时不设置");
        return 1;
    }
    else
    {
        DEBUG_LOGS(std::format("[{}]直接指定（厘）涨停: {}, 跌停: {}, 前收: {}, 最小变动单位: {}",
            options.security, options.upper_limit, options.lower_limit, options.prev_close, options.increment));
        if (g_etf_sh.contains(options.security))
        {
            DEBUG_LOGS(std::format("证券 {} 是 ETF，最小变动单位设置为 1 厘", options.security));
            options.increment = 1;
        }
    }
    
    // if (options.lower_limit < 1000)
    // {
    //     DEBUG_LOGS("跌停价小于 1 元，程序退出");
    //     return 1;
    // }
    if (options.increment == 10)  // 最小价格变动价格是 1 分时
    {
        if (options.lower_limit % 10 != 0)
        {
            DEBUG_LOGS("跌停价不是 1 分的倍数，程序退出");
            return 1;
        }
        if (options.upper_limit % 10 != 0)
        {
            DEBUG_LOGS("涨停价不是 1 分的倍数，程序退出");
            return 1;
        }
    }

    void *context = nullptr;
    void *socket_ot = nullptr;
    void *socket_snap = nullptr;

    if (options.use_pub) // 通过命令行或配置文件指定使用本地 PUB 而非远程 XSUB 发布 ZMQ 全速行情
    {
        options.zmq_xsub_port = 0;
    }
    else
    {
        if (options.zmq_xsub_port == 0 && !options.cmdl_zmq_xsub_port)  // 没有通过命令行或配置文件指定发布全速行情的 XSUB 端口，使用缺省值
        {
            options.zmq_xsub_port = DEFAULT_ZMQ_XSUB_PORT;
        }
        if (options.zmq_xsub_host == NULL)
        {
            options.zmq_xsub_host = DEFAULT_ZMQ_XSUB_HOST;
        }
    }
    if (options.zmq_port_snap == 0 && !options.cmdl_zmq_port_snap )  // 没有通过命令行或配置文件指定发布全速行情的 ZMQ 端口，使用缺省值，如果不需要发布全速行情，则应通过命令行或配置文件显式指定为 0
    {
        options.zmq_port_snap = DEFAULT_ZMQ_PORT_SNAP;
    }
    if (options.hwm_snap == 0)
    {
        options.hwm_snap = DEFAULT_HWM_SNAP;
    }
    if (options.zmq_port_snap != 0 || options.zmq_xsub_port != 0) // 需要通过 ZeroMQ 发布快照数据
    {
        context = zmq_ctx_new();
        socket_snap = zmq_socket(context, ZMQ_PUB);
        zmq_setsockopt(socket_snap, ZMQ_SNDHWM, &options.hwm_snap, sizeof(options.hwm_snap));
        int r;
        if (options.zmq_xsub_port != 0)  // 连接远程的 XSUB 服务器
        {
            r = zmq_connect(socket_snap, std::format("tcp://{}:{}", options.zmq_xsub_host, options.zmq_xsub_port).c_str());
            if (r != 0)
            {
                Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("绑定 ZeroMQ 快照发布端口失败 ({}) : {}", zmq_strerror(errno), errno));
                zmq_close(socket_snap);
                zmq_ctx_term(context);
                return 1;
            }
        }
        else
        {
            r = zmq_bind(socket_snap, std::format("tcp://*:{}", options.zmq_port_snap).c_str());
            if (r != 0)
            {
                Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("绑定 ZeroMQ 快照发布端口失败 ({}) : {}", zmq_strerror(errno), errno));
                zmq_close(socket_snap);
                zmq_ctx_term(context);
                return 1;
            }
        }
        int linger = 0;
        size_t linger_size = sizeof(linger);
        r = zmq_getsockopt(socket_snap, ZMQ_LINGER, &linger, &linger_size);
        if (r != 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("获取 ZeroMQ 快照发布端口的 ZMQ_LINGER 选项失败 ({}) : {}", zmq_strerror(errno), errno));
            zmq_close(socket_snap);
            zmq_ctx_term(context);
            return 1;
        }
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("绑定 ZeroMQ 快照发布端口成功: {}, linger = {}", options.zmq_port_snap, linger));
        linger = 5000;  // 5 秒
        linger_size = sizeof(linger);
        r = zmq_setsockopt(socket_snap, ZMQ_LINGER, &linger, linger_size);
        if (r == 0)
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("设置 ZeroMQ 快照发布端口的 ZMQ_LINGER 选项成功: linger = {}", linger));
        else
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("设置 ZeroMQ 快照发布端口的 ZMQ_LINGER 选项失败 ({}) : {}", zmq_strerror(errno), errno));
    }
    else
    {
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, "不需要通过 ZeroMQ 发布快照数据");
    }
    if (options.parquet != NULL) // 从 parquet 文件中读取逐笔数据优先
    {
        if (socket_snap != nullptr)
        {
            std::cout << std::format("准备处理 parquet 文件，同时将通过 ZeroMQ 发布快照数据，请连接完客户端到本机的 {} 端口后按回车继续...", options.zmq_port_snap) << std::endl;
            std::cin.get();
        }
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("开始处理上交所逐笔数据文件 {}", options.parquet));
        (void)RunSH(options.parquet, options.security, socket_snap, options.upper_limit, options.lower_limit, options.prev_close, options.increment, options.md_source);
        Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("处理上交所逐笔数据文件 {} 完成", options.parquet));
        if (context != nullptr)
        {
            zmq_close(socket_snap);
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, "关闭 ZeroMQ 快照发布端口");
            zmq_ctx_term(context);
            Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, "销毁 ZeroMQ 上下文");
        }
        return 0;
    }

    // 从 ZeroMQ 获取逐笔数据进行实时处理
    if (options.zmq_host_ot == NULL || std::strlen(options.zmq_host_ot) == 0)
    {
        options.zmq_host_ot = DEFAULT_ZMQ_HOST_OT;
        Shot2t::log(sys_log_level, LOG_LEVEL::INFO, std::format("ZeroMQ 逐笔数据源地址未设置，使用缺省值: {}", options.zmq_host_ot));
    }
    if (options.zmq_port_ot == 0)
    {
        options.zmq_port_ot = DEFAULT_ZMQ_PORT_OT;
        Shot2t::log(sys_log_level, LOG_LEVEL::INFO, std::format("ZeroMQ 逐笔数据源端口未设置，使用缺省值: {}", options.zmq_port_ot));
    }
    if (options.hwm_ot == 0)
    {
        options.hwm_ot = DEFAULT_HWM_OT;
    }
    Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("开始处理来自 ZeroMQ {}:{} 的上交所逐笔数据", options.zmq_host_ot, options.zmq_port_ot));
    if (context == nullptr) // 前面判断不需要发布快照数据，所以没有初始化 ZeroMQ
    {
        context = zmq_ctx_new();
    }
    socket_ot = zmq_socket(context, ZMQ_SUB); // 用于接收逐笔数据
    zmq_setsockopt(socket_ot, ZMQ_SNDHWM, &options.hwm_ot, sizeof(options.hwm_ot));
    zmq_connect(socket_ot, std::format("tcp://{}:{}", options.zmq_host_ot, options.zmq_port_ot).c_str());
    zmq_setsockopt(socket_ot, ZMQ_SUBSCRIBE, options.security, std::strlen(options.security));

    char buffer[1024];  // 1024 足够了，frame 1 是 6 字符的证券代码，frame 2 是 NGTSTick 结构体
    mdl_shl2_msg::NGTSTick *resp = (mdl_shl2_msg::NGTSTick *)buffer;
    int more = 0;
    size_t more_size;
    uint32_t tl_locatltime;
    char tmpbuf[32]; // 用于字符串表示的通联服务器时间戳
    Tick_info *t = nullptr;
    Shot2t o1{options.upper_limit, options.lower_limit, options.prev_close, options.increment, LOG_LEVEL::DEBUG};

    LYPROTO::QUOTA::MarketData o;
    o.set_exchid("1");                 // 沪市
    o.set_category("S");               // 股票、基金
    o.set_mdsource(options.md_source); // 快照行情源标签
    o.set_stkid(options.security);     // 证券代码
    o.set_highlimit(options.upper_limit * 10); // 涨停价，万倍
    o.set_lowlimit(options.lower_limit * 10);   // 跌停价，万倍
    o.set_preclose(options.prev_close * 10);   // 昨收价，万倍

    while (true)
    {
        auto r = zmq_recv(socket_ot, buffer, sizeof(buffer), 0);  // frame 1 是 6 字符的证券代码
        if (r == -1)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 1 失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(r) > sizeof(buffer))
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 1 异常: 数据长度 {} 超过缓冲区大小 {}, 退出", r, sizeof(buffer)));
            break;
        }
        more_size = sizeof(more);
        r = zmq_getsockopt(socket_ot, ZMQ_RCVMORE, &more, &more_size);
        if (r != 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("获取 ZeroMQ 上交所逐笔数据 ZMQ_RCVMORE 标志失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (more != 1)  // 必须要有 frame 2
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 1 后，ZMQ_RCVMORE 标志非 1，少了 frame 2，退出"));
            break;
        }
        r = zmq_recv(socket_ot, buffer, sizeof(buffer), 0);  // frame 2 是 NGTSTick 结构体
        if (r == -1)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 2 失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(r) > sizeof(buffer))
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 2 异常: 数据长度 {} 超过缓冲区大小 {}, 退出", r, sizeof(buffer)));
            break;
        }
        more_size = sizeof(more);
        r = zmq_getsockopt(socket_ot, ZMQ_RCVMORE, &more, &more_size);
        if (r != 0)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("获取 ZeroMQ 上交所逐笔数据 ZMQ_RCVMORE 标志失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (more != 1)  // 必须要有 frame 3
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 2 后，ZMQ_RCVMORE 标志非 1，少了 frame 3，退出"));
            break;
        }
        r = zmq_recv(socket_ot, &tl_locatltime, sizeof(tl_locatltime), 0);  // frame 3 是 LocalTime
        if (r == -1)
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 3 失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(r) != sizeof(tl_locatltime))
        {
            Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("接收 ZeroMQ 上交所逐笔数据 frame 3 异常: 数据长度 {} 不等于期望长度 {}, 退出", r, sizeof(tl_locatltime)));
            break;
        }
        snprintf(tmpbuf, sizeof(tmpbuf), "%d", tl_locatltime); // 通联接收时间戳，目前 parquet 文件中没有
        o.set_rcvsvrtime(tmpbuf);
        t = o1.put_instruction(*resp);
        if (t != nullptr)
        {
            int r = send_tick_zmq(socket_snap, o, options.security, *t);
            if (r < 0)
            {
                Shot2t::log(sys_log_level, LOG_LEVEL::ERROR, std::format("send_tick_zmq 失败: {}", zmq_strerror(errno)));
                break;
            }
        }
    }

    zmq_close(socket_ot);
    zmq_ctx_term(context);
    return 0;
}
