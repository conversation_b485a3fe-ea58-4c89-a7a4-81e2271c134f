# dyot 配置文件
# 本配置文件的各项配置都可被命令行参数覆盖

# 逐笔交易文件前缀，如果不想保存交易文件，请将此项设置为 null，或不设置
# 深市逐笔委托文件将保存为 filep + "sz_order.csv"
# 沪市逐笔成交文件将保存为 filep + "sh_trans.csv"
# 沪市逐笔委托成交合并文件将保存为 filep + "sh_transorder.csv"
# 支持日期格式符，如 %Y-%m-%d
# 请参考 https://en.cppreference.com/w/cpp/io/manip/put_time
# 如果要用纯数字或YAML关键字作文件名，请加引号
filep: /b/dymd/%Y-%m-%d/

# 日志文件前缀，如果不想保存日志文件，请将此项设置为 null，或不设置
# 通联库日志文件将保存为 logfp + ".log" 和 logfp + ".trace.log"
# 程序日志文件将保存为 logfp + ".std.log"
# 支持日期格式符，如 %Y-%m-%d
# 请参考 https://en.cppreference.com/w/cpp/io/manip/put_time
# 如果要用纯数字或YAML关键字作文件名，请加引号
logfp: /b/dymd/%Y-%m-%d/dyot

# 通联 token，缺省为 2025.6 的主线 4EEB86544CA6F15C44F63118F55224CC
#token: 4EEB86544CA6F15C44F63118F55224CC
# 备线
token: 4E401BF9EC6FB0EECB1EE0AA7F8FC3FF

# 是否在后台运行，缺省为 false
background: true
