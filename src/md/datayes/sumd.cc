// 超级行情 super market data：股票、基金行情消息择优发布程序
// 从不同行情源订阅行情数据，择优发布到 ZeroMQ
//   - 支持 L1、L2 行情
//   - 支持逐笔转全速行情
//   - 支持更高发布频率

#include <thread>
#include <getopt.h>
#include <iostream>
#include <fstream>
#include <format>
#include <string>
#include <unordered_map>
#include <vector>
#include <list>
#include <unordered_set>
#include <zmq.h>
#include <chrono>

#include "fkYAML/node.hpp"
#include "lyproto.quota.pb.h"

#include "sumd.h"
#include "ut_zmd.h" // 极简行情数据结构

constexpr char VER_SUMD[] = "0.0.2";  // 程序版本
constexpr char DEFAULT_CONFIG_FILE[] = "sumd.yaml";  // 缺省配置文件名
constexpr char DEFAULT_OUTPUT_HOST[] = "";  // 缺省行情输出服务器地址
constexpr int DEFAULT_OUTPUT_PORT = 30003;    // 缺省行情输出端口
constexpr char DEFAULT_OUTPUT_HOST_UT[] = "";  // 缺省极简行情输出服务器地址
constexpr int DEFAULT_OUTPUT_PORT_UT = 30005;  // 缺省极简行情输出端口
constexpr int DEFAULT_OUTPUT_INTV_MIN = 100;  // 缺省最小行情输出间隔，毫秒
constexpr int DEFAULT_OUTPUT_INTV_MAX = 500;  // 缺省最大行情输出间隔，毫秒，必须是 1000 的因数
constexpr int DEFAULT_OUTPUT_INTV_STEP = 50;  // 缺省时间驱动的行情输出间隔步进，毫秒，必须是 1000 的因数
constexpr int DEFAULT_RANDOM_ADD = 5;  // 缺省针对最小行情输出间隔提前的时间，毫秒
constexpr char DEFAULT_SUBSCRIPTION[] = "S";  // 缺省订阅所有股票、基金行情
constexpr int DEFAULT_HWM = 100;  // 缺省 ZeroMQ socket 的 high water mark，包括输入与输出，高值反而有害，
                                  // 因为处理、发送堆积的过时数据没有意义且浪费资源
constexpr int MAX_NUM_SECURITIES = 20000;                // 最大证券数量，会用它来确定 SecurityInfo unordered_map 容器的大小，以免发生 rehashing
constexpr char ZMQ_TIMER_TRANSPORT[] = "inproc://zmq_timer";

// 行情源，需要从配置构造
struct MdSource
{
    std::string name; // 行情源名称
    std::string host; // 行情源 ZMQ 服务器地址
    int port; // 行情源 ZMQ 服务器端口
};

// 证券信息，保存最新关键行情信息，缓存未发送的行情消息
// 注意这里保存的都是最新已知数据，不一定是某个单个行情源的最新数据，有可能是综合多个行情源的最新数据
struct SecurityInfo
{
    MDSourceType last_source_type;                            // 最新行情源类型
    ExchID exch_id;                                           // 交易所标识
    int64_t volume;                                           // 累计成交量
    int withdraw_num;                                         // 累计撤单笔数，仅逐笔合成与上交所 L2 行情有，用于比较上交所 L2 行情和逐笔合成行情的先后，
                                                              // 上交所证券应保持其递增，非上交所证券应保持为 0
    int64_t total_buy_volume;                                 // 当前总申买量，仅逐笔合成与上交所 L2 行情有，用于比较上交所 L2 行情和逐笔合成行情的先后，
                                                              // 仅在累计成交量与累计撤单笔数相同的情况下有时间非减性。非上交所证券应保持为 0
    int64_t total_sell_volume;                                // 当前总申卖量，仅逐笔合成与上交所 L2 行情有，用于比较上交所 L2 行情和逐笔合成行情的先后，
                                                              // 仅在累计成交量与累计撤单笔数相同的情况下有时间非减性。非上交所证券应保持为 0
    int32_t last_exchtime;                                    // 格式为 HHMMSSmmm，对于深交所为最新行情时间戳（可为L1、L2或逐笔合成行情），对于上交所为最新的逐笔合成行情时间戳
    int32_t last_exchtime_1;                                  // 格式为 HHMMSSmmm，仅用于上交所，为最新已知 L1 行情时间戳，粒度为毫秒
    int32_t last_exchtime_2;                                  // 格式为 HHMMSSmmm，仅用于上交所，为最新已知 L2 行情时间戳，粒度为秒
    bool has_saved_msg;                                       // 是否有保存的行情消息
    std::chrono::system_clock::time_point next_fast_pub_time; // 下次快速发布时间
    int bucket;                                               // 下次最长发布间隔定时器所在的槽位，-1 表示没有定时器
    std::list<std::unordered_map<std::string, SecurityInfo>::iterator>::iterator it_maxwait;  // 最大发布间隔定时器槽位链表中的位置，用于删除定时器
    int saved_pb_msg_size;                                    // 保存的行情消息的长度
    char saved_pb_msg[1024];                                  // 保存的行情消息
    std::chrono::system_clock::time_point saved_time;         // 保存的行情消息的接收时间点，定时器到期时如果保存的行情的接收时间老于 50 毫秒则放弃发布，直接进入立即触发状态
};

// 程序参数
struct ProgramOptions
{
    std::string conf;   // 配置 YAML 文件路径
    std::vector<MdSource> sources;  // 行情源
    std::unordered_map<std::string, MDSourceType> md_source_type_dict; // 行情源类型字典，key 为消息中的 mdsource 字段，value 为行情源类型
    std::vector<std::string> subscriptions; // 订阅的行情主题，必须以字符 'S' 开头，因为指数编码与股票、基金编码有重合，指数编码以 'I' 开头
    std::string output_host;   // 输出行情的 ZeroMQ XSUB 服务器地址
    int output_port;   // 输出行情的 ZeroMQ XSUB 服务器端口
    std::string output_host_ut;   // 输出极简行情的 ZeroMQ XSUB 服务器地址
    int output_port_ut;   // 输出极简行情的 ZeroMQ XSUB 服务器端口
    int output_intv_min; // 最小行情输出间隔，毫秒
    int output_intv_max; // 最大行情输出间隔，毫秒，必须是 1000 的因数
    int output_intv_step; // 时间驱动的行情输出间隔步进，毫秒，必须是 1000 的因数
    int hwm;            // ZeroMQ socket 的 high water mark，包括输入与输出
    bool help;
    bool background;    // Added background option
};

// Load configuration from YAML file
static bool LoadConfigFromYaml(const char *config_file, ProgramOptions *options)
{
    if (config_file == nullptr)
    {
        return false;
    }
    std::ifstream ifs(config_file);
    if (!ifs.good())
    {
        std::cout << std::format("Error opening configuration file: {}", config_file) << std::endl;
        return false;
    }
    fkyaml::node cfg;
    try
    {
        cfg = fkyaml::node::deserialize(ifs);
    }
    catch (const fkyaml::exception &e)
    {
        std::cout << "!!!Config file parse error: " << e.what() << std::endl;
        return false;
    }

    try
    {
        const auto &config = cfg.as_map();
        for (const auto &c : config)
        {
            auto kt = c.first.get_type();
            if (kt == fkyaml::node_type::STRING)
            { // key must be a string
                auto k = c.first.get_value<std::string>();
                if (k == "sources")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            MdSource source;
                            for (const auto &sc : s.as_map())
                            {
                                auto sk = sc.first.get_value<std::string>();
                                if (sk == "name")
                                {
                                    source.name = sc.second.get_value<std::string>();
                                }
                                else if (sk == "host")
                                {
                                    source.host = sc.second.get_value<std::string>();
                                }
                                else if (sk == "port")
                                {
                                    source.port = sc.second.get_value<int>();
                                }
                                else
                                {
                                    std::cout << "unkown config: " << sk << std::endl;
                                }
                            }
                            options->sources.push_back(source);
                        }
                    }
                    else
                    {
                        std::cout << "Config error: sources is not a sequence" << std::endl;
                        return false;
                    }
                }
                else if (k == "md_source_type_dict")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::MAPPING)
                    {
                        for (const auto &m : c.second.as_map())
                        {
                            auto mk = m.first.get_value<std::string>();
                            auto mv = m.second.get_value<std::string>();
                            if (md_source_type_from_string(mv) == MDSourceType::UNKOWN)
                            {
                                std::cout << "Config error: unknown md source type " << mv << std::endl;
                                return false;
                            }
                            options->md_source_type_dict[mk] = md_source_type_from_string(mv);
                        }
                    }
                    else
                    {
                        std::cout << "Config error: md_source_type_dict is not a mapping，忽略" << std::endl;
                    }
                }
                else if (k == "subscriptions")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            options->subscriptions.push_back(s.get_value<std::string>());
                        }
                    }
                    else
                    {
                        std::cout << "Config error: subscriptions is not a sequence" << std::endl;
                        return false;
                    }
                }
                else if (k == "output")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::MAPPING)
                    {
                        for (const auto &o : c.second.as_map())
                        {
                            auto ok = o.first.get_value<std::string>();
                            if (ok == "host")
                            {
                                options->output_host = o.second.get_value<std::string>();
                            }
                            else if (ok == "port")
                            {
                                options->output_port = o.second.get_value<int>();
                            }
                            else if (ok == "host_ut")
                            {
                                options->output_host_ut = o.second.get_value<std::string>();
                            }
                            else if (ok == "port_ut")
                            {
                                options->output_port_ut = o.second.get_value<int>();
                            }
                            else if (ok == "intv_min")
                            {
                                options->output_intv_min = o.second.get_value<int>();
                            }
                            else if (ok == "intv_max")
                            {
                                options->output_intv_max = o.second.get_value<int>();
                            }
                            else if (ok == "intv_step")
                            {
                                options->output_intv_step = o.second.get_value<int>();
                            }
                            else
                            {
                                std::cout << "unkown config: " << ok << std::endl;
                            }
                        }
                    }
                    else
                    {
                        std::cout << "Config error: output is not a map" << std::endl;
                        return false;
                    }
                }
                else if (k == "high_water_mark")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::INTEGER)
                    {
                        options->hwm = c.second.get_value<int>();
                    }
                    else
                    {
                        std::cout << "Config error: high_water_mark is not an int" << std::endl;
                        return false;
                    }
                }
                else if (k == "background")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::BOOLEAN)
                    {
                        options->background = c.second.get_value<bool>();
                    }
                    else
                    {
                        std::cout << "Config error: background is not a bool" << std::endl;
                        return false;
                    }
                }
                else
                {
                    std::cout << "unkown config: " << k << std::endl;
                }
            }
        }
        std::cout << std::format("Loaded configuration from {}", config_file) << std::endl;
    }
    catch (const fkyaml::exception &e)
    {
        std::cout << "!!!Config file parse error: " << e.what() << std::endl;
        return false;
    }
    return true;
}

static void PrintUsage(const char *progname)
{
    std::cout << std::format("超级行情 sumd 程序 Version: {}", VER_SUMD) << std::endl;
    std::cout << std::format("Usage: {} [OPTIONS]", progname) << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << std::format("  -c, --conf <file>   Configuration file path (default: {})", DEFAULT_CONFIG_FILE) << std::endl;
    std::cout << "  -b, --background    Run in background" << std::endl;
    std::cout << "  -h, --help          Display this help and exit" << std::endl;
}

static bool ParseCommandLine(int argc, char *argv[], ProgramOptions &options)
{
    static struct option long_options[] = {
        {"conf", required_argument, NULL, 'c'},
        {"background", no_argument, NULL, 'b'},
        {"help", no_argument, NULL, 'h'},
        {NULL, 0, NULL, 0}};

    int c;
    while ((c = getopt_long(argc, argv, "c:bh", long_options, NULL)) != -1)
    {
        switch (c)
        {
        case 'c':
            options.conf = optarg;
            break;
        case 'b':
            options.background = true;
            break;
        case 'h':
            options.help = true;
            break;
        default:
            return false;
        }
    }
    return true;
}

static bool GetProgramOptions(int argc, char *argv[], ProgramOptions &options)
{
    if (!ParseCommandLine(argc, argv, options))
    {
        PrintUsage(argv[0]);
        BAD_THING("解析命令行参数错误");
    }
    if (options.help)
    {
        PrintUsage(argv[0]);
        return false;
    }
    if (options.conf.empty())
    {
        options.conf = DEFAULT_CONFIG_FILE;
    }
    if (!LoadConfigFromYaml(options.conf.c_str(), &options))
    {
        BAD_THING("加载配置文件错误");
    }
    if (options.sources.empty())
    {
        BAD_THING("没有配置行情源");
    }
    if (options.subscriptions.empty())
    {
        options.subscriptions.push_back(DEFAULT_SUBSCRIPTION);
    }
    for (const auto &s : options.subscriptions)
    {
        if (s[0] != 'S')
        {
            BAD_THING(std::format("订阅的行情主题必须以字母S开头，但有主题 {} 没有以S开头", s));
        }
    }
    if (1000 % options.output_intv_max != 0)
    {
        BAD_THING("最大行情输出间隔必须是 1000 的因数");
    }
    if (1000 % options.output_intv_step != 0)
    {
        BAD_THING("行情输出间隔步进必须是 1000 的因数");
    }
    if (options.output_intv_step >= options.output_intv_max)
    {
        BAD_THING("行情输出间隔步进必须小于最大行情输出间隔");
    }
    if (options.output_host_ut.empty() && options.output_host.empty())
    {
        options.output_host = "127.0.0.1"; // 如果都没设置或者都设置为空，则缺省输出 PB 行情到本地
    }
    return true;
}

// 从每秒的整数倍时间点发送消息，消息内容为倍数，参数 intv_step 为毫秒为单位的间隔时间，它必须可以整除 1000
static void zmq_timer(void *ctx, int intv_step)
{
    void * socket = zmq_socket(ctx, ZMQ_PUB);
    zmq_bind(socket, ZMQ_TIMER_TRANSPORT);
    
    char content[sizeof(int) + 1];  // ZMQ 发布的内容为 'T' + multiplier 的二进制表示
    content[0] = 'T';
    while (true)
    {
        auto now = std::chrono::system_clock::now();
        auto ms_since_epoch = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        auto next_ms = ((ms_since_epoch / intv_step) + 1) * intv_step;
        auto next_time = std::chrono::system_clock::time_point(std::chrono::milliseconds(next_ms));
        
        std::this_thread::sleep_until(next_time);
        
        // 计算该秒内毫秒数除以 intv_step 的倍数
        int ms_in_second = next_ms % 1000;
        int *multiplierp = reinterpret_cast<int *>(content + 1);
        *multiplierp = ms_in_second / intv_step;
        
        zmq_send(socket, &content, sizeof(content), 0);
    }
}

// 设置两个定时器：一个是最大发布间隔定时器，一个是最小发布间隔定时器
// - 最大发布间隔定时器，此定时器由定时器线程发布的定时器消息驱动
//   * 如果原先挂在某个槽位上，则应该先从原来的槽位中删除
//   * 根据当前时间点，挂入对应的最大发布间隔时点对应的定时器槽位中
// - 最小发布间隔定时器，此定时器由行情消息本身驱动，每次行情消息都会检查是否触发
static inline std::list<std::unordered_map<std::string, SecurityInfo>::iterator>::iterator
set_timers(std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> &timer_lists,
        std::unordered_map<std::string, SecurityInfo>::iterator it, int intv_min, int intv_max, int intv_step)
{
    auto now = std::chrono::system_clock::now();
    int num_bucks = 1000 / intv_step;  // 定时器槽位数
    auto ms_since_last_whole_second = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count() % 1000;
    auto curent_bucket = ms_since_last_whole_second / intv_step;  // 此时刻所在的定时器槽位
    int bucket = (curent_bucket + intv_max / intv_step) % num_bucks;
    std::list<std::unordered_map<std::string, SecurityInfo>::iterator>::iterator next;
    if (it->second.bucket != -1)  // 原先挂在某个槽位上，要先从原来的槽位中删除
    {
        next = timer_lists[it->second.bucket].erase(it->second.it_maxwait);
    }
    // 在 SecurityInfo 中指回定时器槽位链表中的位置，以便后续删除定时器时使用
    it->second.it_maxwait = timer_lists[bucket].insert(timer_lists[bucket].end(), it);
    it->second.bucket = bucket;
    it->second.next_fast_pub_time = now + std::chrono::milliseconds(intv_min);
    return next;
}

static void send_zmq(void *pub_socket, void *ut_pub_socket, const char *buff, int msg_len)
{
    static uint64_t mdid = 0;
    if (ut_pub_socket != nullptr)  // 输出极简行情
    {
        LYPROTO::QUOTA::MarketData o;
        o.ParseFromArray(buff + 15, msg_len - 15);
        zmd_t z{0};

        z.ty = 'S';
        memcpy(z.code, o.stkid().c_str(), o.stkid().size());
        z.st = 0;
        int n = 5;
        if (o.bp_size() < 5)
        {
            n = o.bp_size();
        }
        uint32_t *p = &z.BP1;
        for (int i = 0; i < n; ++i)
        {
            *p = o.bp(i); // 申买价，扩大万倍
            p++;
            *p = o.ba(i); // 申买量
            p += 3;
        }
        if (o.sp_size() < 5)
        {
            n = o.sp_size();
        }
        else
        {
            n = 5;
        }
        p = &z.SP1;
        for (int i = 0; i < n; ++i)
        {
            *p = o.sp(i); // 申卖价，扩大万倍
            p++;
            *p = o.sa(i); // 申卖量
            p += 3;
        }
        z.mdid = mdid++;
        zmq_send(ut_pub_socket, &z, sizeof(z), 0);
    }
    if (pub_socket != nullptr)  // 输出 PB 行情
    {
        zmq_send(pub_socket, buff, msg_len, 0);
    }
}

// 处理深交所消息成交量没变的情况
static void proc_sz_mesg_same_volume(std::unordered_map<std::string, SecurityInfo>::iterator &it, LYPROTO::QUOTA::MarketData &o, void *pub_socket,
    void *ut_pub_socket, const char *buff, int msg_len,  // 本消息的内容及其长度
    MDSourceType mdsource_type,  // 本消息的行情源类型，L1、L2还是逐笔合成
    std::chrono::time_point<std::chrono::system_clock> &now,  // 收到本消息的时间点
    int32_t exchtime,       // 本消息的交易所时间
    int intv_min, int intv_max, int intv_step,
    std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> &timer_lists)
{
    if ((mdsource_type == MDSourceType::L1 || mdsource_type == MDSourceType::L2)  // 本行情为交易所原始 L1 或 L2 行情
        && (it->second.last_source_type == MDSourceType::L1 || it->second.last_source_type == MDSourceType::L2))  // 前一行情也是交易所原始 L1 或 L2 行情
    {
        // 这是比较常见的情形，不同行情源的原始行情在短时间内相继到来
        // 深交所原始 L1 和 L2 行情的时间戳都只提供到秒数，且为 3 的倍数，所以只要时间戳不同，大的就是新的
        if (exchtime > it->second.last_exchtime)  // 本消息的行情时间戳较新，为新行情
        {
            send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
            it->second.has_saved_msg = false;
            it->second.last_exchtime = exchtime;
            it->second.last_source_type = mdsource_type;
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        }
        return;
    }
    else if ((mdsource_type == MDSourceType::OT) && (it->second.last_source_type == MDSourceType::OT))  // 本消息与前消息都是逐笔合成行情
    {
        // 交易所时间戳精确到毫秒，只要本消息时间戳大于等于前一消息的时间戳，就是新消息（前提假设是只有一个逐笔合成行情源），
        // 注意不同逐笔合成消息的交易所时间戳可以是相等的，因为其来源的逐笔消息的时间戳可以是相等的
        // 由于本程序假设对于每支证券最多只有一个逐笔合成行情源，所以后到的逐笔合成行情肯定比原来的新，我们不必检查时间戳了
        // 到达最小发布间隔则发布之，否则保存到缓存中
        if (now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
        {
            send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
            it->second.has_saved_msg = false;
            it->second.last_exchtime = exchtime; // 不需要更新 volume，因为本函数是处理 volume 相同的特殊情况
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        }
        else  // 还没有到最小发送间隔时间点，缓存此消息
        {
            it->second.saved_pb_msg_size = msg_len;
            memcpy(it->second.saved_pb_msg, buff, msg_len);
            it->second.has_saved_msg = true;
            it->second.saved_time = now;
            it->second.last_exchtime = exchtime;
        }
        return;
    }
    else  // 分别为逐笔合成行情与原始交易所行情
    {
        // 如果两个行情的交易所时间戳不在同一个 3 秒周期内，肯定可以区分先后
        int this_ms = exchtime / 10000000 * 3600000 + exchtime % 10000000 / 100000 * 60000 + exchtime % 100000;
        int last_ms = it->second.last_exchtime / 10000000 * 3600000 + it->second.last_exchtime % 10000000 / 100000 * 60000 + it->second.last_exchtime % 100000;
        int this_3sec = this_ms / 3000;
        int last_3sec = last_ms / 3000;
        if (this_3sec > last_3sec)  // 以 3 秒为单位，本消息的行情时间较新，为新行情
        {
            if ((now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
                || (mdsource_type == MDSourceType::L2 || mdsource_type == MDSourceType::L1))  // 交易所原始行情特殊待遇，只要它是新的就发送，不管最小时间间隔
            {
                send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
                it->second.has_saved_msg = false;
                it->second.last_exchtime = exchtime;
                it->second.last_source_type = mdsource_type;
                set_timers(timer_lists, it, intv_min, intv_max, intv_step);
            }
            else  // 本行情是合成行情，但还没到最小发送间隔时间点，缓存此消息
            {
                it->second.saved_pb_msg_size = msg_len;
                memcpy(it->second.saved_pb_msg, buff, msg_len);
                it->second.has_saved_msg = true;
                it->second.saved_time = now;
                it->second.last_exchtime = exchtime;
                it->second.last_source_type = mdsource_type;
            }
            return;
        }
        else if (this_3sec < last_3sec)  // 以 3 秒为单位，本消息的行情旧
        {
            return;
        }
        else  // 两个行情的交易所时间戳在同一个 3 秒周期内，无法完全确定性地区分先后
        {
            // 根据实际情况，在每一个 3 秒周期内，交易所行情都在约前 1.5 秒内进行切片，再加上一点余量，我们认为
            // 在一个 3 秒周期的最后 1 秒切片发布的合成逐笔行情应该比该周期内的交易所行情新
            if (this_ms % 3000 >= 2000) // 本消息肯定是合成消息(否则模是 0)，且在 3 秒周期的后 1 秒内切片发布，应该比交易所行情新
            {
                if (now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
                {
                    send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
                    it->second.has_saved_msg = false;
                    it->second.last_exchtime = exchtime;
                    it->second.last_source_type = mdsource_type;
                    set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                }
                else  // 还没有到最小发送间隔时间点，缓存此消息
                {
                    it->second.saved_pb_msg_size = msg_len;
                    memcpy(it->second.saved_pb_msg, buff, msg_len);
                    it->second.has_saved_msg = true;
                    it->second.saved_time = now;
                    it->second.last_exchtime = exchtime;
                    it->second.last_source_type = mdsource_type;
                }
                return;
            }
            else if (last_ms % 3000 < 2000 && (it->second.last_source_type == MDSourceType::OT))
            {
                // 本消息是交易所原始消息，而前消息是合成消息，且是在 3 秒周期的前 2 秒内切片，
                // 所以认为本消息应该比前消息新，只是晚到了一点
                // 由于本消息是交易所原始消息，有特殊待遇，只要它是新的就发送，不管最小时间间隔
                // 注意下面没有更新 last_exchtime，因为合成消息的 exchtime 更大
                send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
                it->second.has_saved_msg = false;
                it->second.last_source_type = mdsource_type;
                set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                return;
            }
            else if (it->second.last_source_type == MDSourceType::OT && last_ms % 3000 >= 2000)
            {
                // 本消息是交易所原始消息，前消息是合成消息，且前消息是在 3 秒周期的后 1 秒内切片发布，应该比交易所行情新
                // 按照交易所消息驱动缓存合成消息的原则，如果有缓存消息，我们把它发出去
                if (it->second.has_saved_msg)
                {
                    send_zmq(pub_socket, ut_pub_socket, it->second.saved_pb_msg, it->second.saved_pb_msg_size);
                    it->second.has_saved_msg = false;
                    set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                }
                return;
            }
            return;  // 最后一种情况：本消息为一个旧的合成消息，直接返回
        }
    }
}

// 处理上交所消息成交量没变的情况
static void proc_sh_mesg_same_volume(std::unordered_map<std::string, SecurityInfo>::iterator &it, LYPROTO::QUOTA::MarketData &o, void *pub_socket,
    void *ut_pub_socket, const char *buff, int msg_len,  // 本消息的内容及其长度
    MDSourceType mdsource_type,  // 本消息的行情源类型，L1、L2还是逐笔合成
    std::chrono::time_point<std::chrono::system_clock> &now,  // 收到本消息的时间点
    int32_t exchtime,       // 本消息的交易所时间
    int intv_min, int intv_max, int intv_step,
    std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> &timer_lists)
{
    int this_sec = exchtime / 10000000 * 3600 + exchtime % 10000000 / 100000 * 60 + exchtime % 100000 / 1000;
    int last_sec;
    if (it->second.last_source_type == MDSourceType::L1)
    {
        last_sec = it->second.last_exchtime_1 / 10000000 * 3600 + it->second.last_exchtime_1 % 10000000 / 100000 * 60 + it->second.last_exchtime_1 % 100000 / 1000;
    }
    else if (it->second.last_source_type == MDSourceType::L2)
    {
        last_sec = it->second.last_exchtime_2 / 10000000 * 3600 + it->second.last_exchtime_2 % 10000000 / 100000 * 60 + it->second.last_exchtime_2 % 100000 / 1000;
    }
    else
    {
        last_sec = it->second.last_exchtime / 10000000 * 3600 + it->second.last_exchtime % 10000000 / 100000 * 60 + it->second.last_exchtime % 100000 / 1000;
    }

    // 本消息与前消息都是原始交易所行情，常见情形：不同行情源的同一切片在短时间内相继到来。交易所 L1 时间戳与同一行情的 L2 时间戳在秒数上一般是相等的，
    // 最多可能相差 1，所以我们只需要判断秒数即可
    if ((mdsource_type == MDSourceType::L1 || mdsource_type == MDSourceType::L2)  // 本行情为交易所原始 L1 或 L2 行情
        && (it->second.last_source_type == MDSourceType::L1 || it->second.last_source_type == MDSourceType::L2))  // 前一行情也是交易所原始 L1 或 L2 行情
    {
        int sec_diff = this_sec - last_sec;
        if (sec_diff >= -1 && sec_diff <= 1 )  // 两个行情的交易所时间戳相差不超过 1 秒，是同一个行情
        {
            if (mdsource_type == MDSourceType::L2 && it->second.last_source_type == MDSourceType::L1)
            {
                // 用 L2 行情中的累计撤单与当前总申买量、总申卖量来更新之前 L1 的证券信息，利于后续与逐笔合成行情比较
                it->second.withdraw_num = o.widnum();
                it->second.total_buy_volume = o.totalba();
                it->second.total_sell_volume = o.totalsa();
                it->second.last_source_type = MDSourceType::L2;
                it->second.last_exchtime_2 = exchtime;
            }
            return;
        }
        else if (sec_diff > 1)
        {
            // 本消息比前消息新，这种情况应该只出现在成交很不活跃的证券上，因为中间没有收到逐笔行情
            send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
            it->second.has_saved_msg = false;
            if (mdsource_type == MDSourceType::L2)
            {
                it->second.withdraw_num = o.widnum();
                it->second.total_buy_volume = o.totalba();
                it->second.total_sell_volume = o.totalsa();
                it->second.last_exchtime_2 = exchtime;
            }
            else
            {
                it->second.last_exchtime_1 = exchtime;
            }
            it->second.last_source_type = mdsource_type;
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
            return;
        }
        else // 本消息比前消息老，这种情况说明本行情源严重滞后
        {
            if (mdsource_type == MDSourceType::L1 && it->second.last_exchtime_1 < exchtime)
            {
                it->second.last_exchtime_1 = exchtime;
            }
            else if (mdsource_type == MDSourceType::L2 && it->second.last_exchtime_2 < exchtime)
            {
                it->second.last_exchtime_2 = exchtime;
                // L2 行情与逐笔行情都严重滞后的情况下可能发生
                if (it->second.withdraw_num < o.widnum() ||
                    (it->second.withdraw_num == o.widnum() && (it->second.total_buy_volume < o.totalba() || it->second.total_sell_volume < o.totalsa())))
                {
                    it->second.withdraw_num = o.widnum();
                    it->second.total_buy_volume = o.totalba();
                    it->second.total_sell_volume = o.totalsa();
                }
            }
            return;
        }
    }

    // 本消息与前消息都是逐笔合成行情，而我们目前只支持一个逐笔合成行情源，则即使时间戳相同，后收到的也必然是更新的行情
    if ((mdsource_type == MDSourceType::OT) && (it->second.last_source_type == MDSourceType::OT))
    {
        if (now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
        {
            send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
            it->second.has_saved_msg = false;
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        }
        else  // 还没有到最小发送间隔时间点，缓存此消息
        {
            it->second.saved_pb_msg_size = msg_len;
            memcpy(it->second.saved_pb_msg, buff, msg_len);
            it->second.has_saved_msg = true;
            it->second.saved_time = now;
        }
        it->second.last_exchtime = exchtime;
        it->second.withdraw_num = o.widnum();
        it->second.total_buy_volume = o.totalba();
        it->second.total_sell_volume = o.totalsa();
        return;
    }

    // 下面为其它情形：本消息与前消息中有一个是逐笔合成行情，一个是原始交易所行情

    // 本消息为逐笔合成行情消息，上一消息为交易所原始行情 L1 或 L2 消息
    if (mdsource_type == MDSourceType::OT)
    {
        if (it->second.last_source_type == MDSourceType::L1)  // 前一消息为 L1 行情
        {
            // 无法严格准确判断两个行情的先后，因为交易所 L1 行情没有撤单统计，其当前总申买量、总卖量也无法用于行情比较
            // 只能比交易所时间戳了。根据观察，上交所的 L1 行情时间戳可能来自一台专门的发布机，至少会滞后 15 毫秒，平均
            // 滞后 100 毫秒左右，考虑到交易所消息的权威性，所以这里我们要求逐笔消息的时间戳大于 L1 的时间戳减去 15
            // 毫秒才算更新
            int this_ms = exchtime / 10000000 * 3600000 + exchtime % 10000000 / 100000 * 60000 + exchtime % 100000;
            int last_ms = it->second.last_exchtime_1 / 10000000 * 3600000 + it->second.last_exchtime_1 % 10000000 / 100000 * 60000 + it->second.last_exchtime_1 % 100000;
            if (this_ms - last_ms > -15)
            {
                if (now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
                {
                    send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
                    it->second.has_saved_msg = false;
                    set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                }
                else  // 还没有到最小发送间隔时间点，缓存此消息
                {
                    it->second.saved_pb_msg_size = msg_len;
                    memcpy(it->second.saved_pb_msg, buff, msg_len);
                    it->second.has_saved_msg = true;
                    it->second.saved_time = now;
                }
                it->second.last_exchtime = exchtime;
                it->second.withdraw_num = o.widnum();
                it->second.total_buy_volume = o.totalba();
                it->second.total_sell_volume = o.totalsa();
                it->second.last_source_type = MDSourceType::OT;
                return;
            }
            // 本合成消息可能旧，但也可能其实是新的，或者虽比前 L1 旧，但其中的累计撤单笔数有可能有所更新
            if (o.widnum() > it->second.withdraw_num)  // 本合成消息中累计撤单值更大
            {
                it->second.withdraw_num = o.widnum();
                it->second.total_buy_volume = o.totalba();
                it->second.total_sell_volume = o.totalsa();
            }
            return;
        }
        // 前一消息为 L2 行情，用累计撤单数进行比较
        if ((o.widnum() > it->second.withdraw_num)  // 本合成消息中累计撤单值更大
            || (o.widnum() == it->second.withdraw_num && (o.totalba() > it->second.total_buy_volume || o.totalsa() > it->second.total_sell_volume)))  // 本合成消息中挂单量更大
        {
            it->second.last_exchtime = exchtime;
            it->second.last_source_type = mdsource_type;
            it->second.withdraw_num = o.widnum();
            it->second.total_buy_volume = o.totalba();
            it->second.total_sell_volume = o.totalsa();
            if (now >= it->second.next_fast_pub_time)  // 已经到了最小发送间隔时间点
            {
                send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
                it->second.has_saved_msg = false;
                set_timers(timer_lists, it, intv_min, intv_max, intv_step);
            }
            else  // 还没有到最小发送间隔时间点，缓存此消息
            {
                it->second.saved_pb_msg_size = msg_len;
                memcpy(it->second.saved_pb_msg, buff, msg_len);
                it->second.has_saved_msg = true;
                it->second.saved_time = now;
            }
        }
        return;
    }

    // 这里开始处理前消息为合成消息，本消息为交易所原始行情

    if (mdsource_type == MDSourceType::L1) // 本消息为交易所 L1 行情，上一消息为逐笔合成消息，无法严格准确判断两个行情的先后
    {
        int this_ms = exchtime / 10000000 * 3600000 + exchtime % 10000000 / 100000 * 60000 + exchtime % 100000;
        int last_ms = it->second.last_exchtime / 10000000 * 3600000 + it->second.last_exchtime % 10000000 / 100000 * 60000 + it->second.last_exchtime % 100000;
        int last_ms_l2 = it->second.last_exchtime_2 / 10000000 * 3600000 + it->second.last_exchtime_2 % 10000000 / 100000 * 60000 + it->second.last_exchtime_2 % 100000;
        int l1_l2_diff = this_ms - last_ms_l2; // 与最近一次 L2 行情的时间差，单位毫秒
        if (l1_l2_diff >= -1000 && l1_l2_diff <= 1000)  // 与最近一次 L2 行情的时间相差在 1000 毫秒内，认为是同一条行情
        {
            // 前行情是合成行情，而本行情与更前的 L2 行情是同一个行情，所以本行情较旧
            if (it->second.has_saved_msg) // 本 L1 消息较旧，但有缓存的合成行情，按原始行情驱动的原则，发布之
            {
                send_zmq(pub_socket, ut_pub_socket, it->second.saved_pb_msg, it->second.saved_pb_msg_size);
                it->second.has_saved_msg = false;
                set_timers(timer_lists, it, intv_min, intv_max, intv_step);
            }
            return;
        }
        if (this_ms >= last_ms + 15) // 我们只要求 L1 消息时间戳比逐笔合成的 10 毫秒级时间戳大 15 毫秒就算更新，当然这有可能是错误的
        {
            send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
            it->second.has_saved_msg = false;
            it->second.last_exchtime_1 = exchtime;
            it->second.last_source_type = MDSourceType::L1;
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        }
        else if (it->second.has_saved_msg)  // 本 L1 消息较旧，但有缓存的合成行情，按原始行情驱动的原则，发布之
        {
            send_zmq(pub_socket, ut_pub_socket, it->second.saved_pb_msg, it->second.saved_pb_msg_size);
            it->second.has_saved_msg = false;
            set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        }
        return;
    }

    // 本消息为一个交易所原始 L2 行情消息，上一消息为逐笔合成消息
    if ((o.widnum() > it->second.withdraw_num)  // 本 L2 消息中累计撤单值更大
        || (o.widnum() == it->second.withdraw_num && (o.totalba() > it->second.total_buy_volume || o.totalsa() > it->second.total_sell_volume)))  // 本 L2 消息中挂单量更大
    {
        // 对于交易所新行情，直接发布，不考虑是否到达最小发送间隔时间点
        send_zmq(pub_socket, ut_pub_socket, buff, msg_len);
        it->second.has_saved_msg = false;
        set_timers(timer_lists, it, intv_min, intv_max, intv_step);
        it->second.last_exchtime_2 = exchtime;
        it->second.last_source_type = MDSourceType::L2;
        it->second.withdraw_num = o.widnum();
        it->second.total_buy_volume = o.totalba();
        it->second.total_sell_volume = o.totalsa();
        return;
    }
    // 本 L2 消息与缓存消息相同或者本 L2 消息更旧，根据原始行情驱动缓存行情的策略，如有缓存行情则发布之
    if (it->second.has_saved_msg)  // 原合成行情还没发布，发布之，如果没有则说明之前发布过了直接返回
    {
        send_zmq(pub_socket, ut_pub_socket, it->second.saved_pb_msg, it->second.saved_pb_msg_size);
        it->second.has_saved_msg = false;
        set_timers(timer_lists, it, intv_min, intv_max, intv_step);
    }
    // 本 L2 消息与缓存消息相同，我们把最新消息类型更新为 L2 消息，这样之后的 L1 消息能比较准确地判断
    if (o.widnum() == it->second.withdraw_num && (o.totalba() == it->second.total_buy_volume) && (o.totalsa() == it->second.total_sell_volume))  // 
    {
        it->second.last_source_type = MDSourceType::L2;
        it->second.last_exchtime_2 = exchtime;
        return;
    }
    DEBUG_LOG(std::format("!!! unexpected situation, should not reach here, stkid={}", o.stkid()));
    return;
}

// 对于每一个行情消息，处理结果可能是：
//   - 是新的，且符合发布条件，发布之，大部分证券信息更新为此消息的信息
//   - 是新的，但未达到发布时机，缓存之，大部分证券信息更新为此消息的信息
//   - 是旧的，但其中可能含有更新的部分信息，在证券信息中更新这些信息
static void proc_mesgs(void * sub_socket, void * pub_socket, void * ut_pub_socket, std::unordered_map<std::string, MDSourceType> &md_source_type_dict,
    int intv_min, int intv_max, int intv_step)
{
    // 证券信息字典，key 为证券代码
    std::unordered_map<std::string, SecurityInfo> security_info_dict;
    security_info_dict.reserve(MAX_NUM_SECURITIES);  // 确保不会发生 rehashing 导致迭代器失效
    int num_securities = 0;  // 证券数量，当此数量超过 MAX_NUM_SECURITIES 时，不再缓存行情消息，以免因为 rehashing 导致保存在定时器槽位链表中的迭代器失效
    std::unordered_set<std::string> ignored_securities; // 因超过 MAX_NUM_SECURITIES 而被忽略的证券

    // 最大发布间隔定时器槽位，每个槽位是一个链表，保存了所有在该时间点需要发布的证券信息的迭代器
    int num_bucks = 1000 / intv_step;
    std::vector<std::list<std::unordered_map<std::string, SecurityInfo>::iterator>> timer_lists(num_bucks);

    char buffer[1024]; // 对于行情来说 1024 足够了
    int more = 0; // 用于接收 ZMQ 消息时检查是否还有下一个 frame
    size_t more_size = sizeof(more);
    while (true)
    {
        auto msg_len = zmq_recv(sub_socket, buffer, sizeof(buffer), 0);
        if (msg_len == -1)
        {
            DEBUG_LOG(std::format("!!!接收行情失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(msg_len) > sizeof(buffer))
        {
            DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 超过缓冲区大小 {}, 忽略", msg_len, sizeof(buffer)));
            continue;
        }
        more_size = sizeof(more);
        if (zmq_getsockopt(sub_socket, ZMQ_RCVMORE, &more, &more_size) != 0)
        {
            DEBUG_LOG(std::format("!!!获取 ZeroMQ 行情 ZMQ_RCVMORE 标志失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (more != 0) // 应该只有一个 frame
        {
            DEBUG_LOG(std::format("!!!接收行情后，ZMQ_RCVMORE 标志非 0，忽略"));
            continue;
        }
        if (buffer[0] == 'T')  // 定时器消息
        {
            int *bucketp = reinterpret_cast<int *>(buffer + 1);
            int bucket = *bucketp;
            auto now = std::chrono::system_clock::now();
            // if (timer_lists[bucket].size() > 0) DEBUG_LOG(std::format("定时器槽位 {} 消息，有 {} 个证券", bucket, timer_lists[bucket].size()));
            for (auto it = timer_lists[bucket].begin(); it != timer_lists[bucket].end();)
            {
                // DEBUG_LOG(std::format("定时器槽位 {} 消息，有 {} 个证券", bucket, timer_lists[bucket].size()));
                auto &si = **it;
                if (si.second.has_saved_msg) {  // 有缓存未发的消息
                    if (now - si.second.saved_time > std::chrono::milliseconds(50))  // 保存的消息已经太久远了，放弃发布
                    {
                        it = timer_lists[bucket].erase(it); // 从定时器链表中删除即可，此时此证券肯定已经进入到立即触发状态了
                        si.second.bucket = -1;
                        continue;
                    }
                    send_zmq(pub_socket, ut_pub_socket, si.second.saved_pb_msg, si.second.saved_pb_msg_size);
                    si.second.has_saved_msg = false;
                    it = set_timers(timer_lists, *it, intv_min, intv_max, intv_step);
                    // it = timer_lists[bucket].erase(it);
                }
                else // 此证券自上次发送以来没有收到任何消息，由下一次任何行情消息到达时触发发布，因为那时的时间肯定会超过原来设置的最小发送间隔时点
                {
                    it = timer_lists[bucket].erase(it);
                    si.second.bucket = -1;
                }
            }
            continue;
        }
        else  // 行情消息
        {
            if (msg_len < 16)
            {
                DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 小于 16， 忽略", msg_len));
                continue;
            }
            LYPROTO::QUOTA::MarketData o;
            if (!o.ParseFromArray(buffer + 15, msg_len - 15))
            {
                DEBUG_LOG(std::format("!!!解析行情失败 {}，忽略，msg_len = {}", std::string_view(buffer, 15), msg_len));
                continue;
            }
            std::string stkid = o.stkid();
            int32_t exchtime;
            try
            {
                exchtime = std::stoi(o.exchtime());
            }
            catch (const std::exception &e)
            {
                DEBUG_LOG(std::format("!!!Invalid exchtime: {} from {}, security: {}", o.exchtime(), o.mdsource(), stkid));
                continue;
            }
            MDSourceType mdsource_type = md_source_type_dict[o.mdsource()];  // 本条消息的行情源类型
            ExchID exch_id = exch_id_from_string(o.exchid());  // 本条消息的交易所标识
            auto it = security_info_dict.find(stkid);
            if (it == security_info_dict.end())  // 此证券还未收录
            {
                // DEBUG_LOG(std::format("!!! total = {}, stkid={}\n{}", num_securities, stkid, o.DebugString()));
                if (exch_id != ExchID::SH && exch_id != ExchID::SZ)  // 不是沪深交易所的证券，直接忽略
                {
                    auto ignored = ignored_securities.insert(stkid); // 加入忽略列表
                    if (ignored.second)                              // 插入成功
                    {
                        DEBUG_LOG(std::format("!!!未知证券交易所, ignore {} exchid={}\n{}", stkid, o.exchid(), o.DebugString()));
                    }
                    continue;
                }
                if (num_securities >= MAX_NUM_SECURITIES)  // 证券数量已达到上限
                {
                    auto ignored = ignored_securities.insert(stkid);  // 加入忽略列表
                    if (ignored.second)  // 插入成功
                    {
                        DEBUG_LOG(std::format("!!!Too many securities, ignore {} exchid={}\n{}", stkid, o.exchid(), o.DebugString()));
                    }
                    continue;
                }
                send_zmq(pub_socket, ut_pub_socket, buffer, msg_len);  // 第一条行情消息直接发送
                // 记录此证券的行情信息，设置两个定时器
                if (exch_id == ExchID::SH)  // 上交所行情
                {
                    if (mdsource_type == MDSourceType::L1) // L1 行情没有撤单、总申买申卖量数据
                    {
                        it = security_info_dict.emplace(stkid, SecurityInfo{mdsource_type, exch_id, o.volume(), 0, 0, 0, 0,
                            exchtime, 0, false}).first;
                    }
                    else if (mdsource_type == MDSourceType::L2) // L2 行情有累计撤单、当前总申买量、总申卖量数据
                    {
                        it = security_info_dict.emplace(stkid, SecurityInfo{mdsource_type, exch_id, o.volume(), o.widnum(), o.totalba(), o.totalsa(), 0,
                            0, exchtime, false}).first;
                    }
                    else // 逐笔合成行情有累计撤单、当前总申买量、总申卖量数据
                    {
                        it = security_info_dict.emplace(stkid, SecurityInfo{mdsource_type, exch_id, o.volume(), o.widnum(), o.totalba(), o.totalsa(), exchtime,
                            0, 0, false}).first;
                    }
                }
                else
                { // 深交所的交易所行情没有撤单统计，其当前总申买量、总卖量无法用于行情比较，为避免误导逐笔行情的处理逻辑（可能都有这两类统计），初始化为 0 且之后不更新
                    it = security_info_dict.emplace(stkid, SecurityInfo{mdsource_type, exch_id, o.volume(), 0, 0, 0,
                        exchtime, 0, 0, false}).first;
                }
                it->second.bucket = -1;
                set_timers(timer_lists, it, intv_min, intv_max, intv_step);  // 设置两个定时器
                ++num_securities;
            }
            else  // 此证券已收录，要跟之前的行情进行比对
            {
                auto now = std::chrono::system_clock::now();
                int64_t volume = o.volume();  // 累计成交量大小是确定性的行情新旧比较条件，因为它是一个随时间单调增长的量，成交量大意味着行情更新
                int withdraw_num = 0; // 累计撤单笔数
                if (exch_id == ExchID::SH)
                {
                    withdraw_num = o.widnum();  // 累计撤单笔数目前只有上交所通联 L2 行情和逐笔合成行情才有，对于 L1 行情，此字段为 0
                }
                if (volume > it->second.volume)  // 累计成交量大，此行情消息比前消息更新
                {
                    it->second.last_source_type = mdsource_type;
                    it->second.volume = volume;
                    if ((now >= it->second.next_fast_pub_time) ||                                 // 已经到了最小发送间隔时间点
                        (mdsource_type == MDSourceType::L2 || mdsource_type == MDSourceType::L1)) // 交易所原始行情特殊待遇，只要它是新的就发送，不管最小时间间隔
                    {
                        send_zmq(pub_socket, ut_pub_socket, buffer, msg_len);
                        it->second.has_saved_msg = false;
                        if (exch_id == ExchID::SH)
                        {
                            if (mdsource_type == MDSourceType::L2 || mdsource_type == MDSourceType::OT)  // 只有上交所 L2 行情和逐笔合成行情才有撤单统计和总申买量、总申卖量
                            {
                                it->second.withdraw_num = withdraw_num;
                                it->second.total_buy_volume = o.totalba();
                                it->second.total_sell_volume = o.totalsa();
                                if (mdsource_type == MDSourceType::L2)
                                {
                                    it->second.last_exchtime_2 = exchtime;
                                }
                                else // 逐笔合成行情
                                {
                                    it->second.last_exchtime = exchtime;
                                }
                            }
                            else // 上交所 L1 行情
                            {
                                it->second.last_exchtime_1 = exchtime;
                            }
                        }
                        else // 深交所
                        {
                            it->second.last_exchtime = exchtime;
                        }
                        set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                        continue;
                    }
                    else  // 是合成行情且最新，但还没有到前设的最小发送间隔时间，缓存此消息
                    {
                        it->second.saved_pb_msg_size = msg_len;
                        memcpy(it->second.saved_pb_msg, buffer, msg_len);
                        it->second.has_saved_msg = true;
                        it->second.saved_time = now;
                        it->second.last_exchtime = exchtime;
                        if (exch_id == ExchID::SH)  // 上交所行情的撤单统计和总申买量、总申卖量用于比较
                        {
                            it->second.withdraw_num = withdraw_num;
                            it->second.total_buy_volume = o.totalba();
                            it->second.total_sell_volume = o.totalsa();
                        }
                        continue;
                    }
                }
                // 成交量更小，或者成交量相等而撤单更小，说明是旧数据
                else if (volume < it->second.volume || (withdraw_num > 0 && withdraw_num < it->second.withdraw_num))
                {
                    // 对于上交所证券，由于 L1 行情缺少撤单统计，即使是旧数据，也可能带有更新的信息，如：
                    // 实际发生的行情序列为：成交1（累计成交 1000, 累计撤单 10），撤单2（累计成交 1000， 累计撤单 11），成交3（累计成交 1100，累计撤单 11）
                    // 收到的行情序列为：合成成交1（累计成交 1000，累计撤单 10），L1成交3（累计成交 1100，累计撤单 0），合成撤单2（累计成交 1000，累计撤单 11）
                    // 这里最后收到的 合成撤单2 行情中的累计成交值小于前面收到的 L1成交3，所以是更旧的行情，但其包含的累计撤单信息更新
                    // 但似乎在这种情况下更新累计撤单对后续的比对没有意义，所以我们就不更新了

                    // 旧的交易所数据也驱动缓存的合成数据的发送的算法存在一个问题：有多个交易所行情源的情况下会导致合成行情发布频率增加，
                    // 但同时也考虑到发走一个缓存的合成数据后，在下一个同样旧的交易所数据到来之前的时间不会太长（不同交易所行情之前的时延
                    // 一般不会差太多），也不一定会再累积很多新的合成数据，所以频繁就频繁一点吧
                    // 对多个交易所行情的说明：
                    // 连续的旧交易所行情不会进到这里，因为它们是同一切片，会进入到 proc_sh_mesg_same_volume() 或 proc_sz_mesg_same_volume() 去处理，在那里不会重复触发
                    // 进到这里的是不同行情源相同切片行情之间夹着合成行情的情形
                    if ((mdsource_type == MDSourceType::L2 || mdsource_type == MDSourceType::L1)
                        && it->second.has_saved_msg) // 交易所原始行情特殊待遇，它可以触发发布缓存的合成消息
                    {
                        send_zmq(pub_socket, ut_pub_socket, it->second.saved_pb_msg, it->second.saved_pb_msg_size);
                        it->second.has_saved_msg = false;
                        set_timers(timer_lists, it, intv_min, intv_max, intv_step);
                    }
                    continue;
                }
                else  // 成交量相同的情况，存在不确定性
                {
                    if (ExchID::SZ == exch_id)  // 深交所行情消息
                    {
                        proc_sz_mesg_same_volume(it, o, pub_socket, ut_pub_socket, buffer, msg_len, mdsource_type, now, exchtime, intv_min, intv_max, intv_step, timer_lists);
                    }
                    else if (ExchID::SH == exch_id)  // 上交所行情消息
                    {
                        // 在这里没有直接比较累计撤单，因为上交所 L1 行情没有此字段，在连续撤单情况下如果直接比较可能导致错误，如：
                        //   - 假设当前证券信息中累计撤单值为 9
                        //   - 撤单 10 的合成行情晚于撤单 11 的 L1 行情
                        //   - 撤单 11 的 L1 行情首先被处理，但其本身没有累计撤单字段，无法更新证券信息中的累计撤单值，仍然为 9
                        //   - 撤单 10 的合成行情随后被处理，由于其累计撤单值 10 大于证券信息中的 9，直接比较会导致错误结果，因为实际上其信息旧于前面的 L1 行情
                        // 统一进入到 proc_sh_mesg_same_volume() 中处理
                        proc_sh_mesg_same_volume(it, o, pub_socket, ut_pub_socket, buffer, msg_len, mdsource_type, now, exchtime, intv_min, intv_max, intv_step, timer_lists);
                    }
                    continue; // 其它交易所的都忽略，应该不会到这里，因为前面已经放进忽略列表了
                }
            }
        }
    }
}

int main(int argc, char *argv[])
{
    ProgramOptions options = {
        DEFAULT_CONFIG_FILE,
        {},
        {},
        {},
        DEFAULT_OUTPUT_HOST,
        DEFAULT_OUTPUT_PORT,
        DEFAULT_OUTPUT_HOST_UT,
        DEFAULT_OUTPUT_PORT_UT,
        DEFAULT_OUTPUT_INTV_MIN,
        DEFAULT_OUTPUT_INTV_MAX,
        DEFAULT_OUTPUT_INTV_STEP,
        DEFAULT_HWM,
        false,
        false
    };

    if (!GetProgramOptions(argc, argv, options))
    {
        return -1;
    }

    auto zmq_ctx = zmq_ctx_new();
    auto zmq_timer_thread = std::thread(zmq_timer, zmq_ctx, options.output_intv_step); // 启动定时器
    void * sub_socket = zmq_socket(zmq_ctx, ZMQ_SUB);  // 用于订阅行情及定时器消息
    zmq_setsockopt(sub_socket, ZMQ_RCVHWM, &options.hwm, sizeof(options.hwm));
    zmq_connect(sub_socket, ZMQ_TIMER_TRANSPORT); // 订阅定时器消息
    for (const auto &s : options.sources)
    {
        std::string endpoint = std::format("tcp://{}:{}", s.host, s.port);
        zmq_connect(sub_socket, endpoint.c_str()); // 订阅行情消息
    }
    zmq_setsockopt(sub_socket, ZMQ_SUBSCRIBE, "T", 1); // 订阅定时器消息
    for (const auto &s : options.subscriptions)
    {
        zmq_setsockopt(sub_socket, ZMQ_SUBSCRIBE, s.c_str(), s.size());
    }
    void * pub_socket = nullptr;
    void * ut_pub_socket = nullptr;
    if (!options.output_host_ut.empty())
    {
        ut_pub_socket = zmq_socket(zmq_ctx, ZMQ_PUB);
        zmq_setsockopt(ut_pub_socket, ZMQ_SNDHWM, &options.hwm, sizeof(options.hwm));
        zmq_connect(ut_pub_socket, std::format("tcp://{}:{}", options.output_host_ut, options.output_port_ut).c_str());
    }
    if (!options.output_host.empty())
    {
        pub_socket = zmq_socket(zmq_ctx, ZMQ_PUB);
        zmq_setsockopt(pub_socket, ZMQ_SNDHWM, &options.hwm, sizeof(options.hwm));
        zmq_connect(pub_socket, std::format("tcp://{}:{}", options.output_host, options.output_port).c_str());
    }
    proc_mesgs(sub_socket, pub_socket, ut_pub_socket, options.md_source_type_dict, options.output_intv_min, options.output_intv_max, options.output_intv_step);
    zmq_timer_thread.join();
    zmq_ctx_term(zmq_ctx);
    return 0;
}
