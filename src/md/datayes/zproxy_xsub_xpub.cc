#include <zmq.h>
#include <string>
#include <format>
#include <iostream>
#include <getopt.h>

constexpr int DEFAULT_XSUB_PORT = 30001;
constexpr int DEFAULT_XPUB_PORT = 30002;

static void print_usage(const char *program_name)
{
    std::cout << std::format("Usage: {} [options]\n", program_name);
    std::cout << "Options:\n";
    std::cout << "  -s, --xsub_port=PORT  XSUB port (default: " << DEFAULT_XSUB_PORT << ")\n";
    std::cout << "  -p, --xpub_port=PORT  XPUB port (default: " << DEFAULT_XPUB_PORT << ")\n";
    std::cout << "  -h, --help            Display this help and exit\n";
}

int main(int argc, char *argv[])
{
    int xsub_port = DEFAULT_XSUB_PORT;
    int xpub_port = DEFAULT_XPUB_PORT;

    int opt;
    int option_index = 0;
    static struct option long_options[] = {
        {"xsub_port", required_argument, 0, 's'},
        {"xpub_port", required_argument, 0, 'p'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}};

    while ((opt = getopt_long(argc, argv, "s:p:h", long_options, &option_index)) != -1)
    {
        switch (opt)
        {
        case 's':
            xsub_port = std::stoi(optarg);
            break;
        case 'p':
            xpub_port = std::stoi(optarg);
            break;
        case 'h':
            print_usage(argv[0]);
            return 0;
        default:
            break;
        }
    }

    void *context = zmq_ctx_new();
    void *xsub = zmq_socket(context, ZMQ_XSUB);
    void *xpub = zmq_socket(context, ZMQ_XPUB);

    zmq_bind(xsub, std::format("tcp://*:{}", xsub_port).c_str());
    zmq_bind(xpub, std::format("tcp://*:{}", xpub_port).c_str());

    zmq_proxy(xsub, xpub, NULL);

    zmq_close(xsub);
    zmq_close(xpub);
    zmq_ctx_destroy(context);

    return 0;
}