# dytk 与 getMktLimit.py 共用的配置文件
# 本配置文件的各项配置都可被命令行参数覆盖

# 行情文件前缀，如果不想保存行情文件，请将此项设置为 null，或不设置
# 深市L2市场行情文件将保存为 filep + "sz_tick.csv"
# 沪市L2市场行情文件将保存为 filep + "sh_tick.csv"
# 深市L2指数行情文件将保存为 filep + "sz_index.csv"
# 沪市L2指数行情文件将保存为 filep + "sh_index.csv"
# 深市静态信息文件将保存为 filep + "sz_minfo.csv"
# 沪市静态信息文件将保存为 filep + "sh_minfo.csv"
# getMktLimit.py 获得的涨跌停文件将保存为 filep + "limits_%Y%m%d.csv"
# 支持日期格式符，如 %Y-%m-%d
# 请参考 https://en.cppreference.com/w/cpp/io/manip/put_time
# 如果要用纯数字或YAML关键字作文件名，请加引号
filep: /b/dymd/%Y-%m-%d/

# 日志文件前缀，如果不想保存日志文件，请将此项设置为 null，或不设置
# 通联库日志文件将保存为 logfp + ".log" 和 logfp + ".trace.log"
# 程序日志文件将保存为 logfp + ".std.log"
# 支持日期格式符，如 %Y-%m-%d
# 请参考 https://en.cppreference.com/w/cpp/io/manip/put_time
# 如果要用纯数字或YAML关键字作文件名，请加引号
# 另外请不要用相对路径，因为通联SDK对相对路径的支持有问题
logfp: /b/dymd/%Y-%m-%d/dytk

# 通联 SDK token，缺省为 2025.6 的主线 4EEB86544CA6F15C44F63118F55224CC
#token: 4EEB86544CA6F15C44F63118F55224CC
# 备线
token: 4E401BF9EC6FB0EECB1EE0AA7F8FC3FF

# getMktLimit.py 使用的通联 API token，缺省为 2025.6 获得的 8ffcdc27aa291300241687fce22121d7dfdb8d2a225378b23e9ef7141d96b168
#token_api: 8ffcdc27aa291300241687fce22121d7dfdb8d2a225378b23e9ef7141d96b168

# redis 服务器地址，dytk 与 getMktLimit.py 共用
# 如果不想保存行情文件，请将此项设置为 null，或不设置
redis_host: ***********

# redis 端口，dytk 与 getMktLimit.py 共用
# 缺省为 6379
redis_port: 6300

# redis key，dytk 与 getMktLimit.py 共用
# 缺省为 STKLST2
# 如果要用纯数字或YAML关键字作Redis key，请加引号
redis_key: STKLST3

# ZeroMQ 端口，缺省为 9869
zmq_port: 9869

# 行情源标签，缺省为 DYL2
md_source: DYL2

# 是否在后台运行，缺省为 false
background: true

# 订阅
subs:
  sz:  # 深交所订阅
    securities:  # 股票与基金
      # 2025.11.11 白工给出的深交所 ETF 列表
      - "159633"
      - "159845"
      - "159901"
      - "159902"
      - "159915"
      - "159919"
      - "159922"
      - "159934"
      - "159937"
      - "159949"
      - "159952"
      - "159992"
      - "159995"
    security_files:  # 从文件中读取 ETF 成分股列表，该文件每日由脚本从通联 Web API 获取
      - etfcons_dy_sz.yaml
    indexes: # 与 ETF 相关联的深交所、中证、国证指数
      - "399005"  # 中小100
      - "399006"  # 创业板指
      - "399330"  # 深证100
      - "399673"  # 创业板50
      - "399975"  # 证券公司
      - "980017"  # 国证芯片
  sh:  # 上交所订阅
    securities:  # 股票与基金
      # 2025.11.11 白工给出的上交所 ETF 列表
      - "510050"
      - "510100"
      - "510180"
      - "510210"
      - "510300"
      - "510310"
      - "510330"
      - "510500"
      - "510880"
      - "511010"
      - "511880"
      - "511990"
      - "512000"
      - "512010"
      - "512050"
      - "512070"
      - "512100"
      - "512760"
      - "512880"
      - "515050"
      - "518800"
      - "518880"
      - "588000"
      - "588050"
      - "588080"
      - "588200"
      - "588220"
    security_files:
      - etfcons_dy_sh.yaml
    indexes: # 与 ETF 相关联的上交所、中证指数
      - "000001"  # 上证指数
      - "000010"  # 上证180
      - "000015"  # 红利指数
      - "000016"  # 上证50
      - "000300"  # 沪深300
      - "000510"  # 中证A500
      - "000685"  # 科创芯片
      - "000688"  # 科创50
      - "000698"  # 科创100
      - "000849"  # 300非银
      - "000852"  # 中证1000
      - "000905"  # 中证500
      - "000913"  # 300医药

# 通联回放端口
# server_sz: 168.36.1.167:9022
# server_sh: 168.36.1.167:9022
