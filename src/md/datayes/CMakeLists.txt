cmake_minimum_required(VERSION 3.20)
project(DataYes)

set(CMAKE_INSTALL_MESSAGE ALWAYS)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O2 -Wall")
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")

# Include directories
include_directories(
    ./mdl_sdk_2_13_231/include/
    ../include
)

# Library directories
link_directories(./mdl_sdk_2_13_231/libs/linux)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(ZMQ REQUIRED libzmq)

# 使用系统的静态 Protobuf 库
set(Protobuf_ROOT "/usr")
find_package(Protobuf 3.21.0 REQUIRED)

find_package(Arrow REQUIRED)
find_package(Parquet REQUIRED)

# Source files
set(COMMON_SOURCES dymd.cc ../common/lyproto.quota.pb.cc)

# Executables
# 通联 L2 行情转 ZMQ，并保存 CSV 文件，静态数据写入 Redis 与 CSV 文件
add_executable(dytk ${COMMON_SOURCES} dytk.cc)

# 通联逐笔行情保存为 CSV，其中深交所逐笔委托(6.33) 与成交(6.36) 分别保存到不同的文件
add_executable(dyot ${COMMON_SOURCES} dyot.cc)

# 通联逐笔深交所逐笔转 ZMQ，每个 Channel 两个端口分别发布委托(6.33) 和成交(6.36) 数据
# add_executable(dyszot2z dyszot2z.cc)

# 通联逐笔深交所逐笔转 ZMQ，所有 Channel 发布到同一个端口，发布通联的委托成交合并数据 6.53
add_executable(dyszot2za dyszot2za.cc)

# 通联逐笔上交所逐笔转 ZMQ，每个 Channel 一个端口，弃用
# add_executable(dyshot2z dyshot2z.cc)

# 通联逐笔上交所逐笔转 ZMQ，所有 Channel 发布到同一个端口
add_executable(dyshot2za dyshot2za.cc)

# 通联逐笔上交所逐笔 ZMQ 转 CSV 文件
add_executable(dyshotz2f dyshotz2f.cc)

# 通联逐笔深交所逐笔委托 ZMQ 转 CSV 文件，仅 Channel 2011 的演示版本，弃用，将用通联委托成交合并数据
# add_executable(dyszoz2f dyszoz2f.cc)

# 通联逐笔深交所逐笔成交 ZMQ 转 CSV 文件，仅 Channel 2011 的演示版本，弃用，将用通联委托成交合并数据
# add_executable(dysztz2f dysztz2f.cc)

# 通联逐笔上交所 ZMQ 转全速行情快照程序
add_executable(dyshotsnap ../common/lyproto.quota.pb.cc dyshotsnap.cc dyshot2t.cc)

# 通联逐笔深交所 ZMQ 转全速行情快照程序
add_executable(dyszotsnap ../common/lyproto.quota.pb.cc dyszotsnap.cc dyszot2t.cc)

# 通联逐笔 ZMQ XSUB 与 XPUB 代理程序
add_executable(zproxy_xsub_xpub zproxy_xsub_xpub.cc)

# 超级行情 sumd 程序
add_executable(sumd ../common/lyproto.quota.pb.cc sumd.cc)

# 超级指数行情 sumdi 程序
add_executable(sumdi ../common/lyproto.quota.pb.cc sumdi.cc)

# Link libraries for 通联 L2 行情转 ZMQ 并保存 CSV 文件的程序
target_link_libraries(dytk PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} protobuf::libprotobuf m -l:libhiredis.a)

# Link libraries for 通联逐笔行情保存为 CSV 文件的程序，其中深交所逐笔委托、成交分开保存
target_link_libraries(dyot PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} protobuf::libprotobuf m -l:libhiredis.a)

# Link libraries for 通联深交所逐笔转 ZMQ 程序
# target_link_libraries(dyszot2z PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)
target_link_libraries(dyszot2za PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)

# 通联上交所逐笔转 ZMQ 程序
# target_link_libraries(dyshot2z PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)
target_link_libraries(dyshot2za PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)

# 通联逐笔上交所逐笔 ZMQ 转 CSV 文件
target_link_libraries(dyshotz2f PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)

# 通联逐笔深交所逐笔委托 ZMQ 转 CSV 文件
# target_link_libraries(dyszoz2f PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)

# 通联逐笔深交所逐笔成交 ZMQ 转 CSV 文件
# target_link_libraries(dysztz2f PRIVATE mdl_api dl pthread rt ${ZMQ_LIBRARIES} m)

# Link libraries for 通联逐笔上交所 ZMQ 转全速行情快照程序
target_link_libraries(dyshotsnap PRIVATE 
    mdl_api dl pthread rt ${ZMQ_LIBRARIES} protobuf::libprotobuf m 
    Arrow::arrow_static Parquet::parquet_static -l:libhiredis.a
)

# Link libraries for 通联逐笔深交所 ZMQ 转全速行情快照程序
target_link_libraries(dyszotsnap PRIVATE 
    mdl_api dl pthread rt ${ZMQ_LIBRARIES} protobuf::libprotobuf m 
    Arrow::arrow_static Parquet::parquet_static -l:libhiredis.a
)

# Link libraries for 通联逐笔 ZMQ XSUB 与 XPUB 代理程序
target_link_libraries(zproxy_xsub_xpub PRIVATE ${ZMQ_LIBRARIES} m)

# Link libraries for 超级行情 sumd 程序
target_link_libraries(sumd PRIVATE ${ZMQ_LIBRARIES} protobuf::libprotobuf m)

# Link libraries for 超级指数行情 sumdi 程序
target_link_libraries(sumdi PRIVATE ${ZMQ_LIBRARIES} protobuf::libprotobuf m)

# Installation
install(TARGETS dytk dyot dyszot2za dyshot2za dyshotz2f dyszotsnap dyshotsnap zproxy_xsub_xpub sumd sumdi
    DESTINATION /usr/local/sbin
)

install(FILES ./mdl_sdk_2_13_231/libs/linux/libmdl_api.so
    DESTINATION /usr/local/lib
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
)
