#!/usr/bin/python3
# -*- coding: utf-8 -*-
# 通过通联接口获取ETF成份股信息，保存成中畅、通联或华泰格式的 YAML 配置
# API 文档见 https://mall.datayes.com/datapreview/141

import pandas as pd
import requests
import yaml
import sys
import argparse
from datetime import datetime
import time

def log_print(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}", flush=True)

# 创建命令行参数解析器
parser = argparse.ArgumentParser(description='获取通联ETF成份股信息')
parser.add_argument('-e', '--etf', type=str, help='ETF代码，如510050，可指定多个ETF，用逗号分隔，缺省为常用 40 支 ETF')
parser.add_argument('-d', '--day', type=str, help='指定日期，格式为YYYYMMDD')
parser.add_argument('-t', '--token', type=str, help='API访问令牌')
parser.add_argument('-m', '--format', type=str, help='配置文件格式，可为中畅、通联或华泰格式，分别用 zc、dy 和 ht 表示，可都选用逗号分隔，缺省为 zc')
parser.add_argument('-f', '--file', type=str, help='保存 YAML 文件的路径前缀，保存时深市加后缀 _zc_sz.yaml、_dy_sz.yaml 或 _ht_sz.yaml，沪市加后缀 _zc_sh.yaml、_dy_sh.yaml 或 _ht_sh.yaml，缺省为 etfcons')
args = parser.parse_args()

format_zc = False
format_dy = False
format_ht = False
# 获取配置文件格式
if args.format:
    formats = args.format.split(',')
    if 'zc' in formats:
        format_zc = True
    if 'dy' in formats:
        format_dy = True
    if 'ht' in formats:
        format_ht = True

if not format_zc and not format_dy and not format_ht:
    format_zc = True

# 获取 ETF 代码
if args.etf:
    etf = args.etf
else:
    # 白工 2025.11.11 给出的 ETF 列表
    etf = '159633,159845,159901,159902,159915,159919,159922,159934,159937,159949,159952,159992,159995,510050,510100,510180,510210,510300,510310,510330,510500,510880,511010,511880,511990,512000,512010,512050,512070,512100,512760,512880,515050,518800,518880,588000,588050,588080,588200,588220'

# 获取日期参数
if args.day:
    day = args.day
else:
    day = datetime.today().strftime("%Y%m%d")

# 获取token参数
if args.token:
    token_api = args.token
else:
    token_api = '8ffcdc27aa291300241687fce22121d7dfdb8d2a225378b23e9ef7141d96b168'

# 获取保存文件路径前缀
if args.file:
    filep = args.file
else:
    filep = 'etfcons'

# 构建API URL
api_url = f'/api/fund/getFundETFCons.json?field=consExchangeCD,consTicker&ticker={etf}&beginDate={day}&endDate={day}'
# api_url = f'/api/fund/getFundETFCons.json?field=&ticker={args.etf}&beginDate={day}&endDate={day}'


# 创建头信息,传入token
headers = {"Authorization": "Bearer " + token_api,
           "Accept-Encoding": "gzip, deflate"}

if format_zc:
    f_zc_sh_big = open(f"{filep}_zc_sh_big.yaml", "w")  # 比 _small.yaml 多大约 60 只
    f_zc_sh_small = open(f"{filep}_zc_sh_small.yaml", "w")
    f_zc_sz = open(f"{filep}_zc_sz.yaml", "w")
    # print("subscriptions:", file=f_zc_sh)
    # print("subscriptions:", file=f_zc_sz)

if format_dy:
    f_dy_sh = open(f"{filep}_dy_sh.yaml", "w")
    f_dy_sz = open(f"{filep}_dy_sz.yaml", "w")
    print("subscriptions:", file=f_dy_sh)
    print("subscriptions:", file=f_dy_sz)

if format_ht:
    f_ht_sh = open(f"{filep}_ht_sh.yaml", "w")
    f_ht_sz = open(f"{filep}_ht_sz.yaml", "w")
    print("subscriptions:", file=f_ht_sh)
    print("subscriptions:", file=f_ht_sz)

# 访问api,获取数据
should_retry = False
while True:
    try:
        log_print(f"正在获取 {day} 的ETF成份股数据...")
        res = requests.request("GET", url='https://api.datayes.com/data/v1/' + api_url, headers=headers)
        code = res.status_code
        result = res.content.decode('utf-8')

        if code == 200 and eval(result)['retCode'] == 1:
            # 将数据转化为DataFrame格式
            df = pd.DataFrame(eval(result)['data'])
            log_print(f"成功获取到 {len(df)} 条数据")
            secs = sorted(df[df['consExchangeCD'] == 'XSHE']['consTicker'].unique())
            for sec in secs:
                if format_zc:
                    print(f"- SZ.{sec}.L1", file=f_zc_sz)
                if format_dy:
                    print(f"  - '{sec}'", file=f_dy_sz)
                if format_ht:
                    print(f"  - {sec}.SZ", file=f_ht_sz)
            if format_zc:
                log_print(f'深市成份股保存到 {filep}_zc_sz.yaml，共 {len(secs)} 只')
            if format_dy:
                log_print(f'深市成份股保存到 {filep}_dy_sz.yaml，共 {len(secs)} 只')
            if format_ht:
                log_print(f'深市成份股保存到 {filep}_ht_sz.yaml，共 {len(secs)} 只')
            secs = sorted(df[df['consExchangeCD'] == 'XSHG']['consTicker'].unique()) # 上交所的成分股
            i = 0
            for sec in secs:
                if format_zc:
                    if i < 60:  # 前 60 只保存到 _big.yaml 中
                        print(f"- SH.{sec}.L1", file=f_zc_sh_big)
                    else:  # 剩下的交错保存到 _big.yaml 和 _small.yaml 中
                        if i % 2 == 0:
                            print(f"- SH.{sec}.L1", file=f_zc_sh_big)
                        else:
                            print(f"- SH.{sec}.L1", file=f_zc_sh_small)
                    i += 1
                if format_dy:
                    print(f"  - '{sec}'", file=f_dy_sh)
                if format_ht:
                    print(f"  - {sec}.SH", file=f_ht_sh)
            if format_zc:
                log_print(f'沪市成份股保存到 {filep}_zc_sh_big.yaml 和 {filep}_zc_sh_small.yaml，共 {len(secs)} 只')
            if format_dy:
                log_print(f'沪市成份股保存到 {filep}_dy_sh.yaml，共 {len(secs)} 只')
            if format_ht:
                log_print(f'沪市成份股保存到 {filep}_ht_sh.yaml，共 {len(secs)} 只')
            sys.exit(0)
        else:
            log_print(f"获取数据失败，状态码: {code}, 返回信息: {result}")
            should_retry = True
    except Exception as e:
        log_print(f"获取数据失败: {e}")
        should_retry = True
    
    if should_retry:
        log_print("5分钟后重试...")
        time.sleep(300)  # 等待5分钟
        continue
