// 通过逐笔数据重建订单簿并输出 tick 数据

#include <iostream>
#include <format>
#include <chrono>
#include <set>
#include <cmath>
#include <string>

#include "dyshot2t.h"

void print_tick_info(Tick_info &t)
{
    std::cout << std::format("交易所时间 {}, 交易所时间数 {}, 最新价 {}, 卖一买一价差 {}", t.time, t.exchtime, t.latest, t.spread) << std::endl;

    std::cout << std::format("20档买价: {}", t.bid20prices[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20prices[i]);
    }
    std::cout << std::endl;
    std::cout << std::format("20档买量: {}", t.bid20qty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20qty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖价: {}", t.ask20prices[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20prices[i]);
    }
    std::cout << std::endl;
    std::cout << std::format("20档卖量: {}", t.ask20qty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20qty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档买单笔数: {}", t.bid20num[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20num[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖单笔数: {}", t.ask20num[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20num[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档买单中大单买量: {}", t.bid20lgqty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20lgqty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖单中大单卖量: {}", t.ask20lgqty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20lgqty[i]);
    }
    std::cout << std::endl;

    if (t.orderbook_tier.size() > 0)
    {
        auto it = t.orderbook_tier.begin();
        std::cout << std::format("价格档位信息: [{}: {}]", it->first, it->second);
        for (it++; it != t.orderbook_tier.end(); it++)
        {
            std::cout << std::format(", [{}: {}]", it->first, it->second);
        }
        std::cout << std::endl;
    }
    else
    {
        std::cout << "价格档位信息: 无" << std::endl;
    }

    std::cout << std::format("昨收价 {}, 最新价 {}, 卖一买一价差 {}, tick_vol {}, tick_money {}, 总有效申买量 {}, 总有效申卖量 {}，总成交量 {}，总成交金额 {}，总成交笔数 {}",
                             t.prev_close, t.latest, t.spread, t.tick_vol, t.tick_money, t.total_buy_qty, t.total_sell_qty, t.trade_qty, std::lround(t.trade_money / 1000.0), t.trade_num)
              << std::endl;
    std::cout << std::format("5万以下小单净流入金额 {}, 5至30万中单净流入金额 {}, 30至100万大单净流入金额 {}, 100万以上特大单净流入金额 {}, 总计 {}",
                             t.sm_order_cum_vol, t.mid_order_cum_vol, t.lg_order_cum_vol, t.slg_order_cum_vol, t.sm_order_cum_vol + t.mid_order_cum_vol + t.lg_order_cum_vol + t.slg_order_cum_vol)
              << std::endl;
}

Shot2t::Shot2t(int upper_limit, int lower_limit, int prev_close, int increment, LOG_LEVEL log_level)
    : upper_limit_(upper_limit), lower_limit_(lower_limit), prev_close_(prev_close), increment_(increment), log_level_(log_level)
{
    mid_threshold_ = 50000 / ((lower_limit + upper_limit) * 0.001 / 2);
    lg_threshold_ = 300000 / ((lower_limit + upper_limit) * 0.001 / 2);
    slg_threshold_ = 1000000 / ((lower_limit + upper_limit) * 0.001 / 2);

    if (prev_close > 10000)
    {
        disc_coef_ = 0.001; // 10元以上股票，按 0.1% 分档，一共有 201 个档
    }
    else if (prev_close > 5000 && prev_close <= 10000)
    {
        disc_coef_ = 0.002; // 5元以上到10元及以下的股票，按 0.2% 分档，一共有 101 个档
    }
    else if (prev_close > 2000 && prev_close <= 5000)
    {
        disc_coef_ = 0.005; // 2元以上到5元及以下的股票，按 0.5% 分档，一共有 21 个档
    }
    else
    {
        disc_coef_ = 0.01; // 5元以下的股票，按 1% 分档，一共有 21 个档(如果是 ST 股票，涨跌幅限制为 5%，则只有 11 个档)
    }

    for (int p = lower_limit_; p < upper_limit_ + increment_; p += increment_)
    {
        tier_dict_[p] = std::lround((double)std::lround(((double)p / (double)prev_close_ - 1.0L) / disc_coef_) * disc_coef_ * 1000.0L);
    }
    std::set<int> tier_set;
    for (const auto &[key, value] : tier_dict_)
    {
        tier_set.insert(value);
    }
    for (auto p : tier_set)
    {
        orderbook_tier_[p] = 0;
    }
}

Shot2t::~Shot2t() = default;

void Shot2t::log(LOG_LEVEL level, std::string msg)
{
    if (level >= log_level_)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cout << std::format("[{}][{}] {}", std::chrono::current_zone()->to_local(tp_utc), LOG_LEVEL2Str(level), msg) << std::endl;
    }
}

void Shot2t::log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg)
{
    if (level >= sys_level)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cout << std::format("[{}][{}] {}", std::chrono::current_zone()->to_local(tp_utc), LOG_LEVEL2Str(level), msg) << std::endl;
    }
}

void Shot2t::export_tick_info_(Tick_info &tick_info)
{
    int i = 0;
    auto it = it_bid1_;
    while (i < 20 && it != orderbook_.end()) // 20档买价
    {
        // 1. 从 orderbook_ 中取出 20 个价位
        // 2. 将这些价位的订单量、订单笔数、大单订单量等信息填入 tick_info 中
        tick_info.bid20prices[i] = it->first;
        tick_info.bid20qty[i] = it->second.total_qty[0];
        tick_info.bid20num[i] = it->second.orders[0].size();
        tick_info.bid20lgqty[i] = orderbook_vol_lg_[it->first][0];
        it--;
        i++;
    }
    i = 0;
    it = it_ask1_;
    while (i < 20 && it != orderbook_.end()) // 20档卖价
    {
        tick_info.ask20prices[i] = it->first;
        tick_info.ask20qty[i] = it->second.total_qty[1];
        tick_info.ask20num[i] = it->second.orders[1].size();
        tick_info.ask20lgqty[i] = orderbook_vol_lg_[it->first][1];
        it++;
        i++;
    }
    tick_info.total_buy_qty = total_buy_qty_;
    tick_info.total_sell_qty = total_sell_qty_;
    tick_info.prev_close = prev_close_;
    tick_info.latest = latest_;
    tick_info.mid_price = (latest_buy_price_ + latest_sell_price_) / 2;
    tick_info.spread = ask1_ - bid1_;
    tick_info.sm_order_cum_vol = lround(sm_order_cum_vol_);
    tick_info.mid_order_cum_vol = lround(mid_order_cum_vol_);
    tick_info.lg_order_cum_vol = lround(lg_order_cum_vol_);
    tick_info.slg_order_cum_vol = lround(slg_order_cum_vol_);
    tick_info.buy_order_index = buy_order_index_;
    tick_info.sell_order_index = sell_order_index_;
    tick_info.trade_money = trade_money_;
    tick_info.trade_qty = trade_qty_;
    tick_info.trade_num = trade_num_;
    tick_info.withdraw_num = total_inst_cancel_;  // 累计买卖撤单笔数
}

void Shot2t::export_tick_info_(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier, Tick_info &tick_info)
{
    // std::memcpy(tick_info.time, t, 23); // 如 "2025-08-04 14:56:59.470", 长度正好为 23
    std::strncpy(tick_info.time, t, sizeof(tick_info.time) - 1); // 当前只用时间部分
    tick_info.time[sizeof(tick_info.time) - 1] = 0;
    tick_info.exchtime = exchtime;
    tick_info.tick_type = tick_type;
    tick_info.tick_direc = tick_direc;
    tick_info.orderbook_tier = tier;

    int i = 0;
    auto it = it_bid1_;
    while (i < 20 && it != orderbook_.end()) // 20档买价
    {
        // 1. 从 orderbook_ 中取出 20 个价位
        // 2. 将这些价位的订单量、订单笔数、大单订单量等信息填入 tick_info 中
        tick_info.bid20prices[i] = it->first;
        tick_info.bid20qty[i] = it->second.total_qty[0];
        tick_info.bid20num[i] = it->second.orders[0].size();
        auto itlg = orderbook_vol_lg_.find(it->first);
        if (itlg == orderbook_vol_lg_.end())
        {
            tick_info.bid20lgqty[i] = 0;
        }
        else
        {
            tick_info.bid20lgqty[i] = itlg->second[0];
        }
        // tick_info.bid20lgqty[i] = orderbook_vol_lg_[it->first][0];
        it--;
        i++;
    }
    while (i < 20) // 当前不够20档，剩下的用 0 填充
    {
        tick_info.bid20prices[i] = 0;
        tick_info.bid20qty[i] = 0;
        tick_info.bid20num[i] = 0;
        tick_info.bid20lgqty[i] = 0;
        i++;
    }
    i = 0;
    it = it_ask1_;
    while (i < 20 && it != orderbook_.end()) // 20档卖价
    {
        tick_info.ask20prices[i] = it->first;
        tick_info.ask20qty[i] = it->second.total_qty[1];
        tick_info.ask20num[i] = it->second.orders[1].size();
        // if (tick_type == TICK_TYPE_TRANSACTION && tick_direc == TICK_DIRECT_AUCTION)
        //     std::cout << "xxx4.5" << std::endl;
        auto itlg = orderbook_vol_lg_.find(it->first);
        if (itlg == orderbook_vol_lg_.end())
        {
            tick_info.ask20lgqty[i] = 0;
        }
        else
        {
            // std::cout << "xxx4.7" << std::endl;
            tick_info.ask20lgqty[i] = itlg->second[1];
        }
        // tick_info.ask20lgqty[i] = orderbook_vol_lg_[it->first][1];
        it++;
        i++;
    }
    while (i < 20) // 当前不够20档，剩下的用 0 填充
    {
        tick_info.ask20prices[i] = 0;
        tick_info.ask20qty[i] = 0;
        tick_info.ask20num[i] = 0;
        tick_info.ask20lgqty[i] = 0;
        i++;
    }
    tick_info.total_buy_qty = total_buy_qty_;
    tick_info.total_sell_qty = total_sell_qty_;
    tick_info.prev_close = prev_close_;
    tick_info.latest = latest_;
    tick_info.mid_price = (latest_buy_price_ + latest_sell_price_) / 2;
    tick_info.spread = ask1_ - bid1_;
    tick_info.sm_order_cum_vol = lround(sm_order_cum_vol_);
    tick_info.mid_order_cum_vol = lround(mid_order_cum_vol_);
    tick_info.lg_order_cum_vol = lround(lg_order_cum_vol_);
    tick_info.slg_order_cum_vol = lround(slg_order_cum_vol_);
    tick_info.buy_order_index = buy_order_index_;
    tick_info.sell_order_index = sell_order_index_;
    tick_info.trade_money = trade_money_;
    tick_info.trade_qty = trade_qty_;
    tick_info.trade_num = trade_num_;
    tick_info.withdraw_num = total_inst_cancel_;  // 累计买卖撤单笔数
}

void Shot2t::print_tier_dict(void)
{
    std::cout << std::format("disc_coef: {}, increment: {}, tier_dict[{}]:", disc_coef_, increment_, tier_dict_.size()) << std::endl;
    for (auto p : tier_dict_)
    {
        std::cout << p.first << " " << p.second << std::endl;
    }
}

Tick_info *Shot2t::put_instruction(const order_raw_sh &order)
{
    SHOrderType order_type = order_raw_sh2SHOrderType(order);
    SHTickBSFlag tick_bs_flag = order_raw_sh2SHTickBSFlag(order);
    int order_price = static_cast<int>(std::lround(order.price * 1000.0)); // 申报价格，以厘为单位
    int64_t buy_order = order.buyOrderNo;
    int64_t sell_order = order.sellOrderNo;
    uint32_t exchtime = HHMMSSmmm2uint32_t(order.tickTime.c_str());
    const char *tick_time_str = order.tickTime.c_str();
    int64_t order_qty = order.qty;
    double trade_money = order.tradeMoney; // 成交信息时：成交金额（三位小数），新增委托订单时：已成交的委托数量（次生委托订单），0.0（原始委托订单）

    return put_instruction(tick_time_str, exchtime, order_type, tick_bs_flag, order_price, buy_order, sell_order, order_qty, trade_money);
}

Tick_info *Shot2t::put_instruction(const mdl_shl2_msg::NGTSTick &order)
{
    SHOrderType order_type = NGTSTick2SHOrderType(order);
    SHTickBSFlag tick_bs_flag = NGTSTick2SHTickBSFlag(order);
    int order_price = order.Price.m_Value; // 申报价格，以厘为单位
    int64_t buy_order = order.BuyOrderNO;
    int64_t sell_order = order.SellOrderNO;
    uint32_t exchtime = order.TickTime.m_Value;
    char tick_time_str[13];
    (void)std::snprintf(tick_time_str, sizeof(tick_time_str), "%02d:%02d:%02d.%03d", order.TickTime.GetHour(), order.TickTime.GetMinute(), order.TickTime.GetSecond(), order.TickTime.GetMilliSec());
    tick_time_str[sizeof(tick_time_str) - 1] = 0;
    int64_t order_qty = order.Qty;
    double trade_money = order.TradeMoney.GetDouble(); // 成交信息时：成交金额（三位小数），新增委托订单时：已成交的委托数量（次生委托订单），0.0（原始委托订单）

    return put_instruction(tick_time_str, exchtime, order_type, tick_bs_flag, order_price, buy_order, sell_order, order_qty, trade_money);
}

Tick_info *Shot2t::put_instruction(const char *tick_time_str, uint32_t exchtime, SHOrderType order_type, SHTickBSFlag tick_bs_flag, int order_price, int64_t buy_order, int64_t sell_order, int64_t order_qty, double trade_money)
{
    if (market_state_ == SHMarketState::START) // 此时只能由指令订单'开市集合竞价'来启动市场，或者'停牌'
    {
        if (order_type == SHOrderType::S && tick_bs_flag == SHTickBSFlag::OCALL)
        {
            market_state_ = SHMarketState::OCALL;
            whether_snapshot_ = false;
            DEBUG_LOG("market_state_ = SHMarketState::OCALL, 进入开盘集合竞价");
            total_inst_state_++; // 总指令订单数
            return nullptr;
        }
        else if (order_type == SHOrderType::S && tick_bs_flag == SHTickBSFlag::SUSP)
        {
            market_state_ = SHMarketState::SUSP;
            whether_snapshot_ = false;
            DEBUG_LOG("market_state_ = SHMarketState::SUSP, 进入停牌");
            total_inst_state_++; // 总指令订单数
            return nullptr;
        }
        else
        {
            BAD_THING(std::format("market_state_ == SHMarketState::START, order.type = {}, order.tickBSFlag = {}", SHOrderType2Str(order_type), SHTickBSFlag2Str(tick_bs_flag)));
            return nullptr;
        }
    }
    if (order_type == SHOrderType::S) // 指令订单
    {
        total_inst_state_++;
        if (tick_bs_flag == SHTickBSFlag::TRADE || (tick_bs_flag == SHTickBSFlag::CLOSE && increment_ == 10))
        {
            // 开盘集合竞价结束进入连续自动撮合或者股票（这里用最小价格变动单位来判断）收盘集合竞价结束进入闭市
            // 对于 ETF，连续撮合后直接结束，没有收盘集合竞价阶段，所以不需要重新计算买一、卖一及更新相应的叠代器（但它涉及未完结主动订单问题，在后面处理）
            market_state_ = SHMarketState::TRADE;
            if (tick_bs_flag == SHTickBSFlag::CLOSE)
            {
                market_state_ = SHMarketState::CLOSE;
                DEBUG_LOG("market_state_ = SHMarketState::CLOSE, 股票集合竞价结束进入闭市");
                if (pending_buy_no_ != 0 || pending_sell_no_ != 0)
                {
                    DEBUG_LOG(std::format("股票闭市时还有未完成的主动订单待处理, pending_buy_no_ = {}, pending_sell_no_ = {}", pending_buy_no_, pending_sell_no_));
                }
            }
            whether_snapshot_ = true;
            // DEBUG_LOG(std::format("market_state_ = SHMarketState::TRADE, 进入连续自动撮合, 准备计算买一、卖一价"));
            // 计算买一价和卖一价，根据集合竞价期间是否有成交、是否有剩余买单、卖单等条件进行分析：
            //   1、集合竞价有成交，形成成交价，当前在成交价上有 3 种可能
            //      A: 有剩余卖单，那么成交价就是当前卖一价，买一价就是紧邻的非空低价位
            //      B：有剩余买单，那么成交价就是当前买一价，卖一价就是紧邻的非空高价位
            //      C：成交价无买单、无卖单，如果有紧邻更高的非空价位，那么卖一价就是此价位，买一价是紧邻的非空低价位
            //   2、集合竞价无成交
            //      A：如果有买单，则从低开始找第一个无买单的价位，该价位就是卖一，买一则是回退一位的价位
            //      B：如果没有买单，但有卖单，则最低非空价位就是卖一价
            bid1_ = 0;
            it_bid1_ = orderbook_.end();
            ask1_ = 0;
            it_ask1_ = orderbook_.end();
            if (latest_ > 0) // 集合竞价期间有成交
            {
                auto it = orderbook_.lower_bound(latest_); // 指向大于等于成交价的最低非空价位
                if (it != orderbook_.end())                // 大于等于成交价的非空价位是存在的，大于此价位上只存在卖单。如果此价位就是成交价本身，那么它上面要么是剩余买单，要么是剩余卖单
                {
                    DEBUG_LOG(std::format("大于等于成交价的价位是有买单或卖单的, 成交价为 {}, 大于等于成交价的最低非空价位为 {}", latest_, it->first));
                    if (!it->second.orders[0].empty()) // 有买单，it 所指向的价位肯定就是成交位本身
                    {
                        DEBUG_LOG(std::format("大于等于成交价最低非空价位上有买单，因此买一价就是此价位 {}", it->first));
                        bid1_ = it->first;
                        it_bid1_ = it;
                        it++;                       // 如果还有卖单，一定在更高价位上
                        if (it != orderbook_.end()) // 更高价位上有卖单
                        {
                            ask1_ = it->first;
                            it_ask1_ = it;
                            DEBUG_LOG(std::format("卖一价为 {}", it->first));
                        }
                        else // 完全无卖单，卖一为涨停价
                        {
                            DEBUG_LOG(std::format("无卖单，卖一为涨停价 {}", ask1_));
                        }
                    }
                    else // 该价位（可能是成交价，也可能是高于成交价的最低邻近价位）没有买单，那么只可能是有卖单
                    {
                        ask1_ = it->first; // 本价位肯定有卖单
                        it_ask1_ = it;
                        DEBUG_LOG(std::format("大于等于成交价最低非空价位上有卖单，因此卖一价就是此价位 {}", it->first));
                        it--;                       // 如果还有买单，一定在更低价位上
                        if (it != orderbook_.end()) // 更低价位上有买单
                        {
                            bid1_ = it->first;
                            it_bid1_ = it;
                            DEBUG_LOG(std::format("买一价为 {}", it->first));
                        }
                        else
                        {
                            DEBUG_LOG(std::format("无买单，买一为跌停价 {}", bid1_));
                        }
                    }
                }
                else // 成交价和高于成交价的价位没有买单和卖单，说明所有卖单都已成交（之前低于成交价的卖单肯定都已成交了），卖一为涨停价，此时可能还剩下买单
                {
                    DEBUG_LOG(std::format("所有卖单都已成交，卖一为涨停 {}", ask1_));
                    it--;                       // 如果还有买单，一定在更低价位上
                    if (it != orderbook_.end()) // 更低价位上有买单
                    {
                        bid1_ = it->first;
                        it_bid1_ = it;
                        DEBUG_LOG(std::format("买一价为 {}", it->first));
                    }
                    else // 完全无剩余买单和卖单，应极为罕见，买一为跌停价
                    {
                        DEBUG_LOG(std::format("完全无剩余买单和卖单！买一价为跌停价 {}", bid1_));
                    }
                }
            }
            else // 集合竞价期间没有成交
            {
                if (total_buy_qty_ > 0) // 有买单
                {
                    DEBUG_LOG(std::format("集合竞价期间没有成交，但有买单"));
                    auto it = orderbook_.begin();
                    while (!it->second.orders[0].empty() && it != orderbook_.end()) // 从最低价开始找，找到第一个没有买单的价位
                    {
                        it++;
                    }
                    if (it != orderbook_.end()) // 有卖单
                    {
                        ask1_ = it->first;
                        it_ask1_ = it;
                        DEBUG_LOG(std::format("也有卖单，卖一价为 {}", it->first));
                        it--; // 回退一个价位是最高买价
                        bid1_ = it->first;
                        it_bid1_ = it;
                        DEBUG_LOG(std::format("买一价为 {}", it->first));
                    }
                    else // 全是买单，没有卖单
                    {
                        it_bid1_ = std::prev(orderbook_.end());
                        bid1_ = it_bid1_->first; // ask1_ 保持 upper_limit_
                        DEBUG_LOG(std::format("买一价为 {}，没有卖单，卖一价为涨停价 {}", bid1_, ask1_));
                    }
                }
                else // 没有买单
                {
                    if (total_sell_qty_ > 0) // 有卖单
                    {
                        ask1_ = orderbook_.begin()->first;
                        it_ask1_ = orderbook_.begin();
                        DEBUG_LOG(std::format("集合竞价期间没有成交，没有买单，买一为跌停价 {}, 但有卖单，卖一价为 {}", bid1_, ask1_));
                    } // 完全无买单和卖单，应极为罕见
                    else
                    {
                        DEBUG_LOG(std::format("集合竞价期间没有成交，也没有买单和卖单，应极为罕见"));
                    }
                }
            }
            if (tick_bs_flag == SHTickBSFlag::TRADE)
            {
                DEBUG_LOG(std::format("market_state_ = SHMarketState::TRADE, 进入连续自动撮合, 集合竞价期间成交价为 {}, 当前买一价为 {} {}, 卖一价为 {} {}",
                                      latest_, bid1_, it_bid1_->first, ask1_, it_ask1_->first));
            }

            // 生成的第一条 tick 信息
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_);
            buy_order_index_ = sell_order_index_ = 0;
            export_tick_info_(tick_time_str, exchtime, TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_, tick_info_);
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, xxx_fake_orderbook_tier_);
            return &tick_info_;
        }
        else if (tick_bs_flag == SHTickBSFlag::CCALL) // 收盘集合竞价，在后续集合竞价阶段处理未完结订单的资金流向问题，不在此处处理
        {
            market_state_ = SHMarketState::CCALL;
            whether_snapshot_ = false;
            DEBUG_LOG(std::format("market_state_ = SHMarketState::CCALL, 进入收盘集合竞价"));
            if (pending_buy_no_ != 0 || pending_sell_no_ != 0)
            {
                DEBUG_LOG(std::format("收盘集合竞价开始时还有未完成的主动订单待处理, pending_buy_no_ = {}, pending_sell_no_ = {}, 由后续集合竞价阶段处理...", pending_buy_no_, pending_sell_no_));
            }
            return nullptr;
        }
        else if (tick_bs_flag == SHTickBSFlag::CLOSE) // 前面已经处理过股票的闭市，这里是针对 ETF 的
        {
            market_state_ = SHMarketState::CLOSE;
            whether_snapshot_ = false;
            DEBUG_LOG(std::format("market_state_ = SHMarketState::CLOSE, 进入 ETF 闭市"));
            // ETF 没有集合竞价阶段，所以可能还有之前连续撮合阶段未完成的主动订单待处理，这里进行处理
            if (pending_buy_no_ != 0 || pending_sell_no_ != 0)
            {
                DEBUG_LOG(std::format("ETF 闭市时还有未完成的主动订单待处理, pending_buy_no_ = {}, pending_sell_no_ = {}, 进行处理...", pending_buy_no_, pending_sell_no_));
                if (pending_buy_no_ != 0 && pending_sell_no_ != 0)
                {
                    BAD_THING(std::format("同时存在未完结买订单 {} 和卖订单 {}", pending_buy_no_, pending_sell_no_));
                }
                if (pending_buy_no_ != 0) // 有未完结主动买订单
                {
                    if (pending_buy_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ += pending_buy_money_;
                    else
                        slg_order_cum_vol_ += pending_buy_money_;
                }
                else // 有未完结主动卖订单
                {
                    if (pending_sell_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ -= pending_sell_money_;
                    else
                        slg_order_cum_vol_ -= pending_sell_money_;
                }
            }
            // 生成的最后一条 tick 信息
            // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, xxx_fake_orderbook_tier_);
            buy_order_index_ = sell_order_index_ = 0;
            export_tick_info_(tick_time_str, exchtime, TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_, tick_info_);
            return &tick_info_;
        }
        else if (tick_bs_flag == SHTickBSFlag::ENDTR) // 交易结束
        {
            market_state_ = SHMarketState::ENDTR;
            whether_snapshot_ = false;
            DEBUG_LOG(std::format("market_state_ = SHMarketState::ENDTR, 进入交易结束, 处理委托 {}, 撤单 {}, 成交 {}, 指令 {}, 共 {} 条消息",
                                  total_inst_order_, total_inst_cancel_, total_inst_trans_, total_inst_state_,
                                  total_inst_order_ + total_inst_cancel_ + total_inst_trans_ + total_inst_state_));
            return nullptr;
        }
        else
        {
            BAD_THING(std::format("订单指令, 未知的 order.tickBSFlag = {}", SHTickBSFlag2Str(tick_bs_flag)));
            return nullptr;
        }
    }
    if (order_type != SHOrderType::A && order_type != SHOrderType::D && order_type != SHOrderType::T) // 非法的订单类型
    {
        BAD_THING(std::format("非法的订单类型, order.type = {}", SHOrderType2Str(order_type)));
        return nullptr;
    }

    if (order_type == SHOrderType::A) // 新增委托订单：1、集合竞价期间原始订单；2、连续竞价期间原始订单或次生订单
    {
        TradeType direction = (tick_bs_flag == SHTickBSFlag::B) ? TradeType::BUY : TradeType::SELL;
        int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;
        double qty_origin = order_qty + trade_money; // 原始申报数量，对于原始订单 TradeMoney 为0，对于次生订单，TraceMoney 为已成交数量
        bool is_derived_order = (trade_money > 0.0); // 是否为次生订单

        total_inst_order_++;                   // 总委托订单数
        orderpool_[order_no][0] = order_price; // 订单池以订单编号为索引，值为[价格(厘), 数量]
        orderpool_[order_no][1] = order_qty;

        // 处理可能的完结的订单
        if (pending_buy_no_ != 0 && pending_sell_no_ != 0)
        {
            BAD_THING(std::format("同时存在未完结买订单 {} 和卖订单 {}", pending_buy_no_, pending_sell_no_));
        }
        if (is_derived_order) // 本订单为次生订单
        {
            if (direction == TradeType::BUY) // 本条订单是次生买订单
            {
                if (order_no != pending_buy_no_)
                {
                    BAD_THING(std::format("本条次生买订单不是当前未完结主动买订单的后续订单, order_no = {}, pending_buy_no_ = {}", order_no, pending_buy_no_));
                    return nullptr;
                }
                if (pending_buy_qty_ != static_cast<int64_t>(trade_money))
                {
                    BAD_THING(std::format("次生委托买订单 {} 的已成交量不等于累积成交量, pending_buy_qty_ = {}, tradeMoney = {}", order_no, pending_buy_qty_, trade_money));
                    return nullptr;
                }
                if (qty_origin < mid_threshold_) // 原始订单是5万以下小买单
                {
                    sm_order_cum_vol_ += pending_buy_money_;
                }
                else if (qty_origin <= lg_threshold_) // 原始订单是5万至30万中买单
                {
                    mid_order_cum_vol_ += pending_buy_money_;
                }
                else if (qty_origin <= slg_threshold_) // 原始订单是30万至100万大买单
                {
                    lg_order_cum_vol_ += pending_buy_money_;
                }
                else // 原始订单是100万以上超大买单
                {
                    slg_order_cum_vol_ += pending_buy_money_;
                }
                pending_buy_no_ = 0;
                pending_buy_money_ = 0.0;
                pending_buy_qty_ = 0;
            }
            else // 本条订单是次生卖订单
            {
                if (order_no != pending_sell_no_)
                {
                    BAD_THING(std::format("本条次生卖订单不是当前未完结主动卖订单的后续订单, order_no = {}, pending_sell_no_ = {}", order_no, pending_sell_no_));
                    return nullptr;
                }
                if (pending_sell_qty_ != static_cast<int64_t>(trade_money))
                {
                    BAD_THING(std::format("次生委托卖订单 {} 的已成交量不等于累积成交量, pending_sell_qty_ = {}, tradeMoney = {}", order_no, pending_sell_qty_, trade_money));
                    return nullptr;
                }
                if (qty_origin < mid_threshold_) // 原始订单是5万以下小卖单
                {
                    sm_order_cum_vol_ -= pending_sell_money_;
                }
                else if (qty_origin <= lg_threshold_) // 原始订单是5万至30万中卖单
                {
                    mid_order_cum_vol_ -= pending_sell_money_;
                }
                else if (qty_origin <= slg_threshold_) // 原始订单是30万至100万大卖单
                {
                    lg_order_cum_vol_ -= pending_sell_money_;
                }
                else // 原始订单是100万以上超大卖单
                {
                    slg_order_cum_vol_ -= pending_sell_money_;
                }
                pending_sell_no_ = 0;
                pending_sell_money_ = 0.0;
                pending_sell_qty_ = 0;
            }
        }
        else // 本次订单为原始订单
        {
            if (pending_buy_no_ != 0) // 终结未完结的主动买订单，无法确定是全部成交还是未成交部分自动撤
            {
                if (pending_buy_qty_ < mid_threshold_) // 已成交部分为小单
                {
                    sm_order_cum_vol_ += pending_buy_money_;
                }
                else if (pending_buy_qty_ <= lg_threshold_) // 已成交部分为中单
                {
                    mid_order_cum_vol_ += pending_buy_money_;
                }
                else if (pending_buy_qty_ <= slg_threshold_) // 已成交部分为大单
                {
                    lg_order_cum_vol_ += pending_buy_money_;
                }
                else // 已成交部分为超大单
                {
                    slg_order_cum_vol_ += pending_buy_money_;
                }
                pending_buy_no_ = 0;
                pending_buy_money_ = 0.0;
                pending_buy_qty_ = 0;
            }
            else if (pending_sell_no_ != 0) // 终结未完结的主动卖订单，无法确定是全部成交还是未成交部分自动撤
            {
                if (pending_sell_qty_ < mid_threshold_) // 已成交部分为小单
                {
                    sm_order_cum_vol_ -= pending_sell_money_;
                }
                else if (pending_sell_qty_ <= lg_threshold_) // 已成交部分为中单
                {
                    mid_order_cum_vol_ -= pending_sell_money_;
                }
                else if (pending_sell_qty_ <= slg_threshold_) // 已成交部分为大单
                {
                    lg_order_cum_vol_ -= pending_sell_money_;
                }
                else // 已成交部分为超大单
                {
                    slg_order_cum_vol_ -= pending_sell_money_;
                }
                pending_sell_no_ = 0;
                pending_sell_money_ = 0.0;
                pending_sell_qty_ = 0;
            }
        }

        if (qty_origin <= mid_threshold_) // 将本订单按原始数量加入不同大小订单编号集
        {
            orderpool_sm_.insert(order_no);
        }
        else if (qty_origin <= lg_threshold_)
        {
            orderpool_mid_.insert(order_no);
        }
        else if (qty_origin <= slg_threshold_)
        {
            orderpool_lg_.insert(order_no);
        }
        else
        {
            orderpool_slg_.insert(order_no);
        }

        if (qty_origin > lg_threshold_) // 以价格为索引的30万以上大单当前有效总量统计，不区分大单和超大单了
        {
            if (orderbook_vol_lg_.contains(order_price))
            {
                orderbook_vol_lg_[order_price][direct_index] += order_qty;
            }
            else
            {
                orderbook_vol_lg_[order_price][direct_index] = order_qty;
            }
        }

        orderbook_[order_price].orders[direct_index][order_no] = order_qty; // 订单薄以价格为索引，值为[买单, 卖单]，买单和卖单都是以订单编号为索引，值为数量
        orderbook_[order_price].total_qty[direct_index] += order_qty;

        // 连续竞价阶段在新增委托时要重新计算买一、卖一并生成 tick 信息
        if (market_state_ == SHMarketState::TRADE)
        {
            if (direction == TradeType::BUY) // 本新增订单是一个买单
            {
                buy_order_index_ = order_no;
                sell_order_index_ = 0;
                if (order_price > bid1_)
                {
                    bid1_ = order_price;
                    if (total_buy_qty_ > 0) // 原来就有买单
                    {
                        it_bid1_++; // 本买单价在原来买一价更高的相邻价位
                    }
                    else
                    {
                        it_bid1_ = orderbook_.begin(); // 本买单是当前唯一买单，肯定是最底价位
                    }
                }
            }
            else // 本新增订单是一个卖单
            {
                buy_order_index_ = 0;
                sell_order_index_ = order_no;
                if (order_price < ask1_ || ask1_ == 0) // 新增的卖单价格更低，或者当前还没有卖单，因此它要变成新的卖一价
                {
                    if (order_price < bid1_) // 新增的卖单价格比买一价还低，说明程序不正常了
                    {
                        BAD_THING(std::format("新增的卖单价格比买一价还低, order_price = {}, bid1_ = {}, ask1_ = {}", order_price, bid1_, ask1_));
                    }
                    ask1_ = order_price;
                    if (total_sell_qty_ > 0) // 原来就有卖单
                    {
                        it_ask1_--; // 本卖单价在原来卖一价更低的相邻价位
                    }
                    else
                    {
                        it_ask1_ = std::prev(orderbook_.end()); // 本卖单是当前唯一卖单，肯定是最高价位
                    }
                }
            }
        }

        // XXX 待处理，计算 order_aggr

        if (direction == TradeType::BUY) // 当前总申买量和总申卖量
        {
            total_buy_qty_ += order_qty;
        }
        else
        {
            total_sell_qty_ += order_qty;
        }

        if (!tier_dict_.contains(order_price))
        {
            print_tier_dict();
            BAD_THING(std::format("新增委托订单: 在涨跌千分点字典中未找到订单对应价位, order_price = {}, 不在涨跌停价之间？", order_price));
            return nullptr;
        }
        int tier = tier_dict_[order_price]; // 根据申报价格查找应该属于哪一档
        orderbook_tier_[tier] += order_qty;

        if (!whether_snapshot_) // 只在连续竞价期间或集合竞价结束时生成Tick行情
        {
            return nullptr;
        }
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_ORDER,
        //     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, xxx_fake_orderbook_tier_);
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_ORDER,
        //                                                     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        // Tick_info tick_info(order.tickTime.c_str(), TICK_TYPE_ORDER,
        //                     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        export_tick_info_(tick_time_str, exchtime, TICK_TYPE_ORDER, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
        tick_info_.tick_vol = order_qty;
        tick_info_.tick_money = order_qty * order_price / 1000.0;
        return &tick_info_;
    }
    else if (order_type == SHOrderType::D) // 撤销委托订单，即撤单
    {
        int64_t cancel_qty = order_qty;
        TradeType direction = (tick_bs_flag == SHTickBSFlag::B) ? TradeType::BUY : TradeType::SELL;
        int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标
        int cancel_price = order_price;
        int64_t cancel_index = (direction == TradeType::BUY) ? buy_order : sell_order;

        total_inst_cancel_++; // 总撤单订单数
        // 处理可能的完结的订单（上一条消息是成交信息）
        if (pending_buy_no_ != 0 && pending_sell_no_ != 0)
        {
            BAD_THING(std::format("撤单消息，但同时存在未完结买订单 {} 和卖订单 {}", pending_buy_no_, pending_sell_no_));
        }
        if (pending_buy_no_ != 0) // 终结未完结的买订单（刚好完全成交或自动撤的市价单）
        {
            if (pending_buy_qty_ < mid_threshold_) // 已成交部分为小单
            {
                sm_order_cum_vol_ += pending_buy_money_;
            }
            else if (pending_buy_qty_ <= lg_threshold_) // 已成交部分为中单
            {
                mid_order_cum_vol_ += pending_buy_money_;
            }
            else if (pending_buy_qty_ <= slg_threshold_) // 已成交部分为大单
            {
                lg_order_cum_vol_ += pending_buy_money_;
            }
            else // 已成交部分为超大单
            {
                slg_order_cum_vol_ += pending_buy_money_;
            }
            pending_buy_no_ = 0;
            pending_buy_money_ = 0.0;
            pending_buy_qty_ = 0;
        }
        else if (pending_sell_no_ != 0) // 终结未完结的卖订单（刚好完全成交或自动撤的市价单）
        {
            if (pending_sell_qty_ < mid_threshold_) // 已成交部分为小单
            {
                sm_order_cum_vol_ -= pending_sell_money_;
            }
            else if (pending_sell_qty_ <= lg_threshold_) // 已成交部分为中单
            {
                mid_order_cum_vol_ -= pending_sell_money_;
            }
            else if (pending_sell_qty_ <= slg_threshold_) // 已成交部分为大单
            {
                lg_order_cum_vol_ -= pending_sell_money_;
            }
            else // 已成交部分为超大单
            {
                slg_order_cum_vol_ -= pending_sell_money_;
            }
            pending_sell_no_ = 0;
            pending_sell_money_ = 0.0;
            pending_sell_qty_ = 0;
        }

        if (direction == TradeType::BUY) // 当前总申买量和总申卖量，先冲减，后面更新买一、卖一时要用于一些判断
        {
            if (total_buy_qty_ < cancel_qty)
            {
                BAD_THING(std::format("撤单 {} 当前总申买量小于撤单量, total_buy_qty_ = {}, cancel_qty = {}", cancel_index, total_buy_qty_, cancel_qty));
                return nullptr;
            }
            total_buy_qty_ -= cancel_qty;
            if (total_buy_qty_ == 0)
            {
                DEBUG_LOG(std::format("撤单后总申买量为 0, total_buy_qty_ = 0"));
            }
        }
        else
        {
            if (total_sell_qty_ < cancel_qty)
            {
                BAD_THING(std::format("撤单 {} 当前总申卖量小于撤单量, total_sell_qty_ = {}, cancel_qty = {}", cancel_index, total_sell_qty_, cancel_qty));
                return nullptr;
            }
            total_sell_qty_ -= cancel_qty;
            if (total_sell_qty_ == 0)
            {
                DEBUG_LOG(std::format("撤单后总申卖量为 0, total_sell_qty_ = 0"));
            }
        }

        int r = orderbook_[cancel_price].orders[direct_index].erase(cancel_index); // 从订单薄中删除订单
        if (r == 0)                                                                // 未找到要删除的订单
        {
            BAD_THING(std::format("在主订单薄中未找到要删除的订单, cancel_index = {}", cancel_index));
            return nullptr;
        }
        if (market_state_ == SHMarketState::TRADE) // 连续竞价阶段要更新买一、卖一价
        {
            if (cancel_price == bid1_) // 撤单价为买一价，说明被撤的订单是一个买单
            {
                buy_order_index_ = cancel_index;
                sell_order_index_ = 0;
                if (orderbook_[cancel_price].orders[0].empty()) // 该价位的买单都已删除
                {
                    if (total_buy_qty_ == 0) // 所有买单都已删除
                    {
                        bid1_ = 0;
                        it_bid1_ = orderbook_.end();
                    }
                    else // 仍有买单，但已删除买一价
                    {
                        it_bid1_--; // 新买一价为更低的相邻价位
                        bid1_ = it_bid1_->first;
                    }
                }
            }
            else if (cancel_price == ask1_) // 撤单价为卖一价，说明被撤的订单是一个卖单
            {
                buy_order_index_ = 0;
                sell_order_index_ = cancel_index;
                if (orderbook_[cancel_price].orders[1].empty()) // 该价位的卖单都已删除
                {
                    if (total_sell_qty_ == 0) // 所有卖单都已删除
                    {
                        ask1_ = 0;
                        it_ask1_ = orderbook_.end();
                    }
                    else // 仍有卖单，但已删除卖一价
                    {
                        it_ask1_++; // 新卖一价为更高的相邻价位
                        if (it_ask1_ == orderbook_.end())
                        {
                            BAD_THING(std::format("统计仍有卖单, 但 it_ask1_ 到底了"));
                        }
                        ask1_ = it_ask1_->first;
                    }
                }
            }
        }
        // DEBUG_LOG(std::format("after delete: bid1 {} {} ask1 {} {}", bid1_, it_bid1_->first, ask1_, it_ask1_->first));

        if (orderbook_[cancel_price].orders[0].empty() && orderbook_[cancel_price].orders[1].empty()) // 该价位的订单都已删除
        {
            orderbook_.erase(cancel_price);
        }
        if (orderbook_.contains(cancel_price)) // 该价位上还有订单，要调减该价位的该方向的订单量总量
        {
            if (orderbook_[cancel_price].total_qty[direct_index] < cancel_qty) // 该价位的订单量小于要删除的数量
            {
                BAD_THING(std::format("在订单量总量统计中当前量小于要删除的数量, cancel_price = {}, direct_index = {}, cancel_qty = {}", cancel_price, direct_index, cancel_qty));
                return nullptr;
            }
            orderbook_[cancel_price].total_qty[direct_index] -= cancel_qty;
        }

        r = orderpool_.erase(cancel_index); // 从订单池中删除订单
        if (r == 0)                         // 未找到要删除的订单
        {
            BAD_THING(std::format("在订单池中未找到要删除的订单, cancel_index = {}", cancel_index));
            return nullptr;
        }

        bool was_lg = false;                      // 删除前曾属于大单或超大单（之前加入时是以原始订单量来计算的，现在原始订单量的信息已经没有了）
        if (orderpool_sm_.contains(cancel_index)) // 属于小单
        {
            orderpool_sm_.erase(cancel_index);
        }
        else if (orderpool_mid_.contains(cancel_index)) // 属于中单
        {
            orderpool_mid_.erase(cancel_index);
        }
        else if (orderpool_lg_.contains(cancel_index)) // 属于大单
        {
            was_lg = true;
            orderpool_lg_.erase(cancel_index);
        }
        else if (orderpool_slg_.contains(cancel_index)) // 属于超大单
        {
            was_lg = true;
            orderpool_slg_.erase(cancel_index);
        }
        else
        {
            BAD_THING(std::format("在4种订单池子中都未找到要删除的订单, cancel_index = {}", cancel_index));
            return nullptr;
        }

        if (was_lg)
        {
            if (!orderbook_vol_lg_.contains(cancel_price)) // 以价格为索引的大单量总量统计，该价位未曾有大单或超大单
            {
                BAD_THING(std::format("在大单量总量统计中未找到要减量的价位, cancel_price = {}, direct_index = {}, cancel_qty = {}", cancel_price, direct_index, cancel_qty));
                return nullptr;
            }
            else if (orderbook_vol_lg_[cancel_price][direct_index] < cancel_qty) // 该价位的大单量小于要删除的数量
            {
                BAD_THING(std::format("在大单量总量统计中当前量小于要删除的数量, cancel_price = {}, direct_index = {}, cancel_qty = {}, vol = {}, "
                                      "RecvTime = {}, cancel_index = {}",
                                      cancel_price, direct_index, cancel_qty, orderbook_vol_lg_[cancel_price][direct_index], tick_time_str, cancel_index));
                return nullptr;
            }
            orderbook_vol_lg_[cancel_price][direct_index] -= cancel_qty;
            if (orderbook_vol_lg_[cancel_price][0] == 0 && orderbook_vol_lg_[cancel_price][1] == 0) // 该价位的大单都已删除
            {
                orderbook_vol_lg_.erase(cancel_price);
            }
        }

        if (!tier_dict_.contains(cancel_price))
        {
            BAD_THING(std::format("撤单: 在涨跌千分点字典中未找到订单对应价位, cancel_price = {}, 不在涨跌停价之间？", cancel_price));
            return nullptr;
        }
        int tier = tier_dict_[cancel_price];
        if (orderbook_tier_[tier] < cancel_qty)
        {
            BAD_THING(std::format("撤单：在涨跌千分点挂单量统计中当前量小于要删除的数量, tier = {}, cancel_qty = {}", tier, cancel_qty));
            return nullptr;
        }
        orderbook_tier_[tier] -= cancel_qty;

        if (!whether_snapshot_) // 只在连续竞价期间或集合竞价结束时生成Tick行情
        {
            return nullptr;
        }
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_CANCEL,
        //     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, xxx_fake_orderbook_tier_);
        // auto &tick_info = tick_info_snapshots_.emplace_back(order.tickTime.c_str(), TICK_TYPE_CANCEL,
        //                                                     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        // Tick_info tick_info(order.tickTime.c_str(), TICK_TYPE_CANCEL,
        //                     direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_);
        export_tick_info_(tick_time_str, exchtime, TICK_TYPE_CANCEL, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
        tick_info_.tick_vol = cancel_qty;
        tick_info_.tick_money = cancel_qty * cancel_price / 1000.0;
        return &tick_info_;
    }
    else if (order_type == SHOrderType::T) // 成交信息
    {
        int64_t trade_qty = order_qty;

        buy_order_index_ = buy_order;
        sell_order_index_ = sell_order;

        // 更累计成交统计
        trade_qty_ += trade_qty;  // 累计成交量
        trade_money_ += trade_qty * order_price;  // 累计成交金额，单位厘
        trade_num_++;  // 累计成交笔数

        total_inst_trans_++;   // 总成交订单数
        latest_ = order_price; // 以厘为单位的成交价格

        if (market_state_ == SHMarketState::OCALL || market_state_ == SHMarketState::CCALL) // 开市集合竞价和收盘集合竞价，成交双方订单肯定都在订单池里
        {
            if (!orderpool_.contains(buy_order)) // 买单不在订单池中
            {
                BAD_THING(std::format("集合竞价期间在订单池中未找到成交买单, buy_order = {}", buy_order));
                return nullptr;
            }
            if (!orderpool_.contains(sell_order)) // 卖单不在订单池中
            {
                BAD_THING(std::format("集合竞价期间在订单池中未找到成交卖单, sell_order = {}", sell_order));
                return nullptr;
            }
            // 集合竞价阶段的买、卖、成交价可能都不相等
            int buy_order_price = orderpool_[buy_order][0];   // 原始买单报价，单位厘
            int sell_order_price = orderpool_[sell_order][0]; // 原始卖单报价，单位厘

            // 不同大小订单的净流入资金统计，注意这里没有把订单从数量大小分类池中去除，因为订单总数不多，所以性能影响可以接受
            // 撤单时删除是因为不必检查是否完全成交，这里要检查是完全成交才能删除，所以删除的代价稍大
            bool was_lg_buy = false;
            if (orderpool_sm_.contains(buy_order)) // 买单属于小单
                sm_order_cum_vol_ += trade_money;
            else if (orderpool_mid_.contains(buy_order)) // 买单属于中单
                mid_order_cum_vol_ += trade_money;
            else if (orderpool_lg_.contains(buy_order)) // 买单属于大单
            {
                lg_order_cum_vol_ += trade_money;
                was_lg_buy = true;
            }
            else if (orderpool_slg_.contains(buy_order)) // 买单属于超大单
            {
                slg_order_cum_vol_ += trade_money;
                was_lg_buy = true;
            }
            else
            {
                BAD_THING(std::format("集合竞价阶段成交信息: 在4种订单池子中都未找到买单, buy_order = {}", buy_order));
                return nullptr;
            }
            bool was_lg_sell = false;
            if (orderpool_sm_.contains(sell_order)) // 卖单属于小单
                sm_order_cum_vol_ -= trade_money;
            else if (orderpool_mid_.contains(sell_order)) // 卖单属于中单
                mid_order_cum_vol_ -= trade_money;
            else if (orderpool_lg_.contains(sell_order)) // 卖单属于大单
            {
                lg_order_cum_vol_ -= trade_money;
                was_lg_sell = true;
            }
            else if (orderpool_slg_.contains(sell_order)) // 卖单属于超大单
            {
                slg_order_cum_vol_ -= trade_money;
                was_lg_sell = true;
            }
            else
            {
                BAD_THING(std::format("集合竞价阶段成交信息: 在4种订单池子中都未找到卖单, sell_order = {}", sell_order));
                return nullptr;
            }

            orderpool_[buy_order][1] -= trade_qty;                         // 修改订单池中成交买订单的数量，减掉成交部分
            orderbook_[buy_order_price].orders[0][buy_order] -= trade_qty; // 修改主订单薄中买单表中该订单的数量，减掉成交部分
            orderbook_[buy_order_price].total_qty[0] -= trade_qty;
            if (orderpool_[buy_order][1] == 0) // 该买单已全部成交
            {
                orderpool_.erase(buy_order);
                orderbook_[buy_order_price].orders[0].erase(buy_order);
                if (orderbook_[buy_order_price].orders[0].empty() && orderbook_[buy_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    orderbook_.erase(buy_order_price);
                }
            }

            orderpool_[sell_order][1] -= trade_qty;                          // 修改订单池中成交卖订单的数量，减掉成交部分
            orderbook_[sell_order_price].orders[1][sell_order] -= trade_qty; // 修改主订单薄中卖单表中该订单的数量，减掉成交部分
            orderbook_[sell_order_price].total_qty[1] -= trade_qty;
            if (orderpool_[sell_order][1] == 0) // 该卖单已全部成交
            {
                orderpool_.erase(sell_order);
                orderbook_[sell_order_price].orders[1].erase(sell_order);
                if (orderbook_[sell_order_price].orders[0].empty() && orderbook_[sell_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    orderbook_.erase(sell_order_price);
                }
            }

            total_buy_qty_ -= trade_qty;  // 调整当前总申买量
            total_sell_qty_ -= trade_qty; // 调整当前总申卖量

            if (was_lg_buy) // 买单属于大单
            {
                if (!orderbook_vol_lg_.contains(buy_order_price)) // 买单对应价位未曾有大单
                {
                    BAD_THING(std::format("成交信息: 在大单量总量统计中未找到买单对应价位, buy_order_price = {}", buy_order_price));
                    return nullptr;
                }
                else if (orderbook_vol_lg_[buy_order_price][0] < trade_qty) // 买单对应价位的大单量小于要删除的数量
                {
                    BAD_THING(std::format("成交信息: 在大单量总量统计中当前量小于本次成交的数量, buy_order_price = {}, trade_qty = {}, vol = {}", buy_order_price, trade_qty, orderbook_vol_lg_[buy_order_price][0]));
                    return nullptr;
                }
                orderbook_vol_lg_[buy_order_price][0] -= trade_qty;                                           // 调整大单统计薄中买单对应价位的总申买量
                if (orderbook_vol_lg_[buy_order_price][0] == 0 && orderbook_vol_lg_[buy_order_price][1] == 0) // 买单对应价位的大单都已删除
                {
                    orderbook_vol_lg_.erase(buy_order_price);
                }
            }
            if (was_lg_sell) // 卖单属于大单
            {
                if (!orderbook_vol_lg_.contains(sell_order_price)) // 卖单对应价位未曾有大单
                {
                    BAD_THING(std::format("成交信息: 在大单量总量统计中未找到卖单对应价位, sell_order_price = {}", sell_order_price));
                    return nullptr;
                }
                else if (orderbook_vol_lg_[sell_order_price][1] < trade_qty) // 卖单对应价位的大单量小于要删除的数量
                {
                    BAD_THING(std::format("成交信息: 在大单量总量统计中当前量小于本次成交的数量, sell_order_price = {}, trade_qty = {}, vol = {}", sell_order_price, trade_qty, orderbook_vol_lg_[sell_order_price][1]));
                    return nullptr;
                }
                orderbook_vol_lg_[sell_order_price][1] -= trade_qty;                                            // 调整大单统计薄中卖单对应价位的总申卖量
                if (orderbook_vol_lg_[sell_order_price][0] == 0 && orderbook_vol_lg_[sell_order_price][1] == 0) // 卖单对应价位的大单都已删除
                {
                    orderbook_vol_lg_.erase(sell_order_price);
                }
            }

            // XXX 需要吗？待处理，集合竞价期间更新买一、卖一
            if ((bid1_ > 0) && (ask1_ > 0) && whether_snapshot_)
            {
            }
            if (!tier_dict_.contains(buy_order_price)) // 买单对应价位不在涨跌停价内
            {
                BAD_THING(std::format("成交信息: 在涨跌千分点字典中未找到买单对应价位, buy_order_price = {}, 不在涨跌停价内？", buy_order_price));
                return nullptr;
            }
            int tier = tier_dict_[buy_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                BAD_THING(std::format("成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
                return nullptr;
            }
            orderbook_tier_[tier] -= order_qty;
            if (!tier_dict_.contains(sell_order_price)) // 卖单对应价位不在涨跌停价内
            {
                BAD_THING(std::format("成交信息: 在涨跌千分点字典中未找到卖单对应价位, sell_order_price = {}, 不在涨跌停价内？", sell_order_price));
                return nullptr;
            }
            tier = tier_dict_[sell_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                BAD_THING(std::format("成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
                return nullptr;
            }
            orderbook_tier_[tier] -= order_qty;
        }
        else if (market_state_ == SHMarketState::TRADE) // 连续竞价阶段的成交信息
        {
            // 1. 主动订单委托信息肯定尚未发布（如果它不产生次生订单的话则永远不会发布），但本条成交可能是同一笔主动订单的后续成交
            // 2. 被动订单委托信息肯定已经发布（它也可能是个次生订单）
            // 3. 同一笔主动订单成交信息连续发布
            // 4. 如果有次生订单，则在连续发布完成交信息后立即发布
            // 5. 如果不产生次生订单，则下一条信息可能是新的委托订单，也可能是下一笔成交信息，也可能是指令订单
            int other_index;
            TradeType direction;
            int multiplier = 1; // 资金流入
            int64_t active_order, passive_order;

            // 设置一些变量如 direct_index 等，处理可能的未完结订单
            if (tick_bs_flag == SHTickBSFlag::B) // 本次成交是主动买订单
            {
                direction = TradeType::BUY;
                active_order = buy_order;
                passive_order = sell_order;
                other_index = 1;
                if (pending_buy_no_ == 0) // 还没有未完结主动买订单，因此本次成交的主动买订单肯定是一个新的主动买单
                {
                    if (pending_sell_no_ != 0) // 有未完结主动卖订单，说明原主动卖订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                    {
                        if (pending_sell_qty_ <= mid_threshold_)
                            sm_order_cum_vol_ -= pending_sell_money_;
                        else if (pending_sell_qty_ <= lg_threshold_)
                            mid_order_cum_vol_ -= pending_sell_money_;
                        else if (pending_sell_qty_ <= slg_threshold_)
                            lg_order_cum_vol_ -= pending_sell_money_;
                        else
                            slg_order_cum_vol_ -= pending_sell_money_;
                        pending_sell_no_ = 0; // 终结未完结的主动卖订单
                        pending_sell_qty_ = 0;
                        pending_sell_money_ = 0.0;
                    }
                    pending_buy_no_ = active_order;
                    pending_buy_qty_ = trade_qty;
                    pending_buy_money_ = trade_money;
                }
                else if (pending_buy_no_ != active_order) // 有未终结主动买订单，但不是当前订单，原主动买订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                {
                    if (pending_buy_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ += pending_buy_money_;
                    else if (pending_buy_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ += pending_buy_money_;
                    else
                        slg_order_cum_vol_ += pending_buy_money_;
                    pending_buy_no_ = active_order;
                    pending_buy_qty_ = trade_qty;
                    pending_buy_money_ = trade_money;
                }
                else // 已有未完结主动买订单，且是当前订单
                {
                    pending_buy_qty_ += trade_qty;
                    pending_buy_money_ += trade_money;
                }
                total_sell_qty_ -= trade_qty; // 被动的卖方订单之前是被计入总申卖量的，现在成交了要调整当前总申卖量
            }
            else if (tick_bs_flag == SHTickBSFlag::S) // 本次成交是主动卖订单
            {
                direction = TradeType::SELL;
                active_order = sell_order;
                passive_order = buy_order;
                other_index = 0;
                multiplier = -1;           // 资金流出
                if (pending_sell_no_ == 0) // 还没有未完结主动卖订单，因此本次成交的主动卖订单肯定是一个新的主动卖单
                {
                    if (pending_buy_no_ != 0) // 有未完结主动买订单，说明原主动买订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                    {
                        if (pending_buy_qty_ <= mid_threshold_)
                            sm_order_cum_vol_ += pending_buy_money_;
                        else if (pending_buy_qty_ <= lg_threshold_)
                            mid_order_cum_vol_ += pending_buy_money_;
                        else if (pending_buy_qty_ <= slg_threshold_)
                            lg_order_cum_vol_ += pending_buy_money_;
                        else
                            slg_order_cum_vol_ += pending_buy_money_;
                        pending_buy_no_ = 0; // 终结未完结的主动买订单
                        pending_buy_qty_ = 0;
                        pending_buy_money_ = 0.0;
                    }
                    pending_sell_no_ = active_order;
                    pending_sell_qty_ = trade_qty;
                    pending_sell_money_ = trade_money;
                }
                else if (pending_sell_no_ != active_order) // 已有未完结主动卖订单，但不是当前订单，原主动卖订单未产生次生订单，要对其进行资金净流入统计，然后终结之
                {
                    if (pending_sell_qty_ <= mid_threshold_)
                        sm_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= lg_threshold_)
                        mid_order_cum_vol_ -= pending_sell_money_;
                    else if (pending_sell_qty_ <= slg_threshold_)
                        lg_order_cum_vol_ -= pending_sell_money_;
                    else
                        slg_order_cum_vol_ -= pending_sell_money_;
                    pending_sell_no_ = active_order;
                    pending_sell_qty_ = trade_qty;
                    pending_sell_money_ = trade_money;
                }
                else // 已有未完结主动卖订单，且是当前订单
                {
                    pending_sell_qty_ += trade_qty;
                    pending_sell_money_ += trade_money;
                }
                total_buy_qty_ -= trade_qty; // 被动的买方订单之前是被计入总申买量的，现在成交了要调整当前总申买量
            }
            else
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 未知的tickBSFlag, tickBSFlag = {}", SHTickBSFlag2Str(tick_bs_flag)));
                return nullptr;
            }

            if (!orderpool_.contains(passive_order)) // 被动订单不在订单池中
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 被动订单不在订单池中, passive_order = {}, direction = {}", passive_order, SHTickBSFlag2Str(tick_bs_flag)));
                return nullptr;
            }
            if (orderpool_.contains(active_order)) // 主动订单出现在订单池中，也不合理
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 主动订单出现在订单池中, active_order = {}, direction = {}", active_order, SHTickBSFlag2Str(tick_bs_flag)));
                return nullptr;
            }

            // 取出被动订单的申报价格
            int64_t passive_order_price = orderpool_[passive_order][0];
            if (!orderbook_.contains(passive_order_price)) // 被动订单对应价位不在订单薄中
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 在订单薄中未找到被动订单对应价位, passive_order_price = {}", passive_order_price));
                return nullptr;
            }
            if (!orderbook_[passive_order_price].orders[other_index].contains(passive_order)) // 被动订单不在订单薄中
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 在订单薄对应价位中未找到被动订单号, passive_order = {}", passive_order));
                return nullptr;
            }

            // 被动方造成的资金净流向
            bool passive_is_lg = false;
            if (orderpool_sm_.contains(passive_order))
            {
                sm_order_cum_vol_ -= multiplier * trade_money;
            }
            else if (orderpool_mid_.contains(passive_order))
            {
                mid_order_cum_vol_ -= multiplier * trade_money;
            }
            else if (orderpool_lg_.contains(passive_order))
            {
                lg_order_cum_vol_ -= multiplier * trade_money;
                passive_is_lg = true;
            }
            else if (orderpool_slg_.contains(passive_order))
            {
                slg_order_cum_vol_ -= multiplier * trade_money;
                passive_is_lg = true;
            }
            // 主动方原始订单量还未知，所以要等到可能的次生委托单再作判断

            // 更新 orderpool_ 中的被动订单的未成交数量
            orderpool_[passive_order][1] -= trade_qty;
            if (orderpool_[passive_order][1] == 0) // 该订单已全部成交
            {
                orderpool_.erase(passive_order);
            }

            if (direction == TradeType::BUY && passive_order_price != ask1_)
            {
                BAD_THING(std::format("连续竞价成交: 买单成交，但被动订单价位 {} 不是卖一价 {}，被动订单号 {}", passive_order_price, ask1_, passive_order));
                return nullptr;
            }
            else if (direction == TradeType::SELL && passive_order_price != bid1_)
            {
                BAD_THING(std::format("连续竞价成交: 卖单成交，但被动订单价位 {} 不是买一价 {}, 被动订单号 {}", passive_order_price, bid1_, passive_order));
                return nullptr;
            }
            // 更新 orderbook_ 中的被动订单的未成交数量
            orderbook_[passive_order_price].orders[other_index][passive_order] -= trade_qty; // 被动订单在订单薄中，成交了要从里边减去
            orderbook_[passive_order_price].total_qty[other_index] -= trade_qty;
            if (orderbook_[passive_order_price].orders[other_index][passive_order] == 0) // 被动订单已全部成交
            {
                // DEBUG_LOG(std::format("连续竞价成交: 被动订单已全部成交, passive_order = {}", passive_order));
                orderbook_[passive_order_price].orders[other_index].erase(passive_order);

                // 连续竞价期间有成交时，要更新买一、卖一
                if (direction == TradeType::BUY) // 本次成交是主动买
                {
                    // DEBUG_LOG(std::format("连续竞价主动买成交：被动订单全部成交，卖一价 {} {} 剩余订单数：{}", ask1_, it_ask1_->first, it_ask1_->second.orders[1].size()));
                    if (it_ask1_->second.orders[1].empty()) // 本价位的卖单已全部成交
                    {
                        // std::cout << "此价位卖单全部成交，将要更新 ask1_: " << it_ask1_->first << std::endl;
                        if (total_sell_qty_ > 0) // 仍有卖单，但已删除卖一价，说明卖一价更高的相邻价位上有卖单
                        {
                            it_ask1_++; // 新卖一价为更高的相邻价位
                            ask1_ = it_ask1_->first;
                        }
                        else
                        {
                            ask1_ = 0;
                            it_ask1_ = orderbook_.end();
                        }
                        // std::cout << "新的 ask1_: " << ask1_ << std::endl;
                    }
                }
                else // 本次成交是主动卖
                {
                    if (it_bid1_->second.orders[0].empty()) // 本价位的买单已全部成交
                    {
                        // std::cout << "此价位买单全部成交，将要更新 bid1_: " << it_bid1_->first << " total_buy_qty_: " << total_buy_qty_ << std::endl;
                        if (total_buy_qty_ > 0) // 仍有买单，但已删除买一价，说明买一价更低的相邻价位上有买单
                        {
                            it_bid1_--; // 新买一价为更低的相邻价位
                            if (it_bid1_ == orderbook_.end())
                            {
                                BAD_THING(std::format("连续竞价成交: it_bid1_ 到底了"));
                                return nullptr;
                            }
                            bid1_ = it_bid1_->first;
                        }
                        else
                        {
                            // DEBUG_LOG(std::format("当前已经没有买单，买一价为跌停价 {}", bid1_));
                            bid1_ = 0;
                            it_bid1_ = orderbook_.end();
                        }
                        // std::cout << "新的 bid1_: " << bid1_ << std::endl;
                    }
                }
                if (orderbook_[passive_order_price].orders[0].empty() && orderbook_[passive_order_price].orders[1].empty()) // 该价位的买卖订单都已删除
                {
                    if (orderbook_[passive_order_price].total_qty[other_index] != 0)
                    {
                        BAD_THING(std::format("连续竞价成交: 订单薄价位 {} 中所有订单都已删除, 但被动订单量总量不为0, 被动订单号 {}, other_index {}", passive_order_price, passive_order, other_index));
                        return nullptr;
                    }
                    orderbook_.erase(passive_order_price);
                }
            }
            else // 被动订单未全部成交，应该是意味着主动单已经完全成交，后续不会再出现该主动单号了
            {
                // DEBUG_LOG(std::format("连续竞价成交: 被动订单未全部成交, passive_order = {}", passive_order));
            }

            if (passive_is_lg) // 被动订单是大单或超大单
            {
                if (!orderbook_vol_lg_.contains(passive_order_price)) // 以价格为索引的30万以上大单当前有效总量统计
                {
                    BAD_THING(std::format("连续竞价阶段成交信息: 在大单量总量统计中未找到被动订单对应价位, passive_order_price = {}", passive_order_price));
                    return nullptr;
                }
                if (orderbook_vol_lg_[passive_order_price][other_index] < trade_qty) // 该价位的大单量小于要删除的数量
                {
                    BAD_THING(std::format("连续竞价阶段成交信息: 在大单量总量统计中当前量小于本次成交的数量, passive_order_price = {}, trade_qty = {}, vol = {}", passive_order_price, trade_qty, orderbook_vol_lg_[passive_order_price][other_index]));
                    return nullptr;
                }
                orderbook_vol_lg_[passive_order_price][other_index] -= trade_qty;
                if (orderbook_vol_lg_[passive_order_price][0] == 0 && orderbook_vol_lg_[passive_order_price][1] == 0) // 该价位的大单量总量都已删除
                {
                    orderbook_vol_lg_.erase(passive_order_price);
                }
            }
            if (!tier_dict_.contains(passive_order_price)) // 被动订单对应价位不在涨跌停价内
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 在涨跌千分点字典中未找到被动订单对应价位, passive_order_price = {}, 不在涨跌停价内？", passive_order_price));
                return nullptr;
            }
            int tier = tier_dict_[passive_order_price];
            if (orderbook_tier_[tier] < trade_qty)
            {
                BAD_THING(std::format("连续竞价阶段成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
                return nullptr;
            }
            orderbook_tier_[tier] -= trade_qty;

            export_tick_info_(tick_time_str, exchtime, TICK_TYPE_TRANSACTION, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
            tick_info_.tick_vol = trade_qty;
            tick_info_.tick_money = trade_qty * order_price / 1000.0;
            return &tick_info_;
        }
    }

    return nullptr; // 应该不会到这里
}
