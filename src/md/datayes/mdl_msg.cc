
// 验证通联的 MDL 消息体可以直接存储
// 无需链接 mdl_api 库, 因为我们只使用了头文件中有关消息体的结构和方法, 没用到在库中实现的私有的构造函数
// g++ -std=c++20 mdl_msg.cc

#include <iostream>
#include <format>

#include "mdl_sdk_2_13_231/include/mdl_api.h"
#include "mdl_sdk_2_13_231/include/mdl_szl2_msg.h"
#include "mdl_sdk_2_13_231/include/mdl_shl2_msg.h"

using namespace datayes::mdl;

// security=300737, price=5.17, qty=1300
unsigned char o[] = {0xdf, 0x07, 0x00, 0x00, 0x7d, 0xf0, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x2e, 0x00,
                     0x00, 0x00, 0x06, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x04, 0x00, 0x2d, 0x00, 0x00, 0x00, 0xf4, 0xc9,
                     0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00,
                     0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x32, 0x00, 0x00, 0x00, 0x30, 0x31, 0x31, 0x00, 0x33, 0x30,
                     0x30, 0x37, 0x33, 0x37, 0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=301607, price=35.7, qty=500
unsigned char o1[] = {0xdc, 0x07, 0x00, 0x00, 0x1c, 0x07, 0xc2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x2e, 0x00,
                      0x00, 0x00, 0x06, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x04, 0x00, 0x2d, 0x00, 0x00, 0x00, 0x88, 0x72,
                      0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf4, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00,
                      0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x32, 0x00, 0x00, 0x00, 0x30, 0x31, 0x31, 0x00, 0x33, 0x30,
                      0x31, 0x36, 0x30, 0x37, 0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=123089, price=141.497, qty=30
unsigned char o2[] = {0xf0, 0x07, 0x00, 0x00, 0xbb, 0xf1, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x2e, 0x00,
                      0x00, 0x00, 0x06, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x04, 0x00, 0x2d, 0x00, 0x00, 0x00, 0x3a, 0x97,
                      0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00,
                      0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x32, 0x00, 0x00, 0x00, 0x30, 0x31, 0x31, 0x00, 0x31, 0x32,
                      0x33, 0x30, 0x38, 0x39, 0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=159750, price=1.15, qty=32800
unsigned char o3[] = {0xe6, 0x07, 0x00, 0x00, 0x8e, 0xe6, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x2e, 0x00,
                      0x00, 0x00, 0x06, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x04, 0x00, 0x2d, 0x00, 0x00, 0x00, 0xec, 0x2c,
                      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x00,
                      0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x32, 0x00, 0x00, 0x00, 0x30, 0x31, 0x31, 0x00, 0x31, 0x35,
                      0x39, 0x37, 0x35, 0x30, 0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=000955, price=4.85, qty=100
unsigned char t[] = {0xdc, 0x07, 0x00, 0x00, 0x1b, 0x07, 0xc2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x3a, 0x00,
                     0x00, 0x00, 0x1a, 0x07, 0xc2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0x4e, 0x70, 0x00, 0x00, 0x00,
                     0x00, 0x00, 0x06, 0x00, 0x28, 0x00, 0x00, 0x00, 0x04, 0x00, 0x29, 0x00, 0x00, 0x00, 0x74, 0xbd,
                     0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x00,
                     0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x30, 0x31, 0x31, 0x00, 0x30, 0x30, 0x30, 0x39, 0x35, 0x35,
                     0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=002975, price=0, qty=4200
unsigned char t1[] = {0xde, 0x07, 0x00, 0x00, 0xa0, 0xe3, 0xbe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x3a, 0x00,
                      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x10, 0xaa, 0x00, 0x00, 0x00,
                      0x00, 0x00, 0x06, 0x00, 0x28, 0x00, 0x00, 0x00, 0x04, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00,
                      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x68, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0x00,
                      0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x30, 0x31, 0x31, 0x00, 0x30, 0x30, 0x32, 0x39, 0x37, 0x35,
                      0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

// security=002865, price=46.46, qty=100
unsigned char t2[] = {0xdc, 0x07, 0x00, 0x00, 0x1e, 0x07, 0xc2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x3a, 0x00,
                      0x00, 0x00, 0xc2, 0xff, 0xc1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1d, 0x07, 0xc2, 0x00, 0x00, 0x00,
                      0x00, 0x00, 0x06, 0x00, 0x28, 0x00, 0x00, 0x00, 0x04, 0x00, 0x29, 0x00, 0x00, 0x00, 0xd8, 0x16,
                      0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x00,
                      0x00, 0x00, 0x9e, 0xd8, 0xab, 0x05, 0x30, 0x31, 0x31, 0x00, 0x30, 0x30, 0x32, 0x38, 0x36, 0x35,
                      0x00, 0x31, 0x30, 0x32, 0x20, 0x00};

void printo(unsigned char *m, int len)
{
    mdl_szl2_msg::Order300192_v2 *resp = (mdl_szl2_msg::Order300192_v2 *)m;
    std::cout << std::format("深交所委托 sizeof pack={}(+{}), ChannelNo={}, ApplSeqNum={}, streamid={}, security={}({}), secidsource={}, price={}({}), qty={}",
                             len, len - sizeof(*resp), resp->ChannelNo, resp->ApplSeqNum, resp->MDStreamID.c_str(), resp->SecurityID.c_str(), resp->SecurityID.Length, resp->SecurityIDSource.c_str(), resp->Price.GetDouble(), resp->Price.m_Value, resp->OrderQty)
              << std::endl;
}

void printt(unsigned char *m, int len)
{
    mdl_szl2_msg::Transaction300191_v2 *t = (mdl_szl2_msg::Transaction300191_v2 *)m;
    std::cout << std::format("深交所成交 sizeof pack={}(+{}), ChannelNo={}, ApplSeqNum={}, security={}, price={}({}), qty={}", len, len - sizeof(*t), t->ChannelNo, t->ApplSeqNum, t->SecurityID.c_str(), t->LastPx.GetDouble(), t->LastPx.m_Value, t->LastQty) << std::endl;
}

void printshot(unsigned char *m)
{
    mdl_shl2_msg::NGTSTick *shot = (mdl_shl2_msg::NGTSTick *)m;
    std::cout << std::format("上交所委托成交合并 price={}({}){}, TradeMoney={}({}){}", shot->Price.GetFloat(), shot->Price.m_Value / 1000.0, shot->Price.m_Value, shot->TradeMoney.GetDouble(), shot->TradeMoney.m_Value / 1000.0, shot->TradeMoney.m_Value) << std::endl;
}

int main()
{
    std::cout << std::format("sizeof MDLTime={}", sizeof(MDLTime)) << std::endl;
    std::cout << std::format("sizeof 深交所 Order300192_v2固定部分长度={}", sizeof(mdl_szl2_msg::Order300192_v2)) << std::endl;
    std::cout << std::format("sizeof 深交所 Transaction300191_v2固定部分长度={}", sizeof(mdl_szl2_msg::Transaction300191_v2)) << std::endl;
    printt(t1, sizeof(t1));
    printt(t2, sizeof(t2));
    printo(o, sizeof(o));
    printo(o1, sizeof(o1));
    printo(o2, sizeof(o2));
    printo(o3, sizeof(o3));
    printt(t, sizeof(t));

    unsigned char buf[1024];
    mdl_shl2_msg::NGTSTick *shot = (mdl_shl2_msg::NGTSTick *)buf;
    std::cout << std::format("sizeof 上交所 NGTSTick固定部分长度={}, sizeof(MDLFloatT<3>)={}, sizeof(MDLDoubleT<3>)={}", sizeof(mdl_shl2_msg::NGTSTick), sizeof(MDLFloatT<3>), sizeof(MDLDoubleT<3>)) << std::endl;

    shot->Price.m_Value = 464617560; // 464617.56
    shot->TradeMoney.m_Value = 558001341;
    printshot(buf);

    shot->Price.m_Value = 464617577; // 464617.56
    shot->TradeMoney.m_Value = 558000001;
    printshot(buf);

    shot->Price.m_Value = 464617578; // 464617.56
    shot->TradeMoney.m_Value = 558000011;
    printshot(buf);

    shot->Price.m_Value = 464617579; // 464617.6 !!! 这个跳变比较大
    printshot(buf);

    shot->Price.m_Value = 464617570; // 464617.56 !!!
    printshot(buf);

    shot->Price.m_Value = 464617580; // 464617.6 !!!
    printshot(buf);

    shot->Price.m_Value = 464617590; // 464617.6 !!!
    printshot(buf);

    std::cout << std::format("float 464617.6 - 464617.56 = {}, double = {}", 464617.6f - 464617.56f, 464617.6 - 464617.56) << std::endl;

    shot->Price.m_Value = 64617577; // 64617.58
    printshot(buf);

    shot->Price.m_Value = 4617577; // 4617.577 这个有效数字就可以比较精确了，不过价格一般不会这么大，所以够用了
    printshot(buf);

    mdl_szl2_msg::CombinedTick *ct = (mdl_szl2_msg::CombinedTick *)buf;
    std::cout << std::format("sizeof 深交所 CombinedTick固定部分长度={}, sizeof(MDLTime)={}, sizeof(MDLDoubleT<4>)={}", sizeof(mdl_szl2_msg::CombinedTick), sizeof(MDLTime), sizeof(MDLDoubleT<4>)) << std::endl;

    ct->Price.m_Value = 464617560; // 46461.756
    std::cout << std::format("深交所 CombinedTick price={}({})", ct->Price.GetDouble(), ct->Price.m_Value) << std::endl;
    return 0;
}
