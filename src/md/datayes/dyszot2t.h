#pragma once
#include <cstdint>
#include <string>
#include <string_view>
#include <cstring>
#include <set>
#include <map>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <format>
#include <iostream>

#include "mdl_api.h"
#include "mdl_szl2_msg.h"

using namespace datayes::mdl;

inline void bad_thing(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cerr << std::format("[{}][BAD][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
    exit(1);
}

// 用于主 put_instruction() 中，用于生成错误信息，exchtime 与 order_type 等参数来自主 put_instruction()
#define BAD_THING(msg) bad_thing(__func__, __LINE__, std::format("<{}> Type={}, price={}, buy_order={}, sell_order={}, qty={}, bid1={}, ask1={}, total_sell_qty_={}, total_buy_qty_={}, 已处理 {} 条记录后发生错误: {}", uint32_t2HHMMSSmmm_view(exchtime), SZOrderType2Str(order_type), order_price, buy_order, sell_order, order_qty, bid1_, ask1_, total_sell_qty_, total_buy_qty_, total_inst_, msg))

// 用于类内部其它函数中
#define BAD_THING_(msg) bad_thing(__func__, __LINE__, std::format("bid1={}, ask1={}, total_sell_qty_={}, total_buy_qty_={}, 已处理 {} 条记录后发生错误: {}", bid1_, ask1_, total_sell_qty_, total_buy_qty_, total_inst_, msg))

// 用于任何位置
#define BAD_THING2(msg) bad_thing(__func__, __LINE__, std::format("发生错误: {}", msg))

// 用于 Szot2t 类内部
#define DEBUG_LOG(msg) log(LOG_LEVEL::DEBUG, std::format("[{}:{}] 处理消息 {}: {}", __func__, __LINE__, total_inst_, msg))

// 可用于 Szot2t 类外部，由全局变量 sys_log_level 控制是否显示，最后一个字母 S 表示这是类里的静态函数
#define DEBUG_LOGS(msg) Szot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("[{}:{}] {}", __func__, __LINE__, msg))

// 把 HH:MM:SS.mmm 格式的时间戳转换为 uint32_t 类型的 HHMMSSmmm
inline uint32_t HHMMSSmmm2uint32_t(const char *t)
{
    return std::stoi(std::string(t, 0, 2)) * 10000000 + std::stoi(std::string(t, 3, 2)) * 100000 + std::stoi(std::string(t, 6, 2)) * 1000 + std::stoi(std::string(t, 9, 3));
}

inline uint32_t HHMMSSmmm2uint32_t(const std::string_view &t)
{
    return std::stoi(std::string(t, 0, 2)) * 10000000 + std::stoi(std::string(t, 3, 2)) * 100000 + std::stoi(std::string(t, 6, 2)) * 1000 + std::stoi(std::string(t, 9, 3));
}

// 把 uint32_t 类型的 HHMMSSmmm 转换为 HH:MM:SS.mmm 格式的时间戳
inline std::string uint32_t2HHMMSSmmm(uint32_t t)
{
    return std::format("{:02d}:{:02d}:{:02d}.{:03d}", t / 10000000, (t % 10000000) / 100000, (t % 100000) / 1000, t % 1000);
}

inline std::string_view uint32_t2HHMMSSmmm_view(uint32_t t)
{
    static char buf[24];
    std::format_to(std::begin(buf), "{:02d}:{:02d}:{:02d}.{:03d}", t / 10000000, (t % 10000000) / 100000, (t % 100000) / 1000, t % 1000);
    return std::string_view(buf);
}

// 经小江 Python 程序整理后的深交所合并逐笔信息
struct order_raw_sz
{
    std::string recvTime;  // 接收程序收到逐笔数据的时间戳
    int channelNo;  // 通道号
    int64_t applSeqNum; // 逐笔序号，每个 channel 从 1 开始单独连续编号
    int streamID;  // 行情类别，11 现货（股票，基金，债券等）集中竞价交易逐笔行情，21 质押式回购交易逐笔行情，41 期权集中竞价交易逐笔行情
    int64_t bidApplSeqNum;  // 买方委托索引，从 1 开始计数，0 表示无对应委托
    int64_t offerApplSeqNum;  // 卖方委托索引，从 1 开始计数，0 表示无对应委托
    int64_t securityID;  // 证券代码
    int securityIDSource; // 证券代码源，101 上交所，102 深交所，106 北交所，等等，见华泰文档
    double lastPx;         // 成交价格，以元为单位，对于撤单为 0.0
    int64_t lastQty;    // 成交、撤单数量，股
    int execType;  // 成交类别，52 撤单，70 正常成交
    std::string transactionTime; // 深交所原始逐笔数据中的时间戳，据观察精确到百分之一秒，格式为 HH:MM:SS.mmm
    int64_t delay;               // 接收程序计算的时延，单位微秒
    double price;     // 委托价格，以元为单位
    int64_t orderQty;   // 委托数量，股
    int side;          // 买卖方向，49 买，50 卖，出借 70，借入 71
    int orderType;  // 订单类别，49 市价委托，50 限价委托，85 本方最优
    uint32_t tltime;  // 通联行情时间，HHMMSSmmm
};

// 深交所市场状态，根据交易所时间戳来决定
enum class SZMarketState
{
    START, // 启动，初始阶段
    OCALL, // 开盘集合竞价委托，9:15-9:25前
    OCALL_PUB, // 开盘集合竞价发布成交，9:25整
    TRADE, // 连续自动撮合，9:30-11:30, 13:00-14:57前
    CCALL, // 收盘集合竞价委托，14:57-15:00前
    CCALL_PUB  // 收盘集合竞价发布成交，15:00整
};

enum class SZOrderType
{
    Market = 49,           // 新增市价委托订单
    Limit = 50,            // 新增限价委托订单
    Cancel = 52,           // 撤销委托订单，即撤单
    Transaction = 70,      // 成交
    Best = 85,             // 新增本方最优委托订单
    UNKOWN = 100           // 未知，不应该出现
};

// 一般用于日志打印
inline std::string_view SZOrderType2Str(SZOrderType t)
{
    switch (t)
    {
    case SZOrderType::Market:
        return "Market";
    case SZOrderType::Limit:
        return "Limit";
    case SZOrderType::Best:
        return "Best";
    case SZOrderType::Cancel:
        return "Cancel";
    case SZOrderType::Transaction:
        return "Transaction";
    default:
        return "UNKOWN";
    }
}

inline SZOrderType int2SZOrderType(int s)
{
    if (s == 49)
        return SZOrderType::Market;
    if (s == 50)
        return SZOrderType::Limit;
    if (s == 85)
        return SZOrderType::Best;
    if (s == 52)
        return SZOrderType::Cancel;
    if (s == 70)
        return SZOrderType::Transaction;
    return SZOrderType::UNKOWN;
}

// 买卖方向
enum class TradeType
{
    BUY = 0,
    SELL = 1
};

enum class TickType
{
    ORDER = 0,  // 新增委托订单
    CANCEL = 1,  // 撤销委托订单，即撤单
    TRANSACTION = 2,  // 成交
};

#define TICK_TYPE_ORDER 0
#define TICK_TYPE_CANCEL 1
#define TICK_TYPE_TRANSACTION 2

#define TICK_DIRECT_BUY 1
#define TICK_DIRECT_SELL 2
#define TICK_DIRECT_AUCTION 0

// 连续竞价期间，每个委托、成交或撤单，都对应生成一个Tick_info
// 开盘集合竞价结束及收盘集合竞价结束时各生成一个Tick_info
struct Tick_info
{
    char time[24];             // 交易所行情时间，如 "2025-08-04 14:56:59.470", 长度正好为 23，现在只用时间部分
    uint32_t exchtime;         // 交易所行情时间，HHMMSSmmm
    int8_t tick_type;          // 消息类型：委托 TICK_TYPE_ORDER 0、撤单 TICK_TYPE_CANCEL 1、成交 TICK_TYPE_TRANSACTION 2
    int8_t tick_direc;         // 消息方向：买 TICK_DIRECT_BUY 1、卖 TICK_DIRECT_SELL 2、集合竞价 TICK_DIRECT_AUCTION 0
    int bid20prices[20];       // 20档买价，单位厘
    int64_t bid20qty[20];      // 20档买量
    int ask20prices[20];       // 20档卖价，单位厘
    int64_t ask20qty[20];      // 20档卖量
    int bid20num[20];          // 20档买单笔数
    int ask20num[20];          // 20档卖单笔数
    int64_t bid20lgqty[20];    // 20档买中大单买量
    int64_t ask20lgqty[20];    // 20档卖中大单卖量
    std::map<int, int> orderbook_tier; // 价格档位信息，key 是相对于前收价的涨跌千分点（如 -100 表示 -10%），value 是该价位的挂单量，构造函数中会把所有可能千分点的挂单量都初始化为0，key 数量是不变的，全量输出
    int prev_close;            // 昨收价，单位厘
    int latest;                // 最新价，单位厘
    int high;                  // 最高价，单位厘
    int low;                   // 最低价，单位厘
    int open;                  // 开盘价，单位厘
    int trade_num;             // 累计成交笔数
    int64_t trade_qty;         // 累计成交量
    int64_t trade_money;       // 累计成交金额，单位厘
    int mid_price;             // 买一、卖一中间价，单位厘
    int spread;                // 卖一、买一价差，单位厘
    int w_avg_bid_price;       // 加权平均委买价，单位厘
    int w_avg_ask_price;       // 加权平均委卖价，单位厘
    int64_t tick_vol;          // 连续竞价期间为委托、撤单或成交量，开盘前集合竞价与收盘集合竞价为期间合并的成交量
    double tick_money;         // 连续竞价期间为委托、撤单或成交金额，开盘前集合竞价与收盘集合竞价为期间合并的成交金额，单位元
    int order_aggr;
    int64_t total_buy_qty;     // 当前总有效申买量
    int64_t total_sell_qty;    // 当前总有效申卖量
    int64_t sm_order_cum_vol;  // 5万以下小单的净流入金额，单位元
    int64_t mid_order_cum_vol; // 5万至30万中单的净流入金额，单位元
    int64_t lg_order_cum_vol;  // 30万至100万大单的净流入金额，单位元
    int64_t slg_order_cum_vol; // 100万以上特大单的净流入金额，单位元
    int64_t buy_order_index;   // 当前买单编号
    int64_t sell_order_index;  // 当前卖单编号

    Tick_info(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier)
    {
        std::memcpy(time, t, 23);  // 如 "2025-08-04 14:56:59.470", 长度正好为 23
        time[23] = 0;
        this->exchtime = exchtime;
        this->tick_type = tick_type;
        this->tick_direc = tick_direc;
        this->orderbook_tier = tier;
    }
    Tick_info() {}
};

void print_tick_info(Tick_info &tick_info);

enum class LOG_LEVEL
{
    DEBUG = 0,
    INFO,
    WARNING,
    ERROR
};

inline std::string_view LOG_LEVEL2Str(LOG_LEVEL level)
{
    switch (level)
    {
    case LOG_LEVEL::DEBUG:
        return "DEBUG";
    case LOG_LEVEL::INFO:
        return "INFO";
    case LOG_LEVEL::WARNING:
        return "WARNING";
    case LOG_LEVEL::ERROR:
        return "ERROR";
    default:
        return "UNKNOWN";
    }
}

// 价位上的订单信息
struct price_order
{
    std::map<int64_t, int64_t> orders[2];  // 买单和卖单的[委托单号, 数量]映射，对于深交所我们用 map 而非 unordered_map，是因为说不定更有利于自行计算成交
    int64_t total_qty[2];  // 买单和卖单的总数量，输出20档
};

class Szot2t
{
public:
    Szot2t(int upper_limit, int lower_limit, int prev_close, int increment, LOG_LEVEL log_level = LOG_LEVEL::WARNING);
    Tick_info *put_instruction(const order_raw_sz &order);
    Tick_info *put_instruction(uint32_t exchtime, SZOrderType order_type, int order_price, int64_t buy_order, int64_t sell_order, int64_t order_qty, uint32_t dy_time, int64_t msg_no);
    Tick_info *put_instruction(const mdl_szl2_msg::CombinedTick &order, int dy_time, int64_t msg_no);
    Tick_info *gen_close_tick_info(); // 深交所没有状态订单，无法确定收盘集合竞价成交发布何时结束，所以需要外部调用此函数来生成收盘集合竞价结束时的 tick 信息
    void print_tier_dict(void);
    void print_orderbook_tier(void);
    void print_orderbook(void);
    void print_orderbook(int price);
    void print_orderpool(void);
    void print_tick_info_snapshots(void);
    void print_money_balance(void);
    void print_vol_stat(void);
    void print_vol_lg_stat(void);
    void log(LOG_LEVEL level, std::string msg);
    static void log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg);
private:
    int upper_limit_;  // 涨停价，单位厘
    int lower_limit_;  // 跌停价，单位厘
    int prev_close_;   // 昨收价，单位厘
    double mid_threshold_;  // 以昨收价计算，5万元订单对应的股数
    double lg_threshold_;   // 以昨收价计算，30万元订单对应的股数
    double slg_threshold_;  // 以昨收价计算，100万元订单对应的股数
    float disc_coef_; // 从跌停 -10% 到涨停 10% 之间，一共 20 个百分点，根据前收盘价计算分档粒度
    int increment_;  // 最小价格变动单位，ETF 为 0.001 元，一般股票为 0.01 元，要乘以 1000，所以分别是 1 和 10
    bool whether_snapshot_ = false;  // 是否计算行情快照
    int64_t total_buy_qty_ = 0;  // 当前买单总数量
    int64_t total_sell_qty_ = 0;  // 当前卖单总数量
    std::map<int, int> tier_dict_;  // key 是所有可能价格，value 是相对于前收价的涨跌千分点，构造函数中根据涨跌停价、是否ETF来计算，key 数量是不变的
    std::map<int, int> orderbook_tier_;  // key 是相对于前收价的涨跌千分点，value 是该价位的挂单量，构造函数中会把所有可能千分点的值都初始化为0，key 数量是不变的，全量输出
                                         // XXX 这里没有买卖分开，那么：1、看不出挂单是买的还是卖的，2、在当前最优价附近可能出现买卖同时统计的现象
    std::map<int, struct price_order> orderbook_;  // key 是以厘为单位的报价，value 是买单和卖单的[委托单号, 数量]映射以及总数量，key 数量是动态增减的
                                                   // 连续竞价期间，订单簿中只有待成交的被动订单（无法成交的限价委托立即进入订单簿变成被动订单）
    std::map<int, struct price_order>::iterator it_bid1_;  // 买一在 orderbook_ 中的位置
    std::map<int, struct price_order>::iterator it_ask1_;  // 卖一在 orderbook_ 中的位置
    std::unordered_map<int64_t, int64_t [2]> orderpool_;  // 订单池，key 是委托单编号，value 是委托单的报价（厘）和数量，这里没有买或卖的信息，key 数量是动态增减的

    // 按委托数量分的订单池，只看委托数量，不看委托价格，所以在委托消息中即插入
    std::set<int64_t> orderpool_sm_;  // 小单池，内容是委托单编号
    std::set<int64_t> orderpool_mid_;  // 中单池，内容是委托单编号
    std::set<int64_t> orderpool_lg_;  // 大单池，内容是委托单编号
    std::set<int64_t> orderpool_slg_;  // 特大单池，内容是委托单编号
    std::map<int, int64_t [2]> orderbook_vol_lg_;  // 报价为索引的30万以上大单（含特大单）总量，key 是以厘为单位的报价，value 是大单中买单、卖单各自的当前有效总数量，
                                                   // key 数量是动态增减的，输出20档，输出档位与 orderbook_ 一致，空值输出为 0
    SZMarketState market_state_ = SZMarketState::START; // 市场状态，适用于深市
    std::vector<Tick_info> tick_info_snapshots_;  // 行情信息快照序列
    double sm_order_cum_vol_ = 0.0;  // 5万以下小单的净流入金额，单位元
    double mid_order_cum_vol_ = 0.0;  // 5万至30万中单的净流入金额，单位元
    double lg_order_cum_vol_ = 0.0;  // 30万至100万大单的净流入金额，单位元
    double slg_order_cum_vol_ = 0.0;  // 100万以上特大单的净流入金额，单位元
    int bid1_ = 0;  // 当前买一价
    int ask1_ = 0;  // 当前卖一价

    // 成交统计
    int64_t trade_qty_ = 0;  // 累计成交量
    int64_t trade_money_ = 0;  // 累计成交金额，单位厘
    int trade_num_ = 0;  // 累计成交笔数

    // 深交所连续竞价阶段，任一时刻最多只会有一个当前主动订单，主动买或者主动卖
    int64_t pending_order_ = 0;  // 深交所连续竞价阶段，当前主动订单编号
    int64_t pending_qty_apply_ = 0;  // 深交所连续竞价阶段，当前主动订单的原始申报数量
    int64_t pending_qty_ = 0;  // 深交所连续竞价阶段，当前主动订单累积成交数量
    int64_t pending_avail_qty_ = 0;  // 深交所连续竞价阶段，当前主动限价订单当前可用数量，委托时即已计算出来，它小于申报数量则意味着有剩余要转为被动订单进入
                                     // 订单池和主订单薄。成交时更新，对于无法完全成交的限价单，当此变量减到 0 时限价单将转为被动订单进入订单池
    bool pending_is_market_ = false;  // 当前 pending 的主动单是否是市价单
    TradeType pending_direction_ = TradeType::BUY;  // 当前主动订单方向
    int pending_price_ = 0;  // 当前主动限价单的报价，对于市价单无意义
    int pending_latest_price_ = 0;  // 当前主动订单最新成交价
    int pending_price_num_ = 0;  // 当前主动订单已成交的价格数量，可用于判断市价单类型：对方最优最多只能有一个成交价，5档剩撤最多有5个，全档剩撤可以超过5个
    bool pending_should_tick_after_ate1_ = false;  // 当前主动订单是否应该在吃完对方最优后生成 tick 信息，在市价单且对方所有最优市价单无法完全满足时设为 true
    bool pending_expect_cancel_ = false;  // 收到市价单时，如果对手方无订单，则设为 true
    int latest_ = 0;     // 最新成交价
    int latest_buy_price_ = 0;  // 最新成交买单报价
    int latest_sell_price_ = 0; // 最新成交卖单报价
    SZOrderType latest_order_type_ = SZOrderType::UNKOWN;  // 连续竞价阶段的最新消息类型
    LOG_LEVEL log_level_;
    int total_inst_ = 0;   // 已处理消息数
    int64_t buy_order_index_ = 0;  // 买单编号
    int64_t sell_order_index_ = 0; // 卖单编号
    Tick_info tick_info_;

    void export_tick_info_(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier, Tick_info &tick_info); // 导出一些不同情形下（委托、撤单、成交、状态变化等）共同的 tick 信息
    void calculate_bid1_ask1_(void);  // 开盘集合竞价成交发布结束，计算买一卖一，并生成 tick 信息
    void insert_limit_order_(int order_price, int64_t order_no, int64_t order_qty, TradeType direction);  // 新增限价委托订单，不进行盘口计算
    void cancel_order_(int64_t order_no, int64_t cancel_qty, TradeType direction, bool do_bid1_ask1);  // 撤销委托订单
    void auction_transaction_(int64_t buy_order, int64_t sell_order, int64_t trade_qty, int64_t trade_price);  // 处理集合竞价期间的成交
    void close_pending_order_();  // 处理当前未完结的主动对方最优市价订单
    void print_orderbook_price_(int price); // 打印指定价位的详细订单信息，包括买和卖单
    void print_orderbook_price_(int price, TradeType direction); // 打印指定价位的买或卖单详细订单信息
    void print_orderbook_price_(std::map<int, struct price_order>::iterator it); // 打印指定价位的详细订单信息，包括买和卖单
    void print_orderbook_price_(std::map<int, struct price_order>::iterator it, TradeType direction); // 打印指定价位的买或卖单详细订单信息
    void print_orderbook_price_(std::map<int, struct price_order>::iterator it, TradeType direction, int levels); // 打印指定价位开始的指定档位数的买或卖单详细订单信息
};
