# Build

On Ubuntu 24.04, install dependencies with:
```
sudo apt update
sudo apt install libzmq3-dev
sudo apt install libhiredis-dev
sudo apt install libprotobuf-dev
sudo apt install -y -V ca-certificates lsb-release wget
wget https://packages.apache.org/artifactory/arrow/$(lsb_release --id --short | tr 'A-Z' 'a-z')/apache-arrow-apt-source-latest-$(lsb_release --codename --short).deb
sudo apt install -y -V ./apache-arrow-apt-source-latest-$(lsb_release --codename --short).deb
sudo apt update
sudo apt install libarrow-dev
sudo apt install libparquet-dev
```
其中 Apache Arrow 和 Parquet 开发库是从 Apache Arrow 仓库安装的，参见 https://arrow.apache.org/install/

Then you can make the executable with:
```
mkdir -p build
cd build
cmake ..
make -j12
```

Install with:
```
sudo make install
```

Uninstall with:
```
sudo make uninstall
```

Executable will be installed into /usr/local/sbin.

lyproto.quota.pb.h 和 lyproto.quota.pb.cc 由下列命令从 lyproto.quota.proto 生成:
```
protoc --cpp_out=. lyproto.quota.proto
```

其中 protoc 版本为 Ubuntu 24 的 3.21.12 版。

修改过 lyproto.quota.proto 后，还需要重新生成 quota_pb2.py，命令为:
```
protoc --python_out=. lyproto.quota.proto
mv lyproto/quota_pb2.py test/quota_pb2.py
rm -rf lyproto
```

# Run
运行机上要安装 ZMQ、Protobuf 运行库，不必安装 HiRedis、Arrow、Parquet 运行库（因为它们是静态链接的，但可能要安装 Arrow、Parquet 库的依赖库，也许更麻烦），另外必须安装通联的 MDL 动态库 libmdl_api.so，版本为 2.13.231。

运行程序时，如果需要指定配置文件，可以使用 -c 选项，例如:
```
dytk -c dytk.yaml
```

如果不指定配置文件，程序将使用各种缺省值。

运行
```
dytk -h
dyot -h
```

to see help information.

# 说明
It is recommended to use YAML configuration file to specify parameters, rather than command line arguments.

NOTE:
- YAML configuration file should be of UTF-8 encoding, otherwise it may fail to load.
- dytk and getMktLimit.py share the same YAML configuration file.
- DNS 必须正常工作，因为程序中写死了通联行情服务器的域名，没有使用 IP 地址

## 关于涨跌停价
- ZMQ 发布的行情信息要包含涨跌停价
- 沪市 L2 行情 4.4 中没有涨跌停价信息。沪市的静态信息 4.23 中带有涨跌停信息，且根据实测，其更新时间应该为前一日收盘后，因此在程序中会取静态信息 4.23 并以其中的涨跌停价构造一个 map，供开盘后写行情数据时使用
- 深市 L2 行情 6.28 中直接带有涨跌停价信息
- 另外，涨跌停价信息可通过 Web API 获取，参见 getMktLimit.py

## 关于静态信息中的除权、除息信息
- 沪市静态信息 4.23 中有所谓的除权比例、除息金额，应该是最近一次的信息，但并没有除权、除息的日期信息，所以是不完整的无效信息
- 深市静态信息 6.51 中带有除权、除息标志，但没有除权比例、除息金额和日期等信息，所以也不是完整的有效信息
