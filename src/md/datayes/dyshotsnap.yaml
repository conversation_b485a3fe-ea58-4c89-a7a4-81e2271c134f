# 上交所逐笔转极速行情程序 dyshotsnap 配置文件
# 本配置文件的各项配置都可被命令行参数覆盖

# 上交所逐笔数据文件，仅包含一只证券的 parquet 文件
# 本配置优先于从 ZeroMQ 读取逐笔信息的配置
# 如果不想从 parquet 文件中读取逐笔数据，请将此项设置为 null，或不设置
# 如果文件名是纯数字或 YAML 关键字，请加引号
parquet: ../orderbook/600900.parquet

# 日志文件前缀，如果不想保存日志文件，请将此项设置为 null，或不设置
# 程序日志文件将保存为 logfp + ".log"
# 支持日期格式符，如 %Y-%m-%d
# 请参考 https://en.cppreference.com/w/cpp/io/manip/put_time
# 如果日志文件前缀是纯数字或 YAML 关键字，请加引号
logfp: /b/dymd/%Y-%m-%d/dyot

# Redis 服务器地址，用于获取涨跌停价、昨收价、最小价格变动单位
redis_host: ************

# Redis 端口，用于获取涨跌停价、昨收价、最小价格变动单位
redis_port: 6379

# Redis key，用于获取涨跌停价、昨收价、最小价格变动单位
redis_key: STKLST2

# 发布极速行情快照的 ZeroMQ 端口
zmq_port_snap: 30002
# zmq_port_snap: 0

# 接收逐笔数据的 ZeroMQ 服务器地址
zmq_host_ot: 127.0.0.1

# 接收逐笔数据的 ZeroMQ 端口
zmq_port_ot: 30001

# 是否在后台运行，缺省为 false
background: false

# 涨停价，单位厘
upper_limit: 30790

# 跌停价，单位厘
lower_limit: 25190

# 昨收价，单位厘
prev_close: 27990

# 最小价格变动单位，单位厘
increment: 10
