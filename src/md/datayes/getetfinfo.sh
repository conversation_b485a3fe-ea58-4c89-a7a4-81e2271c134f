#!/bin/bash
# 通过通联接口获取ETF成份股信息，CSV 格式
# API 文档见 https://mall.datayes.com/datapreview/140

token_api="8ffcdc27aa291300241687fce22121d7dfdb8d2a225378b23e9ef7141d96b168"
day=`date +%Y%m%d`

if [ -z "$1" ]; then
	etfs="159633 159845 159901 159902 159915 159919 159922 159934 159937 159949 159952 159992 159995 510050 510100 510180 510210 510300 510310 510330 510500 510880 511010 511880 511990 512000 512010 512050 512070 512100 512760 512880 515050 518800 518880 588000 588050 588080 588200 588220"
else
	etfs=$1
fi

# 构建API URL
# url="https://api.datayes.com/data/v1/api/fund/getFundETFCons.json?field=consExchangeCD consTicker&ticker=${etf}&beginDate=${day}&endDate=${day}"
# url="https://api.datayes.com/data/v1/api/fund/getFundETFCons.csv?field=consExchangeCD,consTicker&ticker=${etf}&beginDate=${day}&endDate=${day}"
#url="https://api.datayes.com/data/v1/api/fund/getFundETFCons.csv?field=&ticker=${etf}&beginDate=${day}&endDate=${day}"

# 创建头信息,传入token
header_auth="Authorization: Bearer ${token_api}"
header_accept="Accept-Encoding: identity"

# 访问api,获取数据
# echo 正在获取 ${day} 的ETF ${etf} 成份股数据 ...
for etf in $etfs; do
	url="https://api.datayes.com/data/v1/api/fund/getFundETFPRList.csv?field=&ticker=${etf}&beginDate=${day}&endDate=${day}"
	wget --header "$header_auth" --header "$header_accept" -O - "$url"
done
