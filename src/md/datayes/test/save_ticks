#!/bin/bash
# 保存 4 种行情的主要信息到 CSV 文件
# 保存优选指数行情主要信息到 CSV 文件

d=`date +%Y%m%d`

nohup ./save_tick.py -f tick_htl1_${d} -z tcp://168.36.1.173:9869 > save_htl1.log 2>&1 &
nohup ./save_tick.py -f tick_gxsz_${d} -z tcp://168.36.1.52:9869 > save_gxsz.log 2>&1 &
nohup ./save_tick.py -f tick_gxsh1_${d} -z tcp://168.36.1.115:9869 > save_gxsh1.log 2>&1 &
nohup ./save_tick.py -f tick_gxsh2_${d} -z tcp://168.36.1.115:9870 > save_gxsh2.log 2>&1 &
nohup ./save_tick.py -f tick_dyot_${d} -z tcp://168.36.1.67:30002 > save_dyot.log 2>&1 &
nohup ./save_tick.py -f tick_dyl2_${d} -z tcp://168.36.1.67:9869 > save_dyl2.log 2>&1 &
nohup ./save_tick.py -f tick_super_${d} -z tcp://127.0.0.1:30004 > save_super.log 2>&1 &
