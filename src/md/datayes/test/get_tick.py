#!/usr/bin/python3
# 显示 ZMQ 行情，如：
# python3 get_tick.py -z ************:9869 -s S600519
# python3 get_tick.py -z ************:9869,localhost:30002 -s S600519
# python3 get_tick.py -z localhost:30004 -s S

import argparse
import zmq
from datetime import datetime
import quota_pb2 as LYPBQuota

def pb_parse_s(msg:bytes, recvtime):
    msglen = int(msg[7:15])
    pbmsg = msg[15: 15 + msglen]
    o = LYPBQuota.MarketData()
    if not o.ParseFromString(pbmsg):
        print(f"Parse error: {msg}")
        return
    print(f'<{recvtime}> {len(msg)} bytes, {msg[1:8]}')
    #print(o)
    #print()

parser = argparse.ArgumentParser(description='显示 ZMQ 行情')
parser.add_argument('-z', '--zmq', type=str, default='************:9869', help='ZeroMQ 服务器地址，如 localhost:30002，可用逗号分隔多个地址')
parser.add_argument('-s', '--sub', type=str, default='', help='订阅行情主题，如 S600519，I30')

args = parser.parse_args()

context = zmq.Context()
socket = context.socket(zmq.SUB)
zmqs = args.zmq.split(',')
for z in zmqs:
    socket.connect('tcp://' + z)
socket.setsockopt_string(zmq.SUBSCRIBE, args.sub)

while True:
    content = socket.recv()
    now = datetime.now()
    pb_parse_s(content, now.hour * 10000000 + now.minute * 100000 + now.second * 1000 + now.microsecond // 1000)

