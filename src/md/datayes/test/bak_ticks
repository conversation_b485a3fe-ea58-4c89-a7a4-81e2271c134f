#!/bin/bash
# 把 4 种行情主要信息 CSV 文件 转移到 23 上
# 把优先选指数行情主要信息 CSV 文件转移到 23 上

pkill save_tick.py

d=`date +%Y%m%d`
bak_dir=/B/ticks/`date +%Y-%m-%d`/

mkdir -p ${bak_dir}
mv tick_htl1_${d}.csv ${bak_dir}/tick_htl1.csv
mv tick_htl1_${d}_i.csv ${bak_dir}/tick_htl1_i.csv
mv tick_gxsz_${d}.csv ${bak_dir}/tick_gxsz.csv
mv tick_gxsz_${d}_i.csv ${bak_dir}/tick_gxsz_i.csv
mv tick_gxsh1_${d}.csv ${bak_dir}/tick_gxsh1.csv
rm tick_gxsh1_${d}_i.csv
mv tick_gxsh2_${d}.csv ${bak_dir}/tick_gxsh2.csv
mv tick_gxsh2_${d}_i.csv ${bak_dir}/tick_gxsh2_i.csv
mv tick_dyot_${d}.csv ${bak_dir}/tick_dyot.csv
rm tick_dyot_${d}_i.csv
mv tick_dyl2_${d}.csv ${bak_dir}/tick_dyl2.csv
mv tick_dyl2_${d}_i.csv ${bak_dir}/tick_dyl2_i.csv
mv tick_super_${d}_i.csv ${bak_dir}/tick_super_i.csv
mv tick_super_${d}.csv ${bak_dir}/tick_super.csv
