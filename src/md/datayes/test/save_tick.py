#!/usr/bin/python3

import argparse
import zmq
from datetime import datetime
import quota_pb2 as LYPBQuota

def pb_parse_s(msg:bytes, f, fi, recvtime):
    msglen = int(msg[7:15])
    pbmsg = msg[15: 15 + msglen]
    o = LYPBQuota.MarketData()
    if not o.ParseFromString(pbmsg):
        print(f"Parse error: {msg}")
        return
    if msg[0] == ord('S'):
        if len(o.BP) == 0 or len(o.SP) == 0:
            f.write(f"{o.stkId},{o.Category},{o.ExchId},{o.ExchTime},{o.RcvSvrTime},{recvtime},{o.PubSvrTime},{o.Latest},{o.Knock},0,0,0,0,{o.Volume},{o.WiDNum},{o.TotalBA},{o.TotalSA},{o.MdSource}\n")
        else:
            f.write(f"{o.stkId},{o.Category},{o.ExchId},{o.ExchTime},{o.RcvSvrTime},{recvtime},{o.PubSvrTime},{o.Latest},{o.Knock},{o.BP[0]},{o.BA[0]},{o.SP[0]},{o.SA[0]},{o.Volume},{o.WiDNum},{o.TotalBA},{o.TotalSA},{o.MdSource}\n")
    elif msg[0] == ord('I'):
        fi.write(f"{o.stkId},{o.Category},{o.ExchId},{o.ExchTime},{o.RcvSvrTime},{recvtime},{o.PubSvrTime},{o.Latest},{o.Knock},{o.Volume},{o.WiDNum},{o.TotalBA},{o.TotalSA},{o.MdSource}\n")
    else:
        print(f"Unknown message type: {msg[0]}")

parser = argparse.ArgumentParser(description='保存行情时间戳到文件')
parser.add_argument('-f', '--file', type=str, default='tick', help='快照行情保存文件名前缀，指数与股票基金行情分别保存到两个文件')
parser.add_argument('-z', '--zmq', type=str, default='tcp://************:9869', help='ZeroMQ 服务器地址，如 tcp://localhost:30002')
parser.add_argument('-s', '--sub', type=str, default='', help='订阅行情主题，如 S600519，I3010*')

args = parser.parse_args()
f = open(args.file + ".csv", "w")
f.write("stkId,Category,ExchId,ExchTime,RcvSvrTime,RecvTime,PubSvrTime,Latest,Knock,BP1,BA1,SP1,SA1,Volume,WiDNum,TotalBA,TotalSA,MdSource\n")
fi = open(args.file + "_i.csv", "w")
fi.write("stkId,Category,ExchId,ExchTime,RcvSvrTime,RecvTime,PubSvrTime,Latest,Knock,Volume,WiDNum,TotalBA,TotalSA,MdSource\n")
context = zmq.Context()
socket = context.socket(zmq.SUB)
#socket.connect("tcp://localhost:30002")
socket.connect(args.zmq)
#socket.connect("tcp://***********:9870")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S510050")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S159922")
socket.setsockopt_string(zmq.SUBSCRIBE, args.sub)
#socket.setsockopt_string(zmq.SUBSCRIBE, "S600900")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399001")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399001")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399106")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S600519")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S002520")

while True:
    content = socket.recv()
    now = datetime.now()
    pb_parse_s(content, f, fi, now.hour * 10000000 + now.minute * 100000 + now.second * 1000 + now.microsecond // 1000)

