#!/bin/bash
#
# 给定日期与证券代码，通过保存的 CVS 数据获取其当日的涨跌停价和前收价，通过 Redis 获取其最小价格变动单位

if [ $# -lt 2 ]; then
        echo "Usage: getp YYYYMMDD|YYMMDD|MMDD|MDD|DD|D SecID"
        exit
fi

datestr=$1
datel=${#datestr}
if [ $datel -eq 8 ]; then
	y=${datestr:0:4}
	m=${datestr:4:2}
	d=${datestr:6:2}
elif [ $datel -eq 6 ]; then
	y=20${datestr:0:2}
	m=${datestr:2:2}
	d=${datestr:4:2}
elif [ $datel -eq 4 ]; then
	y=`date +%Y`
	m=${datestr:0:2}
	d=${datestr:2:2}
elif [ $datel -eq 3 ]; then
	y=`date +%Y`
	m=0${datestr:0:1}
	d=${datestr:1:2}
elif [ $datel -eq 2 ]; then
	y=`date +%Y`
	m=`date +%m`
	d=${datestr}
elif [ $datel -eq 1 ]; then
	y=`date +%Y`
	m=`date +%m`
	d=0${datestr}
fi

s=$2
s=${s:0:6}  # 证券代码只取前 6 个数字

prices=`csvgrep -c ticker -m $s /A/md/tao/${y}-${m}-${d}/limits_${y}${m}${d}.csv | csvcut -c limitUpPrice,limitDownPrice,preClosePrice| tail -1`
#echo $prices

h=`echo $prices | cut -d, -f1`
h=`echo "$h * 1000" | bc | cut -d. -f1`   # 涨停价

l=`echo $prices | cut -d, -f2`
l=`echo "$l * 1000" | bc | cut -d. -f1`   # 跌停价

c=`echo $prices | cut -d, -f3`
c=`echo "$c * 1000" | bc | cut -d. -f1`   # 昨收价

ticksize=`redis-cli -h 168.36.1.170 HGET STKLST2:${s} TickSize`
ticksize=`echo "$ticksize * 1000" | bc | cut -d. -f1`  # 最小报价变动单位，厘
echo "-u $h -w $l -v $c -i ${ticksize}"

