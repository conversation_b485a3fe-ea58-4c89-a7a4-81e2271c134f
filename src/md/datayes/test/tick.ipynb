{"cells": [{"cell_type": "markdown", "id": "b07c92a4-3744-4c99-b35e-2c3ed3da7a36", "metadata": {}, "source": ["L2 tick 数据，主要用于研究状态标志，目的是要转换成国泰的标志"]}, {"cell_type": "code", "execution_count": 1, "id": "508b375d-4a09-4937-993a-d4e511517e46", "metadata": {}, "outputs": [], "source": ["from nb_common import *"]}, {"cell_type": "code", "execution_count": 15, "id": "71b7c884-8a74-402b-ac32-2797d929c2b1", "metadata": {}, "outputs": [], "source": ["dy_sz_tick = pd.read_csv('/A/pk/dy_sz_tick_0520.csv', parse_dates = ['RecvTime', 'UpdateTime'], dtype = {'SecurityID': 'str'})"]}, {"cell_type": "code", "execution_count": 17, "id": "abf04852-554a-4321-b314-3efba2484d0c", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>UpdateTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>StreamID</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>TradingPhase</th>\n", "      <th>PreCloPrice</th>\n", "      <th>TurnNum</th>\n", "      <th>Volume</th>\n", "      <th>...</th>\n", "      <th>SellPrice8</th>\n", "      <th>SellVolume8</th>\n", "      <th>SellNumOrders8</th>\n", "      <th>SellPrice9</th>\n", "      <th>SellVolume9</th>\n", "      <th>SellNumOrders9</th>\n", "      <th>SellPrice10</th>\n", "      <th>SellVolume10</th>\n", "      <th>SellNumOrders10</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1830520</th>\n", "      <td>2025-05-20 15:06:19.559167</td>\n", "      <td>2025-05-20 15:06:18</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>301320</td>\n", "      <td>102</td>\n", "      <td>E0</td>\n", "      <td>18.76</td>\n", "      <td>38199</td>\n", "      <td>14760464</td>\n", "      <td>...</td>\n", "      <td>19.74</td>\n", "      <td>300.0</td>\n", "      <td>2.0</td>\n", "      <td>19.55</td>\n", "      <td>1300.0</td>\n", "      <td>4.0</td>\n", "      <td>19.75</td>\n", "      <td>5800.0</td>\n", "      <td>7.0</td>\n", "      <td>1559167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1830521</th>\n", "      <td>2025-05-20 15:06:19.559211</td>\n", "      <td>2025-05-20 15:06:18</td>\n", "      <td>1012</td>\n", "      <td>10</td>\n", "      <td>301603</td>\n", "      <td>102</td>\n", "      <td>E0</td>\n", "      <td>56.01</td>\n", "      <td>10658</td>\n", "      <td>3317628</td>\n", "      <td>...</td>\n", "      <td>54.99</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>54.76</td>\n", "      <td>7200.0</td>\n", "      <td>5.0</td>\n", "      <td>55.04</td>\n", "      <td>500.0</td>\n", "      <td>1.0</td>\n", "      <td>1559211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1830522</th>\n", "      <td>2025-05-20 15:06:19.559653</td>\n", "      <td>2025-05-20 15:06:18</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>301526</td>\n", "      <td>102</td>\n", "      <td>E0</td>\n", "      <td>3.91</td>\n", "      <td>15302</td>\n", "      <td>27859596</td>\n", "      <td>...</td>\n", "      <td>3.92</td>\n", "      <td>175900.0</td>\n", "      <td>18.0</td>\n", "      <td>3.74</td>\n", "      <td>1500.0</td>\n", "      <td>2.0</td>\n", "      <td>3.93</td>\n", "      <td>56800.0</td>\n", "      <td>20.0</td>\n", "      <td>1559653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1830523</th>\n", "      <td>2025-05-20 15:06:19.559693</td>\n", "      <td>2025-05-20 15:06:18</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>301428</td>\n", "      <td>102</td>\n", "      <td>E0</td>\n", "      <td>38.23</td>\n", "      <td>10600</td>\n", "      <td>3455201</td>\n", "      <td>...</td>\n", "      <td>39.13</td>\n", "      <td>2400.0</td>\n", "      <td>2.0</td>\n", "      <td>38.88</td>\n", "      <td>2200.0</td>\n", "      <td>2.0</td>\n", "      <td>39.14</td>\n", "      <td>2900.0</td>\n", "      <td>4.0</td>\n", "      <td>1559693</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1830524</th>\n", "      <td>2025-05-20 15:06:19.559728</td>\n", "      <td>2025-05-20 15:06:18</td>\n", "      <td>1015</td>\n", "      <td>10</td>\n", "      <td>301489</td>\n", "      <td>102</td>\n", "      <td>E0</td>\n", "      <td>74.90</td>\n", "      <td>40541</td>\n", "      <td>10742356</td>\n", "      <td>...</td>\n", "      <td>81.71</td>\n", "      <td>200.0</td>\n", "      <td>2.0</td>\n", "      <td>81.24</td>\n", "      <td>4300.0</td>\n", "      <td>5.0</td>\n", "      <td>81.75</td>\n", "      <td>2500.0</td>\n", "      <td>1.0</td>\n", "      <td>1559728</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 90 columns</p>\n", "</div>"], "text/plain": ["                          RecvTime          UpdateTime  ChannelNo  StreamID  \\\n", "1830520 2025-05-20 15:06:19.559167 2025-05-20 15:06:18       1013        10   \n", "1830521 2025-05-20 15:06:19.559211 2025-05-20 15:06:18       1012        10   \n", "1830522 2025-05-20 15:06:19.559653 2025-05-20 15:06:18       1013        10   \n", "1830523 2025-05-20 15:06:19.559693 2025-05-20 15:06:18       1011        10   \n", "1830524 2025-05-20 15:06:19.559728 2025-05-20 15:06:18       1015        10   \n", "\n", "        SecurityID  SecurityIDSource TradingPhase  PreCloPrice  TurnNum  \\\n", "1830520     301320               102     E0              18.76    38199   \n", "1830521     301603               102     E0              56.01    10658   \n", "1830522     301526               102     E0               3.91    15302   \n", "1830523     301428               102     E0              38.23    10600   \n", "1830524     301489               102     E0              74.90    40541   \n", "\n", "           Volume  ...  SellPrice8  SellVolume8  SellNumOrders8  SellPrice9  \\\n", "1830520  14760464  ...       19.74        300.0             2.0       19.55   \n", "1830521   3317628  ...       54.99        200.0             1.0       54.76   \n", "1830522  27859596  ...        3.92     175900.0            18.0        3.74   \n", "1830523   3455201  ...       39.13       2400.0             2.0       38.88   \n", "1830524  10742356  ...       81.71        200.0             2.0       81.24   \n", "\n", "         SellVolume9  SellNumOrders9  SellPrice10  SellVolume10  \\\n", "1830520       1300.0             4.0        19.75        5800.0   \n", "1830521       7200.0             5.0        55.04         500.0   \n", "1830522       1500.0             2.0         3.93       56800.0   \n", "1830523       2200.0             2.0        39.14        2900.0   \n", "1830524       4300.0             5.0        81.75        2500.0   \n", "\n", "         SellNumOrders10    Delay  \n", "1830520              7.0  1559167  \n", "1830521              1.0  1559211  \n", "1830522             20.0  1559653  \n", "1830523              4.0  1559693  \n", "1830524              1.0  1559728  \n", "\n", "[5 rows x 90 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.tail()"]}, {"cell_type": "code", "execution_count": 12, "id": "dc68c7f4-416c-42df-8adb-38534e47b1b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["count    1.830525e+06\n", "mean     1.025018e+06\n", "std      4.684088e+05\n", "min      2.055560e+05\n", "25%      6.316130e+05\n", "50%      1.027222e+06\n", "75%      1.423092e+06\n", "max      3.298188e+06\n", "Name: Delay, dtype: float64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.Delay.describe()"]}, {"cell_type": "code", "execution_count": 13, "id": "b3add9d3-01d3-4b88-a73e-ab8062a0ffe4", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: >"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 2880x1296 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dy_sz_tick.Delay.hist(bins=4000, figsize=(40, 18))"]}, {"cell_type": "code", "execution_count": 29, "id": "61f3ebae-4a78-4e98-830f-fa7bd56ae497", "metadata": {}, "outputs": [], "source": ["pd.options.display.max_rows=1000"]}, {"cell_type": "code", "execution_count": 35, "id": "d0c9e781-a47b-49ab-8648-52a42060cfb1", "metadata": {}, "outputs": [], "source": ["pd.options.display.max_info_columns=200"]}, {"cell_type": "code", "execution_count": 21, "id": "5b30d0f3-7b4b-455d-97c7-95d01aaeb65a", "metadata": {}, "outputs": [], "source": ["dy_sh_tick = pd.read_csv('/dev/shm/0526/dy_sh_tick.csv', parse_dates = ['RecvTime', 'UpdateTime'], dtype = {'SecurityID': 'str'})"]}, {"cell_type": "code", "execution_count": 38, "id": "ec100f20-83b9-4fe3-9bee-cfe368ec513e", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"text/plain": ["<AxesSubplot: >"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 2592x1728 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dy_sh_tick.InstruStatus.hist(figsize=(36, 24))"]}, {"cell_type": "code", "execution_count": 39, "id": "305a954d-e320-439a-aafe-068ca61ca940", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['START', 'SUSP', 'OCALL', 'TRADE', 'CCALL', 'CLOSE', 'ENDTR'],\n", "      dtype=object)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sh_tick.InstruStatus.unique()"]}, {"cell_type": "code", "execution_count": 81, "id": "b48877d4-b19f-457f-b5df-566bac3f3b78", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WarUpperPri</th>\n", "      <th>IOPV</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14931815</th>\n", "      <td>0.9379</td>\n", "      <td>0.938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14931816</th>\n", "      <td>0.9495</td>\n", "      <td>0.950</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14931817</th>\n", "      <td>1.0428</td>\n", "      <td>1.043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14931818</th>\n", "      <td>0.9725</td>\n", "      <td>0.973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14931819</th>\n", "      <td>0.7188</td>\n", "      <td>0.719</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>14931820 rows × 2 columns</p>\n", "</div>"], "text/plain": ["          WarUpperPri   IOPV\n", "0              0.0000  0.000\n", "1              0.0000  0.000\n", "2              0.0000  0.000\n", "3              0.0000  0.000\n", "4              0.0000  0.000\n", "...               ...    ...\n", "14931815       0.9379  0.938\n", "14931816       0.9495  0.950\n", "14931817       1.0428  1.043\n", "14931818       0.9725  0.973\n", "14931819       0.7188  0.719\n", "\n", "[14931820 rows x 2 columns]"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sh_tick[['WarUpper<PERSON>ri', 'IOPV']]"]}, {"cell_type": "code", "execution_count": 40, "id": "61d34c7f-18d7-499e-9847-fc52758fcd29", "metadata": {}, "outputs": [], "source": ["dy_sz_tick = pd.read_csv('/dev/shm/0526/dy_sz_tick.csv', parse_dates = ['RecvTime', 'UpdateTime'], dtype = {'SecurityID': 'str'})"]}, {"cell_type": "code", "execution_count": 42, "id": "08ff263f-7403-4fa8-8c14-b3f2a4c76ed2", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['S0      ', 'S1      ', 'O0      ', 'O1      ', 'H0      ',\n", "       'B0      ', 'B1      ', 'T0      ', 'T1      ', 'C0      ',\n", "       'C1      ', 'E0      ', 'E1      '], dtype=object)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.TradingPhase.unique()"]}, {"cell_type": "code", "execution_count": 45, "id": "1b43abe3-3214-4bf1-b317-c03bb78cb845", "metadata": {}, "outputs": [{"data": {"text/plain": ["TradingPhase\n", "T0          13041162\n", "B0            374149\n", "E0            350067\n", "O0            111252\n", "C0             60316\n", "S0             42659\n", "T1              4977\n", "B1              1995\n", "E1              1869\n", "H0               570\n", "S1               230\n", "O1               210\n", "C1                63\n", "Name: count, dtype: int64"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.TradingPhase.value_counts()"]}, {"cell_type": "code", "execution_count": 75, "id": "8d819392-a72a-460d-a155-ee32c53b4abf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>UpdateTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>StreamID</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>TradingPhase</th>\n", "      <th>PreCloPrice</th>\n", "      <th>TurnNum</th>\n", "      <th>Volume</th>\n", "      <th>Turnover</th>\n", "      <th>LastPrice</th>\n", "      <th>OpenPrice</th>\n", "      <th>HighPrice</th>\n", "      <th>LowPrice</th>\n", "      <th>DifPrice1</th>\n", "      <th>DifPrice2</th>\n", "      <th>PE1</th>\n", "      <th>PE2</th>\n", "      <th>PreCloseIOPV</th>\n", "      <th>IOPV</th>\n", "      <th>TotalOfferQty</th>\n", "      <th>WeightedAvgOfferPx</th>\n", "      <th>TotalBidQty</th>\n", "      <th>WeightedAvgBidPx</th>\n", "      <th>HighLimitPrice</th>\n", "      <th>LowLimitPrice</th>\n", "      <th>OpenInt</th>\n", "      <th>OptPremiumRatio</th>\n", "      <th>BuyPrice1</th>\n", "      <th>BuyVolume1</th>\n", "      <th>BuyNumOrders1</th>\n", "      <th>BuyPrice2</th>\n", "      <th>BuyVolume2</th>\n", "      <th>BuyNumOrders2</th>\n", "      <th>BuyPrice3</th>\n", "      <th>BuyVolume3</th>\n", "      <th>BuyNumOrders3</th>\n", "      <th>BuyPrice4</th>\n", "      <th>BuyVolume4</th>\n", "      <th>BuyNumOrders4</th>\n", "      <th>BuyPrice5</th>\n", "      <th>BuyVolume5</th>\n", "      <th>BuyNumOrders5</th>\n", "      <th>BuyPrice6</th>\n", "      <th>BuyVolume6</th>\n", "      <th>BuyNumOrders6</th>\n", "      <th>BuyPrice7</th>\n", "      <th>BuyVolume7</th>\n", "      <th>BuyNumOrders7</th>\n", "      <th>BuyPrice8</th>\n", "      <th>BuyVolume8</th>\n", "      <th>BuyNumOrders8</th>\n", "      <th>BuyPrice9</th>\n", "      <th>BuyVolume9</th>\n", "      <th>BuyNumOrders9</th>\n", "      <th>BuyPrice10</th>\n", "      <th>BuyVolume10</th>\n", "      <th>BuyNumOrders10</th>\n", "      <th>SellPrice1</th>\n", "      <th>SellVolume1</th>\n", "      <th>SellNumOrders1</th>\n", "      <th>SellPrice2</th>\n", "      <th>SellVolume2</th>\n", "      <th>SellNumOrders2</th>\n", "      <th>SellPrice3</th>\n", "      <th>SellVolume3</th>\n", "      <th>SellNumOrders3</th>\n", "      <th>SellPrice4</th>\n", "      <th>SellVolume4</th>\n", "      <th>SellNumOrders4</th>\n", "      <th>SellPrice5</th>\n", "      <th>SellVolume5</th>\n", "      <th>SellNumOrders5</th>\n", "      <th>SellPrice6</th>\n", "      <th>SellVolume6</th>\n", "      <th>SellNumOrders6</th>\n", "      <th>SellPrice7</th>\n", "      <th>SellVolume7</th>\n", "      <th>SellNumOrders7</th>\n", "      <th>SellPrice8</th>\n", "      <th>SellVolume8</th>\n", "      <th>SellNumOrders8</th>\n", "      <th>SellPrice9</th>\n", "      <th>SellVolume9</th>\n", "      <th>SellNumOrders9</th>\n", "      <th>SellPrice10</th>\n", "      <th>SellVolume10</th>\n", "      <th>SellNumOrders10</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [RecvT<PERSON>, UpdateTime, ChannelNo, StreamID, SecurityID, SecurityIDSource, TradingPhase, PreCloPrice, TurnNum, Volume, Turnover, LastPrice, OpenPrice, HighPrice, LowPrice, DifPrice1, DifPrice2, PE1, PE2, PreCloseIOPV, IOPV, TotalOfferQty, WeightedAvgOfferPx, TotalBidQty, WeightedAvgBidPx, HighLimitPrice, LowLimitPrice, OpenInt, OptPremiumRatio, BuyPrice1, BuyVolume1, BuyNumOrders1, BuyPrice2, BuyVolume2, BuyNumOrders2, BuyPrice3, BuyVolume3, BuyNumOrders3, BuyPrice4, BuyVolume4, BuyNumOrders4, Buy<PERSON>rice5, BuyVolume5, BuyNumOrders5, BuyPrice6, BuyVolume6, BuyNumOrders6, Buy<PERSON>rice7, BuyVolume7, BuyNumOrders7, BuyPrice8, BuyVolume8, <PERSON><PERSON>umOrders8, BuyPrice9, Buy<PERSON><PERSON>ume9, <PERSON><PERSON>umOrders9, BuyPrice10, BuyVolume10, Buy<PERSON>umOrders10, SellPrice1, <PERSON><PERSON><PERSON>olume1, SellNumOrders1, SellPrice2, SellVolume2, SellNumOrders2, SellPrice3, SellVolume3, SellNumOrders3, SellPrice4, SellVolume4, SellNumOrders4, SellPrice5, SellVolume5, SellNumOrders5, SellPrice6, SellVolume6, SellNumOrders6, SellPrice7, SellVolume7, SellNumOrders7, SellPrice8, SellVolume8, SellNumOrders8, SellPrice9, SellVolume9, SellNumOrders9, SellPrice10, SellVolume10, SellNumOrders10, Delay]\n", "Index: []"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.loc[(dy_sz_tick.TradingPhase == 'B0      ') & (dy_sz_tick.UpdateTime < '09:31:00') & (dy_sz_tick.UpdateTime > '09:25:00')].head()"]}, {"cell_type": "code", "execution_count": 80, "id": "7f3580f5-fcbb-4421-8dbc-7ae291bcf1a1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>UpdateTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>StreamID</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>TradingPhase</th>\n", "      <th>PreCloPrice</th>\n", "      <th>TurnNum</th>\n", "      <th>Volume</th>\n", "      <th>Turnover</th>\n", "      <th>LastPrice</th>\n", "      <th>OpenPrice</th>\n", "      <th>HighPrice</th>\n", "      <th>LowPrice</th>\n", "      <th>DifPrice1</th>\n", "      <th>DifPrice2</th>\n", "      <th>PE1</th>\n", "      <th>PE2</th>\n", "      <th>PreCloseIOPV</th>\n", "      <th>IOPV</th>\n", "      <th>TotalOfferQty</th>\n", "      <th>WeightedAvgOfferPx</th>\n", "      <th>TotalBidQty</th>\n", "      <th>WeightedAvgBidPx</th>\n", "      <th>HighLimitPrice</th>\n", "      <th>LowLimitPrice</th>\n", "      <th>OpenInt</th>\n", "      <th>OptPremiumRatio</th>\n", "      <th>BuyPrice1</th>\n", "      <th>BuyVolume1</th>\n", "      <th>BuyNumOrders1</th>\n", "      <th>BuyPrice2</th>\n", "      <th>BuyVolume2</th>\n", "      <th>BuyNumOrders2</th>\n", "      <th>BuyPrice3</th>\n", "      <th>BuyVolume3</th>\n", "      <th>BuyNumOrders3</th>\n", "      <th>BuyPrice4</th>\n", "      <th>BuyVolume4</th>\n", "      <th>BuyNumOrders4</th>\n", "      <th>BuyPrice5</th>\n", "      <th>BuyVolume5</th>\n", "      <th>BuyNumOrders5</th>\n", "      <th>BuyPrice6</th>\n", "      <th>BuyVolume6</th>\n", "      <th>BuyNumOrders6</th>\n", "      <th>BuyPrice7</th>\n", "      <th>BuyVolume7</th>\n", "      <th>BuyNumOrders7</th>\n", "      <th>BuyPrice8</th>\n", "      <th>BuyVolume8</th>\n", "      <th>BuyNumOrders8</th>\n", "      <th>BuyPrice9</th>\n", "      <th>BuyVolume9</th>\n", "      <th>BuyNumOrders9</th>\n", "      <th>BuyPrice10</th>\n", "      <th>BuyVolume10</th>\n", "      <th>BuyNumOrders10</th>\n", "      <th>SellPrice1</th>\n", "      <th>SellVolume1</th>\n", "      <th>SellNumOrders1</th>\n", "      <th>SellPrice2</th>\n", "      <th>SellVolume2</th>\n", "      <th>SellNumOrders2</th>\n", "      <th>SellPrice3</th>\n", "      <th>SellVolume3</th>\n", "      <th>SellNumOrders3</th>\n", "      <th>SellPrice4</th>\n", "      <th>SellVolume4</th>\n", "      <th>SellNumOrders4</th>\n", "      <th>SellPrice5</th>\n", "      <th>SellVolume5</th>\n", "      <th>SellNumOrders5</th>\n", "      <th>SellPrice6</th>\n", "      <th>SellVolume6</th>\n", "      <th>SellNumOrders6</th>\n", "      <th>SellPrice7</th>\n", "      <th>SellVolume7</th>\n", "      <th>SellNumOrders7</th>\n", "      <th>SellPrice8</th>\n", "      <th>SellVolume8</th>\n", "      <th>SellNumOrders8</th>\n", "      <th>SellPrice9</th>\n", "      <th>SellVolume9</th>\n", "      <th>SellNumOrders9</th>\n", "      <th>SellPrice10</th>\n", "      <th>SellVolume10</th>\n", "      <th>SellNumOrders10</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>13577201</th>\n", "      <td>2025-05-26 14:57:00.190970</td>\n", "      <td>2025-05-26 14:57:00</td>\n", "      <td>1012</td>\n", "      <td>10</td>\n", "      <td>000016</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>4.950</td>\n", "      <td>17123</td>\n", "      <td>29877549</td>\n", "      <td>1.487604e+08</td>\n", "      <td>4.990</td>\n", "      <td>4.960</td>\n", "      <td>5.010</td>\n", "      <td>4.920</td>\n", "      <td>0.040</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>5.450</td>\n", "      <td>4.460</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4.980</td>\n", "      <td>1000.0</td>\n", "      <td>0.0</td>\n", "      <td>4.980</td>\n", "      <td>1000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>888851.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>190970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13577202</th>\n", "      <td>2025-05-26 14:57:00.191048</td>\n", "      <td>2025-05-26 14:57:00</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>000001</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>11.460</td>\n", "      <td>47104</td>\n", "      <td>69359158</td>\n", "      <td>7.941382e+08</td>\n", "      <td>11.420</td>\n", "      <td>11.440</td>\n", "      <td>11.500</td>\n", "      <td>11.400</td>\n", "      <td>-0.040</td>\n", "      <td>0.000</td>\n", "      <td>5.32</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>12.610</td>\n", "      <td>10.310</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>11.420</td>\n", "      <td>1000.0</td>\n", "      <td>0.0</td>\n", "      <td>11.420</td>\n", "      <td>1000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000</td>\n", "      <td>663700.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>191048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13577203</th>\n", "      <td>2025-05-26 14:57:00.191272</td>\n", "      <td>2025-05-26 14:57:00</td>\n", "      <td>1012</td>\n", "      <td>10</td>\n", "      <td>000025</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>17.800</td>\n", "      <td>20294</td>\n", "      <td>14241230</td>\n", "      <td>2.557423e+08</td>\n", "      <td>17.950</td>\n", "      <td>17.720</td>\n", "      <td>18.200</td>\n", "      <td>17.530</td>\n", "      <td>0.150</td>\n", "      <td>0.010</td>\n", "      <td>56.62</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>19.580</td>\n", "      <td>16.020</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>191272</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13577204</th>\n", "      <td>2025-05-26 14:57:00.191317</td>\n", "      <td>2025-05-26 14:57:00</td>\n", "      <td>1012</td>\n", "      <td>10</td>\n", "      <td>000027</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>6.410</td>\n", "      <td>14963</td>\n", "      <td>11190626</td>\n", "      <td>7.175918e+07</td>\n", "      <td>6.420</td>\n", "      <td>6.400</td>\n", "      <td>6.450</td>\n", "      <td>6.380</td>\n", "      <td>0.010</td>\n", "      <td>-0.010</td>\n", "      <td>22.92</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>7.050</td>\n", "      <td>5.770</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>191317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13577205</th>\n", "      <td>2025-05-26 14:57:00.191375</td>\n", "      <td>2025-05-26 14:57:00</td>\n", "      <td>1012</td>\n", "      <td>10</td>\n", "      <td>000028</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>25.170</td>\n", "      <td>6672</td>\n", "      <td>2406356</td>\n", "      <td>6.018580e+07</td>\n", "      <td>25.010</td>\n", "      <td>25.150</td>\n", "      <td>25.180</td>\n", "      <td>24.900</td>\n", "      <td>-0.160</td>\n", "      <td>0.010</td>\n", "      <td>21.66</td>\n", "      <td>0</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>27.690</td>\n", "      <td>22.650</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>191375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641060</th>\n", "      <td>2025-05-26 15:00:03.945182</td>\n", "      <td>2025-05-26 15:00:03</td>\n", "      <td>1023</td>\n", "      <td>10</td>\n", "      <td>159956</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>1.272</td>\n", "      <td>45</td>\n", "      <td>217100</td>\n", "      <td>2.745493e+05</td>\n", "      <td>1.263</td>\n", "      <td>1.256</td>\n", "      <td>1.280</td>\n", "      <td>1.256</td>\n", "      <td>-0.009</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>1.2732</td>\n", "      <td>1.2639</td>\n", "      <td>49900</td>\n", "      <td>1.327</td>\n", "      <td>115800</td>\n", "      <td>1.207</td>\n", "      <td>1.526</td>\n", "      <td>1.018</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1.260</td>\n", "      <td>4800.0</td>\n", "      <td>2.0</td>\n", "      <td>1.268</td>\n", "      <td>9800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.258</td>\n", "      <td>10000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.280</td>\n", "      <td>2000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.257</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.300</td>\n", "      <td>1800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.256</td>\n", "      <td>4400.0</td>\n", "      <td>8.0</td>\n", "      <td>1.301</td>\n", "      <td>23700.0</td>\n", "      <td>1.0</td>\n", "      <td>1.250</td>\n", "      <td>5700.0</td>\n", "      <td>2.0</td>\n", "      <td>1.309</td>\n", "      <td>5000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.240</td>\n", "      <td>600.0</td>\n", "      <td>1.0</td>\n", "      <td>1.501</td>\n", "      <td>3300.0</td>\n", "      <td>1.0</td>\n", "      <td>1.228</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.520</td>\n", "      <td>4300.0</td>\n", "      <td>2.0</td>\n", "      <td>1.225</td>\n", "      <td>23700.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.220</td>\n", "      <td>50000.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.211</td>\n", "      <td>1000.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>945182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641061</th>\n", "      <td>2025-05-26 15:00:03.945224</td>\n", "      <td>2025-05-26 15:00:03</td>\n", "      <td>1023</td>\n", "      <td>10</td>\n", "      <td>159961</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>1.611</td>\n", "      <td>110</td>\n", "      <td>1644402</td>\n", "      <td>2.619796e+06</td>\n", "      <td>1.592</td>\n", "      <td>1.606</td>\n", "      <td>1.606</td>\n", "      <td>1.588</td>\n", "      <td>-0.019</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>1.6105</td>\n", "      <td>1.5957</td>\n", "      <td>154000</td>\n", "      <td>1.616</td>\n", "      <td>173700</td>\n", "      <td>1.569</td>\n", "      <td>1.772</td>\n", "      <td>1.450</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1.592</td>\n", "      <td>62900.0</td>\n", "      <td>1.0</td>\n", "      <td>1.596</td>\n", "      <td>62700.0</td>\n", "      <td>1.0</td>\n", "      <td>1.587</td>\n", "      <td>1300.0</td>\n", "      <td>2.0</td>\n", "      <td>1.611</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.583</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.624</td>\n", "      <td>67800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.580</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.627</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.577</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.640</td>\n", "      <td>18800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.560</td>\n", "      <td>83500.0</td>\n", "      <td>2.0</td>\n", "      <td>1.650</td>\n", "      <td>3800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.552</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.697</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.544</td>\n", "      <td>18800.0</td>\n", "      <td>1.0</td>\n", "      <td>1.705</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.540</td>\n", "      <td>6500.0</td>\n", "      <td>1.0</td>\n", "      <td>1.714</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.772</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>945224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641062</th>\n", "      <td>2025-05-26 15:00:03.945267</td>\n", "      <td>2025-05-26 15:00:03</td>\n", "      <td>1023</td>\n", "      <td>10</td>\n", "      <td>159966</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>0.453</td>\n", "      <td>490</td>\n", "      <td>11310915</td>\n", "      <td>5.082620e+06</td>\n", "      <td>0.450</td>\n", "      <td>0.452</td>\n", "      <td>0.454</td>\n", "      <td>0.446</td>\n", "      <td>-0.003</td>\n", "      <td>0.002</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.4531</td>\n", "      <td>0.4492</td>\n", "      <td>3674200</td>\n", "      <td>0.456</td>\n", "      <td>1377000</td>\n", "      <td>0.444</td>\n", "      <td>0.544</td>\n", "      <td>0.362</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.449</td>\n", "      <td>2000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.450</td>\n", "      <td>2710000.0</td>\n", "      <td>4.0</td>\n", "      <td>0.448</td>\n", "      <td>526700.0</td>\n", "      <td>5.0</td>\n", "      <td>0.451</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>0.447</td>\n", "      <td>44800.0</td>\n", "      <td>5.0</td>\n", "      <td>0.452</td>\n", "      <td>47600.0</td>\n", "      <td>3.0</td>\n", "      <td>0.446</td>\n", "      <td>178600.0</td>\n", "      <td>25.0</td>\n", "      <td>0.453</td>\n", "      <td>1800.0</td>\n", "      <td>3.0</td>\n", "      <td>0.445</td>\n", "      <td>104900.0</td>\n", "      <td>13.0</td>\n", "      <td>0.455</td>\n", "      <td>20000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.444</td>\n", "      <td>167800.0</td>\n", "      <td>4.0</td>\n", "      <td>0.456</td>\n", "      <td>4000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.443</td>\n", "      <td>28400.0</td>\n", "      <td>6.0</td>\n", "      <td>0.457</td>\n", "      <td>100400.0</td>\n", "      <td>2.0</td>\n", "      <td>0.442</td>\n", "      <td>71000.0</td>\n", "      <td>2.0</td>\n", "      <td>0.458</td>\n", "      <td>167900.0</td>\n", "      <td>4.0</td>\n", "      <td>0.441</td>\n", "      <td>11100.0</td>\n", "      <td>3.0</td>\n", "      <td>0.459</td>\n", "      <td>201900.0</td>\n", "      <td>3.0</td>\n", "      <td>0.440</td>\n", "      <td>44400.0</td>\n", "      <td>9.0</td>\n", "      <td>0.460</td>\n", "      <td>30700.0</td>\n", "      <td>4.0</td>\n", "      <td>945267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641063</th>\n", "      <td>2025-05-26 15:00:03.945313</td>\n", "      <td>2025-05-26 15:00:03</td>\n", "      <td>1025</td>\n", "      <td>10</td>\n", "      <td>159969</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>1.144</td>\n", "      <td>48</td>\n", "      <td>488500</td>\n", "      <td>5.518601e+05</td>\n", "      <td>1.132</td>\n", "      <td>1.134</td>\n", "      <td>1.134</td>\n", "      <td>1.127</td>\n", "      <td>-0.012</td>\n", "      <td>0.001</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>1.1436</td>\n", "      <td>1.1306</td>\n", "      <td>389100</td>\n", "      <td>1.147</td>\n", "      <td>538200</td>\n", "      <td>1.110</td>\n", "      <td>1.258</td>\n", "      <td>1.030</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1.129</td>\n", "      <td>88600.0</td>\n", "      <td>1.0</td>\n", "      <td>1.132</td>\n", "      <td>85000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.111</td>\n", "      <td>300000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.151</td>\n", "      <td>300000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.108</td>\n", "      <td>18100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.157</td>\n", "      <td>1300.0</td>\n", "      <td>2.0</td>\n", "      <td>1.096</td>\n", "      <td>120000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.161</td>\n", "      <td>300.0</td>\n", "      <td>1.0</td>\n", "      <td>1.093</td>\n", "      <td>9200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.175</td>\n", "      <td>1000.0</td>\n", "      <td>1.0</td>\n", "      <td>1.087</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.184</td>\n", "      <td>300.0</td>\n", "      <td>1.0</td>\n", "      <td>1.083</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>1.200</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>1.050</td>\n", "      <td>2000.0</td>\n", "      <td>2.0</td>\n", "      <td>1.254</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.258</td>\n", "      <td>1000.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>945313</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641064</th>\n", "      <td>2025-05-26 15:00:03.945358</td>\n", "      <td>2025-05-26 15:00:03</td>\n", "      <td>1025</td>\n", "      <td>10</td>\n", "      <td>159975</td>\n", "      <td>102</td>\n", "      <td>C0</td>\n", "      <td>0.558</td>\n", "      <td>130</td>\n", "      <td>3511900</td>\n", "      <td>1.935459e+06</td>\n", "      <td>0.550</td>\n", "      <td>0.557</td>\n", "      <td>0.557</td>\n", "      <td>0.549</td>\n", "      <td>-0.008</td>\n", "      <td>0.000</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.5575</td>\n", "      <td>0.5517</td>\n", "      <td>331700</td>\n", "      <td>0.565</td>\n", "      <td>280600</td>\n", "      <td>0.539</td>\n", "      <td>0.614</td>\n", "      <td>0.502</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.551</td>\n", "      <td>10000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.553</td>\n", "      <td>20000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.550</td>\n", "      <td>500.0</td>\n", "      <td>1.0</td>\n", "      <td>0.554</td>\n", "      <td>3000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.548</td>\n", "      <td>3600.0</td>\n", "      <td>5.0</td>\n", "      <td>0.558</td>\n", "      <td>200.0</td>\n", "      <td>1.0</td>\n", "      <td>0.547</td>\n", "      <td>800.0</td>\n", "      <td>1.0</td>\n", "      <td>0.559</td>\n", "      <td>800.0</td>\n", "      <td>1.0</td>\n", "      <td>0.546</td>\n", "      <td>5000.0</td>\n", "      <td>1.0</td>\n", "      <td>0.560</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>0.545</td>\n", "      <td>400.0</td>\n", "      <td>3.0</td>\n", "      <td>0.561</td>\n", "      <td>196200.0</td>\n", "      <td>1.0</td>\n", "      <td>0.541</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>0.562</td>\n", "      <td>20700.0</td>\n", "      <td>2.0</td>\n", "      <td>0.540</td>\n", "      <td>300.0</td>\n", "      <td>3.0</td>\n", "      <td>0.563</td>\n", "      <td>10200.0</td>\n", "      <td>2.0</td>\n", "      <td>0.539</td>\n", "      <td>204200.0</td>\n", "      <td>1.0</td>\n", "      <td>0.567</td>\n", "      <td>54500.0</td>\n", "      <td>1.0</td>\n", "      <td>0.535</td>\n", "      <td>100.0</td>\n", "      <td>1.0</td>\n", "      <td>0.602</td>\n", "      <td>26000.0</td>\n", "      <td>1.0</td>\n", "      <td>945358</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>60316 rows × 90 columns</p>\n", "</div>"], "text/plain": ["                           RecvTime          UpdateTime  ChannelNo  StreamID  \\\n", "13577201 2025-05-26 14:57:00.190970 2025-05-26 14:57:00       1012        10   \n", "13577202 2025-05-26 14:57:00.191048 2025-05-26 14:57:00       1011        10   \n", "13577203 2025-05-26 14:57:00.191272 2025-05-26 14:57:00       1012        10   \n", "13577204 2025-05-26 14:57:00.191317 2025-05-26 14:57:00       1012        10   \n", "13577205 2025-05-26 14:57:00.191375 2025-05-26 14:57:00       1012        10   \n", "...                             ...                 ...        ...       ...   \n", "13641060 2025-05-26 15:00:03.945182 2025-05-26 15:00:03       1023        10   \n", "13641061 2025-05-26 15:00:03.945224 2025-05-26 15:00:03       1023        10   \n", "13641062 2025-05-26 15:00:03.945267 2025-05-26 15:00:03       1023        10   \n", "13641063 2025-05-26 15:00:03.945313 2025-05-26 15:00:03       1025        10   \n", "13641064 2025-05-26 15:00:03.945358 2025-05-26 15:00:03       1025        10   \n", "\n", "         SecurityID  SecurityIDSource TradingPhase  PreCloPrice  TurnNum  \\\n", "13577201     000016               102     C0              4.950    17123   \n", "13577202     000001               102     C0             11.460    47104   \n", "13577203     000025               102     C0             17.800    20294   \n", "13577204     000027               102     C0              6.410    14963   \n", "13577205     000028               102     C0             25.170     6672   \n", "...             ...               ...          ...          ...      ...   \n", "13641060     159956               102     C0              1.272       45   \n", "13641061     159961               102     C0              1.611      110   \n", "13641062     159966               102     C0              0.453      490   \n", "13641063     159969               102     C0              1.144       48   \n", "13641064     159975               102     C0              0.558      130   \n", "\n", "            Volume      Turnover  LastPrice  OpenPrice  HighPrice  LowPrice  \\\n", "13577201  29877549  1.487604e+08      4.990      4.960      5.010     4.920   \n", "13577202  69359158  7.941382e+08     11.420     11.440     11.500    11.400   \n", "13577203  14241230  2.557423e+08     17.950     17.720     18.200    17.530   \n", "13577204  11190626  7.175918e+07      6.420      6.400      6.450     6.380   \n", "13577205   2406356  6.018580e+07     25.010     25.150     25.180    24.900   \n", "...            ...           ...        ...        ...        ...       ...   \n", "13641060    217100  2.745493e+05      1.263      1.256      1.280     1.256   \n", "13641061   1644402  2.619796e+06      1.592      1.606      1.606     1.588   \n", "13641062  11310915  5.082620e+06      0.450      0.452      0.454     0.446   \n", "13641063    488500  5.518601e+05      1.132      1.134      1.134     1.127   \n", "13641064   3511900  1.935459e+06      0.550      0.557      0.557     0.549   \n", "\n", "          DifPrice1  DifPrice2    PE1  PE2  PreCloseIOPV    IOPV  \\\n", "13577201      0.040      0.000   0.00    0        0.0000  0.0000   \n", "13577202     -0.040      0.000   5.32    0        0.0000  0.0000   \n", "13577203      0.150      0.010  56.62    0        0.0000  0.0000   \n", "13577204      0.010     -0.010  22.92    0        0.0000  0.0000   \n", "13577205     -0.160      0.010  21.66    0        0.0000  0.0000   \n", "...             ...        ...    ...  ...           ...     ...   \n", "13641060     -0.009      0.000   0.00    0        1.2732  1.2639   \n", "13641061     -0.019      0.000   0.00    0        1.6105  1.5957   \n", "13641062     -0.003      0.002   0.00    0        0.4531  0.4492   \n", "13641063     -0.012      0.001   0.00    0        1.1436  1.1306   \n", "13641064     -0.008      0.000   0.00    0        0.5575  0.5517   \n", "\n", "          TotalOfferQty  WeightedAvgOfferPx  TotalBidQty  WeightedAvgBidPx  \\\n", "13577201              0               0.000            0             0.000   \n", "13577202              0               0.000            0             0.000   \n", "13577203              0               0.000            0             0.000   \n", "13577204              0               0.000            0             0.000   \n", "13577205              0               0.000            0             0.000   \n", "...                 ...                 ...          ...               ...   \n", "13641060          49900               1.327       115800             1.207   \n", "13641061         154000               1.616       173700             1.569   \n", "13641062        3674200               0.456      1377000             0.444   \n", "13641063         389100               1.147       538200             1.110   \n", "13641064         331700               0.565       280600             0.539   \n", "\n", "          HighLimitPrice  LowLimitPrice  OpenInt  OptPremiumRatio  BuyPrice1  \\\n", "13577201           5.450          4.460        0                0      4.980   \n", "13577202          12.610         10.310        0                0     11.420   \n", "13577203          19.580         16.020        0                0        NaN   \n", "13577204           7.050          5.770        0                0        NaN   \n", "13577205          27.690         22.650        0                0        NaN   \n", "...                  ...            ...      ...              ...        ...   \n", "13641060           1.526          1.018        0                0      1.260   \n", "13641061           1.772          1.450        0                0      1.592   \n", "13641062           0.544          0.362        0                0      0.449   \n", "13641063           1.258          1.030        0                0      1.129   \n", "13641064           0.614          0.502        0                0      0.551   \n", "\n", "          BuyVolume1  BuyNumOrders1  BuyPrice2  BuyVolume2  BuyNumOrders2  \\\n", "13577201      1000.0            0.0      4.980      1000.0            0.0   \n", "13577202      1000.0            0.0     11.420      1000.0            0.0   \n", "13577203         NaN            NaN        NaN         NaN            NaN   \n", "13577204         NaN            NaN        NaN         NaN            NaN   \n", "13577205         NaN            NaN        NaN         NaN            NaN   \n", "...              ...            ...        ...         ...            ...   \n", "13641060      4800.0            2.0      1.268      9800.0            1.0   \n", "13641061     62900.0            1.0      1.596     62700.0            1.0   \n", "13641062      2000.0            1.0      0.450   2710000.0            4.0   \n", "13641063     88600.0            1.0      1.132     85000.0            1.0   \n", "13641064     10000.0            1.0      0.553     20000.0            1.0   \n", "\n", "          BuyPrice3  BuyVolume3  BuyNumOrders3  BuyPrice4  BuyVolume4  \\\n", "13577201      0.000    888851.0            0.0        NaN         NaN   \n", "13577202      0.000    663700.0            0.0        NaN         NaN   \n", "13577203        NaN         NaN            NaN        NaN         NaN   \n", "13577204        NaN         NaN            NaN        NaN         NaN   \n", "13577205        NaN         NaN            NaN        NaN         NaN   \n", "...             ...         ...            ...        ...         ...   \n", "13641060      1.258     10000.0            1.0      1.280      2000.0   \n", "13641061      1.587      1300.0            2.0      1.611       200.0   \n", "13641062      0.448    526700.0            5.0      0.451       100.0   \n", "13641063      1.111    300000.0            1.0      1.151    300000.0   \n", "13641064      0.550       500.0            1.0      0.554      3000.0   \n", "\n", "          BuyNumOrders4  BuyPrice5  BuyVolume5  BuyNumOrders5  BuyPrice6  \\\n", "13577201            NaN        NaN         NaN            NaN        NaN   \n", "13577202            NaN        NaN         NaN            NaN        NaN   \n", "13577203            NaN        NaN         NaN            NaN        NaN   \n", "13577204            NaN        NaN         NaN            NaN        NaN   \n", "13577205            NaN        NaN         NaN            NaN        NaN   \n", "...                 ...        ...         ...            ...        ...   \n", "13641060            1.0      1.257       100.0            1.0      1.300   \n", "13641061            1.0      1.583       200.0            1.0      1.624   \n", "13641062            1.0      0.447     44800.0            5.0      0.452   \n", "13641063            1.0      1.108     18100.0            1.0      1.157   \n", "13641064            1.0      0.548      3600.0            5.0      0.558   \n", "\n", "          BuyVolume6  BuyNumOrders6  BuyPrice7  BuyVolume7  BuyNumOrders7  \\\n", "13577201         NaN            NaN        NaN         NaN            NaN   \n", "13577202         NaN            NaN        NaN         NaN            NaN   \n", "13577203         NaN            NaN        NaN         NaN            NaN   \n", "13577204         NaN            NaN        NaN         NaN            NaN   \n", "13577205         NaN            NaN        NaN         NaN            NaN   \n", "...              ...            ...        ...         ...            ...   \n", "13641060      1800.0            1.0      1.256      4400.0            8.0   \n", "13641061     67800.0            1.0      1.580       200.0            1.0   \n", "13641062     47600.0            3.0      0.446    178600.0           25.0   \n", "13641063      1300.0            2.0      1.096    120000.0            1.0   \n", "13641064       200.0            1.0      0.547       800.0            1.0   \n", "\n", "          BuyPrice8  BuyVolume8  BuyNumOrders8  BuyPrice9  BuyVolume9  \\\n", "13577201        NaN         NaN            NaN        NaN         NaN   \n", "13577202        NaN         NaN            NaN        NaN         NaN   \n", "13577203        NaN         NaN            NaN        NaN         NaN   \n", "13577204        NaN         NaN            NaN        NaN         NaN   \n", "13577205        NaN         NaN            NaN        NaN         NaN   \n", "...             ...         ...            ...        ...         ...   \n", "13641060      1.301     23700.0            1.0      1.250      5700.0   \n", "13641061      1.627       200.0            1.0      1.577       200.0   \n", "13641062      0.453      1800.0            3.0      0.445    104900.0   \n", "13641063      1.161       300.0            1.0      1.093      9200.0   \n", "13641064      0.559       800.0            1.0      0.546      5000.0   \n", "\n", "          BuyNumOrders9  BuyPrice10  BuyVolume10  BuyNumOrders10  SellPrice1  \\\n", "13577201            NaN         NaN          NaN             NaN         NaN   \n", "13577202            NaN         NaN          NaN             NaN         NaN   \n", "13577203            NaN         NaN          NaN             NaN         NaN   \n", "13577204            NaN         NaN          NaN             NaN         NaN   \n", "13577205            NaN         NaN          NaN             NaN         NaN   \n", "...                 ...         ...          ...             ...         ...   \n", "13641060            2.0       1.309       5000.0             1.0       1.240   \n", "13641061            1.0       1.640      18800.0             1.0       1.560   \n", "13641062           13.0       0.455      20000.0             1.0       0.444   \n", "13641063            1.0       1.175       1000.0             1.0       1.087   \n", "13641064            1.0       0.560        100.0             1.0       0.545   \n", "\n", "          SellVolume1  SellNumOrders1  SellPrice2  SellVolume2  \\\n", "13577201          NaN             NaN         NaN          NaN   \n", "13577202          NaN             NaN         NaN          NaN   \n", "13577203          NaN             NaN         NaN          NaN   \n", "13577204          NaN             NaN         NaN          NaN   \n", "13577205          NaN             NaN         NaN          NaN   \n", "...               ...             ...         ...          ...   \n", "13641060        600.0             1.0       1.501       3300.0   \n", "13641061      83500.0             2.0       1.650       3800.0   \n", "13641062     167800.0             4.0       0.456       4000.0   \n", "13641063        100.0             1.0       1.184        300.0   \n", "13641064        400.0             3.0       0.561     196200.0   \n", "\n", "          SellNumOrders2  SellPrice3  SellVolume3  SellNumOrders3  SellPrice4  \\\n", "13577201             NaN         NaN          NaN             NaN         NaN   \n", "13577202             NaN         NaN          NaN             NaN         NaN   \n", "13577203             NaN         NaN          NaN             NaN         NaN   \n", "13577204             NaN         NaN          NaN             NaN         NaN   \n", "13577205             NaN         NaN          NaN             NaN         NaN   \n", "...                  ...         ...          ...             ...         ...   \n", "13641060             1.0       1.228        100.0             1.0       1.520   \n", "13641061             1.0       1.552        100.0             1.0       1.697   \n", "13641062             1.0       0.443      28400.0             6.0       0.457   \n", "13641063             1.0       1.083        200.0             1.0       1.200   \n", "13641064             1.0       0.541        100.0             1.0       0.562   \n", "\n", "          SellVolume4  SellNumOrders4  SellPrice5  SellVolume5  \\\n", "13577201          NaN             NaN         NaN          NaN   \n", "13577202          NaN             NaN         NaN          NaN   \n", "13577203          NaN             NaN         NaN          NaN   \n", "13577204          NaN             NaN         NaN          NaN   \n", "13577205          NaN             NaN         NaN          NaN   \n", "...               ...             ...         ...          ...   \n", "13641060       4300.0             2.0       1.225      23700.0   \n", "13641061        100.0             1.0       1.544      18800.0   \n", "13641062     100400.0             2.0       0.442      71000.0   \n", "13641063        100.0             1.0       1.050       2000.0   \n", "13641064      20700.0             2.0       0.540        300.0   \n", "\n", "          SellNumOrders5  SellPrice6  SellVolume6  SellNumOrders6  SellPrice7  \\\n", "13577201             NaN         NaN          NaN             NaN         NaN   \n", "13577202             NaN         NaN          NaN             NaN         NaN   \n", "13577203             NaN         NaN          NaN             NaN         NaN   \n", "13577204             NaN         NaN          NaN             NaN         NaN   \n", "13577205             NaN         NaN          NaN             NaN         NaN   \n", "...                  ...         ...          ...             ...         ...   \n", "13641060             1.0         NaN          NaN             NaN       1.220   \n", "13641061             1.0       1.705        100.0             1.0       1.540   \n", "13641062             2.0       0.458     167900.0             4.0       0.441   \n", "13641063             2.0       1.254        100.0             1.0         NaN   \n", "13641064             3.0       0.563      10200.0             2.0       0.539   \n", "\n", "          SellVolume7  SellNumOrders7  SellPrice8  SellVolume8  \\\n", "13577201          NaN             NaN         NaN          NaN   \n", "13577202          NaN             NaN         NaN          NaN   \n", "13577203          NaN             NaN         NaN          NaN   \n", "13577204          NaN             NaN         NaN          NaN   \n", "13577205          NaN             NaN         NaN          NaN   \n", "...               ...             ...         ...          ...   \n", "13641060      50000.0             1.0         NaN          NaN   \n", "13641061       6500.0             1.0       1.714        100.0   \n", "13641062      11100.0             3.0       0.459     201900.0   \n", "13641063          NaN             NaN       1.258       1000.0   \n", "13641064     204200.0             1.0       0.567      54500.0   \n", "\n", "          SellNumOrders8  SellPrice9  SellVolume9  SellNumOrders9  \\\n", "13577201             NaN         NaN          NaN             NaN   \n", "13577202             NaN         NaN          NaN             NaN   \n", "13577203             NaN         NaN          NaN             NaN   \n", "13577204             NaN         NaN          NaN             NaN   \n", "13577205             NaN         NaN          NaN             NaN   \n", "...                  ...         ...          ...             ...   \n", "13641060             NaN       1.211       1000.0             1.0   \n", "13641061             1.0         NaN          NaN             NaN   \n", "13641062             3.0       0.440      44400.0             9.0   \n", "13641063             1.0         NaN          NaN             NaN   \n", "13641064             1.0       0.535        100.0             1.0   \n", "\n", "          SellPrice10  SellVolume10  SellNumOrders10   Delay  \n", "13577201          NaN           NaN              NaN  190970  \n", "13577202          NaN           NaN              NaN  191048  \n", "13577203          NaN           NaN              NaN  191272  \n", "13577204          NaN           NaN              NaN  191317  \n", "13577205          NaN           NaN              NaN  191375  \n", "...               ...           ...              ...     ...  \n", "13641060          NaN           NaN              NaN  945182  \n", "13641061        1.772         200.0              1.0  945224  \n", "13641062        0.460       30700.0              4.0  945267  \n", "13641063          NaN           NaN              NaN  945313  \n", "13641064        0.602       26000.0              1.0  945358  \n", "\n", "[60316 rows x 90 columns]"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.loc[(dy_sz_tick.TradingPhase == 'C0      ')]"]}, {"cell_type": "code", "execution_count": 82, "id": "6cfd70a4-bcb9-417a-9fe2-f447f9ba491f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>UpdateTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>StreamID</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>TradingPhase</th>\n", "      <th>PreCloPrice</th>\n", "      <th>TurnNum</th>\n", "      <th>Volume</th>\n", "      <th>Turnover</th>\n", "      <th>LastPrice</th>\n", "      <th>OpenPrice</th>\n", "      <th>HighPrice</th>\n", "      <th>LowPrice</th>\n", "      <th>DifPrice1</th>\n", "      <th>DifPrice2</th>\n", "      <th>PE1</th>\n", "      <th>PE2</th>\n", "      <th>PreCloseIOPV</th>\n", "      <th>IOPV</th>\n", "      <th>TotalOfferQty</th>\n", "      <th>WeightedAvgOfferPx</th>\n", "      <th>TotalBidQty</th>\n", "      <th>WeightedAvgBidPx</th>\n", "      <th>HighLimitPrice</th>\n", "      <th>LowLimitPrice</th>\n", "      <th>OpenInt</th>\n", "      <th>OptPremiumRatio</th>\n", "      <th>BuyPrice1</th>\n", "      <th>BuyVolume1</th>\n", "      <th>BuyNumOrders1</th>\n", "      <th>BuyPrice2</th>\n", "      <th>BuyVolume2</th>\n", "      <th>BuyNumOrders2</th>\n", "      <th>BuyPrice3</th>\n", "      <th>BuyVolume3</th>\n", "      <th>BuyNumOrders3</th>\n", "      <th>BuyPrice4</th>\n", "      <th>BuyVolume4</th>\n", "      <th>BuyNumOrders4</th>\n", "      <th>BuyPrice5</th>\n", "      <th>BuyVolume5</th>\n", "      <th>BuyNumOrders5</th>\n", "      <th>BuyPrice6</th>\n", "      <th>BuyVolume6</th>\n", "      <th>BuyNumOrders6</th>\n", "      <th>BuyPrice7</th>\n", "      <th>BuyVolume7</th>\n", "      <th>BuyNumOrders7</th>\n", "      <th>BuyPrice8</th>\n", "      <th>BuyVolume8</th>\n", "      <th>BuyNumOrders8</th>\n", "      <th>BuyPrice9</th>\n", "      <th>BuyVolume9</th>\n", "      <th>BuyNumOrders9</th>\n", "      <th>BuyPrice10</th>\n", "      <th>BuyVolume10</th>\n", "      <th>BuyNumOrders10</th>\n", "      <th>SellPrice1</th>\n", "      <th>SellVolume1</th>\n", "      <th>SellNumOrders1</th>\n", "      <th>SellPrice2</th>\n", "      <th>SellVolume2</th>\n", "      <th>SellNumOrders2</th>\n", "      <th>SellPrice3</th>\n", "      <th>SellVolume3</th>\n", "      <th>SellNumOrders3</th>\n", "      <th>SellPrice4</th>\n", "      <th>SellVolume4</th>\n", "      <th>SellNumOrders4</th>\n", "      <th>SellPrice5</th>\n", "      <th>SellVolume5</th>\n", "      <th>SellNumOrders5</th>\n", "      <th>SellPrice6</th>\n", "      <th>SellVolume6</th>\n", "      <th>SellNumOrders6</th>\n", "      <th>SellPrice7</th>\n", "      <th>SellVolume7</th>\n", "      <th>SellNumOrders7</th>\n", "      <th>SellPrice8</th>\n", "      <th>SellVolume8</th>\n", "      <th>SellNumOrders8</th>\n", "      <th>SellPrice9</th>\n", "      <th>SellVolume9</th>\n", "      <th>SellNumOrders9</th>\n", "      <th>SellPrice10</th>\n", "      <th>SellVolume10</th>\n", "      <th>SellNumOrders10</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-05-26 09:04:09.491462</td>\n", "      <td>2025-05-26 09:04:09</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>002350</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>6.44</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>89.07</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>7.08</td>\n", "      <td>5.80</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>491462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-05-26 09:04:09.491531</td>\n", "      <td>2025-05-26 09:04:09</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>002437</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>2.68</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>25.87</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>2.95</td>\n", "      <td>2.41</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>491531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-05-26 09:04:09.491556</td>\n", "      <td>2025-05-26 09:04:09</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>002565</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>3.97</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>92.76</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>4.37</td>\n", "      <td>3.57</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>491556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-05-26 09:04:09.491576</td>\n", "      <td>2025-05-26 09:04:09</td>\n", "      <td>1013</td>\n", "      <td>10</td>\n", "      <td>002646</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>9.01</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>103.09</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>9.91</td>\n", "      <td>8.11</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>491576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-05-26 09:04:09.491596</td>\n", "      <td>2025-05-26 09:04:09</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>002349</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>7.44</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>28.48</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>8.18</td>\n", "      <td>6.70</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>491596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42884</th>\n", "      <td>2025-05-26 09:14:58.548954</td>\n", "      <td>2025-05-26 09:14:57</td>\n", "      <td>1015</td>\n", "      <td>10</td>\n", "      <td>301328</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>39.75</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>51.11</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>47.70</td>\n", "      <td>31.80</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1548954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42885</th>\n", "      <td>2025-05-26 09:14:58.548976</td>\n", "      <td>2025-05-26 09:14:57</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>301156</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>22.16</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>62.92</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>26.59</td>\n", "      <td>17.73</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1548976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42886</th>\n", "      <td>2025-05-26 09:14:58.549010</td>\n", "      <td>2025-05-26 09:14:57</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>301310</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>38.75</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>45.31</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>46.50</td>\n", "      <td>31.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1549010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42887</th>\n", "      <td>2025-05-26 09:14:58.549038</td>\n", "      <td>2025-05-26 09:14:57</td>\n", "      <td>1011</td>\n", "      <td>10</td>\n", "      <td>301458</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>30.39</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>76.78</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>36.47</td>\n", "      <td>24.31</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1549038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42888</th>\n", "      <td>2025-05-26 09:14:58.549066</td>\n", "      <td>2025-05-26 09:14:57</td>\n", "      <td>1015</td>\n", "      <td>10</td>\n", "      <td>301519</td>\n", "      <td>102</td>\n", "      <td>S0</td>\n", "      <td>12.75</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>153.43</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>15.30</td>\n", "      <td>10.20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1549066</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>42889 rows × 90 columns</p>\n", "</div>"], "text/plain": ["                        RecvTime          UpdateTime  ChannelNo  StreamID  \\\n", "0     2025-05-26 09:04:09.491462 2025-05-26 09:04:09       1013        10   \n", "1     2025-05-26 09:04:09.491531 2025-05-26 09:04:09       1013        10   \n", "2     2025-05-26 09:04:09.491556 2025-05-26 09:04:09       1013        10   \n", "3     2025-05-26 09:04:09.491576 2025-05-26 09:04:09       1013        10   \n", "4     2025-05-26 09:04:09.491596 2025-05-26 09:04:09       1011        10   \n", "...                          ...                 ...        ...       ...   \n", "42884 2025-05-26 09:14:58.548954 2025-05-26 09:14:57       1015        10   \n", "42885 2025-05-26 09:14:58.548976 2025-05-26 09:14:57       1011        10   \n", "42886 2025-05-26 09:14:58.549010 2025-05-26 09:14:57       1011        10   \n", "42887 2025-05-26 09:14:58.549038 2025-05-26 09:14:57       1011        10   \n", "42888 2025-05-26 09:14:58.549066 2025-05-26 09:14:57       1015        10   \n", "\n", "      SecurityID  SecurityIDSource TradingPhase  PreCloPrice  TurnNum  Volume  \\\n", "0         002350               102     S0               6.44        0       0   \n", "1         002437               102     S0               2.68        0       0   \n", "2         002565               102     S0               3.97        0       0   \n", "3         002646               102     S0               9.01        0       0   \n", "4         002349               102     S0               7.44        0       0   \n", "...          ...               ...          ...          ...      ...     ...   \n", "42884     301328               102     S0              39.75        0       0   \n", "42885     301156               102     S0              22.16        0       0   \n", "42886     301310               102     S0              38.75        0       0   \n", "42887     301458               102     S0              30.39        0       0   \n", "42888     301519               102     S0              12.75        0       0   \n", "\n", "       Turnover  LastPrice  OpenPrice  HighPrice  LowPrice  DifPrice1  \\\n", "0           0.0        0.0        0.0        0.0       0.0        0.0   \n", "1           0.0        0.0        0.0        0.0       0.0        0.0   \n", "2           0.0        0.0        0.0        0.0       0.0        0.0   \n", "3           0.0        0.0        0.0        0.0       0.0        0.0   \n", "4           0.0        0.0        0.0        0.0       0.0        0.0   \n", "...         ...        ...        ...        ...       ...        ...   \n", "42884       0.0        0.0        0.0        0.0       0.0        0.0   \n", "42885       0.0        0.0        0.0        0.0       0.0        0.0   \n", "42886       0.0        0.0        0.0        0.0       0.0        0.0   \n", "42887       0.0        0.0        0.0        0.0       0.0        0.0   \n", "42888       0.0        0.0        0.0        0.0       0.0        0.0   \n", "\n", "       DifPrice2     PE1  PE2  PreCloseIOPV  IOPV  TotalOfferQty  \\\n", "0            0.0   89.07    0           0.0   0.0              0   \n", "1            0.0   25.87    0           0.0   0.0              0   \n", "2            0.0   92.76    0           0.0   0.0              0   \n", "3            0.0  103.09    0           0.0   0.0              0   \n", "4            0.0   28.48    0           0.0   0.0              0   \n", "...          ...     ...  ...           ...   ...            ...   \n", "42884        0.0   51.11    0           0.0   0.0              0   \n", "42885        0.0   62.92    0           0.0   0.0              0   \n", "42886        0.0   45.31    0           0.0   0.0              0   \n", "42887        0.0   76.78    0           0.0   0.0              0   \n", "42888        0.0  153.43    0           0.0   0.0              0   \n", "\n", "       WeightedAvgOfferPx  TotalBidQty  WeightedAvgBidPx  HighLimitPrice  \\\n", "0                     0.0            0               0.0            7.08   \n", "1                     0.0            0               0.0            2.95   \n", "2                     0.0            0               0.0            4.37   \n", "3                     0.0            0               0.0            9.91   \n", "4                     0.0            0               0.0            8.18   \n", "...                   ...          ...               ...             ...   \n", "42884                 0.0            0               0.0           47.70   \n", "42885                 0.0            0               0.0           26.59   \n", "42886                 0.0            0               0.0           46.50   \n", "42887                 0.0            0               0.0           36.47   \n", "42888                 0.0            0               0.0           15.30   \n", "\n", "       LowLimitPrice  OpenInt  OptPremiumRatio  BuyPrice1  BuyVolume1  \\\n", "0               5.80        0                0        NaN         NaN   \n", "1               2.41        0                0        NaN         NaN   \n", "2               3.57        0                0        NaN         NaN   \n", "3               8.11        0                0        NaN         NaN   \n", "4               6.70        0                0        NaN         NaN   \n", "...              ...      ...              ...        ...         ...   \n", "42884          31.80        0                0        NaN         NaN   \n", "42885          17.73        0                0        NaN         NaN   \n", "42886          31.00        0                0        NaN         NaN   \n", "42887          24.31        0                0        NaN         NaN   \n", "42888          10.20        0                0        NaN         NaN   \n", "\n", "       BuyNumOrders1  BuyPrice2  BuyVolume2  BuyNumOrders2  BuyPrice3  \\\n", "0                NaN        NaN         NaN            NaN        NaN   \n", "1                NaN        NaN         NaN            NaN        NaN   \n", "2                NaN        NaN         NaN            NaN        NaN   \n", "3                NaN        NaN         NaN            NaN        NaN   \n", "4                NaN        NaN         NaN            NaN        NaN   \n", "...              ...        ...         ...            ...        ...   \n", "42884            NaN        NaN         NaN            NaN        NaN   \n", "42885            NaN        NaN         NaN            NaN        NaN   \n", "42886            NaN        NaN         NaN            NaN        NaN   \n", "42887            NaN        NaN         NaN            NaN        NaN   \n", "42888            NaN        NaN         NaN            NaN        NaN   \n", "\n", "       BuyVolume3  BuyNumOrders3  BuyPrice4  BuyVolume4  BuyNumOrders4  \\\n", "0             NaN            NaN        NaN         NaN            NaN   \n", "1             NaN            NaN        NaN         NaN            NaN   \n", "2             NaN            NaN        NaN         NaN            NaN   \n", "3             NaN            NaN        NaN         NaN            NaN   \n", "4             NaN            NaN        NaN         NaN            NaN   \n", "...           ...            ...        ...         ...            ...   \n", "42884         NaN            NaN        NaN         NaN            NaN   \n", "42885         NaN            NaN        NaN         NaN            NaN   \n", "42886         NaN            NaN        NaN         NaN            NaN   \n", "42887         NaN            NaN        NaN         NaN            NaN   \n", "42888         NaN            NaN        NaN         NaN            NaN   \n", "\n", "       BuyPrice5  BuyVolume5  BuyNumOrders5  BuyPrice6  BuyVolume6  \\\n", "0            NaN         NaN            NaN        NaN         NaN   \n", "1            NaN         NaN            NaN        NaN         NaN   \n", "2            NaN         NaN            NaN        NaN         NaN   \n", "3            NaN         NaN            NaN        NaN         NaN   \n", "4            NaN         NaN            NaN        NaN         NaN   \n", "...          ...         ...            ...        ...         ...   \n", "42884        NaN         NaN            NaN        NaN         NaN   \n", "42885        NaN         NaN            NaN        NaN         NaN   \n", "42886        NaN         NaN            NaN        NaN         NaN   \n", "42887        NaN         NaN            NaN        NaN         NaN   \n", "42888        NaN         NaN            NaN        NaN         NaN   \n", "\n", "       BuyNumOrders6  BuyPrice7  BuyVolume7  BuyNumOrders7  BuyPrice8  \\\n", "0                NaN        NaN         NaN            NaN        NaN   \n", "1                NaN        NaN         NaN            NaN        NaN   \n", "2                NaN        NaN         NaN            NaN        NaN   \n", "3                NaN        NaN         NaN            NaN        NaN   \n", "4                NaN        NaN         NaN            NaN        NaN   \n", "...              ...        ...         ...            ...        ...   \n", "42884            NaN        NaN         NaN            NaN        NaN   \n", "42885            NaN        NaN         NaN            NaN        NaN   \n", "42886            NaN        NaN         NaN            NaN        NaN   \n", "42887            NaN        NaN         NaN            NaN        NaN   \n", "42888            NaN        NaN         NaN            NaN        NaN   \n", "\n", "       BuyVolume8  BuyNumOrders8  BuyPrice9  BuyVolume9  BuyNumOrders9  \\\n", "0             NaN            NaN        NaN         NaN            NaN   \n", "1             NaN            NaN        NaN         NaN            NaN   \n", "2             NaN            NaN        NaN         NaN            NaN   \n", "3             NaN            NaN        NaN         NaN            NaN   \n", "4             NaN            NaN        NaN         NaN            NaN   \n", "...           ...            ...        ...         ...            ...   \n", "42884         NaN            NaN        NaN         NaN            NaN   \n", "42885         NaN            NaN        NaN         NaN            NaN   \n", "42886         NaN            NaN        NaN         NaN            NaN   \n", "42887         NaN            NaN        NaN         NaN            NaN   \n", "42888         NaN            NaN        NaN         NaN            NaN   \n", "\n", "       BuyPrice10  BuyVolume10  BuyNumOrders10  SellPrice1  SellVolume1  \\\n", "0             NaN          NaN             NaN         NaN          NaN   \n", "1             NaN          NaN             NaN         NaN          NaN   \n", "2             NaN          NaN             NaN         NaN          NaN   \n", "3             NaN          NaN             NaN         NaN          NaN   \n", "4             NaN          NaN             NaN         NaN          NaN   \n", "...           ...          ...             ...         ...          ...   \n", "42884         NaN          NaN             NaN         NaN          NaN   \n", "42885         NaN          NaN             NaN         NaN          NaN   \n", "42886         NaN          NaN             NaN         NaN          NaN   \n", "42887         NaN          NaN             NaN         NaN          NaN   \n", "42888         NaN          NaN             NaN         NaN          NaN   \n", "\n", "       SellNumOrders1  SellPrice2  SellVolume2  SellNumOrders2  SellPrice3  \\\n", "0                 NaN         NaN          NaN             NaN         NaN   \n", "1                 NaN         NaN          NaN             NaN         NaN   \n", "2                 NaN         NaN          NaN             NaN         NaN   \n", "3                 NaN         NaN          NaN             NaN         NaN   \n", "4                 NaN         NaN          NaN             NaN         NaN   \n", "...               ...         ...          ...             ...         ...   \n", "42884             NaN         NaN          NaN             NaN         NaN   \n", "42885             NaN         NaN          NaN             NaN         NaN   \n", "42886             NaN         NaN          NaN             NaN         NaN   \n", "42887             NaN         NaN          NaN             NaN         NaN   \n", "42888             NaN         NaN          NaN             NaN         NaN   \n", "\n", "       SellVolume3  SellNumOrders3  SellPrice4  SellVolume4  SellNumOrders4  \\\n", "0              NaN             NaN         NaN          NaN             NaN   \n", "1              NaN             NaN         NaN          NaN             NaN   \n", "2              NaN             NaN         NaN          NaN             NaN   \n", "3              NaN             NaN         NaN          NaN             NaN   \n", "4              NaN             NaN         NaN          NaN             NaN   \n", "...            ...             ...         ...          ...             ...   \n", "42884          NaN             NaN         NaN          NaN             NaN   \n", "42885          NaN             NaN         NaN          NaN             NaN   \n", "42886          NaN             NaN         NaN          NaN             NaN   \n", "42887          NaN             NaN         NaN          NaN             NaN   \n", "42888          NaN             NaN         NaN          NaN             NaN   \n", "\n", "       SellPrice5  SellVolume5  SellNumOrders5  SellPrice6  SellVolume6  \\\n", "0             NaN          NaN             NaN         NaN          NaN   \n", "1             NaN          NaN             NaN         NaN          NaN   \n", "2             NaN          NaN             NaN         NaN          NaN   \n", "3             NaN          NaN             NaN         NaN          NaN   \n", "4             NaN          NaN             NaN         NaN          NaN   \n", "...           ...          ...             ...         ...          ...   \n", "42884         NaN          NaN             NaN         NaN          NaN   \n", "42885         NaN          NaN             NaN         NaN          NaN   \n", "42886         NaN          NaN             NaN         NaN          NaN   \n", "42887         NaN          NaN             NaN         NaN          NaN   \n", "42888         NaN          NaN             NaN         NaN          NaN   \n", "\n", "       SellNumOrders6  SellPrice7  SellVolume7  SellNumOrders7  SellPrice8  \\\n", "0                 NaN         NaN          NaN             NaN         NaN   \n", "1                 NaN         NaN          NaN             NaN         NaN   \n", "2                 NaN         NaN          NaN             NaN         NaN   \n", "3                 NaN         NaN          NaN             NaN         NaN   \n", "4                 NaN         NaN          NaN             NaN         NaN   \n", "...               ...         ...          ...             ...         ...   \n", "42884             NaN         NaN          NaN             NaN         NaN   \n", "42885             NaN         NaN          NaN             NaN         NaN   \n", "42886             NaN         NaN          NaN             NaN         NaN   \n", "42887             NaN         NaN          NaN             NaN         NaN   \n", "42888             NaN         NaN          NaN             NaN         NaN   \n", "\n", "       SellVolume8  SellNumOrders8  SellPrice9  SellVolume9  SellNumOrders9  \\\n", "0              NaN             NaN         NaN          NaN             NaN   \n", "1              NaN             NaN         NaN          NaN             NaN   \n", "2              NaN             NaN         NaN          NaN             NaN   \n", "3              NaN             NaN         NaN          NaN             NaN   \n", "4              NaN             NaN         NaN          NaN             NaN   \n", "...            ...             ...         ...          ...             ...   \n", "42884          NaN             NaN         NaN          NaN             NaN   \n", "42885          NaN             NaN         NaN          NaN             NaN   \n", "42886          NaN             NaN         NaN          NaN             NaN   \n", "42887          NaN             NaN         NaN          NaN             NaN   \n", "42888          NaN             NaN         NaN          NaN             NaN   \n", "\n", "       SellPrice10  SellVolume10  SellNumOrders10    Delay  \n", "0              NaN           NaN              NaN   491462  \n", "1              NaN           NaN              NaN   491531  \n", "2              NaN           NaN              NaN   491556  \n", "3              NaN           NaN              NaN   491576  \n", "4              NaN           NaN              NaN   491596  \n", "...            ...           ...              ...      ...  \n", "42884          NaN           NaN              NaN  1548954  \n", "42885          NaN           NaN              NaN  1548976  \n", "42886          NaN           NaN              NaN  1549010  \n", "42887          NaN           NaN              NaN  1549038  \n", "42888          NaN           NaN              NaN  1549066  \n", "\n", "[42889 rows x 90 columns]"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_tick.loc[dy_sz_tick.UpdateTime < '2025-05-26 09:15:00']"]}, {"cell_type": "code", "execution_count": null, "id": "da4bd109-0c0f-41b2-b0bd-b61c7882d4bc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}