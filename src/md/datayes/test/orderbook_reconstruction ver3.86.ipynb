{"cells": [{"cell_type": "code", "execution_count": 1, "id": "364bea73-1597-48fd-b345-b327765a6498", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["auth success \n", "{'total': 200000000, 'spare': 196071284}\n"]}], "source": ["from nb_common import *\n", "from tqdm.notebook import tqdm\n", "from joblib import Parallel, delayed\n", "import pickle \n", "import copy\n", "import os\n", "from pympler import asizeof\n", "\n", "jq_auth()"]}, {"cell_type": "code", "execution_count": null, "id": "900ad582-547b-40f3-b71c-d6a7f81097a5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af52f86a-f515-435a-88a5-b2a0e0390a8d", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["def reconstruct_orderbook(stock, date, show_progress_bar=True, complete_orderbook=False):\n", "\n", "    if stock[:1] == '6':\n", "\n", "        # f = open('hl_limit', 'rb')\n", "        # hl_limits = pickle.load(f)\n", "    \n", "        # f = open('/A/MD/tao/'+date+'/'+stock+'.SH.pickle', 'rb')\n", "        # raw = []\n", "        # while 1:\n", "        #     try:\n", "        #         raw.append(pickle.load(f))\n", "        #     except:\n", "        #         break\n", "\n", "        raw = pd.read_parquet('/A/pk/0529/stocks/'+str(stock)+'.parquet')\n", "\n", "        stock_postfix = '.XSHG' if stock[:1] == '6' else '.XSHE'\n", "\n", "        if len(raw) < 100 or len(raw) > 1500000:\n", "            return pd.DataFrame()\n", "    \n", "        # to_del = []\n", "        # for tick in raw:\n", "        #     if 'order_type' in tick and tick['order_type'] == 11:\n", "        #         to_del.append(tick)\n", "        # for tick in to_del:\n", "        #     raw.remove(tick)\n", "    \n", "        df = []\n", "        orderbook = {}\n", "        orderbook_vol = {}\n", "        orderbook_vol_lg = {}\n", "        orderpool = {}\n", "        new_order_dict = {}\n", "        orderpool_sm = set()\n", "        orderpool_mid = set()\n", "        orderpool_lg = set()\n", "        orderpool_slg = set()\n", "        sm_order_cum_vol = 0\n", "        mid_order_cum_vol = 0\n", "        lg_order_cum_vol = 0\n", "        slg_order_cum_vol = 0\n", "        order_aggr_dict = {}\n", "        curr_money = 0\n", "        cum_money = 0\n", "        prev_cum_money = 0\n", "        curr_vol = 0\n", "        cum_vol = 0\n", "        prev_cum_vol = 0\n", "        order_aggr = 1\n", "        mid_price = None\n", "        latest = None\n", "        sorted_price_levels = []\n", "        bid1, ask1 = None, None\n", "        total_buy_qty = 0\n", "        total_sell_qty = 0\n", "        # lower_limit = int(hl_limits['low_limit'][stock]*1000)\n", "        # upper_limit = int(hl_limits['high_limit'][stock]*1000)\n", "        upper_limit = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].high_limit*1000)\n", "        lower_limit = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].low_limit*1000)\n", "        mid_threshold = 50000 / ((lower_limit+upper_limit)*0.001/2)\n", "        lg_threshold = 300000 / ((lower_limit+upper_limit)*0.001/2)\n", "        slg_threshold = 1000000 / ((lower_limit+upper_limit)*0.001/2)\n", "        # print(mid_threshold, lg_threshold, slg_threshold)\n", "        raw_len = len(raw)-1\n", "        whether_snapshot = False\n", "        channel_no = None\n", "\n", "        # prev_close = int((lower_limit+upper_limit)/2)\n", "        # stock_postfix = '.XSHG' if stock[:1] == '6' else '.XSHE'\n", "        prev_close = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].pre_close*1000)\n", "        # tier_dict = {}\n", "        # for p in range(int(int(prev_close*0.8*0.1)*10), int(int(prev_close*1.2*0.1)*10)+10, 10):\n", "        #     tier_dict[p] = int((p/prev_close-1)*1000)\n", "        # orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}\n", "\n", "        if stock[:1] == '3' or stock[:3] == '688':\n", "            limit_multiplier = (0.8, 1.2) \n", "        else:\n", "            limit_multiplier = (0.9, 1.1) \n", "        # prev_close = int((lower_limit+upper_limit)/2)\n", "        if prev_close > 10000:\n", "            disc_coef = 0.001\n", "        elif prev_close > 5000 and prev_close <= 10000:\n", "            disc_coef = 0.002\n", "        else:\n", "            disc_coef = 0.005\n", "            \n", "        tier_dict = {}\n", "        for p in range(int(round(prev_close*limit_multiplier[0]*0.1)*10), int(round(prev_close*limit_multiplier[1]*0.1)*10)+10, 10):\n", "            tier_dict[p] = int(int((p/prev_close-1)/disc_coef)*disc_coef*1000)\n", "        orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}\n", "\n", "        if complete_orderbook:\n", "            col_names = ['time', 'orderbook']\n", "        else:\n", "            col_names = ['time']\n", "            col_names += ['bid'+str(i)+'_price' for i in range(1, 20+1)]\n", "            col_names += ['bid'+str(i)+'_qty' for i in range(1, 20+1)]\n", "            col_names += ['ask'+str(i)+'_price' for i in range(1, 20+1)]\n", "            col_names += ['ask'+str(i)+'_qty' for i in range(1, 20+1)]\n", "            col_names += ['bid'+str(i)+'_order_num' for i in range(1, 20+1)]\n", "            col_names += ['ask'+str(i)+'_order_num' for i in range(1, 20+1)]\n", "            col_names += ['bid'+str(i)+'_lg_qty' for i in range(1, 20+1)]\n", "            col_names += ['ask'+str(i)+'_lg_qty' for i in range(1, 20+1)]\n", "            col_names += ['price_tier_'+str(np.abs(i)) if i < 0 else 'price_tier'+str(i) for i in orderbook_tier]\n", "            col_names += ['prev_close', 'latest', 'mid_price', 'spread', \\\n", "                          'tick_type', 'tick_vol', 'tick_money', 'tick_direc', \\\n", "                          'order_aggr', \\\n", "                          'total_buy_qty', 'total_sell_qty', \\\n", "                          'sm_order_cum_amt', 'mid_order_cum_amt', 'lg_order_cum_amt', 'slg_order_cum_amt']\n", "\n", "        if show_progress_bar:\n", "            iterator = tqdm(range(raw.shape[0]))\n", "        else:\n", "            iterator = range(raw.shape[0])\n", "\n", "        common_channel = raw.Channel.value_counts().index[0]\n", "        \n", "        for idx in iterator:\n", "            # time = tick['time']\n", "            # date, time_hms = time.split()\n", "            # time_dt = datetime.datetime.strptime(time, '%Y-%m-%d %H:%M:%S.%f')\n", "\n", "            date = raw.RecvTime.iloc[0].split()[0]\n", "            time = date+' '+raw.TickTime.iloc[idx]\n", "            time_dt = datetime.datetime.strptime(time, '%Y-%m-%d %H:%M:%S.%f')\n", "            time_hms = str(time_dt.time())\n", "            \n", "        \n", "            # if not channel_no:\n", "            #     channel_no = tick['channel_no']\n", "            # else:\n", "            #     if tick['channel_no'] != channel_no:\n", "            #         continue\n", "\n", "            if raw.Channel.iloc[idx] != common_channel:\n", "                continue\n", "        \n", "            if (time_hms > '09:29:00.000000' and time_hms < '14:57:00.000000') or \\\n", "               idx == raw_len:\n", "                whether_snapshot = True\n", "            else:\n", "                whether_snapshot = False\n", "            \n", "            if time_hms > '09:29:00.000000' and not bid1 and not ask1:\n", "                sorted_price_levels = list(sorted(orderbook.keys()))\n", "                if total_buy_qty == 0:\n", "                    bid1 = sorted_price_levels[0]\n", "                    bid1_index = 0\n", "                    ask1 = sorted_price_levels[0]\n", "                    ask1_index = 0\n", "                elif total_sell_qty == 0:\n", "                    bid1 = sorted_price_levels[-1]\n", "                    bid1_index = len(sorted_price_levels)-1\n", "                    ask1 = sorted_price_levels[-1]\n", "                    ask1_index = len(sorted_price_levels)-1\n", "                else:\n", "                    num_lvs = len(sorted_price_levels)\n", "                    for i in range(num_lvs):\n", "                        bid = sorted_price_levels[max(i-1,0)]\n", "                        ask = sorted_price_levels[max(i,0)]\n", "                        if (len(orderbook[bid][1]) == 0 and \\\n", "                            len(orderbook[bid][0]) > 0 and \\\n", "                            len(orderbook[ask][0]) == 0 and \\\n", "                            len(orderbook[ask][1]) > 0): \n", "                            bid1 = bid\n", "                            bid1_index = i-1\n", "                            ask1 = ask\n", "                            ask1_index = i\n", "                            break  \n", "\n", "                if complete_orderbook:\n", "\n", "                    tick_info = [date+' 09:25:00.000000']\n", "                    tick_info += [\n", "                        dict(sorted(copy.deepcopy(orderbook).items())[::-1]),\n", "                    ]\n", "                    df.append(tick_info)\n", "\n", "                else:\n", "\n", "                    tick_info = [date+' 09:25:00.000000']\n", "\n", "                    bid_prices = []\n", "                    bid_qtys = []\n", "                    ask_prices = []\n", "                    ask_qtys = []\n", "\n", "                    for i in range(100):\n", "                        try_bid_index = max(bid1_index-i, 0)\n", "                        try_bid_price = sorted_price_levels[try_bid_index]\n", "                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:\n", "                            bid_prices.append(try_bid_price)\n", "                            bid_qtys.append(orderbook_vol[try_bid_price][0])\n", "                        if len(bid_prices) >= 20:\n", "                            break\n", "                    for i in range(100):\n", "                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)\n", "                        try_ask_price = sorted_price_levels[try_ask_index]\n", "                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:\n", "                            ask_prices.append(try_ask_price)\n", "                            ask_qtys.append(orderbook_vol[try_ask_price][1])\n", "                        if len(ask_prices) >= 20:\n", "                            break\n", "        \n", "                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:20]]\n", "                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:20]]\n", "\n", "                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:20]]\n", "                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:20]]\n", "\n", "                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]\n", "\n", "                    tick_info += bid_prices\n", "                    tick_info += bid_qtys\n", "                    tick_info += ask_prices\n", "                    tick_info += ask_qtys\n", "                    tick_info += bid_num_orders\n", "                    tick_info += ask_num_orders\n", "                    tick_info += bid_lg_qtys\n", "                    tick_info += ask_lg_qtys\n", "                    tick_info += price_tier_arr\n", "                    \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "            \n", "                    if tick_type == 2:\n", "                        curr_money = cum_money - prev_cum_money\n", "                        prev_cum_money = cum_money\n", "                \n", "                        curr_vol = cum_vol - prev_cum_vol\n", "                        prev_cum_vol = cum_vol\n", "            \n", "                    if idx == raw_len:\n", "                        tick_vol = curr_vol\n", "                        tick_money = curr_money\n", "                        tick_direc = 0\n", "\n", "                    tick_direc = 0\n", "                    tick_info += [prev_close, latest, mid_price, spread, \\\n", "                                  tick_type, curr_vol, curr_money, tick_direc, \\\n", "                                  order_aggr, \\\n", "                                  total_buy_qty, total_sell_qty, \\\n", "                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]\n", "                    \n", "                    df.append(tick_info)            \n", "        \n", "            #################################\n", "            ######### Place Order ###########\n", "            #################################\n", "        \n", "            \n", "            # data_type = tick['data_type']\n", "\n", "            data_type = raw.Type.iloc[idx]\n", "            \n", "            if data_type == 'A':\n", "                # direction = 1 if raw.TickBSFlag.iloc[idx] == 'B' else 2\n", "                if raw.TickBSFlag.iloc[idx] == 'B':\n", "                    direction = 1\n", "                elif raw.TickBSFlag.iloc[idx] == 'S':\n", "                    direction = 2\n", "                \n", "                order_direc = direction\n", "                order_qty = raw.Qty.iloc[idx]\n", "                order_no = raw.BuyOrderNO.iloc[idx] if direction == 1 else raw.SellOrderNO.iloc[idx] \n", "                if order_no in new_order_dict:\n", "                    order_qty_ = order_qty + new_order_dict[order_no][0]\n", "                    new_order_dict[order_no][0] += order_qty\n", "                else:\n", "                    order_qty_ = 0\n", "\n", "                # if tick['order_type'] == 2:\n", "                    # order_price = int(round(tick['order_price']*1000))\n", "                # elif tick['order_type'] == 1 or tick['order_type'] == 3:\n", "                #     print(tick)\n", "                #     if direction == 1:\n", "                #         order_price = upper_limit\n", "                #     else:\n", "                #         order_price = lower_limit\n", "\n", "                order_price = int(round(raw.Price.iloc[idx]*1000))\n", "                \n", "                orderpool[order_no] = [order_price, order_qty]\n", "                order_money = (order_price/1000)*order_qty\n", "        \n", "                if bid1 and ask1:\n", "                    if order_no in order_aggr_dict:\n", "                        order_aggr = order_price / order_aggr_dict[order_no]\n", "                    else:\n", "                        order_aggr = order_price / ((bid1+ask1)/2)\n", "                \n", "                if order_price not in orderbook: \n", "                    orderbook[order_price] = [{},{}]\n", "                    orderbook[order_price][direction-1][order_no] = order_qty\n", "                else:\n", "                    orderbook[order_price][direction-1][order_no] = order_qty\n", "        \n", "                if order_price not in orderbook_vol:\n", "                    orderbook_vol[order_price] = [0, 0]\n", "                orderbook_vol[order_price][direction-1] += order_qty\n", "        \n", "                if max(order_qty, order_qty_) <= mid_threshold:\n", "                    orderpool_sm.add(order_no)\n", "                elif max(order_qty, order_qty_) > mid_threshold and max(order_qty, order_qty_) <= lg_threshold:\n", "                    orderpool_mid.add(order_no)\n", "                elif max(order_qty, order_qty_) > lg_threshold and max(order_qty, order_qty_) <= slg_threshold:\n", "                    orderpool_lg.add(order_no)\n", "                elif max(order_qty, order_qty_) > slg_threshold:\n", "                    orderpool_slg.add(order_no)\n", "        \n", "                if max(order_qty, order_qty_) > lg_threshold:\n", "                    if order_price not in orderbook_vol_lg:\n", "                        orderbook_vol_lg[order_price] = [0, 0]\n", "                    orderbook_vol_lg[order_price][direction-1] += order_qty\n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "        \n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "                    \n", "                    if direction == 1:\n", "                        if (order_price > bid1 and order_price < ask1) or \\\n", "                           (total_buy_qty == 0 and order_price < ask1) or \\\n", "                           (total_sell_qty == 0 and order_price > bid1):\n", "                            bid1 = order_price\n", "                    else:\n", "                        if (order_price > bid1 and order_price < ask1) or \\\n", "                           (total_buy_qty == 0 and order_price < ask1) or \\\n", "                           (total_sell_qty == 0 and order_price > bid1):\n", "                            ask1 = order_price      \n", "                        \n", "                if direction == 1:\n", "                    total_buy_qty += order_qty\n", "                else:\n", "                    total_sell_qty += order_qty\n", "                            \n", "                tick_type = 0\n", "                tick_vol = order_qty \n", "                tick_money = (order_price/1000)*order_qty\n", "                tick_direc = direction \n", "\n", "                target_tier = tier_dict[order_price]\n", "                orderbook_tier[target_tier] += order_qty\n", "               \n", "        \n", "            #################################\n", "            ######### Cancel Order ##########\n", "            #################################\n", "        \n", "            \n", "            elif data_type == 'D':\n", "                \n", "                cancel_qty = raw.Qty.iloc[idx]\n", "                # direction = 1 if raw.TickBSFlag.iloc[idx] == 'B' else 2\n", "                if raw.TickBSFlag.iloc[idx] == 'B':\n", "                    direction = 1\n", "                elif raw.TickBSFlag.iloc[idx] == 'S':\n", "                    direction = 2\n", "                cancel_direc = direction\n", "                trade_direc = 0\n", "                order_direc = 0\n", "                whether_trade = 0\n", "                cancel_index = raw.BuyOrderNO.iloc[idx] if direction == 1 else raw.SellOrderNO.iloc[idx] \n", "                cancel_price = int(round(raw.Price.iloc[idx]*1000))\n", "                \n", "                if bid1 and ask1 and whether_snapshot:\n", "            \n", "                    if bid1 == cancel_price and cancel_qty == orderbook_vol[cancel_price][0]:\n", "                        bid1_index = sorted_price_levels.index(bid1)\n", "                        bid2 = sorted_price_levels[max(bid1_index-1,0)]\n", "                        bid1 = bid2\n", "                    elif ask1 == cancel_price and cancel_qty == orderbook_vol[cancel_price][1]:\n", "                        ask1_index = sorted_price_levels.index(ask1)\n", "                        ask2 = sorted_price_levels[min(ask1_index+1,len(sorted_price_levels)-1)]\n", "                        ask1 = ask2\n", "                \n", "                orderbook[cancel_price][direction-1].pop(cancel_index)\n", "                orderpool.pop(cancel_index)\n", "                if len(orderbook[cancel_price][0]) == len(orderbook[cancel_price][1]) == 0:\n", "                    orderbook.pop(cancel_price)\n", "        \n", "                if direction == 1:\n", "                    total_buy_qty -= cancel_qty\n", "                else:\n", "                    total_sell_qty -= cancel_qty\n", "        \n", "                orderbook_vol[cancel_price][direction-1] -= cancel_qty\n", "                if orderbook_vol[cancel_price][0] == 0 and \\\n", "                   orderbook_vol[cancel_price][1] == 0:\n", "                    orderbook_vol.pop(cancel_price)\n", "        \n", "                if cancel_index in orderpool_lg:\n", "                    orderbook_vol_lg[cancel_price][direction-1] -= cancel_qty\n", "                    if orderbook_vol_lg[cancel_price][0] == 0 and \\\n", "                       orderbook_vol_lg[cancel_price][1] == 0:\n", "                        orderbook_vol_lg.pop(cancel_price)\n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "        \n", "                tick_type = 1\n", "                tick_vol = cancel_qty \n", "                tick_money = cancel_qty * (cancel_price/1000)\n", "                tick_direc = direction \n", "        \n", "                order_aggr = 1\n", "\n", "                target_tier = tier_dict[cancel_price]\n", "                orderbook_tier[target_tier] -= cancel_qty\n", "        \n", "            \n", "            #################################\n", "            ######### Transaction ###########\n", "            #################################\n", "                    \n", "        \n", "            elif data_type == 'T':\n", "                trade_qty = raw.Qty.iloc[idx]\n", "                direction = 1 if raw.TickBSFlag.iloc[idx] == 'B' else 2\n", "                trade_direc = direction\n", "                # if trade_direc == '':\n", "                #     trade_direc = 0\n", "                order_direc = 0\n", "                cancel_direc = 0\n", "                trade_price = int(round(raw.Price.iloc[idx]*1000))\n", "                latest = trade_price\n", "                whether_trade = 1\n", "                trade_money = raw.TradeMoney.iloc[idx]\n", "                buy_order = raw.BuyOrderNO.iloc[idx] \n", "                sell_order = raw.SellOrderNO.iloc[idx] \n", "        \n", "                if direction == '' or (buy_order in orderpool and sell_order in orderpool):\n", "        \n", "                    if buy_order in orderpool_sm:\n", "                        sm_order_cum_vol += trade_money\n", "                    if sell_order in orderpool_sm:\n", "                        sm_order_cum_vol -= trade_money\n", "            \n", "                    if buy_order in orderpool_mid:\n", "                        mid_order_cum_vol += trade_money\n", "                    if sell_order in orderpool_mid:\n", "                        mid_order_cum_vol -= trade_money\n", "            \n", "                    if buy_order in orderpool_lg:\n", "                        lg_order_cum_vol += trade_money\n", "                    if sell_order in orderpool_lg:\n", "                        lg_order_cum_vol -= trade_money\n", "        \n", "                    if buy_order in orderpool_slg:\n", "                        slg_order_cum_vol += trade_money\n", "                    if sell_order in orderpool_slg:\n", "                        slg_order_cum_vol -= trade_money\n", "            \n", "                    buy_order_price, buy_order_qty = orderpool[buy_order]\n", "                    sell_order_price, sell_order_qty = orderpool[sell_order]\n", "                        \n", "                    if buy_order in orderpool:\n", "                        orderpool[buy_order][1] -= trade_qty\n", "                        if orderpool[buy_order][1] < 0.1:\n", "                            orderpool.pop(buy_order)\n", "                    if sell_order in orderpool:\n", "                        orderpool[sell_order][1] -= trade_qty\n", "                        if orderpool[sell_order][1] < 0.1:\n", "                            orderpool.pop(sell_order)\n", "               \n", "                    orderbook[buy_order_price][0][buy_order] -= trade_qty\n", "                    orderbook[sell_order_price][1][sell_order] -= trade_qty\n", "                    if orderbook[buy_order_price][0][buy_order] < 0.1:\n", "                        orderbook[buy_order_price][0].pop(buy_order)\n", "                    if orderbook[sell_order_price][1][sell_order] < 0.1:\n", "                        orderbook[sell_order_price][1].pop(sell_order)\n", "            \n", "                    if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:\n", "                        orderbook.pop(buy_order_price)\n", "                    if sell_order_price in orderbook:\n", "                        if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:\n", "                            orderbook.pop(sell_order_price)\n", "            \n", "                    total_buy_qty -= trade_qty\n", "                    total_sell_qty -= trade_qty\n", "            \n", "                    orderbook_vol[buy_order_price][0] -= trade_qty\n", "                    orderbook_vol[sell_order_price][1] -= trade_qty\n", "            \n", "                    if orderbook_vol[buy_order_price][0] == 0 and \\\n", "                       orderbook_vol[buy_order_price][1] == 0:\n", "                        orderbook_vol.pop(buy_order_price)\n", "                    if sell_order_price in orderbook_vol:\n", "                        if orderbook_vol[sell_order_price][0] == 0 and \\\n", "                           orderbook_vol[sell_order_price][1] == 0:\n", "                            orderbook_vol.pop(sell_order_price)\n", "            \n", "                    if buy_order in orderpool_lg:\n", "                        orderbook_vol_lg[buy_order_price][0] -= trade_qty\n", "                        if orderbook_vol_lg[buy_order_price][0] == 0 and \\\n", "                           orderbook_vol_lg[buy_order_price][1] == 0:\n", "                            orderbook_vol_lg.pop(buy_order_price)\n", "            \n", "                    if sell_order in orderpool_lg:\n", "                        orderbook_vol_lg[sell_order_price][1] -= trade_qty\n", "                        if orderbook_vol_lg[sell_order_price][0] == 0 and \\\n", "                           orderbook_vol_lg[sell_order_price][1] == 0:\n", "                            orderbook_vol_lg.pop(sell_order_price)\n", "\n", "                    target_tier = tier_dict[buy_order_price]\n", "                    orderbook_tier[target_tier] -= trade_qty\n", "                    target_tier = tier_dict[sell_order_price]\n", "                    orderbook_tier[target_tier] -= trade_qty\n", "        \n", "                elif buy_order in orderpool and sell_order not in orderpool:\n", "                \n", "                    if sell_order not in new_order_dict:\n", "                        new_order_dict[sell_order] = [0, 0, direction, time_dt, {'s':0,'m':0,'l':0,'sl':0}]\n", "                    new_order_dict[sell_order][0] += trade_qty\n", "                    new_order_dict[sell_order][1] += trade_money\n", "                    if buy_order in orderpool_sm:\n", "                        new_order_dict[sell_order][4]['s'] += trade_money\n", "                    if buy_order in orderpool_mid:\n", "                        new_order_dict[sell_order][4]['m'] += trade_money\n", "                    if buy_order in orderpool_lg:\n", "                        new_order_dict[sell_order][4]['l'] += trade_money\n", "                    if buy_order in orderpool_slg:\n", "                        new_order_dict[sell_order][4]['sl'] += trade_money\n", "                    \n", "                    if bid1 and ask1:\n", "                        order_aggr_dict[sell_order] = int((bid1+ask1)/2)\n", "        \n", "                    buy_order_price, buy_order_qty = orderpool[buy_order]\n", "        \n", "                    orderpool[buy_order][1] -= trade_qty\n", "                    if orderpool[buy_order][1] < 0.1:\n", "                        orderpool.pop(buy_order)\n", "        \n", "                    orderbook[buy_order_price][0][buy_order] -= trade_qty\n", "                    if orderbook[buy_order_price][0][buy_order] < 0.1:\n", "                        orderbook[buy_order_price][0].pop(buy_order)\n", "            \n", "                    if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:\n", "                        orderbook.pop(buy_order_price)\n", "              \n", "                    total_buy_qty -= trade_qty\n", "                    \n", "                    orderbook_vol[buy_order_price][0] -= trade_qty\n", "            \n", "                    if orderbook_vol[buy_order_price][0] == 0 and \\\n", "                       orderbook_vol[buy_order_price][1] == 0:\n", "                        orderbook_vol.pop(buy_order_price)\n", "                        \n", "                    if buy_order in orderpool_lg:\n", "                        orderbook_vol_lg[buy_order_price][0] -= trade_qty\n", "                        if orderbook_vol_lg[buy_order_price][0] == 0 and \\\n", "                           orderbook_vol_lg[buy_order_price][1] == 0:\n", "                            orderbook_vol_lg.pop(buy_order_price)\n", "\n", "                    target_tier = tier_dict[buy_order_price]\n", "                    orderbook_tier[target_tier] -= trade_qty\n", "        \n", "                elif buy_order not in orderpool and sell_order in orderpool:\n", "                        \n", "                    if buy_order not in new_order_dict:\n", "                        new_order_dict[buy_order] = [0, 0, direction, time_dt, {'s':0,'m':0,'l':0,'sl':0}]\n", "                    new_order_dict[buy_order][0] += trade_qty\n", "                    new_order_dict[buy_order][1] += trade_money\n", "                    if sell_order in orderpool_sm:\n", "                        new_order_dict[buy_order][4]['s'] += trade_money   \n", "                    if sell_order in orderpool_mid:\n", "                        new_order_dict[buy_order][4]['m'] += trade_money   \n", "                    if sell_order in orderpool_lg:\n", "                        new_order_dict[buy_order][4]['l'] += trade_money \n", "                    if sell_order in orderpool_slg:\n", "                        new_order_dict[buy_order][4]['sl'] += trade_money \n", "        \n", "                    if bid1 and ask1:\n", "                        order_aggr_dict[buy_order] = int((bid1+ask1)/2)\n", "        \n", "                    sell_order_price, sell_order_qty = orderpool[sell_order]\n", "            \n", "                    orderpool[sell_order][1] -= trade_qty\n", "                    if orderpool[sell_order][1] < 0.1:\n", "                        orderpool.pop(sell_order)\n", "               \n", "                    orderbook[sell_order_price][1][sell_order] -= trade_qty\n", "                    if orderbook[sell_order_price][1][sell_order] < 0.1:\n", "                        orderbook[sell_order_price][1].pop(sell_order)    \n", "                    if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:\n", "                        orderbook.pop(sell_order_price)\n", "            \n", "                    total_sell_qty -= trade_qty\n", "                    \n", "                    orderbook_vol[sell_order_price][1] -= trade_qty\n", "            \n", "                    if orderbook_vol[sell_order_price][0] == 0 and \\\n", "                       orderbook_vol[sell_order_price][1] == 0:\n", "                        orderbook_vol.pop(sell_order_price)\n", "            \n", "                    if sell_order in orderpool_lg:\n", "                        orderbook_vol_lg[sell_order_price][1] -= trade_qty\n", "                        if orderbook_vol_lg[sell_order_price][0] == 0 and \\\n", "                           orderbook_vol_lg[sell_order_price][1] == 0:\n", "                            orderbook_vol_lg.pop(sell_order_price)\n", "\n", "                    target_tier = tier_dict[sell_order_price]\n", "                    orderbook_tier[target_tier] -= trade_qty\n", "             \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    \n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "            \n", "                    if total_buy_qty == 0:\n", "                        bid1 = sorted_price_levels[0]\n", "                        ask1 = sorted_price_levels[0]\n", "                    elif total_sell_qty == 0:\n", "                        bid1 = sorted_price_levels[-1] \n", "                        ask1 = sorted_price_levels[-1]\n", "                    else:\n", "                        for price in range(latest, upper_limit+10, 10):\n", "                            if price in orderbook_vol:\n", "                                if orderbook_vol[price][0] == 0 and orderbook_vol[price][1] > 0:\n", "                                    ask1 = price\n", "                                    break\n", "                        for price in range(latest, lower_limit-10, -10):\n", "                            if price in orderbook_vol:\n", "                                if orderbook_vol[price][0] > 0 and orderbook_vol[price][1] == 0:\n", "                                    bid1 = price\n", "                                    break\n", "                                        \n", "                cum_money += trade_money     \n", "                cum_vol += trade_qty\n", "                \n", "                tick_type = 2\n", "                tick_vol = trade_qty \n", "                tick_money = trade_money\n", "                tick_direc = direction \n", "                \n", "                order_aggr = 1\n", "        \n", "            to_del = []\n", "            for order in new_order_dict:\n", "                vol, mony, direc, trigger_time, rival_order_dict = new_order_dict[order]\n", "                if (time_dt-trigger_time).total_seconds() > 1:\n", "                    to_del.append(order)\n", "                    if direc == 1:\n", "                        sm_order_cum_vol -= rival_order_dict['s']\n", "                        mid_order_cum_vol -= rival_order_dict['m']\n", "                        lg_order_cum_vol -= rival_order_dict['l']\n", "                        slg_order_cum_vol -= rival_order_dict['sl']\n", "                        if vol <= mid_threshold:\n", "                            sm_order_cum_vol += mony\n", "                        elif vol > mid_threshold and vol <= lg_threshold:\n", "                            mid_order_cum_vol += mony\n", "                        elif vol > lg_threshold and vol <= slg_threshold:\n", "                            lg_order_cum_vol += mony\n", "                        elif vol > slg_threshold:\n", "                            slg_order_cum_vol += mony\n", "                    elif direc == 2:\n", "                        sm_order_cum_vol += rival_order_dict['s']\n", "                        mid_order_cum_vol += rival_order_dict['m']\n", "                        lg_order_cum_vol += rival_order_dict['l']\n", "                        slg_order_cum_vol += rival_order_dict['sl']\n", "                        if vol <= mid_threshold:\n", "                            sm_order_cum_vol -= mony\n", "                        elif vol > mid_threshold and vol <= lg_threshold:\n", "                            mid_order_cum_vol -= mony\n", "                        elif vol > lg_threshold and vol <= slg_threshold:\n", "                            lg_order_cum_vol -= mony\n", "                        elif vol > slg_threshold:\n", "                            slg_order_cum_vol -= mony\n", "            for order in to_del:\n", "                new_order_dict.pop(order)\n", "                                \n", "            if idx == raw_len:\n", "                sorted_price_levels = list(sorted(orderbook.keys()))\n", "                if total_buy_qty == 0:\n", "                    bid1 = sorted_price_levels[0]\n", "                    bid1_index = 0\n", "                    ask1 = sorted_price_levels[0]\n", "                    ask1_index = 0\n", "                elif total_sell_qty == 0:\n", "                    bid1 = sorted_price_levels[-1]\n", "                    bid1_index = len(sorted_price_levels)-1\n", "                    ask1 = sorted_price_levels[-1]\n", "                    ask1_index = len(sorted_price_levels)-1\n", "                else:\n", "                    num_lvs = len(sorted_price_levels)\n", "                    for i in range(num_lvs):\n", "                        bid = sorted_price_levels[max(i-1,0)]\n", "                        ask = sorted_price_levels[max(i,0)]\n", "                        if (len(orderbook[bid][1]) == 0 and \\\n", "                            len(orderbook[bid][0]) > 0 and \\\n", "                            len(orderbook[ask][0]) == 0 and \\\n", "                            len(orderbook[ask][1]) > 0): \n", "                            bid1 = bid\n", "                            bid1_index = i-1\n", "                            ask1 = ask\n", "                            ask1_index = i\n", "                            break   \n", "        \n", "            if whether_snapshot:\n", "                \n", "                max_lv = len(sorted_price_levels)-1\n", "                if total_buy_qty == 0:\n", "                    bid1_index = 0\n", "                    ask1_index = 0\n", "                elif total_sell_qty == 0:\n", "                    bid1_index = max_lv\n", "                    ask1_index = max_lv\n", "                else:\n", "                    bid1_index = sorted_price_levels.index(bid1)\n", "                    ask1_index = min(bid1_index+1, max_lv)\n", "\n", "                if complete_orderbook:\n", "\n", "                    tick_info = [time]\n", "                    tick_info += [\n", "                        dict(sorted(copy.deepcopy(orderbook).items())[::-1]),\n", "                    ]\n", "                    df.append(tick_info)\n", "\n", "                else:\n", "\n", "                    tick_info = [time]\n", "\n", "                    bid_prices = []\n", "                    bid_qtys = []\n", "                    ask_prices = []\n", "                    ask_qtys = []\n", "\n", "                    for i in range(100):\n", "                        try_bid_index = max(bid1_index-i, 0)\n", "                        try_bid_price = sorted_price_levels[try_bid_index]\n", "                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:\n", "                            bid_prices.append(try_bid_price)\n", "                            bid_qtys.append(orderbook_vol[try_bid_price][0])\n", "                        if len(bid_prices) >= 20:\n", "                            break\n", "                       \n", "                    for i in range(100):\n", "                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)\n", "                        try_ask_price = sorted_price_levels[try_ask_index]\n", "                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:\n", "                            ask_prices.append(try_ask_price)\n", "                            ask_qtys.append(orderbook_vol[try_ask_price][1])\n", "                        if len(ask_prices) >= 20:\n", "                            break\n", "\n", "                    # for i in range(100):\n", "                    #     try_bid_index = max(bid1_index-i, 0)\n", "                    #     try_bid_price = sorted_price_levels[try_bid_index]\n", "                    #     if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:\n", "                    #         bid_prices.append(try_bid_price)\n", "                    #         bid_qtys.append(orderbook_vol[try_bid_price][0])\n", "                    #     if len(bid_prices) >= 20:\n", "                    #         break\n", "                    # for i in range(100):\n", "                    #     try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)\n", "                    #     try_ask_price = sorted_price_levels[try_ask_index]\n", "                    #     if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:\n", "                    #         ask_prices.append(try_ask_price)\n", "                    #         ask_qtys.append(orderbook_vol[try_ask_price][1])\n", "                    #     if len(ask_prices) >= 20:\n", "                    #         break\n", "\n", "                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:20]]\n", "                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:20]]\n", "\n", "                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:20]]\n", "                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:20]]\n", "\n", "                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]\n", "\n", "                    tick_info += bid_prices\n", "                    tick_info += bid_qtys\n", "                    tick_info += ask_prices\n", "                    tick_info += ask_qtys\n", "                    tick_info += bid_num_orders\n", "                    tick_info += ask_num_orders\n", "                    tick_info += bid_lg_qtys\n", "                    tick_info += ask_lg_qtys\n", "                    tick_info += price_tier_arr\n", "                    \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "            \n", "                    if tick_type == 2:\n", "                        curr_money = cum_money - prev_cum_money\n", "                        prev_cum_money = cum_money\n", "                \n", "                        curr_vol = cum_vol - prev_cum_vol\n", "                        prev_cum_vol = cum_vol\n", "            \n", "                    if idx == raw_len:\n", "                        tick_vol = curr_vol\n", "                        tick_money = curr_money\n", "                        tick_direc = 0\n", "\n", "                    tick_info += [prev_close, latest, mid_price, spread, \\\n", "                                  tick_type, tick_vol, tick_money, tick_direc, \\\n", "                                  order_aggr, \\\n", "                                  total_buy_qty, total_sell_qty, \\\n", "                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]\n", "            \n", "                    df.append(tick_info)          \n", "            \n", "        orderbook = {key: orderbook[key] for key in sorted(orderbook.keys())[::-1]} \n", "        orderbook_vol = {key: orderbook_vol[key] for key in sorted(orderbook_vol.keys())[::-1]} \n", "        orderbook_vol_lg = {key: orderbook_vol_lg[key] for key in sorted(orderbook_vol_lg.keys())[::-1]} \n", "\n", "        orderbook_tier = {key: orderbook_tier[key] for key in sorted(orderbook_tier.keys())[::-1]} \n", "            \n", "        df = pd.DataFrame(df)\n", "        df.columns = col_names   \n", "        df = df.set_index(['time'])\n", "        df.index = pd.to_datetime(df.index)\n", "    \n", "        # return df, orderbook, orderbook_vol, orderbook_tier\n", "        return df\n", "\n", "    else:\n", "\n", "        # f = open('hl_limit', 'rb')\n", "        # hl_limits = pickle.load(f)\n", "    \n", "        # f = open('/A/MD/tao/'+date+'/'+stock+'.SZ.pickle', 'rb')\n", "        # raw = []\n", "        # while 1:\n", "        #     try:\n", "        #         raw.append(pickle.load(f))\n", "        #     except:\n", "        #         break\n", "\n", "        raw = pd.read_parquet('/A/pk/0529/stocks/'+str(stock)+'.parquet')\n", "\n", "        stock_postfix = '.XSHG' if stock[:1] == '6' else '.XSHE'\n", "\n", "        if len(raw) < 100 or len(raw) > 1500000:\n", "            return pd.DataFrame()\n", "        \n", "        df = []\n", "        orderbook = {}\n", "        orderbook_vol = {}\n", "        orderbook_vol_lg = {}\n", "        unmatched_order = {}\n", "        orderpool = {}\n", "        orderpool_sm = set()\n", "        orderpool_mid = set()\n", "        orderpool_lg = set()\n", "        orderpool_slg = set()\n", "        sm_order_cum_vol = 0\n", "        mid_order_cum_vol = 0\n", "        lg_order_cum_vol = 0\n", "        slg_order_cum_vol = 0\n", "        order_aggr = 1\n", "        latest = None\n", "        curr_mid_price = None\n", "        whether_snapshot = False\n", "        sorted_price_levels = []\n", "        bid1, ask1 = None, None\n", "        total_buy_qty = 0\n", "        total_sell_qty = 0\n", "        # lower_limit = int(hl_limits['low_limit'][stock]*1000)\n", "        # upper_limit = int(hl_limits['high_limit'][stock]*1000)\n", "        upper_limit = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].high_limit*1000)\n", "        lower_limit = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].low_limit*1000)\n", "        mid_threshold = 50000 / ((lower_limit+upper_limit)*0.001/2)\n", "        lg_threshold = 300000 / ((lower_limit+upper_limit)*0.001/2)\n", "        slg_threshold = 1000000 / ((lower_limit+upper_limit)*0.001/2)\n", "        \n", "        raw_len = len(raw)-1\n", "        curr_money = 0\n", "        cum_money = 0\n", "        prev_cum_money = 0\n", "        curr_vol = 0\n", "        cum_vol = 0\n", "        prev_cum_vol = 0\n", "        \n", "        mid_prices = []\n", "\n", "        # prev_close = int((lower_limit+upper_limit)/2)\n", "        \n", "        prev_close = int(pd.read_pickle('/A/MD/1d/s/md_s_'+stock+stock_postfix+'.pickle').loc[date].pre_close*1000)\n", "        # tier_dict = {}\n", "        # for p in range(int(int(prev_close*0.8*0.1)*10), int(int(prev_close*1.2*0.1)*10)+10, 10):\n", "        #     tier_dict[p] = int( (p/prev_close-1)/   *1000  )\n", "        # orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}\n", "\n", "        if stock[:1] == '3' or stock[:3] == '688':\n", "            limit_multiplier = (0.8, 1.2) \n", "        else:\n", "            limit_multiplier = (0.9, 1.1) \n", "        # prev_close = int((lower_limit+upper_limit)/2)\n", "        if prev_close > 10000:\n", "            disc_coef = 0.001\n", "        elif prev_close > 5000 and prev_close <= 10000:\n", "            disc_coef = 0.002\n", "        else:\n", "            disc_coef = 0.005\n", "            \n", "        tier_dict = {}\n", "        for p in range(int(round(prev_close*limit_multiplier[0]*0.1)*10), int(round(prev_close*limit_multiplier[1]*0.1)*10)+10, 10):\n", "            tier_dict[p] = int(int((p/prev_close-1)/disc_coef)*disc_coef*1000)\n", "        orderbook_tier = {p:0 for p in np.sort(list(set(tier_dict.values())))}\n", "\n", "        if complete_orderbook:\n", "            col_names = ['time', 'orderbook']\n", "        else:\n", "            col_names = ['time']\n", "            col_names += ['bid'+str(i)+'_price' for i in range(1, 10+1)]\n", "            col_names += ['bid'+str(i)+'_qty' for i in range(1, 10+1)]\n", "            col_names += ['ask'+str(i)+'_price' for i in range(1, 10+1)]\n", "            col_names += ['ask'+str(i)+'_qty' for i in range(1, 10+1)]\n", "            col_names += ['bid'+str(i)+'_order_num' for i in range(1, 10+1)]\n", "            col_names += ['ask'+str(i)+'_order_num' for i in range(1, 10+1)]\n", "            col_names += ['bid'+str(i)+'_lg_qty' for i in range(1, 10+1)]\n", "            col_names += ['ask'+str(i)+'_lg_qty' for i in range(1, 10+1)]\n", "            col_names += ['price_tier_'+str(np.abs(i)) if i < 0 else 'price_tier'+str(i) for i in orderbook_tier]\n", "            col_names += ['prev_close','latest', 'mid_price', 'spread', \\\n", "                          'tick_type', 'tick_vol', 'tick_money', 'tick_direc', \\\n", "                          'order_aggr', \\\n", "                          'total_buy_qty', 'total_sell_qty', \\\n", "                          'sm_order_cum_amt', 'mid_order_cum_amt', 'lg_order_cum_amt', 'slg_order_cum_amt']\n", "\n", "        if show_progress_bar:\n", "            iterator = tqdm(range(raw.shape[0]))\n", "        else:\n", "            iterator = range(raw.shape[0])\n", "        \n", "        for idx in iterator:\n", "        \n", "            # time = tick['time']\n", "            # date, time_hms = time.split()\n", "\n", "            date = raw.RecvTime.iloc[0].split()[0]\n", "            time = date+' '+raw.TransactTime.iloc[idx]\n", "            time_dt = datetime.datetime.strptime(time, '%Y-%m-%d %H:%M:%S.%f')\n", "            time_hms = str(time_dt.time())\n", "            \n", "            if (time_hms > '09:25:00.000000' and time_hms < '14:57:00.000000') or \\\n", "               idx == raw_len:\n", "                whether_snapshot = True\n", "            else:\n", "                whether_snapshot = False\n", "            \n", "            if time_hms > '09:25:00.000000' and not bid1 and not ask1:\n", "                sorted_price_levels = list(sorted(orderbook.keys()))\n", "                if total_buy_qty == 0:\n", "                    bid1 = sorted_price_levels[0]\n", "                    bid1_index = 0\n", "                    ask1 = sorted_price_levels[0]\n", "                    ask1_index = 0\n", "                elif total_sell_qty == 0:\n", "                    bid1 = sorted_price_levels[-1]\n", "                    bid1_index = len(sorted_price_levels)-1 \n", "                    ask1 = sorted_price_levels[-1]\n", "                    ask1_index = len(sorted_price_levels)-1\n", "                else:\n", "                    num_lvs = len(sorted_price_levels)\n", "                    for i in range(num_lvs):\n", "                        bid = sorted_price_levels[max(i-1,0)]\n", "                        ask = sorted_price_levels[max(i,0)]\n", "                        if (len(orderbook[bid][1]) == 0 and \\\n", "                            len(orderbook[bid][0]) > 0 and \\\n", "                            len(orderbook[ask][0]) == 0 and \\\n", "                            len(orderbook[ask][1]) > 0): \n", "                            bid1 = bid\n", "                            bid1_index = i-1\n", "                            ask1 = ask\n", "                            ask1_index = i\n", "                            break  \n", "\n", "                if complete_orderbook:\n", "\n", "                    tick_info = [date+' 09:25:00.000000']\n", "                    tick_info += [dict(sorted(copy.deepcopy(orderbook).items())[::-1])]\n", "\n", "                    df.append(tick_info)\n", "                    \n", "                else:\n", "                \n", "                    bid1_price = bid1\n", "                    if bid1_price in orderbook_vol:\n", "                        bid1_index = sorted_price_levels.index(bid1_price)\n", "                \n", "                    ask1_price = ask1\n", "                    if ask1_price in orderbook_vol:\n", "                        ask1_index = sorted_price_levels.index(ask1_price)\n", "            \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "                    mid_prices.append(mid_price)\n", "                    bid_prices = []\n", "                    bid_qtys = []\n", "                    ask_prices = []\n", "                    ask_qtys = []\n", "                    \n", "                    for i in range(100):\n", "                        try_bid_index = max(bid1_index-i, 0)\n", "                        try_bid_price = sorted_price_levels[try_bid_index]\n", "                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:\n", "                            bid_prices.append(try_bid_price)\n", "                            bid_qtys.append(orderbook_vol[try_bid_price][0])\n", "                        if len(bid_prices) >= 10:\n", "                            break\n", "                    for i in range(100):\n", "                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)\n", "                        try_ask_price = sorted_price_levels[try_ask_index]\n", "                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:\n", "                            ask_prices.append(try_ask_price)\n", "                            ask_qtys.append(orderbook_vol[try_ask_price][1])\n", "                        if len(ask_prices) >= 10:\n", "                            break\n", "\n", "                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:10]]\n", "                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:10]]\n", "\n", "                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:10]]\n", "                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:10]]\n", "\n", "                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]\n", "\n", "                    tick_info = [date+' 09:25:00.000000']\n", "                    \n", "                    tick_info += bid_prices\n", "                    tick_info += bid_qtys\n", "                    tick_info += ask_prices\n", "                    tick_info += ask_qtys\n", "                    tick_info += bid_num_orders\n", "                    tick_info += ask_num_orders\n", "                    tick_info += bid_lg_qtys\n", "                    tick_info += ask_lg_qtys\n", "                    tick_info += price_tier_arr\n", "                    \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "            \n", "                    curr_money = cum_money - prev_cum_money\n", "                    prev_cum_money = cum_money\n", "            \n", "                    curr_vol = cum_vol - prev_cum_vol\n", "                    prev_cum_vol = cum_vol\n", "\n", "                    tick_direc = 0\n", "                    tick_info += [prev_close, latest, mid_price, spread, \\\n", "                                  tick_type, curr_vol, curr_money, tick_direc, \\\n", "                                  order_aggr, \\\n", "                                  total_buy_qty, total_sell_qty, \\\n", "                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]\n", "                    \n", "                    df.append(tick_info)            \n", "                    \n", "\n", "            #################################\n", "            ######### Place Order ###########\n", "            #################################\n", "            \n", "            if raw.ExecType.iloc[idx] == 70:\n", "                data_type = 'transaction'\n", "            elif raw.ExecType.iloc[idx] == 52:\n", "                data_type = 'cancel'\n", "            else:\n", "                data_type = 'order'\n", "            \n", "            if data_type == 'order':\n", "                direction = 1 if raw.Side.iloc[idx] == 49 else 2\n", "                order_qty = raw.OrderQty.iloc[idx]\n", "                order_index = int(raw.ApplSeqNum.iloc[idx])\n", "\n", "                order_type = raw.OrdType.iloc[idx]\n", "                if order_type == 49:\n", "                    order_price = ask1 if direction == 1 else bid1\n", "                elif order_type == 50:\n", "                    order_price = int(round(raw.Price.iloc[idx]*1000))\n", "                elif order_type == 85:\n", "                    order_price = bid1 if direction == 1 else ask1\n", "        \n", "                # if tick['order_type'] == 1:\n", "                #     if tick['order_price'] == '' or \\\n", "                #        abs(tick['order_price']-1.0) < 1e-8 or \\\n", "                #        (direction == 1 and abs(tick['order_price']*1000-lower_limit)<1e-8) or \\\n", "                #        (direction == 2 and abs(tick['order_price']*1000-upper_limit)<1e-8):\n", "                #         if direction == 1:\n", "                #             order_price = ask1\n", "                #         else:\n", "                #             order_price = bid1\n", "                #     else:\n", "                #         order_price = int(round(tick['order_price']*1000))\n", "                # elif tick['order_type'] == 2:\n", "                #     order_price = int(round(tick['order_price']*1000))\n", "                # elif tick['order_type'] == 3:\n", "                #     if direction == 1:\n", "                #         order_price = bid1\n", "                #     else:\n", "                #         order_price = ask1   \n", "\n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    order_aggr = order_price/((bid1+ask1)*0.5)\n", "                  \n", "                orderpool[order_index] = [order_price, order_qty, direction]\n", "        \n", "                if order_price not in orderbook: \n", "                    orderbook[order_price] = [{},{}]\n", "                    orderbook[order_price][direction-1][order_index] = order_qty\n", "                else:\n", "                    orderbook[order_price][direction-1][order_index] = order_qty\n", "        \n", "                if order_price not in orderbook_vol:\n", "                    orderbook_vol[order_price] = [0, 0]\n", "                orderbook_vol[order_price][direction-1] += order_qty\n", "        \n", "        \n", "                if order_qty <= mid_threshold:\n", "                    orderpool_sm.add(order_index)\n", "                elif order_qty > mid_threshold and order_qty <= lg_threshold:\n", "                    orderpool_mid.add(order_index)\n", "                elif order_qty > lg_threshold and order_qty <= slg_threshold:\n", "                    orderpool_lg.add(order_index)\n", "                elif order_qty > slg_threshold:\n", "                    orderpool_slg.add(order_index)\n", "        \n", "                if order_qty > lg_threshold:\n", "                    if order_price not in orderbook_vol_lg:\n", "                        orderbook_vol_lg[order_price] = [0, 0]\n", "                    orderbook_vol_lg[order_price][direction-1] += order_qty\n", "                        \n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "        \n", "                    if direction == 1:\n", "                        if (order_price > bid1 and order_price < ask1) or \\\n", "                           (total_buy_qty == 0 and order_price < ask1) or \\\n", "                           (total_sell_qty == 0 and order_price > bid1):\n", "                            bid1 = order_price\n", "                    else:\n", "                        if (order_price > bid1 and order_price < ask1) or \\\n", "                           (total_buy_qty == 0 and order_price < ask1) or \\\n", "                           (total_sell_qty == 0 and order_price > bid1):\n", "                            ask1 = order_price      \n", "                            \n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook) :\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "        \n", "                if direction == 1:\n", "                    total_buy_qty += order_qty\n", "                else:\n", "                    total_sell_qty += order_qty\n", "                \n", "                tick_type = 0\n", "                tick_vol = order_qty \n", "                tick_money = (order_price/1000)*order_qty\n", "                tick_direc = direction \n", "\n", "                target_tier = tier_dict[order_price]\n", "                orderbook_tier[target_tier] += order_qty\n", "        \n", "        \n", "            #################################\n", "            ######### Cancel Order ##########\n", "            #################################\n", "            \n", "            \n", "            elif data_type == 'cancel':\n", "                trade_qty = raw.LastQty.iloc[idx]\n", "                # direction = tick['trade_bs_flag']\n", "                # trade_price = raw.LastPx.iloc[idx] \n", "                \n", "                order_aggr = 1\n", "                \n", "                # if tick['trade_buy_no'] == '':\n", "                #     cancel_index = str(tick['trade_sell_no'])\n", "                # else:\n", "                #     cancel_index = str(tick['trade_buy_no'])\n", "                cancel_index = int(max(raw.BidApplSeqNum.iloc[idx], raw.OfferApplSeqNum.iloc[idx]))\n", "                \n", "                cancel_price, _, direction = orderpool[cancel_index]\n", "                \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    if bid1 == cancel_price and trade_qty == orderbook_vol[cancel_price][0]:\n", "                        for price in range(bid1-10, lower_limit-10, -10):\n", "                            if price in orderbook_vol:\n", "                                if orderbook_vol[price][0] > 0:\n", "                                    bid1 = price\n", "                                    break\n", "                    elif ask1 == cancel_price and trade_qty == orderbook_vol[cancel_price][1]:\n", "                        for price in range(ask1+10, upper_limit+10, 10):\n", "                            if price in orderbook_vol:\n", "                                if orderbook_vol[price][1] > 0:\n", "                                    ask1 = price\n", "                                    break\n", "        \n", "                orderbook[cancel_price][direction-1].pop(cancel_index)\n", "                orderpool.pop(cancel_index)\n", "                if len(orderbook[cancel_price][0]) == len(orderbook[cancel_price][1]) == 0:\n", "                    orderbook.pop(cancel_price)\n", "        \n", "                if direction == 1:\n", "                    total_buy_qty -= trade_qty\n", "                else:\n", "                    total_sell_qty -= trade_qty\n", "        \n", "                orderbook_vol[cancel_price][direction-1] -= trade_qty\n", "                if orderbook_vol[cancel_price][0] == 0 and \\\n", "                   orderbook_vol[cancel_price][1] == 0:\n", "                    orderbook_vol.pop(cancel_price)\n", "        \n", "                if cancel_index in orderpool_lg:\n", "                    orderbook_vol_lg[cancel_price][direction-1] -= trade_qty\n", "                    if orderbook_vol_lg[cancel_price][0] == 0 and \\\n", "                       orderbook_vol_lg[cancel_price][1] == 0:\n", "                        orderbook_vol_lg.pop(cancel_price)\n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook) :\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "        \n", "                tick_type = 1\n", "                tick_vol = trade_qty \n", "                tick_money = trade_qty * (cancel_price/1000)\n", "                tick_direc = direction \n", "\n", "                target_tier = tier_dict[cancel_price]\n", "                orderbook_tier[target_tier] -= trade_qty\n", "        \n", "        \n", "            ################################\n", "            ######### Transaction ##########\n", "            ################################\n", "                    \n", "        \n", "            elif data_type == 'transaction':\n", "                trade_qty = raw.LastQty.iloc[idx]\n", "                # direction = tick['trade_bs_flag']\n", "                trade_price = int(round(raw.LastPx.iloc[idx]*1000))\n", "                latest = trade_price\n", "                trade_money = trade_price*0.001*trade_qty\n", "                trade_vol = trade_qty\n", "                cancel_vol = 0\n", "                order_vol = 0\n", "        \n", "                order_aggr = 1\n", "                \n", "                buy_order = int(raw.BidApplSeqNum.iloc[idx])\n", "                sell_order = int(raw.OfferApplSeqNum.iloc[idx])\n", "                direction = 1 if buy_order > sell_order else 2\n", "                \n", "                buy_order_price, buy_order_qty, _ = orderpool[buy_order]\n", "                sell_order_price, sell_order_qty, _ = orderpool[sell_order]\n", "                \n", "                if buy_order in orderpool_sm:\n", "                    sm_order_cum_vol += trade_qty\n", "                if sell_order in orderpool_sm:\n", "                    sm_order_cum_vol -= trade_qty\n", "        \n", "                if buy_order in orderpool_mid:\n", "                    mid_order_cum_vol += trade_qty\n", "                if sell_order in orderpool_mid:\n", "                    mid_order_cum_vol -= trade_qty\n", "        \n", "                if buy_order in orderpool_lg:\n", "                    lg_order_cum_vol += trade_qty\n", "                if sell_order in orderpool_lg:\n", "                    lg_order_cum_vol -= trade_qty\n", "        \n", "                if buy_order in orderpool_slg:\n", "                    slg_order_cum_vol += trade_qty\n", "                if sell_order in orderpool_slg:\n", "                    slg_order_cum_vol -= trade_qty\n", "                        \n", "                if trade_price in orderbook_vol:\n", "                    original_trade_price_qty_buy = orderbook_vol[trade_price][0]\n", "                    original_trade_price_qty_sell = orderbook_vol[trade_price][1]\n", "                else:\n", "                    original_trade_price_qty_buy = None\n", "                    original_trade_price_qty_sell = None\n", "            \n", "                orderpool[buy_order][1] -= trade_qty\n", "                orderpool[sell_order][1] -= trade_qty\n", "                if orderpool[buy_order][1] < 0.1:\n", "                    orderpool.pop(buy_order)\n", "                if orderpool[sell_order][1] < 0.1:\n", "                    orderpool.pop(sell_order)\n", "        \n", "                orderbook[buy_order_price][0][buy_order] -= trade_qty\n", "                orderbook[sell_order_price][1][sell_order] -= trade_qty\n", "                if orderbook[buy_order_price][0][buy_order] < 0.1:\n", "                    orderbook[buy_order_price][0].pop(buy_order)\n", "                if orderbook[sell_order_price][1][sell_order] < 0.1:\n", "                    orderbook[sell_order_price][1].pop(sell_order)\n", "        \n", "                if len(orderbook[buy_order_price][0]) == len(orderbook[buy_order_price][1]) == 0:\n", "                    orderbook.pop(buy_order_price)\n", "                if sell_order_price in orderbook:\n", "                    if len(orderbook[sell_order_price][0]) == len(orderbook[sell_order_price][1]) == 0:\n", "                        orderbook.pop(sell_order_price)\n", "        \n", "                total_buy_qty -= trade_qty\n", "                total_sell_qty -= trade_qty\n", "        \n", "                orderbook_vol[buy_order_price][0] -= trade_qty\n", "                orderbook_vol[sell_order_price][1] -= trade_qty\n", "        \n", "                if orderbook_vol[buy_order_price][0] == 0 and \\\n", "                   orderbook_vol[buy_order_price][1] == 0:\n", "                    orderbook_vol.pop(buy_order_price)\n", "                if sell_order_price in orderbook_vol:\n", "                    if orderbook_vol[sell_order_price][0] == 0 and \\\n", "                       orderbook_vol[sell_order_price][1] == 0:\n", "                        orderbook_vol.pop(sell_order_price)\n", "        \n", "                if buy_order in orderpool_lg:\n", "                    orderbook_vol_lg[buy_order_price][0] -= trade_qty\n", "                    if orderbook_vol_lg[buy_order_price][0] == 0 and \\\n", "                       orderbook_vol_lg[buy_order_price][1] == 0:\n", "                        orderbook_vol_lg.pop(buy_order_price)\n", "        \n", "                if sell_order in orderpool_lg:\n", "                    orderbook_vol_lg[sell_order_price][1] -= trade_qty\n", "                    if orderbook_vol_lg[sell_order_price][0] == 0 and \\\n", "                       orderbook_vol_lg[sell_order_price][1] == 0:\n", "                        orderbook_vol_lg.pop(sell_order_price)\n", "        \n", "                if bid1 and ask1 and whether_snapshot:\n", "                    if len(sorted_price_levels) == 0 or len(sorted_price_levels) != len(orderbook):\n", "                        sorted_price_levels = list(sorted(orderbook.keys()))\n", "        \n", "                    if total_buy_qty == 0:\n", "                        bid1 = sorted_price_levels[0]\n", "                        ask1 = sorted_price_levels[0]\n", "                    elif total_sell_qty == 0:\n", "                        bid1 = sorted_price_levels[-1] \n", "                        ask1 = sorted_price_levels[-1]\n", "                    else:     \n", "                        if direction == 1:\n", "                            for price in range(trade_price, upper_limit+10, 10):\n", "                                if price in orderbook_vol:\n", "                                    if orderbook_vol[price][1] > 0:\n", "                                        ask1 = price\n", "                                        break\n", "                            for price in range(ask1, lower_limit-10, -10):\n", "                                if price in orderbook_vol:\n", "                                    # counter += 1\n", "                                    if orderbook_vol[price][0] > 0:\n", "                                        bid1 = price\n", "                                        break\n", "            \n", "                        elif direction == 2:\n", "                            for price in range(trade_price, lower_limit-10, -10):\n", "                                if price in orderbook_vol:\n", "                                    if orderbook_vol[price][0] > 0:\n", "                                        bid1 = price\n", "                                        break\n", "                            for price in range(bid1, upper_limit+10, 10):\n", "                                if price in orderbook_vol:\n", "                                    # counter += 1 \n", "                                    if orderbook_vol[price][1] > 0:\n", "                                        ask1 = price\n", "                                        break\n", "                    \n", "                cum_money += trade_money     \n", "                cum_vol += trade_qty\n", "                \n", "                tick_type = 2\n", "                tick_vol = trade_qty \n", "                tick_money = trade_money\n", "                tick_direc = direction \n", "\n", "                target_tier = tier_dict[buy_order_price]\n", "                orderbook_tier[target_tier] -= trade_qty\n", "                target_tier = tier_dict[sell_order_price]\n", "                orderbook_tier[target_tier] -= trade_qty\n", "                            \n", "                  \n", "            if idx == raw_len:\n", "                sorted_price_levels = list(sorted(orderbook.keys()))\n", "                if total_buy_qty == 0:\n", "                    bid1 = sorted_price_levels[0]\n", "                    bid1_index = 0\n", "                    ask1 = sorted_price_levels[0]\n", "                    ask1_index = 0\n", "                elif total_sell_qty == 0:\n", "                    bid1 = sorted_price_levels[-1]\n", "                    bid1_index = len(sorted_price_levels)-1\n", "                    ask1 = sorted_price_levels[-1]\n", "                    ask1_index = len(sorted_price_levels)-1\n", "                else:\n", "                    num_lvs = len(sorted_price_levels)\n", "                    for i in range(num_lvs):\n", "                        bid = sorted_price_levels[max(i-1,0)]\n", "                        ask = sorted_price_levels[max(i,0)]\n", "                        if (len(orderbook[bid][1]) == 0 and \\\n", "                            len(orderbook[bid][0]) > 0 and \\\n", "                            len(orderbook[ask][0]) == 0 and \\\n", "                            len(orderbook[ask][1]) > 0): \n", "                            bid1 = bid\n", "                            ask1 = ask\n", "                            break  \n", "        \n", "        \n", "            if whether_snapshot:\n", "                \n", "                bid1_price = bid1\n", "                if bid1_price in orderbook_vol:\n", "                    bid1_index = sorted_price_levels.index(bid1_price)\n", "            \n", "                ask1_price = ask1\n", "                if ask1_price in orderbook_vol:\n", "                    ask1_index = sorted_price_levels.index(ask1_price)\n", "\n", "                if complete_orderbook:\n", "\n", "                    # orderbook = dict(sorted(orderbook.items())[::-1])\n", "                    orderbook_vol = dict(sorted(orderbook_vol.items())[::-1])\n", "                    # orderbook_vol_lg = dict(sorted(orderbook_vol_lg.items())[::-1])\n", "\n", "                    tick_info = [time]\n", "                    tick_info += [\n", "                        orderbook\n", "                    ]\n", "                    df.append(tick_info)\n", "\n", "                else:\n", "        \n", "                    bid1_price = bid1\n", "                    if bid1_price in orderbook_vol:\n", "                        bid1_index = sorted_price_levels.index(bid1_price)\n", "                \n", "                    ask1_price = ask1\n", "                    if ask1_price in orderbook_vol:\n", "                        ask1_index = sorted_price_levels.index(ask1_price)\n", "            \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "                    mid_prices.append(mid_price)\n", "                    bid_prices = []\n", "                    bid_qtys = []\n", "                    ask_prices = []\n", "                    ask_qtys = []\n", "                    \n", "                    for i in range(100):\n", "                        try_bid_index = max(bid1_index-i, 0)\n", "                        try_bid_price = sorted_price_levels[try_bid_index]\n", "                        if orderbook_vol[try_bid_price][0] > 0 or (bid1_index-i) <= 0:\n", "                            bid_prices.append(try_bid_price)\n", "                            bid_qtys.append(orderbook_vol[try_bid_price][0])\n", "                        if len(bid_prices) >= 10:\n", "                            break\n", "                    for i in range(100):\n", "                        try_ask_index = min(ask1_index+i, len(sorted_price_levels)-1)\n", "                        try_ask_price = sorted_price_levels[try_ask_index]\n", "                        if orderbook_vol[try_ask_price][1] > 0 or (ask1_index+i) >= len(sorted_price_levels)-1:\n", "                            ask_prices.append(try_ask_price)\n", "                            ask_qtys.append(orderbook_vol[try_ask_price][1])\n", "                        if len(ask_prices) >= 10:\n", "                            break\n", "\n", "                    bid_num_orders = [len(orderbook[p][0]) for p in bid_prices[:10]]\n", "                    ask_num_orders = [len(orderbook[p][1]) for p in ask_prices[:10]]\n", "\n", "                    bid_lg_qtys = [orderbook_vol_lg[p][0] if p in orderbook_vol_lg else 0 for p in bid_prices[:10]]\n", "                    ask_lg_qtys = [orderbook_vol_lg[p][1] if p in orderbook_vol_lg else 0 for p in ask_prices[:10]]\n", "\n", "                    price_tier_arr = [orderbook_tier[p] for p in orderbook_tier]\n", "\n", "                    tick_info = [time]\n", "                    \n", "                    tick_info += bid_prices\n", "                    tick_info += bid_qtys\n", "                    tick_info += ask_prices\n", "                    tick_info += ask_qtys\n", "                    tick_info += bid_num_orders\n", "                    tick_info += ask_num_orders\n", "                    tick_info += bid_lg_qtys\n", "                    tick_info += ask_lg_qtys\n", "                    tick_info += price_tier_arr\n", "                    \n", "                    mid_price = (bid1+ask1)/2\n", "                    spread = ask1 - bid1\n", "            \n", "                    if tick_type == 2:\n", "                        curr_money = cum_money - prev_cum_money\n", "                        prev_cum_money = cum_money\n", "                \n", "                        curr_vol = cum_vol - prev_cum_vol\n", "                        prev_cum_vol = cum_vol\n", "            \n", "                    if idx == raw_len:\n", "                        tick_vol = curr_vol\n", "                        tick_money = curr_money\n", "\n", "                    tick_direc = 0\n", "                    tick_info += [prev_close, latest, mid_price, spread, \\\n", "                                  tick_type, tick_vol, tick_money, tick_direc, \\\n", "                                  order_aggr, \\\n", "                                  total_buy_qty, total_sell_qty, \\\n", "                                  sm_order_cum_vol, mid_order_cum_vol, lg_order_cum_vol, slg_order_cum_vol]\n", "                    \n", "                    df.append(tick_info)            \n", "            \n", "        orderbook = {key: orderbook[key] for key in sorted(orderbook.keys())[::-1]} \n", "        orderbook_vol = {key: orderbook_vol[key] for key in sorted(orderbook_vol.keys())[::-1]} \n", "        orderbook_vol_lg = {key: orderbook_vol_lg[key] for key in sorted(orderbook_vol_lg.keys())[::-1]} \n", "        \n", "        df = pd.DataFrame(df)\n", "        df.columns = col_names   \n", "        df = df.set_index(['time'])\n", "        df.index = pd.to_datetime(df.index)\n", "    \n", "        # return df, orderbook_vol, orderbook_tier\n", "        return df\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "f10e6196-62c3-4437-b7bf-f56e47c50a5f", "metadata": {}, "outputs": [], "source": ["%%time\n", "\n", "df = reconstruct_orderbook(stock='002839', date='2025-05-16', show_progress_bar=True, complete_orderbook=False)"]}, {"cell_type": "code", "execution_count": null, "id": "d737e492-cce3-4597-bed0-c0d6b848b260", "metadata": {"scrolled": true}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "60a61086-5aff-49ed-9abf-d0b8ca7f991d", "metadata": {}, "outputs": [], "source": ["# def func(stock, date, hl_limits, show_progress_bar=True, complete_orderbook=False):\n", "\n", "#     try:\n", "#         stock_ = stock.split('.')[0]\n", "#         # print(stock_)\n", "#         df = reconstruct_orderbook(stock=stock_, date=date, hl_limits=hl_limits, show_progress_bar=show_progress_bar, complete_orderbook=complete_orderbook)\n", "#         df.to_parquet('/A/pk/0516/ob/'+str(stock_)+'.parquet')\n", "#     except:\n", "#         flog = open('log/error_'+str(os.getpid())+'.log','w')\n", "#         print(stock, file=flog)\n", "\n", "\n", "# res = Parallel(n_jobs=40)(delayed(func)(stock=stock, date='2025-05-16', hl_limits=limits, show_progress_bar=False, complete_orderbook=False) for stock in tqdm(stocks[1000:]))"]}, {"cell_type": "code", "execution_count": null, "id": "88f19a37-10d5-439c-bc9e-f3a1c395c02f", "metadata": {"scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d9803e68-fbf9-4233-89c1-bdec0ad97e22", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "329363ff-d0ef-4e72-a957-4310f753d74b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cef60796-7f36-4945-a65a-84f34e362b6d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "edc36b84-7b71-4199-980b-23afd42cbb62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "43ee386f-4348-4aa3-9c2f-630fe63f2408", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "452be312-079e-41b8-bf09-c1b095e9710e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}