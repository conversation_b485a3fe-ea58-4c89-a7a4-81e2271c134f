#!/bin/bash
#
# 用 Python 合并生成的深交所逐笔 Parquet 文件测试深交所逐笔转全速快照程序
#

if [ $# -lt 2 ]; then
        echo "Usage: $0 YYYYMMDD|YYMMDD|MMDD|MDD|DD|D SecID"
	echo "SecID 为深交所证券代码，只取前6个字符，后面多余字符忽略"
        exit
fi

datestr=$1
datel=${#datestr}  # 日期字符串长度
if [ $datel -eq 8 ]; then   # YYYYMMDD
	y=${datestr:0:4}
	m=${datestr:4:2}
	d=${datestr:6:2}
elif [ $datel -eq 6 ]; then   # YYMMDD
	y=20${datestr:0:2}
	m=${datestr:2:2}
	d=${datestr:4:2}
elif [ $datel -eq 4 ]; then   # MMDD
	y=`date +%Y`
	m=${datestr:0:2}
	d=${datestr:2:2}
elif [ $datel -eq 3 ]; then   # MDD
	y=`date +%Y`
	m=0${datestr:0:1}
	d=${datestr:1:2}
elif [ $datel -eq 2 ]; then   # DD
	y=`date +%Y`
	m=`date +%m`
	d=${datestr}
elif [ $datel -eq 1 ]; then  # D
	y=`date +%Y`
	m=`date +%m`
	d=0${datestr}
fi

s=$2
s=${s:0:6}

prices=`csvgrep -c ticker -m $s /A/md/tao/${y}-${m}-${d}/limits_${y}${m}${d}.csv | csvcut -c limitUpPrice,limitDownPrice,preClosePrice| tail -1`
#echo $prices

h=`echo $prices | cut -d, -f1`
h=`echo "$h * 1000" | bc | cut -d. -f1`  # 涨停价，厘

l=`echo $prices | cut -d, -f2`
l=`echo "$l * 1000" | bc | cut -d. -f1`  # 跌停价，厘

c=`echo $prices | cut -d, -f3`
c=`echo "$c * 1000" | bc | cut -d. -f1`  # 昨收价，厘

ticksize=`redis-cli -h 168.36.1.170 HGET STKLST2:${s} TickSize`
ticksize=`echo "$ticksize * 1000" | bc | cut -d. -f1`  # 最小报价变动单位，厘

#echo "/home/<USER>/git/koic/src/md/datayes/build/dyszotsnap -s $s -u $h -w $l -v $c -i $ticksize -x 0 -q /A/md/orderbook/raw/${y}-${m}-${d}/${s}.parquet" > /dev/stderr
#/home/<USER>/git/koic/src/md/datayes/build/dyszotsnap -s $s -u $h -w $l -v $c -i $ticksize -z 0 -x 0 -q /A/md/orderbook/raw/${y}-${m}-${d}/${s}.parquet

echo "dyszotsnap -s $s -u $h -w $l -v $c -i $ticksize -x 0 -q /A/md/orderbook/raw/${y}-${m}-${d}/${s}.parquet" > /dev/stderr
dyszotsnap -s $s -u $h -w $l -v $c -i $ticksize -z 0 -x 0 -q /A/md/orderbook/raw/${y}-${m}-${d}/${s}.parquet
