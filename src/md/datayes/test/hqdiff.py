#!/usr/bin/python3

import zmq
import sys
from datetime import datetime
import threading
import quota_pb2 as LYPBQuota
import copy
import time

lastes_dy = None
lastes_ht = None
lastes_zc = None

t_dy = {'source': 'Tong Lian', 'time': time.perf_counter()}
t_ht = {'source': 'Hua Tai', 'time': time.perf_counter()}
t_zc = {'source': 'Zhong Chang', 'time': time.perf_counter()}

def pb_parse_s(msg:bytes, source):
    global lastes_dy, lastes_ht, lastes_zc, t_dy, t_ht, t_zc
    msglen = int(msg[7:15])
    pbmsg = msg[15: 15 + msglen]
    o = LYPBQuota.MarketData()
    o.ParseFromString(pbmsg)
    now = datetime.now()
    nowp = time.perf_counter()
    mylog = list()
    mylog.append(f'=========== {source} {now} =============')
    if source == "Tong Lian":
        lastes_dy = copy.copy(o)
        t_dy['time'] = nowp
    elif source == "Hua Tai":
        lastes_ht = copy.copy(o)
        t_ht['time'] = nowp
    else:
        lastes_zc = copy.copy(o)
        t_zc['time'] = nowp
    if lastes_dy is None or lastes_ht is None or lastes_zc is None:
        return
    # print(o)
    if o.Category == 'I':
        mylog.append(f'exch_time: {o.ExchTime}, latest: {o.Latest}, open: {o.Open}, high: {o.High}, low: {o.Low}, vol: {o.Volume}, value: {o.Value}, knock: {o.Knock}, status: {o.Status}')
        mylog.append(f'recv_time: {o.RcvSvrTime}')
    else:
        mylog.append(f'exch_time: {o.ExchTime}, latest: {o.Latest}, open: {o.Open}, high: {o.High}, low: {o.Low}, vol: {o.Volume}, value: {o.Value}, knock: {o.Knock}, status: {o.Status}, SP0: {o.SP[0]}, SP1: {o.SP[1]}')
        mylog.append(f'SP2: {o.SP[2]}, BP0: {o.BP[0]}, BP1: {o.BP[1]}, BP2: {o.BP[2]}, BA0: {o.BA[0]}, BA1: {o.BA[1]}, BA2: {o.BA[2]}, SA0: {o.SA[0]}, SA1: {o.SA[1]}, SA2: {o.SA[2]}')
        mylog.append(f'TotalBA: {o.TotalBA}, TotalSA: {o.TotalSA}, WeightedAvgBidPx: {o.WeightedAvgBidPx}, WeightedAvgOfferPx: {o.WeightedAvgOfferPx}, IOPV: {o.IOPV}')
        mylog.append(f'recv_time: {o.RcvSvrTime}')

    #if o.ExchTime != o.RcvSvrTime:
        #print(f'XXXXXXXXXXXXXXX XXXXXXXXXXXX XXXXXXXXXXX ExchTime != RcvSvrTime')

    nowi = now.hour * 10000000 + now.minute * 100000 + now.second * 1000 + now.microsecond // 1000
    delay = nowi - int(o.ExchTime)
    mylog.append(f'delay: {delay} ({nowi} - {o.ExchTime})')

    if lastes_dy is not None and lastes_ht is not None and lastes_zc is not None:
        if abs(int(lastes_dy.ExchTime) - int(lastes_zc.ExchTime)) < 1500 and abs(int(lastes_dy.ExchTime) - int(lastes_ht.ExchTime)) < 1500:
            if lastes_dy.Latest != lastes_zc.Latest or lastes_dy.Latest != lastes_ht.Latest:
                mylog.append(f'Latest: dy-ht {lastes_dy.Latest} != {lastes_ht.Latest} !!!!!!!!!!!!!')
            if lastes_dy.Open != lastes_zc.Open or lastes_dy.Open != lastes_ht.Open:
                mylog.append(f'Open: dy-ht {lastes_dy.Open} != {lastes_ht.Open} !!!!!!!!!!!!!')
            if lastes_dy.High != lastes_zc.High or lastes_dy.High != lastes_ht.High:
                mylog.append(f'High: dy-ht {lastes_dy.High} != {lastes_ht.High} !!!!!!!!!!!!!')
            if lastes_dy.Low != lastes_zc.Low or lastes_dy.Low != lastes_ht.Low:
                mylog.append(f'Low: dy-ht {lastes_dy.Low} != {lastes_ht.Low} !!!!!!!!!!!!!')
            if lastes_dy.Volume != lastes_zc.Volume or lastes_dy.Volume != lastes_ht.Volume:
                mylog.append(f'Volume: dy-ht {lastes_dy.Volume} != {lastes_ht.Volume} !!!!!!!!!!!!!')
            if lastes_dy.Value != lastes_zc.Value or lastes_dy.Value != lastes_ht.Value:
                mylog.append(f'Value: dy-ht {lastes_dy.Value} != {lastes_ht.Value} !!!!!!!!!!!!!')
            if lastes_dy.Knock != lastes_zc.Knock or lastes_dy.Knock != lastes_ht.Knock:
                mylog.append(f'Knock: dy-ht {lastes_dy.Knock} != {lastes_ht.Knock} !!!!!!!!!!!!!')
            if lastes_dy.Status != lastes_zc.Status or lastes_dy.Status != lastes_ht.Status:
                mylog.append(f'Status: dy-ht {lastes_dy.Status} != {lastes_ht.Status} !!!!!!!!!!!!!')

            r = sorted([t_dy, t_ht, t_zc], key=lambda x: x['time'])
            mylog.append(f'{r[0]["source"]} ********** {r[1]["source"]}: +{r[1]["time"] - r[0]["time"]:.3f} *********** {r[2]["source"]}: +{r[2]["time"] - r[0]["time"]:.3f}')
            mylog.append(f'-------------------------')

    print("\n".join(mylog))


# 接收线程函数
def receive_messages(socket, source_name):
    while True:
        try:
            content = socket.recv()
            pb_parse_s(content, source_name)
        except zmq.ZMQError as e:
            print(f"ZMQ Error in {source_name} thread: {e}")
            break
        except Exception as e:
            print(f"Error in {source_name} thread: {e}")
            continue

# 主程序
def main():
    context = zmq.Context()

    # 创建并配置第一个socket (通联)
    socket_dy = context.socket(zmq.SUB)
    # 用当前生产机上主账号测试旧版 dytk
    # dy_server = "tcp://168.36.1.167:9869"
    # 用本机上备账号测试最新版 dytk
    dy_server = "tcp://168.36.1.52:9869"
    socket_dy.connect(dy_server)

    # 创建并配置第二个socket (华泰)
    socket_ht = context.socket(zmq.SUB)
    socket_ht.connect("tcp://168.36.1.173:9869")

    # 创建并配置第三个socket (中畅)
    socket_zc = context.socket(zmq.SUB)
    socket_zc.connect("tcp://168.36.1.90:9869")

    # 获取命令行参数中的股票代码，默认为贵州茅台
    if len(sys.argv) > 1:
        stock = sys.argv[1]
    else:
        stock = 'S600519'

    # 设置订阅
    socket_dy.setsockopt_string(zmq.SUBSCRIBE, stock)
    socket_ht.setsockopt_string(zmq.SUBSCRIBE, stock)
    socket_zc.setsockopt_string(zmq.SUBSCRIBE, stock)

    # 创建并启动接收线程
    dy_thread = threading.Thread(target=receive_messages, args=(socket_dy, "Tong Lian"), daemon=True)
    ht_thread = threading.Thread(target=receive_messages, args=(socket_ht, "Hua Tai"), daemon=True)
    zc_thread = threading.Thread(target=receive_messages, args=(socket_zc, "Zhong Chang"), daemon=True)
    
    dy_thread.start()
    ht_thread.start()
    zc_thread.start()
    
    print(f"Started receiving messages for {stock}")
    print("Press Ctrl+C to exit")
    
    try:
        # 主线程等待，可以通过Ctrl+C中断
        while True:
            dy_thread.join(1.0)  # 每秒检查一次
            ht_thread.join(1.0)
            zc_thread.join(1.0)
            
            # 如果任一线程退出，则退出主程序
            if not dy_thread.is_alive() and not ht_thread.is_alive() and not zc_thread.is_alive():
                print("Three receiver threads have exited")
                break
            elif not dy_thread.is_alive():
                print("Tong Lian receiver thread has exited")
                break
            elif not ht_thread.is_alive():
                print("Hua Tai receiver thread has exited")
                break
            elif not zc_thread.is_alive():
                print("Zhong Chang receiver thread has exited")
                break
                
    except KeyboardInterrupt:
        print("\nExiting...")
    finally:
        # 关闭ZMQ套接字和上下文
        socket_dy.close()
        socket_ht.close()
        socket_zc.close()
        context.term()
        print("ZMQ connections closed")

if __name__ == "__main__":
    main()
