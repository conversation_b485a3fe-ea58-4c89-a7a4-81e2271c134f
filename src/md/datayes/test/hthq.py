#!/usr/local/bin/python3

import zmq
import datetime
import quota_pb2 as <PERSON>YP<PERSON><PERSON>uo<PERSON>

def pb_parse_s(msg:bytes):
    msglen = int(msg[7:15])
    pbmsg = msg[15: 15 + msglen]
    o = LYPBQuota.MarketData()
    o.ParseFromString(pbmsg)
    print(o)
    #print("{}: exchange time: {}, lastest: {}".format(datetime.datetime.now().strftime("%H:%M:%S.%f"), o.ExchTime, o.Latest))

context = zmq.Context()
socket = context.socket(zmq.SUB)
socket.connect("tcp://localhost:9869")
#socket.connect("tcp://***********:9870")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S510050")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S159922")
socket.setsockopt_string(zmq.SUBSCRIBE, "I000001")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399001")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399001")
#socket.setsockopt_string(zmq.SUBSCRIBE, "I399106")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S600519")
#socket.setsockopt_string(zmq.SUBSCRIBE, "S002520")

while True:
    content = socket.recv()
    pb_parse_s(content)

