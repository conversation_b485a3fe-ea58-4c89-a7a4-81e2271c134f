{"cells": [{"cell_type": "code", "execution_count": 23, "id": "5d54a23e-4055-4368-9a80-5252a5bf1254", "metadata": {}, "outputs": [], "source": ["from nb_common import *\n", "from collections import defaultdict"]}, {"cell_type": "code", "execution_count": 3, "id": "3f7e5ebc-b4a6-44bc-a445-8f97b859f7f4", "metadata": {}, "outputs": [], "source": ["f = open(\"/dev/shm/0512_new/a10_order.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "86c02609-8a6a-4e48-93ae-0614d0cb6598", "metadata": {}, "outputs": [{"data": {"text/plain": ["'RecvTime,Market,SecurityID,Date,Time,Index,BizIndex,OrderIndex,Channel,Price,Volume,Side,Type,DealMode,Delay\\n'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["f.readline()"]}, {"cell_type": "code", "execution_count": 5, "id": "425e12bb-0941-4601-a8c6-193590677e61", "metadata": {}, "outputs": [], "source": ["CONF_SID = \"600519\""]}, {"cell_type": "code", "execution_count": 36, "id": "12385573-86fe-41cc-8363-30148f80b5be", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["50288262it [01:38, 513022.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# CONF_SID = \"300059\"\n", "f = open(\"/dev/shm/0512_new/a10_order.csv\")\n", "f.readline()\n", "d_last_seq = defaultdict(int)\n", "d_last_cha = defaultdict(str)\n", "i = 0\n", "for line in tqdm(f):\n", "    try:\n", "        RecvTime,Market,SecurityID,Date,Time,Index,BizIndex,OrderIndex,Channel,Price,Volume,Side,Type,DealMode,Delay = line.split(\",\")\n", "    except:\n", "        print(line)\n", "        break\n", "    if Market[2] == 'Z': continue\n", "    icode = SecurityID\n", "    if d_last_cha[icode]:\n", "        if d_last_cha[icode] != Channel:\n", "            print(f\"Fatal chan [{icode}]: {d_last_cha[icode]} {Channel}\")\n", "            break\n", "            \n", "    else:\n", "        d_last_cha[icode] = Channel\n", "    \n", "    seqno = int(BizIndex)\n", "    if seqno < d_last_seq[icode]:\n", "        print(f\"Fatal seq [{icode}]: { d_last_seq[icode]} Biz = {seqno}\")\n", "        continue\n", "    d_last_seq[icode] = seqno\n", "    \n", "print(i)"]}, {"cell_type": "code", "execution_count": 40, "id": "b912296b-096c-4bcf-827d-0c8a18714764", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["151607101it [04:42, 537567.82it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# CONF_SID = \"300059\"\n", "f = open(\"/dev/shm/0512/a10_order.csv\")\n", "f.readline()\n", "d_last_seq = defaultdict(int)\n", "d_last_cha = defaultdict(str)\n", "i = 0\n", "for line in tqdm(f):\n", "    try:\n", "        RecvTime,Market,SecurityID,Date,Time,Index,BizIndex,OrderIndex,Channel,Price,Volume,Side,Type,DealMode = line.split(\",\")\n", "    except:\n", "        print(line)\n", "        break\n", "    if Market[2] == 'Z': continue  # 只看沪市\n", "    icode = SecurityID\n", "    if d_last_cha[icode]:\n", "        if d_last_cha[icode] != Channel:\n", "            print(f\"Fatal chan [{icode}]: {d_last_cha[icode]} {Channel}\")\n", "            break\n", "            \n", "    else:\n", "        d_last_cha[icode] = Channel\n", "    \n", "    seqno = int(BizIndex)   # 沪市用 BizIndex\n", "    if seqno < d_last_seq[icode]:\n", "        print(f\"Fatal seq [{icode}]: { d_last_seq[icode]} Biz = {seqno}\")\n", "        continue\n", "    d_last_seq[icode] = seqno\n", "    \n", "print(i)"]}, {"cell_type": "code", "execution_count": 42, "id": "ab2b7f57-27a4-45cf-b8a2-d24b08fd41e4", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["823905it [00:01, 425504.72it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002371]: 118173 Biz = 118169 Recv = 2025-05-12 01:15:04.033740170 LastRecv = 2025-05-12 01:15:04.033740170\n", "Fatal seq [300250]: 118175 Biz = 118172 Recv = 2025-05-12 01:15:04.033740170 LastRecv = 2025-05-12 01:15:04.033740170\n", "Fatal seq [300640]: 118171 Biz = 118170 Recv = 2025-05-12 01:15:04.033740170 LastRecv = 2025-05-12 01:15:04.033740170\n", "Fatal seq [002150]: 126609 Biz = 126608 Recv = 2025-05-12 01:15:04.033740170 LastRecv = 2025-05-12 01:15:04.033740170\n", "Fatal seq [300195]: 126611 Biz = 126610 Recv = 2025-05-12 01:15:04.033740170 LastRecv = 2025-05-12 01:15:04.033740170\n"]}, {"name": "stderr", "output_type": "stream", "text": ["8996265it [00:19, 446769.26it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300135]: 1627199 Biz = 1627189 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n", "Fatal seq [300136]: 1565944 Biz = 1565902 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n", "Fatal seq [300339]: 1565904 Biz = 1565895 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n", "Fatal seq [300339]: 1565904 Biz = 1565897 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n", "Fatal seq [300339]: 1565904 Biz = 1565899 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n", "Fatal seq [300363]: 1565914 Biz = 1565910 Recv = 2025-05-12 01:30:41.229056851 LastRecv = 2025-05-12 01:30:41.229056851\n"]}, {"name": "stderr", "output_type": "stream", "text": ["9864944it [00:21, 460123.24it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [003031]: 1959196 Biz = 1959191 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.634589352\n", "Fatal seq [300782]: 1959188 Biz = 1959182 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.634589352\n", "Fatal seq [300866]: 1959208 Biz = 1959192 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [300866]: 1959208 Biz = 1959197 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [300866]: 1959208 Biz = 1959200 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [300866]: 1959208 Biz = 1959202 Recv = 2025-05-12 01:30:51.635560102 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [002090]: 1884129 Biz = 1884127 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [002551]: 1884119 Biz = 1884113 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [002551]: 1884119 Biz = 1884115 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [002551]: 1884119 Biz = 1884117 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [300866]: 1959208 Biz = 1959206 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n", "Fatal seq [301458]: 1959216 Biz = 1959204 Recv = 2025-05-12 01:30:51.636134305 LastRecv = 2025-05-12 01:30:51.635560102\n"]}, {"name": "stderr", "output_type": "stream", "text": ["11551718it [00:24, 469409.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002427]: 2351160 Biz = 2351152 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.950162707\n", "Fatal seq [002809]: 2351161 Biz = 2351157 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.950162707\n", "Fatal seq [159591]: 166337 Biz = 166336 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.950162707\n", "Fatal seq [001339]: 2112235 Biz = 2112227 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [002031]: 2112249 Biz = 2112229 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [002195]: 2351200 Biz = 2351165 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351177 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351180 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351183 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351184 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351187 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351188 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [000768]: 2351190 Biz = 2351189 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n", "Fatal seq [301023]: 2351204 Biz = 2351203 Recv = 2025-05-12 01:31:20.951267139 LastRecv = 2025-05-12 01:31:20.951267139\n"]}, {"name": "stderr", "output_type": "stream", "text": ["14795266it [00:31, 457005.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002916]: 2926550 Biz = 2926544 Recv = 2025-05-12 01:32:39.216260535 LastRecv = 2025-05-12 01:32:39.216149871\n"]}, {"name": "stderr", "output_type": "stream", "text": ["16281708it [00:34, 454316.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002031]: 3179928 Biz = 3179927 Recv = 2025-05-12 01:33:20.059896366 LastRecv = 2025-05-12 01:33:20.059803912\n"]}, {"name": "stderr", "output_type": "stream", "text": ["17440761it [00:37, 460670.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002112]: 3563909 Biz = 3563907 Recv = 2025-05-12 01:33:57.148709960 LastRecv = 2025-05-12 01:33:57.148522247\n"]}, {"name": "stderr", "output_type": "stream", "text": ["17719713it [00:38, 458611.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300397]: 3680452 Biz = 3680444 Recv = 2025-05-12 01:34:05.262979188 LastRecv = 2025-05-12 01:34:05.262848469\n", "Fatal seq [300518]: 3638661 Biz = 3638651 Recv = 2025-05-12 01:34:05.262979188 LastRecv = 2025-05-12 01:34:05.262848469\n", "Fatal seq [300518]: 3638661 Biz = 3638654 Recv = 2025-05-12 01:34:05.262979188 LastRecv = 2025-05-12 01:34:05.262848469\n"]}, {"name": "stderr", "output_type": "stream", "text": ["20239432it [00:43, 460250.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002313]: 4384769 Biz = 4384768 Recv = 2025-05-12 01:35:30.581596706 LastRecv = 2025-05-12 01:35:30.581296414\n", "Fatal seq [002365]: 4098926 Biz = 4098925 Recv = 2025-05-12 01:35:30.581705717 LastRecv = 2025-05-12 01:35:30.581421116\n", "Fatal seq [000678]: 4267064 Biz = 4267062 Recv = 2025-05-12 01:35:33.288864865 LastRecv = 2025-05-12 01:35:33.288808131\n"]}, {"name": "stderr", "output_type": "stream", "text": ["20379392it [00:43, 462914.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159525]: 184298 Biz = 184297 Recv = 2025-05-12 01:35:33.995540574 LastRecv = 2025-05-12 01:35:33.995298336\n"]}, {"name": "stderr", "output_type": "stream", "text": ["21304743it [00:45, 462225.07it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000062]: 4449320 Biz = 4449318 Recv = 2025-05-12 01:36:06.095462939 LastRecv = 2025-05-12 01:36:06.095404321\n", "Fatal seq [300870]: 4449317 Biz = 4449315 Recv = 2025-05-12 01:36:06.095462939 LastRecv = 2025-05-12 01:36:06.095404321\n", "Fatal seq [300870]: 4449317 Biz = 4449316 Recv = 2025-05-12 01:36:06.095462939 LastRecv = 2025-05-12 01:36:06.095404321\n", "Fatal seq [002246]: 4474987 Biz = 4474986 Recv = 2025-05-12 01:36:09.424176474 LastRecv = 2025-05-12 01:36:09.424154787\n"]}, {"name": "stderr", "output_type": "stream", "text": ["22710311it [00:48, 466876.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159352]: 263325 Biz = 263318 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n", "Fatal seq [159352]: 263325 Biz = 263320 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n", "Fatal seq [159352]: 263325 Biz = 263321 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n", "Fatal seq [159352]: 263325 Biz = 263322 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n", "Fatal seq [159352]: 263325 Biz = 263323 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n", "Fatal seq [159352]: 263325 Biz = 263324 Recv = 2025-05-12 01:36:55.543807197 LastRecv = 2025-05-12 01:36:55.543535709\n"]}, {"name": "stderr", "output_type": "stream", "text": ["23278346it [00:50, 467696.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000678]: 4948601 Biz = 4948598 Recv = 2025-05-12 01:37:14.689094203 LastRecv = 2025-05-12 01:37:14.688875323\n", "Fatal seq [302132]: 5129523 Biz = 5129519 Recv = 2025-05-12 01:37:14.689094203 LastRecv = 2025-05-12 01:37:14.688875323\n", "Fatal seq [300083]: 4787242 Biz = 4787240 Recv = 2025-05-12 01:37:17.263341627 LastRecv = 2025-05-12 01:37:17.263217045\n", "Fatal seq [000677]: 4934876 Biz = 4934874 Recv = 2025-05-12 01:37:17.438810735 LastRecv = 2025-05-12 01:37:17.438756203\n", "Fatal seq [000677]: 4934876 Biz = 4934874 Recv = 2025-05-12 01:37:17.438883158 LastRecv = 2025-05-12 01:37:17.438810735\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24628354it [00:53, 462555.74it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300404]: 5062382 Biz = 5062381 Recv = 2025-05-12 01:38:07.796613853 LastRecv = 2025-05-12 01:38:07.796126257\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25374190it [00:54, 458184.71it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000571]: 5800199 Biz = 5800194 Recv = 2025-05-12 01:38:41.477497004 LastRecv = 2025-05-12 01:38:41.477437997\n", "Fatal seq [000571]: 5800199 Biz = 5800196 Recv = 2025-05-12 01:38:41.477562358 LastRecv = 2025-05-12 01:38:41.477437997\n", "Fatal seq [000571]: 5800199 Biz = 5800197 Recv = 2025-05-12 01:38:41.477648725 LastRecv = 2025-05-12 01:38:41.477437997\n", "Fatal seq [000571]: 5800199 Biz = 5800198 Recv = 2025-05-12 01:38:41.477648725 LastRecv = 2025-05-12 01:38:41.477437997\n"]}, {"name": "stderr", "output_type": "stream", "text": ["25798488it [00:55, 468247.00it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002181]: 5321209 Biz = 5321203 Recv = 2025-05-12 01:38:59.455965766 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [300086]: 5321213 Biz = 5321204 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [300967]: 5501813 Biz = 5501805 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [002181]: 5321209 Biz = 5321208 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [300967]: 5501813 Biz = 5501807 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [300967]: 5501813 Biz = 5501809 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n", "Fatal seq [300967]: 5501813 Biz = 5501811 Recv = 2025-05-12 01:38:59.456051727 LastRecv = 2025-05-12 01:38:59.455915026\n"]}, {"name": "stderr", "output_type": "stream", "text": ["26317231it [00:56, 465401.89it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159335]: 446703 Biz = 446702 Recv = 2025-05-12 01:39:17.496646719 LastRecv = 2025-05-12 01:39:17.496571697\n", "Fatal seq [301117]: 5674929 Biz = 5674926 Recv = 2025-05-12 01:39:22.699502243 LastRecv = 2025-05-12 01:39:22.699502243\n", "Fatal seq [301117]: 5674929 Biz = 5674928 Recv = 2025-05-12 01:39:22.699502243 LastRecv = 2025-05-12 01:39:22.699502243\n"]}, {"name": "stderr", "output_type": "stream", "text": ["26691972it [00:57, 468358.01it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300502]: 6134990 Biz = 6134987 Recv = 2025-05-12 01:39:31.338325394 LastRecv = 2025-05-12 01:39:31.338249046\n", "Fatal seq [300502]: 6134990 Biz = 6134989 Recv = 2025-05-12 01:39:31.338325394 LastRecv = 2025-05-12 01:39:31.338249046\n"]}, {"name": "stderr", "output_type": "stream", "text": ["27484551it [00:59, 458350.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002657]: 5911020 Biz = 5911019 Recv = 2025-05-12 01:40:06.263541547 LastRecv = 2025-05-12 01:40:06.263439049\n"]}, {"name": "stderr", "output_type": "stream", "text": ["28466599it [01:01, 468168.33it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002155]: 6376803 Biz = 6376801 Recv = 2025-05-12 01:40:41.139026230 LastRecv = 2025-05-12 01:40:41.138997876\n", "Fatal seq [002155]: 6376803 Biz = 6376802 Recv = 2025-05-12 01:40:41.139026230 LastRecv = 2025-05-12 01:40:41.138997876\n"]}, {"name": "stderr", "output_type": "stream", "text": ["29303524it [01:03, 453740.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002995]: 6094257 Biz = 6094250 Recv = 2025-05-12 01:41:16.350760254 LastRecv = 2025-05-12 01:41:16.350693129\n", "Fatal seq [002995]: 6094257 Biz = 6094252 Recv = 2025-05-12 01:41:16.350760254 LastRecv = 2025-05-12 01:41:16.350693129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["33150987it [01:11, 465297.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300238]: 7265265 Biz = 7265262 Recv = 2025-05-12 01:44:18.843139098 LastRecv = 2025-05-12 01:44:18.842835356\n", "Fatal seq [300238]: 7265265 Biz = 7265264 Recv = 2025-05-12 01:44:18.843139098 LastRecv = 2025-05-12 01:44:18.842835356\n"]}, {"name": "stderr", "output_type": "stream", "text": ["33615195it [01:12, 464464.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [001359]: 7080898 Biz = 7080894 Recv = 2025-05-12 01:44:39.460553608 LastRecv = 2025-05-12 01:44:39.460421181\n", "Fatal seq [001359]: 7080898 Biz = 7080897 Recv = 2025-05-12 01:44:39.460553608 LastRecv = 2025-05-12 01:44:39.460421181\n"]}, {"name": "stderr", "output_type": "stream", "text": ["34503487it [01:14, 463770.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002347]: 7446239 Biz = 7446238 Recv = 2025-05-12 01:45:24.189450290 LastRecv = 2025-05-12 01:45:24.189395653\n"]}, {"name": "stderr", "output_type": "stream", "text": ["37366828it [01:20, 466761.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002466]: 8706902 Biz = 8706900 Recv = 2025-05-12 01:47:51.571397119 LastRecv = 2025-05-12 01:47:51.571397119\n"]}, {"name": "stderr", "output_type": "stream", "text": ["38723641it [01:23, 468523.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300900]: 9056342 Biz = 9056340 Recv = 2025-05-12 01:49:04.502481947 LastRecv = 2025-05-12 01:49:04.502385532\n"]}, {"name": "stderr", "output_type": "stream", "text": ["40374409it [01:27, 466028.09it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002570]: 8765493 Biz = 8765490 Recv = 2025-05-12 01:50:33.592143988 LastRecv = 2025-05-12 01:50:33.592113320\n", "Fatal seq [002570]: 8765493 Biz = 8765491 Recv = 2025-05-12 01:50:33.592143988 LastRecv = 2025-05-12 01:50:33.592113320\n", "Fatal seq [002570]: 8765493 Biz = 8765492 Recv = 2025-05-12 01:50:33.592214867 LastRecv = 2025-05-12 01:50:33.592113320\n"]}, {"name": "stderr", "output_type": "stream", "text": ["40654590it [01:27, 459490.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002115]: 8690702 Biz = 8690698 Recv = 2025-05-12 01:50:48.511163850 LastRecv = 2025-05-12 01:50:48.511072827\n", "Fatal seq [002115]: 8690702 Biz = 8690699 Recv = 2025-05-12 01:50:48.511163850 LastRecv = 2025-05-12 01:50:48.511072827\n", "Fatal seq [002115]: 8690702 Biz = 8690700 Recv = 2025-05-12 01:50:48.511163850 LastRecv = 2025-05-12 01:50:48.511072827\n", "Fatal seq [002115]: 8690702 Biz = 8690701 Recv = 2025-05-12 01:50:48.511163850 LastRecv = 2025-05-12 01:50:48.511072827\n"]}, {"name": "stderr", "output_type": "stream", "text": ["40982600it [01:28, 448726.74it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159213]: 793418 Biz = 793417 Recv = 2025-05-12 01:51:09.826000716 LastRecv = 2025-05-12 01:51:09.825877602\n"]}, {"name": "stderr", "output_type": "stream", "text": ["41589626it [01:29, 464684.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300969]: 9697956 Biz = 9697955 Recv = 2025-05-12 01:51:49.460028581 LastRecv = 2025-05-12 01:51:49.459911602\n"]}, {"name": "stderr", "output_type": "stream", "text": ["44634433it [01:36, 453491.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300829]: 10399281 Biz = 10399279 Recv = 2025-05-12 01:55:11.541018167 LastRecv = 2025-05-12 01:55:11.541002915\n"]}, {"name": "stderr", "output_type": "stream", "text": ["46651812it [01:40, 468964.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300589]: 10004096 Biz = 10004094 Recv = 2025-05-12 01:57:32.058242630 LastRecv = 2025-05-12 01:57:32.057981691\n"]}, {"name": "stderr", "output_type": "stream", "text": ["48856102it [01:45, 466793.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300350]: 10692694 Biz = 10692693 Recv = 2025-05-12 02:00:03.932479072 LastRecv = 2025-05-12 02:00:03.932479072\n", "Fatal seq [002179]: 10535529 Biz = 10535524 Recv = 2025-05-12 02:00:09.130759602 LastRecv = 2025-05-12 02:00:09.130692220\n", "Fatal seq [002285]: 10535527 Biz = 10535525 Recv = 2025-05-12 02:00:09.130759602 LastRecv = 2025-05-12 02:00:09.130692220\n"]}, {"name": "stderr", "output_type": "stream", "text": ["49137447it [01:46, 466875.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002692]: 10756952 Biz = 10756945 Recv = 2025-05-12 02:00:14.970843585 LastRecv = 2025-05-12 02:00:14.970816518\n", "Fatal seq [002692]: 10756952 Biz = 10756946 Recv = 2025-05-12 02:00:14.970843585 LastRecv = 2025-05-12 02:00:14.970816518\n", "Fatal seq [002692]: 10756952 Biz = 10756947 Recv = 2025-05-12 02:00:14.970843585 LastRecv = 2025-05-12 02:00:14.970816518\n", "Fatal seq [002692]: 10756952 Biz = 10756950 Recv = 2025-05-12 02:00:14.970990835 LastRecv = 2025-05-12 02:00:14.970816518\n", "Fatal seq [002692]: 10756952 Biz = 10756951 Recv = 2025-05-12 02:00:14.970990835 LastRecv = 2025-05-12 02:00:14.970816518\n"]}, {"name": "stderr", "output_type": "stream", "text": ["49418941it [01:46, 468202.80it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300395]: 11471580 Biz = 11471579 Recv = 2025-05-12 02:00:26.991991879 LastRecv = 2025-05-12 02:00:26.991916454\n"]}, {"name": "stderr", "output_type": "stream", "text": ["51199059it [01:50, 462607.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002935]: 11406812 Biz = 11406810 Recv = 2025-05-12 02:02:08.094775094 LastRecv = 2025-05-12 02:02:08.094744723\n", "Fatal seq [300925]: 11056682 Biz = 11056681 Recv = 2025-05-12 02:02:10.647804987 LastRecv = 2025-05-12 02:02:10.647659969\n"]}, {"name": "stderr", "output_type": "stream", "text": ["51666872it [01:51, 469125.82it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002298]: 11517618 Biz = 11517616 Recv = 2025-05-12 02:02:37.385225007 LastRecv = 2025-05-12 02:02:37.385061996\n"]}, {"name": "stderr", "output_type": "stream", "text": ["52327940it [01:53, 472693.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [301248]: 12140367 Biz = 12140364 Recv = 2025-05-12 02:03:21.670875177 LastRecv = 2025-05-12 02:03:21.670833915\n", "Fatal seq [301248]: 12140367 Biz = 12140366 Recv = 2025-05-12 02:03:21.670987281 LastRecv = 2025-05-12 02:03:21.670833915\n"]}, {"name": "stderr", "output_type": "stream", "text": ["54120356it [01:56, 458704.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000056]: 12651506 Biz = 12651505 Recv = 2025-05-12 02:05:31.460569622 LastRecv = 2025-05-12 02:05:31.460432599\n"]}, {"name": "stderr", "output_type": "stream", "text": ["58248190it [02:05, 468052.06it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000520]: 13472507 Biz = 13472503 Recv = 2025-05-12 02:10:59.065197967 LastRecv = 2025-05-12 02:10:59.065038099\n", "Fatal seq [000520]: 13472507 Biz = 13472505 Recv = 2025-05-12 02:10:59.065197967 LastRecv = 2025-05-12 02:10:59.065038099\n"]}, {"name": "stderr", "output_type": "stream", "text": ["62422763it [02:14, 465439.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159967]: 957398 Biz = 957397 Recv = 2025-05-12 02:16:04.407643103 LastRecv = 2025-05-12 02:16:04.407486671\n", "Fatal seq [002194]: 13466427 Biz = 13466361 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466363 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466365 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466367 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466369 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466371 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466373 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466375 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466377 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466379 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466382 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466385 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466387 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466389 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466391 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466393 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466395 Recv = 2025-05-12 02:16:09.571287687 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466397 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466402 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466404 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466407 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466410 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466412 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466414 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466417 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466420 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466422 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n", "Fatal seq [002194]: 13466427 Biz = 13466425 Recv = 2025-05-12 02:16:09.571673071 LastRecv = 2025-05-12 02:16:09.571287687\n"]}, {"name": "stderr", "output_type": "stream", "text": ["64384938it [02:19, 467057.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300432]: 13893992 Biz = 13893991 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401115100\n", "Fatal seq [300458]: 14978671 Biz = 14978665 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401068927\n", "Fatal seq [300458]: 14978671 Biz = 14978667 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401068927\n", "Fatal seq [300458]: 14978671 Biz = 14978669 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401068927\n", "Fatal seq [300534]: 14094403 Biz = 14094397 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401068927\n", "Fatal seq [300534]: 14094403 Biz = 14094400 Recv = 2025-05-12 02:18:34.401210826 LastRecv = 2025-05-12 02:18:34.401068927\n", "Fatal seq [300850]: 13908902 Biz = 13908901 Recv = 2025-05-12 02:18:37.744965840 LastRecv = 2025-05-12 02:18:37.744965840\n"]}, {"name": "stderr", "output_type": "stream", "text": ["68530538it [02:28, 463921.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300161]: 14972133 Biz = 14972127 Recv = 2025-05-12 02:24:23.080509167 LastRecv = 2025-05-12 02:24:23.080450738\n", "Fatal seq [300161]: 14972133 Biz = 14972128 Recv = 2025-05-12 02:24:23.080509167 LastRecv = 2025-05-12 02:24:23.080450738\n", "Fatal seq [300161]: 14972133 Biz = 14972129 Recv = 2025-05-12 02:24:23.080509167 LastRecv = 2025-05-12 02:24:23.080450738\n", "Fatal seq [300928]: 14972131 Biz = 14972130 Recv = 2025-05-12 02:24:23.080509167 LastRecv = 2025-05-12 02:24:23.080450738\n"]}, {"name": "stderr", "output_type": "stream", "text": ["71397090it [02:34, 428088.24it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [301359]: 16404019 Biz = 16404017 Recv = 2025-05-12 02:29:27.561242172 LastRecv = 2025-05-12 02:29:27.561035469\n"]}, {"name": "stderr", "output_type": "stream", "text": ["72138336it [02:35, 466494.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000670]: 16547797 Biz = 16547795 Recv = 2025-05-12 02:30:31.801204806 LastRecv = 2025-05-12 02:30:31.801204806\n", "Fatal seq [000670]: 16547797 Biz = 16547796 Recv = 2025-05-12 02:30:31.801204806 LastRecv = 2025-05-12 02:30:31.801204806\n"]}, {"name": "stderr", "output_type": "stream", "text": ["76714686it [02:45, 462585.82it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002194]: 16577655 Biz = 16577653 Recv = 2025-05-12 02:38:28.827222604 LastRecv = 2025-05-12 02:38:28.827222604\n"]}, {"name": "stderr", "output_type": "stream", "text": ["82907965it [02:58, 485408.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300073]: 17895375 Biz = 17895370 Recv = 2025-05-12 02:51:58.208492129 LastRecv = 2025-05-12 02:51:58.208435752\n", "Fatal seq [300073]: 17895375 Biz = 17895371 Recv = 2025-05-12 02:51:58.208492129 LastRecv = 2025-05-12 02:51:58.208435752\n"]}, {"name": "stderr", "output_type": "stream", "text": ["87832443it [03:08, 482660.97it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159871]: 1511610 Biz = 1511609 Recv = 2025-05-12 03:04:29.078227690 LastRecv = 2025-05-12 03:04:29.078227690\n"]}, {"name": "stderr", "output_type": "stream", "text": ["91195727it [03:15, 484002.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300687]: 19887857 Biz = 19887854 Recv = 2025-05-12 03:13:22.685404002 LastRecv = 2025-05-12 03:13:22.685272604\n", "Fatal seq [300687]: 19887857 Biz = 19887855 Recv = 2025-05-12 03:13:22.685404002 LastRecv = 2025-05-12 03:13:22.685272604\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100882405it [03:36, 483557.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [301519]: 23167045 Biz = 23167043 Recv = 2025-05-12 05:05:43.678559783 LastRecv = 2025-05-12 05:05:43.678559783\n"]}, {"name": "stderr", "output_type": "stream", "text": ["104129742it [03:43, 468235.99it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [301256]: 23673098 Biz = 23673095 Recv = 2025-05-12 05:13:17.147079099 LastRecv = 2025-05-12 05:13:17.146970703\n", "Fatal seq [301256]: 23673098 Biz = 23673096 Recv = 2025-05-12 05:13:17.147079099 LastRecv = 2025-05-12 05:13:17.146970703\n", "Fatal seq [301256]: 23673098 Biz = 23673097 Recv = 2025-05-12 05:13:17.147079099 LastRecv = 2025-05-12 05:13:17.146970703\n"]}, {"name": "stderr", "output_type": "stream", "text": ["106086745it [03:47, 462732.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002468]: 23281582 Biz = 23281580 Recv = 2025-05-12 05:17:38.270007664 LastRecv = 2025-05-12 05:17:38.269721253\n"]}, {"name": "stderr", "output_type": "stream", "text": ["107618100it [03:51, 460146.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [301073]: 24320111 Biz = 24320109 Recv = 2025-05-12 05:21:08.505478948 LastRecv = 2025-05-12 05:21:08.505478948\n", "Fatal seq [301073]: 24320111 Biz = 24320110 Recv = 2025-05-12 05:21:08.505478948 LastRecv = 2025-05-12 05:21:08.505478948\n", "Fatal seq [000420]: 24533193 Biz = 24533189 Recv = 2025-05-12 05:21:10.141785969 LastRecv = 2025-05-12 05:21:10.141785969\n", "Fatal seq [000420]: 24533193 Biz = 24533191 Recv = 2025-05-12 05:21:10.141785969 LastRecv = 2025-05-12 05:21:10.141785969\n"]}, {"name": "stderr", "output_type": "stream", "text": ["108085742it [03:52, 464844.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300461]: 24657239 Biz = 24657231 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657232 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657233 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657234 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657235 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657236 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657237 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n", "Fatal seq [300461]: 24657239 Biz = 24657238 Recv = 2025-05-12 05:22:30.458448092 LastRecv = 2025-05-12 05:22:30.458395472\n"]}, {"name": "stderr", "output_type": "stream", "text": ["109675421it [03:55, 457956.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300449]: 24046252 Biz = 24046250 Recv = 2025-05-12 05:26:27.207443249 LastRecv = 2025-05-12 05:26:27.207323395\n", "Fatal seq [300449]: 24046252 Biz = 24046251 Recv = 2025-05-12 05:26:27.207443249 LastRecv = 2025-05-12 05:26:27.207323395\n"]}, {"name": "stderr", "output_type": "stream", "text": ["114300716it [04:05, 469336.88it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159390]: 1733997 Biz = 1733996 Recv = 2025-05-12 05:38:01.133865064 LastRecv = 2025-05-12 05:38:01.133865064\n"]}, {"name": "stderr", "output_type": "stream", "text": ["115895556it [04:08, 467882.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300010]: 25001901 Biz = 25001899 Recv = 2025-05-12 05:42:27.157874218 LastRecv = 2025-05-12 05:42:27.157874218\n", "Fatal seq [300010]: 25001901 Biz = 25001900 Recv = 2025-05-12 05:42:27.157874218 LastRecv = 2025-05-12 05:42:27.157874218\n"]}, {"name": "stderr", "output_type": "stream", "text": ["116971543it [04:11, 467276.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300276]: 25228567 Biz = 25228553 Recv = 2025-05-12 05:45:23.962124398 LastRecv = 2025-05-12 05:45:23.961994781\n", "Fatal seq [300276]: 25228567 Biz = 25228564 Recv = 2025-05-12 05:45:23.962124398 LastRecv = 2025-05-12 05:45:23.961994781\n"]}, {"name": "stderr", "output_type": "stream", "text": ["118594208it [04:14, 441466.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300941]: 27121639 Biz = 27121637 Recv = 2025-05-12 05:50:00.586247755 LastRecv = 2025-05-12 05:50:00.586229215\n", "Fatal seq [300941]: 27121639 Biz = 27121638 Recv = 2025-05-12 05:50:00.586475063 LastRecv = 2025-05-12 05:50:00.586229215\n"]}, {"name": "stderr", "output_type": "stream", "text": ["132337146it [04:44, 450024.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [159201]: 2928713 Biz = 2928712 Recv = 2025-05-12 06:23:22.857351357 LastRecv = 2025-05-12 06:23:22.857351357\n"]}, {"name": "stderr", "output_type": "stream", "text": ["136193284it [04:53, 462380.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300148]: 29791070 Biz = 29791069 Recv = 2025-05-12 06:32:41.380093685 LastRecv = 2025-05-12 06:32:41.380093685\n"]}, {"name": "stderr", "output_type": "stream", "text": ["137948083it [04:57, 460831.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300059]: 31903152 Biz = 31903143 Recv = 2025-05-12 06:36:10.189290379 LastRecv = 2025-05-12 06:36:10.189290379\n", "Fatal seq [300059]: 31903152 Biz = 31903146 Recv = 2025-05-12 06:36:10.189403853 LastRecv = 2025-05-12 06:36:10.189290379\n", "Fatal seq [300059]: 31903152 Biz = 31903147 Recv = 2025-05-12 06:36:10.189495256 LastRecv = 2025-05-12 06:36:10.189290379\n", "Fatal seq [300059]: 31903152 Biz = 31903150 Recv = 2025-05-12 06:36:10.189495256 LastRecv = 2025-05-12 06:36:10.189290379\n", "Fatal seq [300059]: 31903152 Biz = 31903151 Recv = 2025-05-12 06:36:10.189574671 LastRecv = 2025-05-12 06:36:10.189290379\n"]}, {"name": "stderr", "output_type": "stream", "text": ["138781952it [04:58, 458860.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [002823]: 30398121 Biz = 30398120 Recv = 2025-05-12 06:38:04.097236775 LastRecv = 2025-05-12 06:38:04.097213240\n"]}, {"name": "stderr", "output_type": "stream", "text": ["143237218it [05:08, 462194.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300059]: 33212813 Biz = 33212811 Recv = 2025-05-12 06:46:44.421525723 LastRecv = 2025-05-12 06:46:44.421483524\n"]}, {"name": "stderr", "output_type": "stream", "text": ["144260965it [05:10, 459065.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [300145]: 31439070 Biz = 31439068 Recv = 2025-05-12 06:48:25.319292633 LastRecv = 2025-05-12 06:48:25.319201370\n"]}, {"name": "stderr", "output_type": "stream", "text": ["145186547it [05:12, 460062.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fatal seq [000016]: 32917776 Biz = 32917771 Recv = 2025-05-12 06:49:57.278626663 LastRecv = 2025-05-12 06:49:57.278626663\n", "Fatal seq [000016]: 32917776 Biz = 32917773 Recv = 2025-05-12 06:49:57.278649549 LastRecv = 2025-05-12 06:49:57.278626663\n"]}, {"name": "stderr", "output_type": "stream", "text": ["151607101it [05:26, 463976.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# CONF_SID = \"300059\"\n", "f = open(\"/dev/shm/0512/a10_order.csv\")\n", "f.readline()\n", "d_last_seq = defaultdict(int)\n", "d_last_cha = defaultdict(str)\n", "d_last_recv = defaultdict(str)\n", "i = 0\n", "for line in tqdm(f):\n", "    try:\n", "        RecvTime,Market,SecurityID,Date,Time,Index,BizIndex,OrderIndex,Channel,Price,Volume,Side,Type,DealMode = line.split(\",\")\n", "    except:\n", "        print(line)\n", "        break\n", "    if Market[2] != 'Z': continue  # 只看深市\n", "    icode = SecurityID\n", "    if d_last_cha[icode]:\n", "        if d_last_cha[icode] != Channel:\n", "            print(f\"Fatal chan [{icode}]: {d_last_cha[icode]} {Channel}\")\n", "            break\n", "    else:\n", "        d_last_cha[icode] = Channel\n", "    \n", "    seqno = int(Index)  # 深市用 Index\n", "    if seqno < d_last_seq[icode]:\n", "        print(f\"Fatal seq [{icode}]: { d_last_seq[icode]} Biz = {seqno} Recv = {RecvTime} LastRecv = {d_last_recv[icode]}\")\n", "        continue\n", "    d_last_seq[icode] = seqno\n", "    d_last_recv[icode] = RecvTime\n", "\n", "    \n", "print(i)"]}, {"cell_type": "code", "execution_count": 18, "id": "2048f045-51de-4864-bd04-ede5c26fd89c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["23388\n"]}], "source": ["print(i)"]}, {"cell_type": "code", "execution_count": null, "id": "06caf991-df02-4cf5-8e5f-c7a049f24d36", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ad11e797-ab4e-42c5-8567-0ef82c1ed1f2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}