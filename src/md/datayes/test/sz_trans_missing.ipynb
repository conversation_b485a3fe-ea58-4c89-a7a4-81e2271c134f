{"cells": [{"cell_type": "markdown", "id": "40bf5208-3a6f-4ca2-b86b-ecef3df0fcd1", "metadata": {}, "source": ["# 研究深市逐笔交易中的消息记录号 ApplSeqNum 的编码规则"]}, {"cell_type": "markdown", "id": "37fef613-85ac-4165-af6d-538f38418e9f", "metadata": {}, "source": ["原先是为了研究5月16日一些逐笔信息的缺失问题，通过与通联自己导出数据的比对，两种消息缺失的都是尾部的几十个，推测原因是当时程序没有处理 TERM 信息，且写盘用的是 \\n，导致被 kill 时最后一些行没写到文件中。后来程序处理了 TERM 信号，且写盘用的是 << std::endl，实际会调用 flush()，所以5月29日数据没有缺失。"]}, {"cell_type": "code", "execution_count": null, "id": "83071d2f-a719-42b1-8eb8-b2f4587b8748", "metadata": {}, "outputs": [], "source": ["研究 ApplSeqNum 编码规则，通过 groupby 按 ChannelNo 分解，把委托与成交的 ApplSeqNum 进行 merge，再考虑条数，最后结论是，ApplSeqNum 是按 ChannelNo 从 1 开始委托、成交统一编码。"]}, {"cell_type": "code", "execution_count": 1, "id": "c73160c1-7d70-4e52-bdf5-aad697f64816", "metadata": {}, "outputs": [], "source": ["from nb_common import *\n", "pd.options.display.max_info_columns=200"]}, {"cell_type": "code", "execution_count": 51, "id": "11fa3157-2462-4621-b68e-4605bcf6543c", "metadata": {}, "outputs": [], "source": ["dy_sz_t = pd.read_csv(\"/A/pk/0516/dy_sz_trans.csv\", parse_dates = ['RecvTime'], dtype = {'SecurityID': 'str'})"]}, {"cell_type": "code", "execution_count": 86, "id": "df39045c-2766-41e8-a8db-29d3f677d9d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["75"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["90632524-len(dy_sz_t)"]}, {"cell_type": "code", "execution_count": 90, "id": "01bd7a66-7be6-4fdb-b869-4b0b119a307d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2014, 2022, 2025, 2011, 2015, 2032, 2013, 2012, 2024, 2021, 2023,\n", "       2035, 2031, 2034, 2033])"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_t.ChannelNo.unique()"]}, {"cell_type": "code", "execution_count": 102, "id": "c8d00233-bafe-48ac-8aa2-04eb2e65c74b", "metadata": {}, "outputs": [], "source": ["chn = dy_sz_t.groupby('ChannelNo')"]}, {"cell_type": "code", "execution_count": 105, "id": "90f85cdd-e6d0-480c-a004-a1e76c337434", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2011 14564050\n", "2012 14601866\n", "2013 16191266\n", "2014 14969279\n", "2015 14585222\n", "2021 1756652\n", "2022 1412709\n", "2023 1447679\n", "2024 1184812\n", "2025 1594066\n", "2031 1668653\n", "2032 2148708\n", "2033 1711913\n", "2034 924880\n", "2035 1870694\n"]}], "source": ["for ch, trs in chn:\n", "    print(ch, len(trs))"]}, {"cell_type": "code", "execution_count": 131, "id": "718f7a32-ab3b-40fd-bb53-ab70303a1933", "metadata": {}, "outputs": [], "source": ["t2011 = chn.get_group(2011)"]}, {"cell_type": "code", "execution_count": 135, "id": "022d35df-891e-4012-817d-f0e586e12a88", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 824,  890, 1600, 2270, 2274, 2803, 3072, 3417, 3600, 3911, 4104,\n", "       4105, 4226, 4366, 5232, 5435, 7752, 9312])"]}, "execution_count": 135, "metadata": {}, "output_type": "execute_result"}], "source": ["t2011.loc[t2011.ApplSeqNum < 10000].ApplSeqNum.unique()"]}, {"cell_type": "code", "execution_count": 140, "id": "d83d36ec-63a2-4dd5-b2e3-d4bfc3b6dfa6", "metadata": {}, "outputs": [], "source": ["t2012 = chn.get_group(2012)"]}, {"cell_type": "code", "execution_count": 141, "id": "864bf20d-9986-424e-85e4-5ebb5038e137", "metadata": {}, "outputs": [], "source": ["x1 = t2011.merge(t2012, on='ApplSeqNum', how='inner')"]}, {"cell_type": "code", "execution_count": 145, "id": "0a30ce0a-1005-41f7-bf52-5fc02d54cdb1", "metadata": {}, "outputs": [], "source": ["pd.options.display.max_columns=64"]}, {"cell_type": "code", "execution_count": 137, "id": "96db07ca-6fd2-41fe-97e6-bf7f1a1304a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["34202976"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["dy_sz_t.ApplSeqNum.max()"]}, {"cell_type": "code", "execution_count": 138, "id": "edc39269-e4a6-42e7-a6cc-cbb16d27c278", "metadata": {}, "outputs": [{"data": {"text/plain": ["90632449"]}, "execution_count": 138, "metadata": {}, "output_type": "execute_result"}], "source": ["len(dy_sz_t)"]}, {"cell_type": "code", "execution_count": 106, "id": "a8c28f1f-fcf9-404a-b008-acd077b1f761", "metadata": {}, "outputs": [], "source": ["t10000 = dy_sz_t.loc[dy_sz_t.ApplSeqNum <= 10000]"]}, {"cell_type": "code", "execution_count": 139, "id": "46e24a6a-f583-4f80-9cbe-e74346d42cb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["551"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["len(t10000)"]}, {"cell_type": "code", "execution_count": 108, "id": "b60941aa-279f-4df7-9344-918c153d8186", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2014, 2022, 2025, 2011, 2015, 2032, 2013, 2012, 2024, 2021, 2023,\n", "       2035, 2031, 2034, 2033])"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["t10000.ChannelNo.unique()"]}, {"cell_type": "code", "execution_count": 91, "id": "50f17fa0-c006-4e1f-b092-9fff3aac78f6", "metadata": {}, "outputs": [], "source": ["t2014 = dy_sz_t.loc[dy_sz_t.ChannelNo == 2014]"]}, {"cell_type": "code", "execution_count": 92, "id": "c85589e6-2cd2-45b4-a00b-67eb4757cdef", "metadata": {}, "outputs": [{"data": {"text/plain": ["14969279"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["len(t2014)"]}, {"cell_type": "code", "execution_count": 93, "id": "9e1ca9b0-ea58-408e-973d-988d7eb4ac05", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>ApplSeqNum</th>\n", "      <th>StreamID</th>\n", "      <th>BidApplSeqNum</th>\n", "      <th>OfferApplSeqNum</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>LastPx</th>\n", "      <th>LastQty</th>\n", "      <th>ExecType</th>\n", "      <th>TransactTime</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>90611208</th>\n", "      <td>2025-05-16 15:00:04.119160</td>\n", "      <td>2014</td>\n", "      <td>31771905</td>\n", "      <td>11</td>\n", "      <td>31664384</td>\n", "      <td>31643653</td>\n", "      <td>301636</td>\n", "      <td>102</td>\n", "      <td>65.96</td>\n", "      <td>90</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4119160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90611209</th>\n", "      <td>2025-05-16 15:00:04.119164</td>\n", "      <td>2014</td>\n", "      <td>31771906</td>\n", "      <td>11</td>\n", "      <td>31664384</td>\n", "      <td>31644223</td>\n", "      <td>301636</td>\n", "      <td>102</td>\n", "      <td>65.96</td>\n", "      <td>10</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4119164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90611211</th>\n", "      <td>2025-05-16 15:00:04.119170</td>\n", "      <td>2014</td>\n", "      <td>31771907</td>\n", "      <td>11</td>\n", "      <td>31664930</td>\n", "      <td>31644223</td>\n", "      <td>301636</td>\n", "      <td>102</td>\n", "      <td>65.96</td>\n", "      <td>100</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4119170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90611215</th>\n", "      <td>2025-05-16 15:00:04.119183</td>\n", "      <td>2014</td>\n", "      <td>31771908</td>\n", "      <td>11</td>\n", "      <td>31670016</td>\n", "      <td>31644223</td>\n", "      <td>301636</td>\n", "      <td>102</td>\n", "      <td>65.96</td>\n", "      <td>200</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4119183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90611217</th>\n", "      <td>2025-05-16 15:00:04.119189</td>\n", "      <td>2014</td>\n", "      <td>31771909</td>\n", "      <td>11</td>\n", "      <td>31671604</td>\n", "      <td>31644223</td>\n", "      <td>301636</td>\n", "      <td>102</td>\n", "      <td>65.96</td>\n", "      <td>100</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4119189</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           RecvTime  ChannelNo  ApplSeqNum  StreamID  \\\n", "90611208 2025-05-16 15:00:04.119160       2014    31771905        11   \n", "90611209 2025-05-16 15:00:04.119164       2014    31771906        11   \n", "90611211 2025-05-16 15:00:04.119170       2014    31771907        11   \n", "90611215 2025-05-16 15:00:04.119183       2014    31771908        11   \n", "90611217 2025-05-16 15:00:04.119189       2014    31771909        11   \n", "\n", "          BidApplSeqNum  OfferApplSeqNum SecurityID  SecurityIDSource  LastPx  \\\n", "90611208       31664384         31643653     301636               102   65.96   \n", "90611209       31664384         31644223     301636               102   65.96   \n", "90611211       31664930         31644223     301636               102   65.96   \n", "90611215       31670016         31644223     301636               102   65.96   \n", "90611217       31671604         31644223     301636               102   65.96   \n", "\n", "          LastQty  ExecType  TransactTime    Delay  \n", "90611208       90        70  15:00:00.000  4119160  \n", "90611209       10        70  15:00:00.000  4119164  \n", "90611211      100        70  15:00:00.000  4119170  \n", "90611215      200        70  15:00:00.000  4119183  \n", "90611217      100        70  15:00:00.000  4119189  "]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["t2014.tail()"]}, {"cell_type": "code", "execution_count": 94, "id": "82f3a45c-d1e3-4222-9856-00a50fbda88f", "metadata": {}, "outputs": [], "source": ["t2015 = dy_sz_t.loc[dy_sz_t.ChannelNo == 2015]"]}, {"cell_type": "code", "execution_count": 95, "id": "b6c58c66-ce6c-425e-ad17-b80943d8ed73", "metadata": {}, "outputs": [{"data": {"text/plain": ["14585222"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["len(t2015)"]}, {"cell_type": "code", "execution_count": 96, "id": "9942c487-2249-49c0-85b2-53066f4f7e0c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>ApplSeqNum</th>\n", "      <th>StreamID</th>\n", "      <th>BidApplSeqNum</th>\n", "      <th>OfferApplSeqNum</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>LastPx</th>\n", "      <th>LastQty</th>\n", "      <th>ExecType</th>\n", "      <th>TransactTime</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>90632444</th>\n", "      <td>2025-05-16 15:00:04.235733</td>\n", "      <td>2015</td>\n", "      <td>30928291</td>\n", "      <td>11</td>\n", "      <td>30813196</td>\n", "      <td>30765615</td>\n", "      <td>301665</td>\n", "      <td>102</td>\n", "      <td>31.1</td>\n", "      <td>200</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4235733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90632445</th>\n", "      <td>2025-05-16 15:00:04.235736</td>\n", "      <td>2015</td>\n", "      <td>30928292</td>\n", "      <td>11</td>\n", "      <td>30813196</td>\n", "      <td>30766788</td>\n", "      <td>301665</td>\n", "      <td>102</td>\n", "      <td>31.1</td>\n", "      <td>100</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4235736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90632446</th>\n", "      <td>2025-05-16 15:00:04.235738</td>\n", "      <td>2015</td>\n", "      <td>30928293</td>\n", "      <td>11</td>\n", "      <td>30813196</td>\n", "      <td>30768717</td>\n", "      <td>301665</td>\n", "      <td>102</td>\n", "      <td>31.1</td>\n", "      <td>100</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4235738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90632447</th>\n", "      <td>2025-05-16 15:00:04.235741</td>\n", "      <td>2015</td>\n", "      <td>30928294</td>\n", "      <td>11</td>\n", "      <td>30820425</td>\n", "      <td>30768958</td>\n", "      <td>301665</td>\n", "      <td>102</td>\n", "      <td>31.1</td>\n", "      <td>800</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4235741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90632448</th>\n", "      <td>2025-05-16 15:00:04.235743</td>\n", "      <td>2015</td>\n", "      <td>30928295</td>\n", "      <td>11</td>\n", "      <td>30828583</td>\n", "      <td>30768958</td>\n", "      <td>301665</td>\n", "      <td>102</td>\n", "      <td>31.1</td>\n", "      <td>3200</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4235743</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           RecvTime  ChannelNo  ApplSeqNum  StreamID  \\\n", "90632444 2025-05-16 15:00:04.235733       2015    30928291        11   \n", "90632445 2025-05-16 15:00:04.235736       2015    30928292        11   \n", "90632446 2025-05-16 15:00:04.235738       2015    30928293        11   \n", "90632447 2025-05-16 15:00:04.235741       2015    30928294        11   \n", "90632448 2025-05-16 15:00:04.235743       2015    30928295        11   \n", "\n", "          BidApplSeqNum  OfferApplSeqNum SecurityID  SecurityIDSource  LastPx  \\\n", "90632444       30813196         30765615     301665               102    31.1   \n", "90632445       30813196         30766788     301665               102    31.1   \n", "90632446       30813196         30768717     301665               102    31.1   \n", "90632447       30820425         30768958     301665               102    31.1   \n", "90632448       30828583         30768958     301665               102    31.1   \n", "\n", "          LastQty  ExecType  TransactTime    Delay  \n", "90632444      200        70  15:00:00.000  4235733  \n", "90632445      100        70  15:00:00.000  4235736  \n", "90632446      100        70  15:00:00.000  4235738  \n", "90632447      800        70  15:00:00.000  4235741  \n", "90632448     3200        70  15:00:00.000  4235743  "]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["t2015.tail()"]}, {"cell_type": "code", "execution_count": 97, "id": "e5bac62a-cc09-4b20-ba03-95a4d3c9804c", "metadata": {}, "outputs": [], "source": ["t2012 = dy_sz_t.loc[dy_sz_t.ChannelNo == 2012]"]}, {"cell_type": "code", "execution_count": 98, "id": "bf195af0-8398-4467-9c61-55634525c1ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["14601866"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["len(t2012)"]}, {"cell_type": "code", "execution_count": 99, "id": "2db64091-19b9-4293-a13b-c3b4d5f55cf0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>ApplSeqNum</th>\n", "      <th>StreamID</th>\n", "      <th>BidApplSeqNum</th>\n", "      <th>OfferApplSeqNum</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>LastPx</th>\n", "      <th>LastQty</th>\n", "      <th>ExecType</th>\n", "      <th>TransactTime</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2025-05-16 09:15:00.128864</td>\n", "      <td>2012</td>\n", "      <td>1546</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>1545</td>\n", "      <td>002921</td>\n", "      <td>102</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>52</td>\n", "      <td>09:15:00.040</td>\n", "      <td>88864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2025-05-16 09:15:00.128894</td>\n", "      <td>2012</td>\n", "      <td>1563</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>1548</td>\n", "      <td>002921</td>\n", "      <td>102</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>52</td>\n", "      <td>09:15:00.040</td>\n", "      <td>88894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2025-05-16 09:15:00.128905</td>\n", "      <td>2012</td>\n", "      <td>1564</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>1557</td>\n", "      <td>002921</td>\n", "      <td>102</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>52</td>\n", "      <td>09:15:00.040</td>\n", "      <td>88905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2025-05-16 09:15:00.128928</td>\n", "      <td>2012</td>\n", "      <td>1565</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>1558</td>\n", "      <td>002921</td>\n", "      <td>102</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>52</td>\n", "      <td>09:15:00.040</td>\n", "      <td>88928</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2025-05-16 09:15:00.128938</td>\n", "      <td>2012</td>\n", "      <td>1566</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>1559</td>\n", "      <td>002921</td>\n", "      <td>102</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>52</td>\n", "      <td>09:15:00.040</td>\n", "      <td>88938</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     RecvTime  ChannelNo  ApplSeqNum  StreamID  BidApplSeqNum  \\\n", "11 2025-05-16 09:15:00.128864       2012        1546        11              0   \n", "12 2025-05-16 09:15:00.128894       2012        1563        11              0   \n", "13 2025-05-16 09:15:00.128905       2012        1564        11              0   \n", "14 2025-05-16 09:15:00.128928       2012        1565        11              0   \n", "15 2025-05-16 09:15:00.128938       2012        1566        11              0   \n", "\n", "    OfferApplSeqNum SecurityID  SecurityIDSource  LastPx  LastQty  ExecType  \\\n", "11             1545     002921               102     0.0      200        52   \n", "12             1548     002921               102     0.0      200        52   \n", "13             1557     002921               102     0.0      200        52   \n", "14             1558     002921               102     0.0      200        52   \n", "15             1559     002921               102     0.0      200        52   \n", "\n", "    TransactTime  Delay  \n", "11  09:15:00.040  88864  \n", "12  09:15:00.040  88894  \n", "13  09:15:00.040  88905  \n", "14  09:15:00.040  88928  \n", "15  09:15:00.040  88938  "]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["t2012.head()"]}, {"cell_type": "code", "execution_count": 54, "id": "96995fb9-3bae-404e-9311-0f3d0adfcb6e", "metadata": {}, "outputs": [], "source": ["dy_sz_o = pd.read_csv(\"/A/pk/0516/dy_sz_order.csv\", parse_dates = ['RecvTime'], dtype = {'SecurityID': 'str'})"]}, {"cell_type": "code", "execution_count": 147, "id": "799199d4-0bda-4525-ad05-735897061392", "metadata": {}, "outputs": [], "source": ["chno = dy_sz_o.groupby('ChannelNo')"]}, {"cell_type": "code", "execution_count": 149, "id": "0f1797e6-9a11-4ce4-b18d-38e5b2ec6445", "metadata": {}, "outputs": [], "source": ["o2011 = chno.get_group(2011)"]}, {"cell_type": "code", "execution_count": 152, "id": "f5f7a070-cabf-4408-a393-4305bca4807b", "metadata": {}, "outputs": [], "source": ["xxx = t2011.merge(o2011, on='ApplSeqNum', how='inner')"]}, {"cell_type": "code", "execution_count": 154, "id": "0dac8604-391b-49b6-9d56-5a7dcd7f5b59", "metadata": {}, "outputs": [{"data": {"text/plain": ["30903613"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["len(o2011)+len(t2011)"]}, {"cell_type": "code", "execution_count": 155, "id": "e9967d14-b867-40a3-9483-5250a990a8b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["30807001"]}, "execution_count": 155, "metadata": {}, "output_type": "execute_result"}], "source": ["o2011.ApplSeqNum.max()"]}, {"cell_type": "code", "execution_count": 156, "id": "67af8874-7ff9-4da2-b6e1-6f32a7b50531", "metadata": {}, "outputs": [{"data": {"text/plain": ["30903620"]}, "execution_count": 156, "metadata": {}, "output_type": "execute_result"}], "source": ["t2011.ApplSeqNum.max()"]}, {"cell_type": "code", "execution_count": 151, "id": "f7186a46-20e3-4317-a7d5-6a381fde7df5", "metadata": {}, "outputs": [], "source": ["o2012 = chno.get_group(2012)"]}, {"cell_type": "code", "execution_count": 148, "id": "0e49d070-ddf9-49fc-aa78-c4c1edab86b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2011 16339563\n", "2012 16409662\n", "2013 18011700\n", "2014 16802617\n", "2015 16343053\n", "2021 1839901\n", "2022 1462435\n", "2023 1509246\n", "2024 1222314\n", "2025 1660236\n", "2031 1801388\n", "2032 2338108\n", "2033 1861959\n", "2034 1033392\n", "2035 2055627\n"]}], "source": ["for ch, trs in chno:\n", "    print(ch, len(trs))"]}, {"cell_type": "code", "execution_count": 87, "id": "491e28dd-151b-4ca9-a383-5db153a14e8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["-64"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["len(dy_sz_o) - 100691265"]}, {"cell_type": "code", "execution_count": 70, "id": "0d7e3f83-3a7a-40b4-a39b-62351cf2905a", "metadata": {}, "outputs": [], "source": ["dfbid = pd.merge(d1015_end_t, dy_sz_o[['SecurityID', \"ApplSeqNum\", \"Price\", 'RecvTime', 'TransactTime']], left_on=[\"BidApplSeqNum\", 'SecurityID'], right_on=[\"ApplSeqNum\", 'SecurityID'], how=\"left\")"]}, {"cell_type": "code", "execution_count": 74, "id": "25278952-8cc0-46e5-b62b-0717cbbc9832", "metadata": {}, "outputs": [], "source": ["dfbid_missing = dfbid.loc[dfbid.Price.isna()]"]}, {"cell_type": "code", "execution_count": 76, "id": "38fb63ec-94b5-41c2-8f7f-63847b0d0cee", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['000786', '000819', '000837', '001268', '000963', '001368',\n", "       '002028', '002101', '002142', '002594', '002742', '002371',\n", "       '002402', '000035', '002884', '000735', '002717', '003007',\n", "       '300798', '002963', '123218', '300911', '300501', '300363',\n", "       '002153', '300218', '300628', '002278', '002290', '300445',\n", "       '002664', '300818', '300610', '301234', '300304', '300315',\n", "       '300547', '300972', '300752', '300779', '300788', '300792',\n", "       '301168', '301003', '301009', '301128', '301536', '301567',\n", "       '301603'], dtype=object)"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["dfbid_missing.SecurityID.unique()"]}, {"cell_type": "code", "execution_count": 72, "id": "bb6c1dda-a7b0-4b26-bd4a-ded0ae7aab43", "metadata": {}, "outputs": [], "source": ["dfoffer = pd.merge(d1015_end_t, dy_sz_o[['SecurityID', \"ApplSeqNum\", \"Price\", 'RecvTime', 'TransactTime']], left_on=['SecurityID', \"OfferApplSeqNum\"], right_on=['SecurityID', \"ApplSeqNum\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": 75, "id": "fe308b4f-d858-4065-9e27-65f295ff6182", "metadata": {}, "outputs": [], "source": ["dfoffer_missing = dfoffer.loc[dfoffer.Price.isna()]"]}, {"cell_type": "code", "execution_count": 77, "id": "866761e0-9fd5-4a76-8b68-4ea0ad2a1ef7", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['002074', '002376', '002839', '002945', '002841', '300353',\n", "       '301000', '002915', '300708', '301318'], dtype=object)"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["dfoffer_missing.SecurityID.unique()"]}, {"cell_type": "code", "execution_count": 68, "id": "08aa17ea-61a8-436b-82aa-b4ad87fb9711", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>ApplSeqNum</th>\n", "      <th>StreamID</th>\n", "      <th>BidApplSeqNum</th>\n", "      <th>OfferApplSeqNum</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>LastPx</th>\n", "      <th>LastQty</th>\n", "      <th>ExecType</th>\n", "      <th>TransactTime</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>90141072</th>\n", "      <td>2025-05-16 15:00:01.016300</td>\n", "      <td>2012</td>\n", "      <td>30912539</td>\n", "      <td>11</td>\n", "      <td>30879922</td>\n", "      <td>30815486</td>\n", "      <td>000683</td>\n", "      <td>102</td>\n", "      <td>5.16</td>\n", "      <td>4000</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>1016300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90606649</th>\n", "      <td>2025-05-16 15:00:04.092970</td>\n", "      <td>2015</td>\n", "      <td>30912539</td>\n", "      <td>11</td>\n", "      <td>30835536</td>\n", "      <td>30835727</td>\n", "      <td>300708</td>\n", "      <td>102</td>\n", "      <td>11.25</td>\n", "      <td>2200</td>\n", "      <td>70</td>\n", "      <td>15:00:00.000</td>\n", "      <td>4092970</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           RecvTime  ChannelNo  ApplSeqNum  StreamID  \\\n", "90141072 2025-05-16 15:00:01.016300       2012    30912539        11   \n", "90606649 2025-05-16 15:00:04.092970       2015    30912539        11   \n", "\n", "          BidApplSeqNum  OfferApplSeqNum SecurityID  SecurityIDSource  LastPx  \\\n", "90141072       30879922         30815486     000683               102    5.16   \n", "90606649       30835536         30835727     300708               102   11.25   \n", "\n", "          LastQty  ExecType  TransactTime    Delay  \n", "90141072     4000        70  15:00:00.000  1016300  \n", "90606649     2200        70  15:00:00.000  4092970  "]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["d1015_end_t.loc[d1015_end_t.ApplSeqNum == 30912539]"]}, {"cell_type": "code", "execution_count": 32, "id": "e1255ef3-513f-4eaa-9d9e-a6c6f9d7c47b", "metadata": {}, "outputs": [], "source": ["d1015_o = dy_sz_o.loc[dy_sz_o.ChannelNo == 2015]"]}, {"cell_type": "code", "execution_count": 27, "id": "ce6208ee-ad4f-4a4e-9d0c-c200e37d0f3e", "metadata": {}, "outputs": [], "source": ["d1015_o = dy_sz_o.loc[(dy_sz_o.RecvTime > '2025-05-29 14:56:59') & (dy_sz_o.ChannelNo == 2015)]"]}, {"cell_type": "code", "execution_count": 28, "id": "da4d7b25-afe3-4f67-bdf1-d72b9281f067", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 138502 entries, 105555533 to 106291795\n", "Data columns (total 12 columns):\n", " #   Column            Non-Null Count   Dtype         \n", "---  ------            --------------   -----         \n", " 0   RecvTime          138502 non-null  datetime64[ns]\n", " 1   ChannelNo         138502 non-null  int64         \n", " 2   ApplSeqNum        138502 non-null  int64         \n", " 3   StreamID          138502 non-null  int64         \n", " 4   SecurityID        138502 non-null  int64         \n", " 5   SecurityIDSource  138502 non-null  int64         \n", " 6   Price             138502 non-null  float64       \n", " 7   OrderQty          138502 non-null  int64         \n", " 8   Side              138502 non-null  int64         \n", " 9   TransactTime      138502 non-null  object        \n", " 10  OrdType           138502 non-null  int64         \n", " 11  Delay             138502 non-null  int64         \n", "dtypes: datetime64[ns](1), float64(1), int64(9), object(1)\n", "memory usage: 13.7+ MB\n"]}], "source": ["d1015_o.info()"]}, {"cell_type": "code", "execution_count": 29, "id": "cae4201f-b851-4c64-82af-b572f8e43ef9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RecvTime</th>\n", "      <th>ChannelNo</th>\n", "      <th>ApplSeqNum</th>\n", "      <th>StreamID</th>\n", "      <th>SecurityID</th>\n", "      <th>SecurityIDSource</th>\n", "      <th>Price</th>\n", "      <th>OrderQty</th>\n", "      <th>Side</th>\n", "      <th>TransactTime</th>\n", "      <th>OrdType</th>\n", "      <th>Delay</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>105555533</th>\n", "      <td>2025-05-29 14:56:59.001596</td>\n", "      <td>2015</td>\n", "      <td>36215107</td>\n", "      <td>11</td>\n", "      <td>300858</td>\n", "      <td>102</td>\n", "      <td>16.75</td>\n", "      <td>200</td>\n", "      <td>50</td>\n", "      <td>14:56:58.960</td>\n", "      <td>50</td>\n", "      <td>41596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105555540</th>\n", "      <td>2025-05-29 14:56:59.002299</td>\n", "      <td>2015</td>\n", "      <td>36215109</td>\n", "      <td>11</td>\n", "      <td>1390</td>\n", "      <td>102</td>\n", "      <td>31.91</td>\n", "      <td>500</td>\n", "      <td>50</td>\n", "      <td>14:56:58.960</td>\n", "      <td>50</td>\n", "      <td>42299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105555544</th>\n", "      <td>2025-05-29 14:56:59.003278</td>\n", "      <td>2015</td>\n", "      <td>36215110</td>\n", "      <td>11</td>\n", "      <td>2050</td>\n", "      <td>102</td>\n", "      <td>26.57</td>\n", "      <td>300</td>\n", "      <td>50</td>\n", "      <td>14:56:58.960</td>\n", "      <td>50</td>\n", "      <td>43278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105555545</th>\n", "      <td>2025-05-29 14:56:59.003309</td>\n", "      <td>2015</td>\n", "      <td>36215112</td>\n", "      <td>11</td>\n", "      <td>2519</td>\n", "      <td>102</td>\n", "      <td>5.15</td>\n", "      <td>1000</td>\n", "      <td>49</td>\n", "      <td>14:56:58.960</td>\n", "      <td>50</td>\n", "      <td>43309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105555546</th>\n", "      <td>2025-05-29 14:56:59.003333</td>\n", "      <td>2015</td>\n", "      <td>36215113</td>\n", "      <td>11</td>\n", "      <td>678</td>\n", "      <td>102</td>\n", "      <td>17.00</td>\n", "      <td>600</td>\n", "      <td>50</td>\n", "      <td>14:56:58.960</td>\n", "      <td>50</td>\n", "      <td>43333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106291788</th>\n", "      <td>2025-05-29 15:00:00.030461</td>\n", "      <td>2015</td>\n", "      <td>36355214</td>\n", "      <td>11</td>\n", "      <td>2366</td>\n", "      <td>102</td>\n", "      <td>8.24</td>\n", "      <td>1200</td>\n", "      <td>50</td>\n", "      <td>14:59:59.990</td>\n", "      <td>50</td>\n", "      <td>40461</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106291790</th>\n", "      <td>2025-05-29 15:00:00.031336</td>\n", "      <td>2015</td>\n", "      <td>36355215</td>\n", "      <td>11</td>\n", "      <td>2050</td>\n", "      <td>102</td>\n", "      <td>26.50</td>\n", "      <td>500</td>\n", "      <td>50</td>\n", "      <td>14:59:59.990</td>\n", "      <td>50</td>\n", "      <td>41336</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106291791</th>\n", "      <td>2025-05-29 15:00:00.031367</td>\n", "      <td>2015</td>\n", "      <td>36355216</td>\n", "      <td>11</td>\n", "      <td>709</td>\n", "      <td>102</td>\n", "      <td>2.19</td>\n", "      <td>4000</td>\n", "      <td>49</td>\n", "      <td>14:59:59.990</td>\n", "      <td>50</td>\n", "      <td>41367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106291794</th>\n", "      <td>2025-05-29 15:00:00.032535</td>\n", "      <td>2015</td>\n", "      <td>36355217</td>\n", "      <td>11</td>\n", "      <td>933</td>\n", "      <td>102</td>\n", "      <td>16.65</td>\n", "      <td>100</td>\n", "      <td>50</td>\n", "      <td>14:59:59.990</td>\n", "      <td>50</td>\n", "      <td>42535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106291795</th>\n", "      <td>2025-05-29 15:00:00.032565</td>\n", "      <td>2015</td>\n", "      <td>36355218</td>\n", "      <td>11</td>\n", "      <td>300643</td>\n", "      <td>102</td>\n", "      <td>25.20</td>\n", "      <td>200</td>\n", "      <td>49</td>\n", "      <td>14:59:59.990</td>\n", "      <td>50</td>\n", "      <td>42565</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>138502 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                            RecvTime  ChannelNo  ApplSeqNum  StreamID  \\\n", "105555533 2025-05-29 14:56:59.001596       2015    36215107        11   \n", "105555540 2025-05-29 14:56:59.002299       2015    36215109        11   \n", "105555544 2025-05-29 14:56:59.003278       2015    36215110        11   \n", "105555545 2025-05-29 14:56:59.003309       2015    36215112        11   \n", "105555546 2025-05-29 14:56:59.003333       2015    36215113        11   \n", "...                              ...        ...         ...       ...   \n", "106291788 2025-05-29 15:00:00.030461       2015    36355214        11   \n", "106291790 2025-05-29 15:00:00.031336       2015    36355215        11   \n", "106291791 2025-05-29 15:00:00.031367       2015    36355216        11   \n", "106291794 2025-05-29 15:00:00.032535       2015    36355217        11   \n", "106291795 2025-05-29 15:00:00.032565       2015    36355218        11   \n", "\n", "           SecurityID  SecurityIDSource  Price  OrderQty  Side  TransactTime  \\\n", "105555533      300858               102  16.75       200    50  14:56:58.960   \n", "105555540        1390               102  31.91       500    50  14:56:58.960   \n", "105555544        2050               102  26.57       300    50  14:56:58.960   \n", "105555545        2519               102   5.15      1000    49  14:56:58.960   \n", "105555546         678               102  17.00       600    50  14:56:58.960   \n", "...               ...               ...    ...       ...   ...           ...   \n", "106291788        2366               102   8.24      1200    50  14:59:59.990   \n", "106291790        2050               102  26.50       500    50  14:59:59.990   \n", "106291791         709               102   2.19      4000    49  14:59:59.990   \n", "106291794         933               102  16.65       100    50  14:59:59.990   \n", "106291795      300643               102  25.20       200    49  14:59:59.990   \n", "\n", "           OrdType  Delay  \n", "105555533       50  41596  \n", "105555540       50  42299  \n", "105555544       50  43278  \n", "105555545       50  43309  \n", "105555546       50  43333  \n", "...            ...    ...  \n", "106291788       50  40461  \n", "106291790       50  41336  \n", "106291791       50  41367  \n", "106291794       50  42535  \n", "106291795       50  42565  \n", "\n", "[138502 rows x 12 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["d1015_o"]}, {"cell_type": "code", "execution_count": null, "id": "52559361-1039-42b2-b8d1-a0dc323b273c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 5}