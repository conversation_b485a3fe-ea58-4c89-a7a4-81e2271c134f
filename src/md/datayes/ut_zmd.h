#pragma once

// 通过 ZMQ 发布的行情数据结构，追求极致的性能

#include <cstdint>

#pragma pack(push, 1)
typedef struct t_zmd
{
    char ty;       // 类别SIOF
    char code[22]; // 代码，尾部填\0
    uint8_t st;    // 状态，比如熔断，延迟
    uint64_t mdid;
    uint32_t SP1, SA1, BP1, BA1; // 对于股票、基金，SA BA 申买申卖量，对于指数 SA1 BA1 相同，都是成交量缩小万倍，SP1 BP1 相同，都是最新指数扩大万倍
    uint32_t SP2, SA2, BP2, BA2;
    uint32_t SP3, SA3, BP3, BA3;
    uint32_t SP4, SA4, BP4, BA4;
    uint32_t SP5, SA5, BP5, BA5;
} zmd_t;
#pragma pack(pop)
