#!/usr/bin/python3
# -*- coding: utf-8 -*-
# 通过通联接口获取涨跌停信息
# API 文档见 https://mall.datayes.com/datapreview/1357

import pandas as pd
import requests
import sys
import argparse
import yaml
import os
import redis
from datetime import datetime
import time
import socket

def log_print(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}", flush=True)

# 创建命令行参数解析器
parser = argparse.ArgumentParser(description='获取通联涨跌停信息')
parser.add_argument('-d', '--day', type=str, 
                    help='指定日期，格式为YYYYMMDD')
parser.add_argument('-t', '--token', type=str,
                    help='API访问令牌')
parser.add_argument('-c', '--conf', type=str,
                    help='YAML配置文件路径')
parser.add_argument('-f', '--filep', type=str,
                    help='保存CSV文件的路径前缀，如果为空则不保存文件')
parser.add_argument('--redis-host', type=str,
                    help='Redis服务器地址')
parser.add_argument('--redis-port', type=int,
                    help='Redis服务器端口')
parser.add_argument('--redis-key', type=str,
                    help='Redis键名前缀')
args = parser.parse_args()

# 默认配置
config = {
    'token_api': '8ffcdc27aa291300241687fce22121d7dfdb8d2a225378b23e9ef7141d96b168',
    'filep': '',  # 默认为空，表示不用保存到文件
    'redis_host': None,  # 默认不使用Redis
    'redis_port': 6379,
    'redis_key': 'STKLST3'
}

# 从YAML文件加载配置
if args.conf:
    # 先尝试用UTF-8编码读取
    try:
        with open(args.conf, 'r', encoding='utf-8') as file:
            yaml_config = yaml.safe_load(file)
            if yaml_config:
                # 更新配置
                if 'token_api' in yaml_config:
                    config['token_api'] = yaml_config['token_api']
                if 'filep' in yaml_config:
                    config['filep'] = yaml_config['filep']
                if 'redis_host' in yaml_config:
                    config['redis_host'] = yaml_config['redis_host']
                if 'redis_port' in yaml_config:
                    config['redis_port'] = yaml_config['redis_port']
                if 'redis_key' in yaml_config:
                    config['redis_key'] = yaml_config['redis_key']
        log_print(f"已从 {args.conf} 加载配置 (UTF-8编码)")
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，尝试用GBK编码读取
        try:
            with open(args.conf, 'r', encoding='gbk') as file:
                yaml_config = yaml.safe_load(file)
                if yaml_config:
                    # 更新配置
                    if 'token_api' in yaml_config:
                        config['token_api'] = yaml_config['token_api']
                    if 'filep' in yaml_config:
                        config['filep'] = yaml_config['filep']
                    if 'redis_host' in yaml_config:
                        config['redis_host'] = yaml_config['redis_host']
                    if 'redis_port' in yaml_config:
                        config['redis_port'] = yaml_config['redis_port']
                    if 'redis_key' in yaml_config:
                        config['redis_key'] = yaml_config['redis_key']
            log_print(f"已从 {args.conf} 加载配置 (GBK编码)")
        except Exception as e:
            log_print(f"读取配置文件出错 (GBK编码): {e}")
    except Exception as e:
        log_print(f"读取配置文件出错 (UTF-8编码): {e}")

# 命令行参数优先级高于配置文件
if args.token:
    config['token_api'] = args.token
if args.filep:
    config['filep'] = args.filep
if args.redis_host:
    config['redis_host'] = args.redis_host
if args.redis_port:
    config['redis_port'] = args.redis_port
if args.redis_key:
    config['redis_key'] = args.redis_key

# 获取日期参数
if args.day:
    day = args.day
else:
    day = datetime.today().strftime("%Y%m%d")

# 获取token参数
token_api = config['token_api']

# 构建API URL
api_url = f'/api/market/getMktLimit.json?tradeDate={day}'

# 创建头信息,传入token
headers = {"Authorization": "Bearer " + token_api,
           "Accept-Encoding": "gzip, deflate"}

# 访问api,获取数据
should_retry = False
while True:
    try:
        log_print(f"正在获取 {day} 的涨跌停数据...")
        res = requests.request("GET", url='https://api.datayes.com/data/v1/' + api_url, headers=headers)
        code = res.status_code
        result = res.content.decode('utf-8')

        if code == 200 and eval(result)['retCode'] == 1:
            # 将数据转化为DataFrame格式
            df = pd.DataFrame(eval(result)['data'])
            log_print(f"成功获取到 {len(df)} 条数据")
            n_sh = len(df[df['exchangeCD'] == 'XSHG'])
            n_sz = len(df[df['exchangeCD'] == 'XSHE'])
            n_bj = len(df[df['exchangeCD'] == 'XBEI'])
            log_print(f"其中沪市 {n_sh} 条，深市 {n_sz} 条，北交所 {n_bj} 条")
            if n_sh < 3000 or n_sz < 3000 or n_bj < 100:
                log_print(f"数据量异常，先写入")
                should_retry = True
            else:
                should_retry = False
                
            # 如果filep不为空，则保存到文件
            filep = config['filep']
            if filep:
                # 支持日期格式化
                filep = datetime.today().strftime(filep)
                # 确保目录存在
                if not os.path.exists(os.path.dirname(filep)) and os.path.dirname(filep):
                    os.makedirs(os.path.dirname(filep), exist_ok=True)
                
                # 保存CSV文件
                output_file = f"{filep}limits_{day}.csv"
                df.to_csv(output_file, index=False)
                log_print(f"数据已保存到 {output_file}")
            else:
                log_print("未指定文件前缀，数据不保存到文件")
            
            # 如果redis_host不为空，则保存到Redis
            redis_host = config['redis_host']
            if redis_host:
                try:
                    # 连接到Redis
                    r = redis.Redis(
                        host=redis_host,
                        port=config['redis_port'],
                        decode_responses=True
                    )
                    
                    # 测试连接
                    r.ping()
                    
                    # 获取Redis键名前缀
                    redis_key = config['redis_key']
                    
                    # 将数据保存到Redis
                    # 使用Hash结构，每个证券代码作为一个hash
                    for _, row in df.iterrows():
                        # 保证有证券代码，不过应该肯定是有的
                        if 'ticker' not in row or pd.isna(row['ticker']):
                            continue
                        row_dict = {
                            # 'SecurityIDSource': row['exchangeCD'] if 'exchangeCD' in row else '',
                            # 'SecShortName': row['secShortName'].encode('gbk', errors='ignore') if 'secShortName' in row else '',
                            # 'SecShortNameEn': row['secShortNameEn'] if 'secShortNameEn' in row else '',
                            # 'tradeDate': row['tradeDate'] if 'tradeDate' in row else '',
                            'MaxPx': str(round(row['limitUpPrice'] * 10000)) if 'limitUpPrice' in row and not pd.isna(row['limitUpPrice']) else '0',
                            'MinPx': str(round(row['limitDownPrice'] * 10000)) if 'limitDownPrice' in row and not pd.isna(row['limitDownPrice']) else '0',
                            'PreClosePx': str(round(row['preClosePrice'] * 10000)) if 'preClosePrice' in row and not pd.isna(row['preClosePrice']) else '0',
                            'ActPreClosePx': str(round(row['actPreClosePrice'] * 10000)) if 'actPreClosePrice' in row and not pd.isna(row['actPreClosePrice']) else '0',
                            'LimitsDataSource': 'Tonglian',
                            'LimitsFetchTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            'LimitsFetchHost': socket.gethostname()
                        }
                        # 使用证券代码作为键名
                        key = f"{redis_key}:{row['ticker']}"
                        r.hset(key, mapping=row_dict)
                    
                    log_print(f"数据已保存到Redis服务器 {redis_host}:{config['redis_port']}")
                except Exception as e:
                    log_print(f"保存到Redis出错: {e}")
            else:
                log_print("未指定Redis服务器，数据不保存到Redis")
            
            # 成功获取数据，如果数据量基本正常则退出循环
            if not should_retry:
                break
            else:
                log_print("5分钟后重试...")
                time.sleep(300)  # 等待5分钟
                continue
            
        else:
            log_print(f"API返回错误: HTTP状态码 {code}")
            log_print(f"响应内容: {result}")
            log_print("5分钟后重试...")
            time.sleep(300)  # 等待5分钟
            
    except requests.exceptions.RequestException as e:
        log_print(f"网络请求失败: {e}")
        log_print("5分钟后重试...")
        time.sleep(300)  # 等待5分钟
    except Exception as e:
        log_print(f"获取数据时发生错误: {e}")
        log_print("5分钟后重试...")
        time.sleep(300)  # 等待5分钟
