#pragma once
#include <cstdint>
#include <string>
#include <chrono>
#include <format>
#include <iostream>

#define BAD_THING(msg) bad_thing(__func__, __LINE__, msg)
#define DEBUG_LOG(msg) debug_log(__func__, __LINE__, msg)

inline void bad_thing(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cerr << std::format("[{}][ERROR][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
    exit(1);
}

inline void debug_log(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cout << std::format("[{}][DEBUG][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
}

// 行情源类型
enum class MDSourceType
{
    L1,      // Level 1 行情
    L2,      // Level 2 行情
    OT,      // 逐笔全速行情
    UNKOWN,  // 未知，不应该出现
};

inline MDSourceType md_source_type_from_string(const std::string &type)  // 行情源类型字符串转换为 MDSourceType
{
    if (type == "L1")
    {
        return MDSourceType::L1;
    }
    else if (type == "L2")
    {
        return MDSourceType::L2;
    }
    else if (type == "OT")
    {
        return MDSourceType::OT;
    }
    else
    {
        return MDSourceType::UNKOWN;
    }
}

// 交易所标识
enum class ExchID
{
    SH,  // 上海
    SZ,  // 深圳
    UNKOWN,  // 未知
};

inline ExchID exch_id_from_string(const std::string &id)  // 交易所标识字符串转换为 ExchID
{
    if (id == "1")
    {
        return ExchID::SH;
    }
    else if (id == "0")
    {
        return ExchID::SZ;
    }
    else
    {
        return ExchID::UNKOWN;
    }
}