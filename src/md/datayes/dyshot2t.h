#pragma once
#include <cstdint>
#include <string>
#include <string_view>
#include <cstring>
#include <set>
#include <map>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <format>
#include <iostream>

#include "mdl_api.h"
#include "mdl_shl2_msg.h"

using namespace datayes::mdl;

inline void bad_thing(std::string func, int line, std::string msg)
{
    const auto tp_utc{std::chrono::system_clock::now()};

    std::cerr << std::format("[{}][BAD][{}:{}] {}", std::chrono::current_zone()->to_local(tp_utc), func, line, msg) << std::endl;
    exit(1);
}

// 用于 put_instruction() 中，用于生成错误信息
#define BAD_THING(msg) bad_thing(__func__, __LINE__, std::format("<{}> Type={}, Flag {}, price={}, buy_order={}, sell_order={}, qty={}, trade_money={}, 已处理 {} 条记录后发生错误: {}",\
    tick_time_str, SHOrderType2Str(order_type), SHTickBSFlag2Str(tick_bs_flag), order_price, buy_order, sell_order, order_qty, trade_money,\
    total_inst_trans_ + total_inst_order_ + total_inst_cancel_ + total_inst_state_, msg))

// 用于 Shot2t 类内部
#define DEBUG_LOG(msg) log(LOG_LEVEL::DEBUG, std::format("[{}:{}] {}", __func__, __LINE__, msg))

// 可用于 Shot2t 类外部，由全局变量 sys_log_level 控制是否显示，最后一个字母 S 表示这是类里的静态函数
#define DEBUG_LOGS(msg) Shot2t::log(sys_log_level, LOG_LEVEL::DEBUG, std::format("[{}:{}] {}", __func__, __LINE__, msg))

// 把 HH:MM:SS.mmm 格式的时间戳转换为 uint32_t 类型的 HHMMSSmmm
inline uint32_t HHMMSSmmm2uint32_t(const char *t)
{
    return std::stoi(std::string(t, 0, 2)) * 10000000 + std::stoi(std::string(t, 3, 2)) * 100000 + std::stoi(std::string(t, 6, 2)) * 1000 + std::stoi(std::string(t, 9, 3));
}

inline uint32_t HHMMSSmmm2uint32_t(const std::string_view &t)
{
    return std::stoi(std::string(t, 0, 2)) * 10000000 + std::stoi(std::string(t, 3, 2)) * 100000 + std::stoi(std::string(t, 6, 2)) * 1000 + std::stoi(std::string(t, 9, 3));
}

// 上交所发布的逐笔信息
struct order_raw_sh
{
    std::string recvTime;  // 接收程序收到逐笔数据的时间戳
    int64_t bizIndex; // 逐笔序号，每个 channel 从 1 开始单独连续编号
    int64_t channel;
    int64_t securityID;
    std::string tickTime;  // 上交所原始逐笔数据中的时间戳，据观察精确到百分之一秒，格式为 HH:MM:SS.mmm
    uint32_t exchtime;  // 交易所行情时间，HHMMSSmmm
    std::string type;  // A 新增委托订单，D 撤销委托订单，T 成交，S 产品状态订单
    int64_t buyOrderNo;
    int64_t sellOrderNo;
    double price;  // 价格，以元为单位
    int64_t qty;
    double tradeMoney;  // 成交信息时：成交金额（三位小数），新增委托订单时：已成交的委托数量（次生委托订单），0.0（原始委托订单）
    std::string tickBSFlag;  // 成交信息时：B 买方委托单成交，S 卖方委托单成交，N 集合竞价成交；新增或删除委托订单时：B 买方委托单，S 卖方委托单
                             // 产品状态订单：START 启动，OCALL 开市集合竞价，TRADE 连续自动撮合，SUSP 停牌，CCALL 收盘集合竞价，CLOSE 闭市，ENDTR 交易结束
    int64_t delay;  // 接收程序计算的时延，单位微秒
    uint32_t tltime;  // 通联行情时间，HHMMSSmmm
};

enum class SHOrderType
{
    A,           // 新增委托订单
    D,           // 撤销委托订单，即撤单
    T,           // 成交
    S,           // 产品状态订单
    UNKOWN = 100 // 未知，不应该出现
};

// 一般用于日志打印
inline std::string SHOrderType2Str(SHOrderType t)
{
    switch (t)
    {
    case SHOrderType::A:
        return "A";
    case SHOrderType::D:
        return "D";
    case SHOrderType::T:
        return "T";
    case SHOrderType::S:
        return "S";
    default:
        return "UNKOWN";
    }
}

inline SHOrderType str2SHOrderType(const std::string &s)
{
    if (s == "A")
        return SHOrderType::A;
    if (s == "D")
        return SHOrderType::D;
    if (s == "T")
        return SHOrderType::T;
    if (s == "S")
        return SHOrderType::S;
    return SHOrderType::UNKOWN;
}

inline SHOrderType cstr2SHOrderType(const char *s)
{
    if (s == nullptr)
        return SHOrderType::UNKOWN;
    if (std::strcmp(s, "A") == 0)
        return SHOrderType::A;
    if (std::strcmp(s, "D") == 0)
        return SHOrderType::D;
    if (std::strcmp(s, "T") == 0)
        return SHOrderType::T;
    if (std::strcmp(s, "S") == 0)
        return SHOrderType::S;
    return SHOrderType::UNKOWN;
}

inline SHOrderType order_raw_sh2SHOrderType(const order_raw_sh &order)
{
    return str2SHOrderType(order.type);
}

inline SHOrderType NGTSTick2SHOrderType(const mdl_shl2_msg::NGTSTick &t)
{
    return str2SHOrderType(t.Type.c_str());
}

enum class SHTickBSFlag
{
    B,           // 买方委托单成交、买方委托
    S,           // 卖方委托单成交、卖方委托
    N,           // 集合竞价成交
    START,       // 状态：启动
    OCALL,       // 状态：开市集合竞价
    TRADE,       // 状态：连续自动撮合
    SUSP,        // 状态：停牌
    CCALL,       // 状态：闭市集合竞价
    CLOSE,       // 状态：闭市
    ENDTR,       // 状态：交易结束
    UNKOWN = 100 // 未知，不应该出现
};

// 一般用于日志打印
inline std::string SHTickBSFlag2Str(SHTickBSFlag t)
{
    switch (t)
    {
    case SHTickBSFlag::B:
        return "B";
    case SHTickBSFlag::S:
        return "S";
    case SHTickBSFlag::N:
        return "N";
    case SHTickBSFlag::START:
        return "START";
    case SHTickBSFlag::OCALL:
        return "OCALL";
    case SHTickBSFlag::TRADE:
        return "TRADE";
    case SHTickBSFlag::SUSP:
        return "SUSP";
    case SHTickBSFlag::CCALL:
        return "CCALL";
    case SHTickBSFlag::CLOSE:
        return "CLOSE";
    case SHTickBSFlag::ENDTR:
        return "ENDTR";
    default:
        return "UNKOWN";
    }
}

inline SHTickBSFlag str2SHTickBSFlag(const std::string &s)
{
    if (s == "B")
        return SHTickBSFlag::B;
    if (s == "S")
        return SHTickBSFlag::S;
    if (s == "N")
        return SHTickBSFlag::N;
    if (s == "START")
        return SHTickBSFlag::START;
    if (s == "OCALL")
        return SHTickBSFlag::OCALL;
    if (s == "TRADE")
        return SHTickBSFlag::TRADE;
    if (s == "SUSP")
        return SHTickBSFlag::SUSP;
    if (s == "CCALL")
        return SHTickBSFlag::CCALL;
    if (s == "CLOSE")
        return SHTickBSFlag::CLOSE;
    if (s == "ENDTR")
        return SHTickBSFlag::ENDTR;
    return SHTickBSFlag::UNKOWN;
}

inline SHTickBSFlag cstr2SHTickBSFlag(const char *s)
{
    if (s == nullptr)
        return SHTickBSFlag::UNKOWN;
    if (std::strcmp(s, "B") == 0)
        return SHTickBSFlag::B;
    if (std::strcmp(s, "S") == 0)
        return SHTickBSFlag::S;
    if (std::strcmp(s, "N") == 0)
        return SHTickBSFlag::N;
    if (std::strcmp(s, "START") == 0)
        return SHTickBSFlag::START;
    if (std::strcmp(s, "OCALL") == 0)
        return SHTickBSFlag::OCALL;
    if (std::strcmp(s, "TRADE") == 0)
        return SHTickBSFlag::TRADE;
    if (std::strcmp(s, "SUSP") == 0)
        return SHTickBSFlag::SUSP;
    if (std::strcmp(s, "CCALL") == 0)
        return SHTickBSFlag::CCALL;
    if (std::strcmp(s, "CLOSE") == 0)
        return SHTickBSFlag::CLOSE;
    if (std::strcmp(s, "ENDTR") == 0)
        return SHTickBSFlag::ENDTR;
    return SHTickBSFlag::UNKOWN;
}

inline SHTickBSFlag order_raw_sh2SHTickBSFlag(const order_raw_sh &order)
{
    return str2SHTickBSFlag(order.tickBSFlag);
}

inline SHTickBSFlag NGTSTick2SHTickBSFlag(const mdl_shl2_msg::NGTSTick &t)
{
    return str2SHTickBSFlag(t.TickBSFlag.c_str());
}

// 买卖方向
enum class TradeType
{
    BUY = 0,
    SELL = 1
};

enum class TickType
{
    ORDER = 0,  // 新增委托订单
    CANCEL = 1,  // 撤销委托订单，即撤单
    TRANSACTION = 2,  // 成交
};

#define TICK_TYPE_ORDER 0
#define TICK_TYPE_CANCEL 1
#define TICK_TYPE_TRANSACTION 2

#define TICK_DIRECT_BUY 1
#define TICK_DIRECT_SELL 2
#define TICK_DIRECT_AUCTION 0

// 连续竞价期间，每个委托、成交或撤单，都对应生成一个Tick_info
// 开盘集合竞价结束及收盘集合竞价结束时各生成一个Tick_info
struct Tick_info
{
    char time[24];             // 交易所行情时间，如 "2025-08-04 14:56:59.470", 长度正好为 23，现在只用时间部分
    uint32_t exchtime;         // 交易所行情时间，HHMMSSmmm
    int8_t tick_type;          // 消息类型：委托 TICK_TYPE_ORDER 0、撤单 TICK_TYPE_CANCEL 1、成交 TICK_TYPE_TRANSACTION 2
    int8_t tick_direc;         // 消息方向：买 TICK_DIRECT_BUY 1、卖 TICK_DIRECT_SELL 2、集合竞价 TICK_DIRECT_AUCTION 0
    int bid20prices[20];       // 20档买价，单位厘
    int64_t bid20qty[20];      // 20档买量
    int ask20prices[20];       // 20档卖价，单位厘
    int64_t ask20qty[20];      // 20档卖量
    int bid20num[20];          // 20档买单笔数
    int ask20num[20];          // 20档卖单笔数
    int64_t bid20lgqty[20];    // 20档买中大单买量
    int64_t ask20lgqty[20];    // 20档卖中大单卖量
    std::map<int, int> orderbook_tier; // 价格档位信息，key 是相对于前收价的涨跌千分点（如 -100 表示 -10%），value 是该价位的挂单量，构造函数中会把所有可能千分点的挂单量都初始化为0，key 数量是不变的，全量输出
    int prev_close;            // 昨收价，单位厘
    int latest;                // 最新价，单位厘
    int high;                  // 最高价，单位厘
    int low;                   // 最低价，单位厘
    int open;                  // 开盘价，单位厘
    int trade_num;             // 累计成交笔数
    int64_t trade_qty;         // 累计成交量
    int64_t trade_money;       // 累计成交金额，单位厘
    int mid_price;             // 买一、卖一中间价，单位厘
    int spread;                // 卖一、买一价差，单位厘
    int w_avg_bid_price;       // 加权平均委买价，单位厘
    int w_avg_ask_price;       // 加权平均委卖价，单位厘
    int64_t tick_vol;          // 连续竞价期间为委托、撤单或成交量，开盘前集合竞价与收盘集合竞价为期间合并的成交量
    double tick_money;         // 连续竞价期间为委托、撤单或成交金额，开盘前集合竞价与收盘集合竞价为期间合并的成交金额，单位元
    int order_aggr;
    int64_t total_buy_qty;     // 当前总有效申买量
    int64_t total_sell_qty;    // 当前总有效申卖量
    int64_t sm_order_cum_vol;  // 5万以下小单的净流入金额，单位元
    int64_t mid_order_cum_vol; // 5万至30万中单的净流入金额，单位元
    int64_t lg_order_cum_vol;  // 30万至100万大单的净流入金额，单位元
    int64_t slg_order_cum_vol; // 100万以上特大单的净流入金额，单位元
    int64_t buy_order_index;   // 当前买单编号
    int64_t sell_order_index;  // 当前卖单编号
    int withdraw_num;          // 累计买卖撤单笔数

    Tick_info(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier)
    {
        std::memcpy(time, t, 23);  // 如 "2025-08-04 14:56:59.470", 长度正好为 23
        time[23] = 0;
        this->exchtime = exchtime;
        this->tick_type = tick_type;
        this->tick_direc = tick_direc;
        this->orderbook_tier = tier;
    }
    Tick_info() {}
};

void print_tick_info(Tick_info &tick_info);

// 上交所市场状态
enum class SHMarketState
{
    START, // 启动
    OCALL, // 开市集合竞价
    TRADE, // 连续自动撮合
    SUSP,  // 停牌
    CCALL, // 收盘集合竞价
    CLOSE, // 闭市
    ENDTR, // 交易结束
};

// 上交所连续交易阶段，主动交易方订单信息
// 在连续交易阶段
//   - 如果主动交易订单被一次性撮合完成（含市价单部分成交剩余自动撤回），则不会发布该订单的原始委托订单
//   - 如果主动交易订单只是部分完成且剩余部分要继续撮合，则会发布该订单的原始委托订单的修正订单
//   - 修正订单
//       * 在成交信息后紧接着发布（实际验证了至少5个修正订单）
//       * 订单号沿用原始委托订单的订单号
//       * 报价修正为新的挂单价（一般为本方最新成交价，也就是本订单成交的最高价）
struct sh_driver {
    int qty;    // 本方累积交易量
    int money;  // 本方累积交易金额
    TradeType direct;   // 本方是买方还是卖方
    std::string time;   // 本次订单首次出现在成交信息中的时间戳
    double counterpart_sm;  // 对手方小单累计成交金额
    double counterpart_mid;  // 对手方中单累计成交金额
    double counterpart_lg;   // 对手方大单累计成交金额
    double counterpart_slg;  // 对手方特大单累计成交金额
};

enum class LOG_LEVEL
{
    DEBUG = 0,
    INFO,
    WARNING,
    ERROR
};

inline std::string LOG_LEVEL2Str(LOG_LEVEL level)
{
    switch (level)
    {
    case LOG_LEVEL::DEBUG:
        return "DEBUG";
    case LOG_LEVEL::INFO:
        return "INFO";
    case LOG_LEVEL::WARNING:
        return "WARNING";
    case LOG_LEVEL::ERROR:
        return "ERROR";
    default:
        return "UNKNOWN";
    }
}

// 价位上的订单信息
struct price_order
{
    std::unordered_map<int64_t, int64_t> orders[2];  // 买单和卖单的[委托单号, 数量]映射
    int64_t total_qty[2];  // 买单和卖单的总数量，输出20档
};

class Shot2t
{
public:
    Shot2t(int upper_limit, int lower_limit, int prev_close, int increment, LOG_LEVEL log_level = LOG_LEVEL::WARNING);
    ~Shot2t();
    Tick_info *put_instruction(const order_raw_sh &order);
    Tick_info *put_instruction(const char *tick_time_str, uint32_t exchtime, SHOrderType order_type, SHTickBSFlag tick_bs_flag, int order_price, int64_t buy_order, int64_t sell_order, int64_t order_qty, double trade_money);
    Tick_info *put_instruction(const mdl_shl2_msg::NGTSTick &order);
    void print_tier_dict(void);
    void print_orderbook_tier(void);
    void print_orderbook(void);
    void print_orderbook(int price);
    void print_orderpool(void);
    void print_tick_info_snapshots(void);
    void print_money_balance(void);
    void print_vol_stat(void);
    void print_vol_lg_stat(void);
    void log(LOG_LEVEL level, std::string msg);
    static void log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg);
private:
    int upper_limit_;  // 涨停价，单位厘
    int lower_limit_;  // 跌停价，单位厘
    int prev_close_;   // 昨收价，单位厘
    double mid_threshold_;  // 以昨收价计算，5万元订单对应的股数
    double lg_threshold_;   // 以昨收价计算，30万元订单对应的股数
    double slg_threshold_;  // 以昨收价计算，100万元订单对应的股数
    float disc_coef_; // 从跌停 -10% 到涨停 10% 之间，一共 20 个百分点，根据前收盘价计算分档粒度
    int increment_;  // 最小价格变动单位，ETF 为 0.001 元，一般股票为 0.01 元，要乘以 1000，所以分别是 1 和 10
    bool whether_snapshot_ = false;  // 是否计算行情快照
    int64_t total_buy_qty_ = 0;  // 买单总数量
    int64_t total_sell_qty_ = 0;  // 卖单总数量
    std::map<int, int> tier_dict_;  // key 是所有可能价格，value 是相对于前收价的涨跌千分点，构造函数中根据涨跌停价、是否ETF来计算，key 数量是不变的
    std::map<int, int> orderbook_tier_;  // key 是相对于前收价的涨跌千分点，value 是该价位的挂单量，构造函数中会把所有可能千分点的值都初始化为0，key 数量是不变的，全量输出
    std::map<int, struct price_order> orderbook_;  // key 是以厘为单位的报价，value 是买单和卖单的[委托单号, 数量]映射以及总数量，key 数量是动态增减的
    std::map<int, struct price_order>::iterator it_bid1_;  // 买一在 orderbook_ 中的位置
    std::map<int, struct price_order>::iterator it_ask1_;  // 卖一在 orderbook_ 中的位置
    std::unordered_map<int64_t, int64_t [2]> orderpool_;  // 订单池，key 是委托单编号，value 是委托单的报价（厘）和数量，这里没有买或卖的信息，key 数量是动态增减的
    std::set<int64_t> orderpool_sm_;  // 小单池，内容是委托单编号
    std::set<int64_t> orderpool_mid_;  // 中单池，内容是委托单编号
    std::set<int64_t> orderpool_lg_;  // 大单池，内容是委托单编号
    std::set<int64_t> orderpool_slg_;  // 特大单池，内容是委托单编号
    std::map<int, int64_t [2]> orderbook_vol_lg_;  // 报价为索引的30万以上大单（含特大单）总量，key 是以厘为单位的报价，value 是大单中买单、卖单各自的当前有效总数量，
                                                   // key 数量是动态增减的，输出20档，输出档位与 orderbook_ 一致，空值输出为 0
    SHMarketState market_state_ = SHMarketState::START;  // 市场状态，适用于沪市
    std::vector<Tick_info> tick_info_snapshots_;  // 行情信息快照序列
    double sm_order_cum_vol_ = 0.0;  // 5万以下小单的净流入金额，单位元
    double mid_order_cum_vol_ = 0.0;  // 5万至30万中单的净流入金额，单位元
    double lg_order_cum_vol_ = 0.0;  // 30万至100万大单的净流入金额，单位元
    double slg_order_cum_vol_ = 0.0;  // 100万以上特大单的净流入金额，单位元
    int bid1_ = 0;  // 当前买一价
    int ask1_ = 0;  // 当前卖一价

    // 累计成交统计
    int64_t trade_qty_ = 0;  // 累计成交量
    int64_t trade_money_ = 0;  // 累计成交金额，单位厘
    int trade_num_ = 0;  // 累计成交笔数

    // 上交所连续竞价阶段，任一时刻只会有一个当前主动订单，主动买或者主动卖
    int64_t pending_buy_no_ = 0;  // 上交所连续竞价阶段，当前主动买订单编号，在新主动买成交信息中设置，后续的逻辑：
                                  //   - 主动买成交信息，进行买单号比较，相同说明主动买吃掉了更多的卖单，不同说明是新的主动买订单，原主动买订单已完结，更新本变量
                                  //   - 其它类型信息，说明该主动买订单的主动成交已完结（但可能产生次生订单），本变量置为 0
                                  //   - 本变量如果要变化（更新或置0），则根据累积变量 pending_buy_qty_ 和 pending_buy_money_ 进行统计处理
                                  //   - 本变量如果要持续保持不变，则更新累积变量 pending_buy_qty_ 和 pending_buy_money_
    int64_t pending_buy_qty_ = 0;  // 上交所连续竞价阶段，当前主动买订单累积成交数量
    double pending_buy_money_ = 0.0;  // 上交所连续竞价阶段，当前主动买订单累积成交金额
    int64_t pending_sell_no_ = 0;  // 上交所连续竞价阶段，当前主动卖订单编号，在新主动卖成交信息中设置，后续的逻辑：
                                   //   - 主动卖成交信息，进行卖单号比较，相同说明主动卖吃掉了更多的买单，不同说明是新的主动卖订单，原主动卖订单已完结，更新本变量
                                   //   - 其它类型信息，说明该主动买订单的主动成交已完结（但可能产生次生订单），本变量置为 0
                                   //   - 本变量如果要变化（更新或置0），则根据累积变量 pending_sell_qty_ 和 pending_sell_money_ 进行统计处理
                                   //   - 本变量如果要持续保持不变，则更新累积变量 pending_sell_qty_ 和 pending_sell_money_
    int64_t pending_sell_qty_ = 0;  // 上交所连续竞价阶段，当前主动卖订单累积成交数量
    double pending_sell_money_ = 0.0;  // 上交所连续竞价阶段，当前主动卖订单累积成交金额
    int latest_ = 0;     // 最新成交价
    int latest_buy_price_ = 0;       // 最新成交买单报价
    int latest_sell_price_ = 0;      // 最新成交卖单报价
    LOG_LEVEL log_level_;
    int total_inst_trans_ = 0;   // 总成交订单数
    int total_inst_order_ = 0;   // 总委托订单数
    int total_inst_cancel_ = 0;  // 总撤单订单数
    int total_inst_state_ = 0;   // 总指令订单数
    int64_t buy_order_index_ = 0;  // 买单编号
    int64_t sell_order_index_ = 0; // 卖单编号
    std::map<int, int> xxx_fake_orderbook_tier_;  // 临时变量，用于验证 orderbook_tier_ 的正确性
    Tick_info tick_info_;

    void export_tick_info_(Tick_info &tick_info); // 导出一些不同情形下（委托、撤单、成交、状态变化等）共同的 tick 信息
    void export_tick_info_(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier, Tick_info &tick_info); // 导出一些不同情形下（委托、撤单、成交、状态变化等）共同的 tick 信息
};
