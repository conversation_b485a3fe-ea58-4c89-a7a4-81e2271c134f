#include <iostream>
#include <format>
#include <cmath>
#include <chrono>
#include "dyszot2t.h"

void print_tick_info(Tick_info &t)
{
    std::cout << std::format("交易所时间 {}, 交易所时间数 {}, 最新价 {}, 卖一买一价差 {}", t.time, t.exchtime, t.latest, t.spread) << std::endl;

    std::cout << std::format("20档买价: {}", t.bid20prices[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20prices[i]);
    }
    std::cout << std::endl;
    std::cout << std::format("20档买量: {}", t.bid20qty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20qty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖价: {}", t.ask20prices[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20prices[i]);
    }
    std::cout << std::endl;
    std::cout << std::format("20档卖量: {}", t.ask20qty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20qty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档买单笔数: {}", t.bid20num[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20num[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖单笔数: {}", t.ask20num[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20num[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档买单中大单买量: {}", t.bid20lgqty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.bid20lgqty[i]);
    }
    std::cout << std::endl;

    std::cout << std::format("20档卖单中大单卖量: {}", t.ask20lgqty[0]);
    for (int i = 1; i < 20; i++)
    {
        std::cout << std::format(", {}", t.ask20lgqty[i]);
    }
    std::cout << std::endl;

    if (t.orderbook_tier.size() > 0)
    {
        auto it = t.orderbook_tier.begin();
        std::cout << std::format("价格档位信息: [{}: {}]", it->first, it->second);
        for (it++; it != t.orderbook_tier.end(); it++)
        {
            std::cout << std::format(", [{}: {}]", it->first, it->second);
        }
        std::cout << std::endl;
    }
    else
    {
        std::cout << "价格档位信息: 无" << std::endl;
    }

    std::cout << std::format("昨收价 {}, 最新价 {}, 卖一买一价差 {}, tick_vol {}, tick_money {}, 总有效申买量 {}, 总有效申卖量 {}，总成交量 {}，总成交金额 {}，总成交手数 {}",
                             t.prev_close, t.latest, t.spread, t.tick_vol, t.tick_money, t.total_buy_qty, t.total_sell_qty, t.trade_qty, std::lround(t.trade_money * 0.001), t.trade_num)
              << std::endl;
    std::cout << std::format("5万以下小单净流入金额 {}, 5至30万中单净流入金额 {}, 30至100万大单净流入金额 {}, 100万以上特大单净流入金额 {}, 总计 {}",
                             t.sm_order_cum_vol, t.mid_order_cum_vol, t.lg_order_cum_vol, t.slg_order_cum_vol, t.sm_order_cum_vol + t.mid_order_cum_vol + t.lg_order_cum_vol + t.slg_order_cum_vol)
              << std::endl;
}

Szot2t::Szot2t(int upper_limit, int lower_limit, int prev_close, int increment, LOG_LEVEL log_level)
    : upper_limit_(upper_limit), lower_limit_(lower_limit), prev_close_(prev_close), increment_(increment), log_level_(log_level)
{
    mid_threshold_ = 50000 / ((lower_limit + upper_limit) * 0.001 / 2);
    lg_threshold_ = 300000 / ((lower_limit + upper_limit) * 0.001 / 2);
    slg_threshold_ = 1000000 / ((lower_limit + upper_limit) * 0.001 / 2);

    if (prev_close > 10000)
    {
        disc_coef_ = 0.001; // 10元以上股票，按 0.1% 分档，一共有 201 个档
    }
    else if (prev_close > 5000 && prev_close <= 10000)
    {
        disc_coef_ = 0.002; // 5元以上到10元及以下的股票，按 0.2% 分档，一共有 101 个档
    }
    else if (prev_close > 2000 && prev_close <= 5000)
    {
        disc_coef_ = 0.005; // 2元以上到5元及以下的股票，按 0.5% 分档，一共有 21 个档
    }
    else
    {
        disc_coef_ = 0.01; // 5元以下的股票，按 1% 分档，一共有 21 个档(如果是 ST 股票，涨跌幅限制为 5%，则只有 11 个档)
    }

    for (int p = lower_limit_; p < upper_limit_ + increment_; p += increment_)
    {
        tier_dict_[p] = std::lround((double)std::lround(((double)p / (double)prev_close_ - 1.0L) / disc_coef_) * disc_coef_ * 1000.0L);
    }
    std::set<int> tier_set;
    for (const auto &[key, value] : tier_dict_)
    {
        tier_set.insert(value);
    }
    for (auto p : tier_set)
    {
        orderbook_tier_[p] = 0;
    }
}

Tick_info *Szot2t::put_instruction(uint32_t exchtime, SZOrderType order_type, int order_price, int64_t buy_order, int64_t sell_order, int64_t order_qty, uint32_t dy_time, int64_t msg_no)
{
    total_inst_++;
    if (exchtime < 91500000) // 9:15 前，不应该有逐笔消息
    {
        BAD_THING("收到开盘集合竞价时间前的逐笔消息!");
        return nullptr;
    }
    if (exchtime > 150000000) // 15:00 后，不应该有逐笔消息
    {
        BAD_THING("收到收盘集合竞价时间后的逐笔消息!");
        return nullptr;
    }
    if (exchtime < 92500000) // 开盘集合竞价期间，不生成 tick 信息
    {
        if (exchtime < 92000000) // 9:15-9:20 之间只能有限价单或撤单消息
        {
            if (order_type != SZOrderType::Limit && order_type != SZOrderType::Cancel)
            {
                BAD_THING("9:20 前开盘集合竞价期间收到非限价单或撤单消息!");
            }
        }
        if (exchtime >= 92000000) // 9:20-9:25 之间只能有限价单消息
        {
            if (order_type != SZOrderType::Limit)
            {
                BAD_THING("9:20 后开盘集合竞价期间收到非限价单消息!");
            }
        }
        if (market_state_ == SZMarketState::START) // 开盘集合竞价委托阶段的第一条消息
        {
            market_state_ = SZMarketState::OCALL;
            DEBUG_LOG("market_state_ = SZMarketState::OCALL, 进入开盘集合竞价委托阶段");
        }
    }
    else if (exchtime == 92500000) // 开盘集合竞价结束后发布成交信息
    {
        if (order_type != SZOrderType::Transaction) // 此时应该只有成交信息
        {
            BAD_THING("开盘集合竞价结束发布了非成交信息");
        }
        if (market_state_ == SZMarketState::OCALL) // 开盘集合竞价发布成交阶段的第一条消息
        {
            market_state_ = SZMarketState::OCALL_PUB;
            DEBUG_LOG("market_state_ = SZMarketState::OCALL_PUB, 进入开盘集合竞价发布成交阶段");
        }
    }
    else if (exchtime == 150000000) // 收盘集合竞价结束后发布成交信息
    {
        if (order_type != SZOrderType::Transaction) // 此时应该只有成交信息
        {
            log(LOG_LEVEL::WARNING, std::format("收盘集合竞价结束发布了非成交信息，exchtime: {}, order_type: {}, order_price: {}, order_qty: {}", exchtime, SZOrderType2Str(order_type), order_price, order_qty));
        }
        if (market_state_ == SZMarketState::CCALL) // 收盘集合竞价发布成交阶段的第一条消息
        {
            market_state_ = SZMarketState::CCALL_PUB;
            DEBUG_LOG("market_state_ = SZMarketState::CCALL_PUB, 进入收盘集合竞价发布成交阶段");
        }
    }
    else if (exchtime >= 145700000) // 收盘集合竞价委托阶段
    {
        if (order_type != SZOrderType::Limit) // 14:57 后只能有限价单消息
        {
            BAD_THING("14:57 后收到非限价单消息!");
        }
        if (market_state_ == SZMarketState::TRADE) // 收盘集合竞价委托阶段的第一条消息
        {
            if (pending_order_ != 0) // 在收盘集合竞价前还有一个未完结消息
            {
                if (!pending_is_market_) // 未完结消息是限价单
                {
                    BAD_THING(std::format("收盘集合竞价前有未完结的主动订单 {}，且不是市价单，pending_avail_qty_ = {}", pending_order_, pending_avail_qty_));
                    return nullptr;
                }
                DEBUG_LOG(std::format("收盘集合竞价前有未完结的主动订单，但不发布 tick 信息，pending_order_ = {}", pending_order_));
                close_pending_order_();
            }
            market_state_ = SZMarketState::CCALL;
            DEBUG_LOG("market_state_ = SZMarketState::CCALL, 进入收盘集合竞价委托阶段");
        }
    }
    else // 连续自动撮合阶段
    {
        if (market_state_ == SZMarketState::OCALL_PUB) // 集合竞价有成交情况下，连续自动撮合阶段的第一条消息
        {
            market_state_ = SZMarketState::TRADE;
            DEBUG_LOG("market_state_ = SZMarketState::TRADE, 集合竞价有成交，进入连续自动撮合阶段，先要计算买一、卖一价");
            calculate_bid1_ask1_(); // 计算买一、卖一价
            // buy_order_index_ = sell_order_index_ = 0;
            // export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_, tick_info_);
            // return &tick_info_;
        }
        else if (market_state_ == SZMarketState::OCALL) // 集合竞价无成交情况下，连续自动撮合阶段的第一条消息
        {
            market_state_ = SZMarketState::TRADE;
            DEBUG_LOG("market_state_ = SZMarketState::TRADE, 集合竞价无成交，进入连续自动撮合阶段，先要计算买一、卖一价");
            calculate_bid1_ask1_(); // 计算买一、卖一价
            // buy_order_index_ = sell_order_index_ = 0;
            // export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_, tick_info_);
            // return &tick_info_;
        }
        else if (market_state_ == SZMarketState::START) // 集合竞价无委托情况下，连续自动撮合阶段的第一条消息
        {
            market_state_ = SZMarketState::TRADE;
            DEBUG_LOG("market_state_ = SZMarketState::TRADE, 集合竞价无委托，进入连续自动撮合阶段，无需计算买一、卖一价");
        }
    }

    // 前面的市价单确认了没有对对手盘，或本方最优没有队友盘，则应该立即撤销
    if (pending_expect_cancel_ && order_type != SZOrderType::Cancel)
    {
        BAD_THING("前面的市价单确认了没有对对手盘，或本方最优没有队友盘，则应该立即撤销");
    }

    // 开盘集合竞价阶段，只有限价单与撤单，收盘集合竞价阶段，只有限价单
    if (market_state_ == SZMarketState::OCALL || market_state_ == SZMarketState::CCALL)
    {
        TradeType direction = (buy_order > 0) ? TradeType::BUY : TradeType::SELL; // 委托方向
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;

        if (order_type == SZOrderType::Limit) // 限价单
        {
            insert_limit_order_(order_price, order_no, order_qty, direction);
        }
        else // 肯定是撤单，由之前的检查保证。如果是收盘集合竞价，则没有撤单，由之前的检查保证
        {
            cancel_order_(order_no, order_qty, direction, false);
        }
        return nullptr;
    }
    else if (market_state_ == SZMarketState::OCALL_PUB || market_state_ == SZMarketState::CCALL_PUB) // 开盘或收盘集合竞价发布阶段，只有成交消息
    {
        auction_transaction_(buy_order, sell_order, order_qty, order_price);
        return nullptr;
    }

    /***************************
             连续竞价阶段
    ***************************/

    if (order_type == SZOrderType::Limit) // 连续竞价阶段收到限价单消息
    {
        // if (bid1_ != 0 && it_bid1_->first != bid1_)
        // {
        //     BAD_THING(std::format("连续竞价成交: it_bid1_ 指向价位 {}，但 bid1_ 为 {}", it_bid1_->first, bid1_));
        //     return nullptr;
        // }
        if (pending_order_ != 0) // 有未完结主动订单
        {
            // 当前未完结主动订单必须是市价单，而且是部分成交的对方最优市价单，因为
            //   - 限价单一开始就是确定结局的，它肯定已在前面的成交消息中被处理，其结局是全部成交或转为被动订单
            //   - 其它市价单的结局必然是全部成交（前面的成交消息中可确定）或撤销（前面的撤销消息中可确定）
            if (!pending_is_market_)
            {
                BAD_THING(std::format("连续竞价阶段收到限价单消息，但此时有未完结的主动订单 {}，且不是市价单，pending_avail_qty_ = {}", pending_order_, pending_avail_qty_));
                return nullptr;
            }
            close_pending_order_();
            // 这里不用发布 tick 信息，因为本限价单如果不能成交会发布 tick 消息，能成交则紧跟着会有成交消息，不会造成延时
        }
        // if (bid1_ != 0 && it_bid1_->first != bid1_)
        // {
        //     BAD_THING(std::format("连续竞价成交: it_bid1_ 指向价位 {}，但 bid1_ 为 {}", it_bid1_->first, bid1_));
        //     return nullptr;
        // }
        TradeType direction = (buy_order > 0) ? TradeType::BUY : TradeType::SELL; // 委托方向
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;
        if ((direction == TradeType::BUY && (order_price < ask1_ || ask1_ == 0)) // 买单，买价小于当前卖一价，或当前没有卖单
            || (direction == TradeType::SELL && order_price > bid1_))            // 卖单，且卖价大于当前买一价，或当前没有买单
        {
            // 肯定不能成交，直接插入订单簿变成被动订单
            insert_limit_order_(order_price, order_no, order_qty, direction);
            // 更新买一、卖一
            if (direction == TradeType::BUY) // 买单
            {
                buy_order_index_ = order_no;
                sell_order_index_ = 0;
                if (order_price > bid1_) // 本新增的买单价格更高，或者当前还没有买单，因此它要变成新的买一价
                {
                    if (ask1_ > 0 && order_price > ask1_) // 有卖单情况下，新增的买单价格比卖一价还高，说明程序不正常了
                    {
                        BAD_THING(std::format("新增的买单价格比卖一价还高, order_price = {}, bid1_ = {}, ask1_ = {}", order_price, bid1_, ask1_));
                    }
                    if (bid1_ > 0) // 原来有买单
                    {
                        it_bid1_++; // 本买单价在原来买一价更高的相邻价位
                    }
                    else
                    {
                        it_bid1_ = orderbook_.begin(); // 本买单是当前唯一买单，肯定是最底价位
                    }
                    bid1_ = order_price;
                }
            }
            else // 卖单
            {
                buy_order_index_ = 0;
                sell_order_index_ = order_no;
                if (order_price < ask1_ || ask1_ == 0) // 本新增的卖单价格更低，或者当前还没有卖单，因此它要变成新的卖一价
                {
                    if (order_price < bid1_) // 新增的卖单价格比买一价还低，说明程序不正常了
                    {
                        BAD_THING(std::format("新增的卖单价格比买一价还低, order_price = {}, bid1_ = {}, ask1_ = {}", order_price, bid1_, ask1_));
                    }
                    if (ask1_ > 0) // 原来有卖单
                    {
                        it_ask1_--; // 本卖单价在原来卖一价更低的相邻价位
                    }
                    else
                    {
                        it_ask1_ = std::prev(orderbook_.end()); // 本卖单是当前唯一卖单，肯定是最高价位
                    }
                    ask1_ = order_price;
                }
            }
            // 发布 tick 消息并退出
            export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_ORDER, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
            latest_order_type_ = SZOrderType::Limit;
            return &tick_info_;
        }

        // 本限价单可以成交，先计算可用来成交的量
        if (direction == TradeType::BUY) // 限价买单
        {
            auto it = orderbook_.upper_bound(order_price); // 指向第一个大于限价单价格的价位
            if (it == orderbook_.end())                    // 限价买单价格比所有卖单都高
            {
                if (order_qty <= total_sell_qty_) // 买单量小于等于总的卖单量
                {
                    pending_avail_qty_ = order_qty; // 可完全成交
                }
                else // 买单量大于总的卖单量
                {
                    pending_avail_qty_ = total_sell_qty_; // 可以把所有卖单都吃完
                }
            }
            else // 限价买单价格小于卖单最高价
            {
                pending_avail_qty_ = 0;
                for (auto it2 = it_ask1_; it2 != it; it2++)
                {
                    pending_avail_qty_ += it2->second.total_qty[1]; // 该价位的总卖单量
                    if (pending_avail_qty_ >= order_qty)            // 已经大于等于买单量，可以完全成交
                    {
                        break;
                    }
                }
                if (pending_avail_qty_ > order_qty) // 可以完全成交
                {
                    pending_avail_qty_ = order_qty;
                }
            }
        }
        else // 限价卖单
        {
            auto it = orderbook_.lower_bound(order_price); // 指向第一个大于等于限价单价格的价位
            if (it == orderbook_.begin())                  // 限价卖单价格不高于所有买单
            {
                // DEBUG_LOG(std::format("限价卖单价格不高于所有买单，order_price = {}, order_qty = {}, total_buy_qty_ = {}", order_price, order_qty, total_buy_qty_));
                if (order_qty <= total_buy_qty_) // 卖单量小于等于总的买单量
                {
                    pending_avail_qty_ = order_qty; // 可完全成交
                }
                else // 卖单量大于总的买单量
                {
                    pending_avail_qty_ = total_buy_qty_; // 可以把所有买单都吃完
                }
            }
            else // 限价卖单价格大于买单最低价
            {
                pending_avail_qty_ = 0;
                for (auto it2 = it; it2 != orderbook_.end() && it2->first <= bid1_; it2++)
                {
                    pending_avail_qty_ += it2->second.total_qty[0]; // 该价位的总买单量
                    if (pending_avail_qty_ >= order_qty)            // 已经大于等于卖单量，可以完全成交
                    {
                        break;
                    }
                }
                if (pending_avail_qty_ > order_qty) // 可以完全成交
                {
                    pending_avail_qty_ = order_qty;
                }
            }
        }

        pending_is_market_ = false;     // 非市价单
        pending_qty_apply_ = order_qty; // 原始申报数量
        pending_qty_ = 0;               // 累积成交数量
        pending_order_ = order_no;      // 当前主动订单编号
        pending_direction_ = direction; // 当前主动订单方向
        pending_price_ = order_price;   // 当前主动限价单的报价，对于市价单无意义
        pending_latest_price_ = 0;      // 当前主动订单最新成交价
        pending_price_num_ = 0;         // 当前主动订单已成交的价格数量，可用于判断市价单类型：对方最优最多只能有一个成交价，5档剩撤最多有5个，全档剩撤可以超过5个
        pending_should_tick_after_ate1_ = false;
        latest_order_type_ = SZOrderType::Limit;

        return nullptr; // 对于可成交的限价单，延迟到成交结束后再发布 tick 信息
    }

    if (order_type == SZOrderType::Market) // 市价单消息
    {
        if (pending_order_ != 0) // 有未完结主动订单
        {
            // 当前未完结主动订单必须是市价单，而且是部分成交的对方最优市价单，因为
            //   - 限价单一开始就是确定结局的，它肯定已在前面的成交消息中被处理，其结局是全部成交或转为被动订单
            //   - 其它市价单的结局必然是全部成交（前面的成交消息中可确定）或撤销（前面的撤销消息中可确定）
            if (!pending_is_market_)
            {
                BAD_THING("连续竞价阶段收到市价单消息，但此时有未完结的主动订单，且不是市价单");
                return nullptr;
            }
            close_pending_order_();
            // 这里不用发布 tick 信息，因为本市价单如果不能成交会发布 tick 消息，能成交则紧跟着会有成交消息，不会造成延时
        }
        latest_order_type_ = SZOrderType::Market;

        TradeType direction = (buy_order > 0) ? TradeType::BUY : TradeType::SELL; // 委托方向
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;

        // 如果此时对手方订单簿为空，则下一个消息必然是此市价单的自动撤销消息，此市价单就将像从来没有发生过一样
        if (direction == TradeType::BUY && total_sell_qty_ == 0) // 买单，但此时卖方订单簿为空
        {
            pending_order_ = order_no;
            pending_is_market_ = true;
            pending_expect_cancel_ = true;
            // DEBUG_LOG(std::format("市价买单，但此时卖方订单簿为空，市价单会自动撤销, order_no = {}", order_no));
            return nullptr;
        }
        else if (direction == TradeType::SELL && total_buy_qty_ == 0) // 卖单，但此时买方订单簿为空
        {
            pending_order_ = order_no;
            pending_is_market_ = true;
            pending_expect_cancel_ = true;
            // DEBUG_LOG(std::format("市价卖单，但此时买方订单簿为空，市价单会自动撤销, order_no = {}", order_no));
            return nullptr;
        }

        // 市价单，可能为对手方最优剩挂、最优五档剩撤、全档成交剩撤，全档全额成交或全撤销，其中对手方最优剩挂且无法完全成交的情形下的终局具有时间不确定性
        // 因此我们计算一下对手方当前最优价是否能满足可能的对手方剩挂市价单，未来的成交消息中会用到此变量
        if (direction == TradeType::BUY && it_ask1_->second.total_qty[1] < order_qty) // 买单且对手方最优价无法满足
        {
            pending_should_tick_after_ate1_ = true;
        }
        else if (direction == TradeType::SELL && it_bid1_->second.total_qty[0] < order_qty) // 卖单且对手方最优价无法满足
        {
            pending_should_tick_after_ate1_ = true;
        }

        pending_is_market_ = true;      // 市价单
        pending_qty_apply_ = order_qty; // 原始申报数量
        pending_qty_ = 0;               // 累积成交数量
        pending_order_ = order_no;      // 当前主动订单编号
        pending_direction_ = direction; // 当前主动订单方向
        pending_latest_price_ = 0;      // 当前主动订单最新成交价
        pending_price_num_ = 0;         // 当前主动订单已成交的价格数量，可用于判断市价单类型：对方最优最多只能有一个成交价，5档剩撤最多有5个，全档剩撤可以超过5个

        return nullptr; // 对于市价单，延迟到成交结束后、或自动撤销、或对手方最优吃完当前最优价位时再发布 tick 信息
    }
    else if (order_type == SZOrderType::Best) // 本方最优委托消息，在深交所规则中这也称为一种市价单，但在通联分类里单独成一类
    {
        if (pending_order_ != 0) // 有未完结主动订单
        {
            // 当前未完结主动订单必须是市价单，而且是部分成交的对方最优市价单，因为
            //   - 限价单一开始就是确定结局的，它肯定已在前面的成交消息中被处理，其结局是全部成交或转为被动订单
            //   - 其它市价单的结局必然是全部成交（前面的成交消息中可确定）或撤销（前面的撤销消息中可确定）
            if (!pending_is_market_)
            {
                BAD_THING("连续竞价阶段收到限价单消息，但此时有未完结的主动订单，且不是市价单");
                return nullptr;
            }
            close_pending_order_();
            // 这里不用发布 tick 信息，因为本限价单如果不能成交会发布 tick 消息，能成交则紧跟着会有成交消息，不会造成延时
        }

        TradeType direction = (buy_order > 0) ? TradeType::BUY : TradeType::SELL; // 委托方向
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;

        if (direction == TradeType::BUY) // 买单
        {
            order_price = bid1_;
        }
        else // 卖单
        {
            order_price = ask1_;
        }

        // 按照交易规则，如果当前本方订单簿为空，则本方最优委托会自动撤销，不知道这种自动撤销的本方最优委托消息交易所是否还会发出来
        if (order_price == 0) // 本方订单簿为空
        {
            pending_expect_cancel_ = true;
            pending_is_market_ = false;
            pending_order_ = order_no;
            latest_order_type_ = SZOrderType::Best;
            // DEBUG_LOG(std::format("本方最优委托: 本方订单簿为空，等待立即撤销!"));
            return nullptr;
        }

        // 肯定不能成交，直接插入订单簿变成被动订单
        insert_limit_order_(order_price, order_no, order_qty, direction);

        // 本方最优只在连续竞价期间合法，且不会成交，所以要生成 tick 行情，但无需计算买一卖一
        export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_ORDER, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
        tick_info_.tick_vol = order_qty;
        tick_info_.tick_money = order_qty * order_price / 1000.0;
        latest_order_type_ = SZOrderType::Best;
        return &tick_info_;
    }
    else if (order_type == SZOrderType::Cancel) // 连续竞价阶段收到的撤单消息
    {
        // if (bid1_ != 0 && it_bid1_->first != bid1_)
        // {
        //     BAD_THING(std::format("撤单前: it_bid1_ 指向价位 {}，但 bid1_ 为 {}", it_bid1_->first, bid1_));
        //     return nullptr;
        // }
        TradeType direction = (buy_order > 0) ? TradeType::BUY : TradeType::SELL; // 委托方向
        int64_t order_no = (direction == TradeType::BUY) ? buy_order : sell_order;

        if (pending_expect_cancel_) // 前面的市价单确认了没有对对手盘，或本方最优没有队友盘，则应该立即撤销
        {
            pending_order_ = 0;
            pending_expect_cancel_ = false;
            // DEBUG_LOG(std::format("收到前面 {} {} 自动撤销消息", pending_is_market_ ? "市价单" : "本方最优委托", order_no));
            latest_order_type_ = SZOrderType::Cancel;

            return nullptr; // 不需要生成 tick 消息，因为被立即取消的市价单对订单簿没有产生任何影响
        }

        if (pending_order_ > 0) // 有未完结主动订单
        {
            // 处理未完结的主动订单，当前未完结主动订单必须是市价单
            if (!pending_is_market_)
            {
                BAD_THING("连续竞价阶段收到限价单消息，但此时有未完结的主动订单，且不是市价单");
                return nullptr;
            }
            if (pending_order_ != order_no) // 本次撤销的不是当前未完结主动订单，而是在当前 orderbook_ 中的被动订单
            {
                close_pending_order_(); // 当前未完结市价单应该是对方最优市价单
                // 这里不用发布 tick 信息，因为我们马上还要处理本次的撤单消息
            }
            else // 撤销的就是当前未完结的主动订单，大概率是五档剩撤、全档剩撤的自动撤单，小概率是对手方最优吃完当前最优价位后的主动撤销
            {
                pending_order_ = 0;
                latest_order_type_ = SZOrderType::Cancel;

                return nullptr; // 不需要生成 tick 信息，因为被撤销的是还未进入订单簿的当前主动订单，订单簿本身没变化
            }
        }

        // 没有未完结的主动订单，或有未完结的主动订单但前面已经处理完毕，本次撤销的是当前 orderbook_ 中的被动订单
        cancel_order_(order_no, order_qty, direction, true); // 连续竞价期间需要做调整买一、卖一计算
        // if (bid1_ != 0 && it_bid1_->first != bid1_)
        // {
        //     BAD_THING(std::format("撤单后: it_bid1_ 指向价位 {}，但 bid1_ 为 {}", it_bid1_->first, bid1_));
        //     return nullptr;
        // }
        export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_CANCEL, direction == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
        latest_order_type_ = SZOrderType::Cancel;
        return &tick_info_;
    }
    else if (order_type == SZOrderType::Transaction) // 成交消息
    {
        // 连续竞价期间，成交消息一定是可成交的市价或限价单触发的，所以其紧邻的前序消息必然是市价单、限价单或者该市价单或限价单的前序成交消息，
        // 一定不可能是撤单消息或本方最优消息（因为不可成交）
        if (latest_order_type_ == SZOrderType::Cancel || latest_order_type_ == SZOrderType::Best)
        {
            BAD_THING("成交消息前面不可能是撤单消息或本方最优消息");
            return nullptr;
        }
        latest_order_type_ = SZOrderType::Transaction;

        // 连续竞价期间，成交消息一定有一个主动方和一个被动方，主动方即当前未完结主动订单
        if (pending_order_ == 0) // 没有未完结的主动订单
        {
            BAD_THING("连续竞价阶段收到成交消息，但此时没有未完结的主动订单");
            return nullptr;
        }

        int64_t active_order = (pending_direction_ == TradeType::BUY) ? buy_order : sell_order;
        int64_t passive_order = (pending_direction_ == TradeType::BUY) ? sell_order : buy_order;
        int passive_order_index = (pending_direction_ == TradeType::BUY) ? 1 : 0;
        int muliplier = (pending_direction_ == TradeType::BUY) ? 1 : -1; // 用于流入、流出金额的正负

        // 主动方订单应该是当前未完结主动订单，而被动方订单应该在订单池中
        if (active_order != pending_order_) // 买单不是当前未完结主动订单
        {
            BAD_THING("连续竞价阶段收到成交消息，但主动订单不是当前未完结主动订单");
            return nullptr;
        }
        if (!orderpool_.contains(passive_order)) // 被动方订单不在订单池中
        {
            BAD_THING("连续竞价阶段收到成交消息，但被动订单不在订单池中");
            return nullptr;
        }
        if (orderpool_[passive_order][0] != order_price) // 被动方订单报价不等于成交价
        {
            BAD_THING(std::format("连续竞价阶段收到成交消息，但成交价 {} 不等于被动订单报价 {}", order_price, orderpool_[passive_order][0]));
            return nullptr;
        }

        // 检查一下被动订单也应该在主订单薄中
        if (!orderbook_.contains(order_price)) // 被动订单对应价位不在订单薄中
        {
            BAD_THING(std::format("连续竞价阶段收到成交消息，但被动订单对应价位不在订单薄中, order_price = {}", order_price));
            return nullptr;
        }
        if (!orderbook_[order_price].orders[passive_order_index].contains(passive_order)) // 被动订单不在订单薄中
        {
            BAD_THING(std::format("连续竞价阶段收到成交消息，但被动订单不在订单薄中, passive_order = {}", passive_order));
            return nullptr;
        }

        // 连续竞价期间，成交价永远是被动方的最优价
        if (pending_direction_ == TradeType::BUY && order_price != ask1_) // 买单，成交价不是卖一价
        {
            BAD_THING(std::format("连续竞价阶段收到主动买成交消息，但买单成交价 {} 不是卖一价 {}，被动卖方价格为 {}", order_price, ask1_, orderpool_[passive_order][0]));
            return nullptr;
        }
        else if (pending_direction_ == TradeType::SELL && order_price != bid1_) // 卖单，成交价不是买一价
        {
            BAD_THING(std::format("连续竞价阶段收到主动卖成交消息，但卖单成交价 {} 不是买一价 {}，被动买方价格为 {}", order_price, bid1_, orderpool_[passive_order][0]));
            return nullptr;
        }

        // 累计成交量、成交笔数、成交金额，单位厘
        trade_qty_ += order_qty;
        trade_num_++;
        trade_money_ += order_qty * order_price;

        int64_t trade_qty = order_qty;
        int64_t trade_money = order_qty * order_price; // 以厘为单位的成交金额

        buy_order_index_ = buy_order;
        sell_order_index_ = sell_order;

        latest_ = order_price; // 以厘为单位的成交价格
        if (pending_latest_price_ != order_price)
        {
            pending_price_num_++; // 本主动订单已成交过的不同价格数量
            pending_latest_price_ = order_price;
        }

        // 被动单成交造成的不同大小订单的净流入资金统计，XXX 这里将进行优化：因为深交所每笔委托都是知道申报量的，没必要搞按大小分的订单池
        bool was_lg_passive = false;
        if (orderpool_sm_.contains(passive_order)) // 被动单属于小单
        {
            sm_order_cum_vol_ -= muliplier * trade_money / 1000.0;
        }
        else if (orderpool_mid_.contains(passive_order)) // 被动单属于中单
        {
            mid_order_cum_vol_ -= muliplier * trade_money / 1000.0;
        }
        else if (orderpool_lg_.contains(passive_order)) // 被动单属于大单
        {
            lg_order_cum_vol_ -= muliplier * trade_money / 1000.0;
            was_lg_passive = true;
        }
        else if (orderpool_slg_.contains(passive_order)) // 被动单属于超大单
        {
            slg_order_cum_vol_ -= muliplier * trade_money / 1000.0;
            was_lg_passive = true;
        }
        else
        {
            BAD_THING(std::format("成交信息: 在4种订单池子中都未找到被动订单, passive_order = {}", passive_order));
            return nullptr;
        }

        // 被动单成交造成的当前有效大单统计变动
        if (was_lg_passive)
        {
            if (!orderbook_vol_lg_.contains(order_price)) // 以价格为索引的大单量总量统计，成交价（也是被动单价）位未曾有大单或超大单
            {
                BAD_THING(std::format("在大单量总量统计中未找到要减量的价位, order_price = {}", order_price));
                return nullptr;
            }
            else if (orderbook_vol_lg_[order_price][passive_order_index] < trade_qty) // 该价位的大单量小于要删除的数量
            {
                BAD_THING(std::format("在大单量总量统计中当前量小于要删除的数量, order_price = {}, passive_order_index = {}, trade_qty = {}, vol = {}", order_price, passive_order_index, trade_qty, orderbook_vol_lg_[order_price][passive_order_index]));
                return nullptr;
            }
            orderbook_vol_lg_[order_price][passive_order_index] -= trade_qty;
            if (orderbook_vol_lg_[order_price][0] == 0 && orderbook_vol_lg_[order_price][1] == 0) // 该价位的大单都已删除
            {
                orderbook_vol_lg_.erase(order_price);
            }
        }

        // 主动单成交造成的不同大小订单的净注入资金统计，主动单造成的当前有效大单统计先不进行，等待主动单变成被动单时再统计（当然如果最后主动单剩撤了也就无需再统计了）
        if (pending_qty_apply_ <= mid_threshold_) // 主动单属于小单
        {
            sm_order_cum_vol_ += muliplier * trade_money / 1000.0;
        }
        else if (pending_qty_apply_ <= lg_threshold_) // 主动单属于中单
        {
            mid_order_cum_vol_ += muliplier * trade_money / 1000.0;
        }
        else if (pending_qty_apply_ <= slg_threshold_) // 主动单属于大单
        {
            lg_order_cum_vol_ += muliplier * trade_money / 1000.0;
        }
        else // 主动单属于超大单
        {
            slg_order_cum_vol_ += muliplier * trade_money / 1000.0;
        }

        // 更新被动订单成交后当前总申买量或总申卖量
        if (pending_direction_ == TradeType::BUY) // 被动方为卖单
        {
            total_sell_qty_ -= trade_qty;
        }
        else
        {
            total_buy_qty_ -= trade_qty;
        }

        // 更新订单池与主订单簿中的被动订单的未成交数量
        orderpool_[passive_order][1] -= trade_qty;
        orderbook_[order_price].orders[passive_order_index][passive_order] -= trade_qty; // 修改主订单薄中被动订单的数量，减掉成交部分
        orderbook_[order_price].total_qty[passive_order_index] -= trade_qty; // 修改主订单薄中该价位上该方向的总订单量
        bool passive_best_changed = false; // 被动方最优价是否发生变化
        if (orderpool_[passive_order][1] == 0) // 该被动订单已全部成交，如果它是被动方最后一个订单，则会引起被动方最优价变化
        {
            orderpool_.erase(passive_order); // 从订单池中删除
            orderbook_[order_price].orders[passive_order_index].erase(passive_order); // 也要从主订单簿中删除
            if (pending_direction_ == TradeType::BUY && orderbook_[order_price].orders[1].empty()) // 主动买单把被动卖单当前最优价订单都吃完了
            {
                if (orderbook_[order_price].total_qty[1] != 0)
                {
                    BAD_THING(std::format("连续竞价成交: 订单薄价位 {} 中所有卖单都已删除, 但该价位的总卖单量不为 0: {}", order_price, orderbook_[order_price].total_qty[1]));
                    return nullptr;
                }
                if (!orderbook_[order_price].orders[0].empty()) // 该价位竟然有买单
                {
                    BAD_THING(std::format("连续竞价成交: 订单薄价位 {} 中所有卖单都已删除, 但该价位的总买单量不为 0: {}", order_price, orderbook_[order_price].total_qty[0]));
                    return nullptr;
                }
                if (total_sell_qty_ > 0) // 对手方仍有其它卖单
                {
                    if (it_ask1_ == orderbook_.end())
                    {
                        BAD_THING(std::format("连续竞价成交: it_ask1_ 已经到底了, 卖方却还有申报量"));
                        return nullptr;
                    }
                    it_ask1_++; // 新的对手方卖一价为更高的相邻价位
                    ask1_ = it_ask1_->first;
                    if (ask1_ < lower_limit_) BAD_THING(std::format("连续竞价成交: 新的对手方卖一价 {} 比跌停价 {} 还低", ask1_, lower_limit_));
                }
                else // 所有对手方卖单都已删除
                {
                    ask1_ = 0;
                    it_ask1_ = orderbook_.end();
                }
                orderbook_.erase(order_price);
                passive_best_changed = true;
            }
            else if (pending_direction_ == TradeType::SELL && orderbook_[order_price].orders[0].empty()) // 主动卖单把被动买单当前最优价订单都吃完了
            {
                if (orderbook_[order_price].total_qty[0] != 0)
                {
                    BAD_THING(std::format("连续竞价成交: 订单薄价位 {} 中所有买单都已删除, 但该价位的总买单量不为 0: {}", order_price, orderbook_[order_price].total_qty[0]));
                    return nullptr;
                }
                if (!orderbook_[order_price].orders[1].empty()) // 该价位竟然有卖单
                {
                    BAD_THING(std::format("连续竞价成交: 订单薄价位 {} 中所有买单都已删除, 但该价位的总卖单量不为 0: {}", order_price, orderbook_[order_price].total_qty[1]));
                    return nullptr;
                }
                if (total_buy_qty_ > 0) // 对手方仍有其它买单
                {
                    // if (it_bid1_ == orderbook_.end())
                    // {
                    //     BAD_THING(std::format("连续竞价成交: it_bid1_ 已经是底了, 买方却还有申报量"));
                    //     return nullptr;
                    // }
                    // if (it_bid1_->first != bid1_)
                    // {
                    //     BAD_THING(std::format("连续竞价成交: it_bid1_ 指向价位 {}，但 bid1_ 为 {}", it_bid1_->first, bid1_));
                    //     return nullptr;
                    // }
                    it_bid1_--; // 新的对手方买一价为更低的相邻价位
                    // if (it_bid1_ == orderbook_.end())
                    // {
                    //     BAD_THING(std::format("连续竞价成交: it_bid1_ 已经到底了, 买方却还有申报量"));
                    //     return nullptr;
                    // }
                    bid1_ = it_bid1_->first;
                    // if (bid1_ > upper_limit_) BAD_THING(std::format("连续竞价成交: 新的对手方买一价 {} 比涨停价 {} 还高", bid1_, upper_limit_));
                    // // XXX
                    // if (ask1_ > 0 && bid1_ > ask1_) BAD_THING(std::format("连续竞价成交: 新的对手方买一价 {} 比卖一价 {} 还高", bid1_, ask1_));
                }
                else // 所有对手方买单都已删除
                {
                    bid1_ = 0;
                    it_bid1_ = orderbook_.end();
                }
                orderbook_.erase(order_price);
                passive_best_changed = true;
            }
        }

        // 更新被动订单成交带来的有效订单量档位统计变化
        if (!tier_dict_.contains(order_price))
        {
            BAD_THING(std::format("成交信息: 在涨跌千分点字典中未找到成交价对应价位, order_price = {}, 不在涨跌停价之间？", order_price));
            return nullptr;
        }
        int tier = tier_dict_[order_price];
        if (orderbook_tier_[tier] < trade_qty)
        {
            BAD_THING(std::format("成交信息: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
            return nullptr;
        }
        orderbook_tier_[tier] -= trade_qty;

        bool should_tick = false;               // 是否应该生成 tick 信息
        pending_qty_ += order_qty;              // 主动订单累计成交量
        if (pending_qty_ == pending_qty_apply_) // 主动订单已全部成交
        {
            close_pending_order_(); // 直接终结本主动订单
            should_tick = true;
        }
        else if (!pending_is_market_) // 当前主动订单是限价单
        {
            pending_avail_qty_ -= order_qty; // 可用来成交的数量减少
            if (pending_avail_qty_ < 0)
            {
                BAD_THING(std::format("连续竞价成交: 限价单可用数量减少后变为负数, pending_avail_qty_ = {}", pending_avail_qty_));
                return nullptr;
            }
            if (pending_avail_qty_ == 0)     // 无法完全成交的限价单已转为被动订单
            {
                close_pending_order_(); // 直接终结本主动订单
                should_tick = true;
            }
        }
        else if (passive_best_changed && pending_should_tick_after_ate1_)
        {
            // 市价单第一次把对手方最优价吃完时，应该生成 tick 信息，这是因为如果是对手方最优市价单，则在交易所主机上此单已经进入了被动订单簿，
            // 下一消息出现的时间是不确定的，所以我们生成 tick 信息以避免这种延时。后续如果还继续吃，则可排除是对手方最优市价单
            should_tick = true;
            pending_should_tick_after_ate1_ = false;
        }

        if (should_tick)
        {
            export_tick_info_(uint32_t2HHMMSSmmm(exchtime).c_str(), exchtime, TICK_TYPE_TRANSACTION, pending_direction_ == TradeType::BUY ? TICK_DIRECT_BUY : TICK_DIRECT_SELL, orderbook_tier_, tick_info_);
            tick_info_.tick_vol = trade_qty;
            tick_info_.tick_money = trade_qty * order_price / 1000.0;
            return &tick_info_;
        }
        return nullptr;
    }
    else
    {
        BAD_THING(std::format("未知的订单类型, order_type = {}", SZOrderType2Str(order_type)));
        return nullptr;
    }
    return nullptr;
}

// 从 ZMQ 接收的委托成交合并消息
Tick_info *Szot2t::put_instruction(const mdl_szl2_msg::CombinedTick &order, int dy_time, int64_t msg_no)
{
    return put_instruction(order.TransactTime.m_Value, static_cast<SZOrderType>(order.Type), order.Price.m_Value / 10, order.BidApplSeqNum, order.OfferApplSeqNum, order.Qty, dy_time, msg_no);
}

// 深交所没有状态订单，无法确定收盘集合竞价成交发布何时结束，所以需要外部调用此函数来生成收盘集合竞价结束时的 tick 信息
Tick_info *Szot2t::gen_close_tick_info()
{
    calculate_bid1_ask1_(); // 重新计算买一、卖一价
    export_tick_info_("15:00:00.000", 150000000, TICK_TYPE_TRANSACTION, TICK_DIRECT_AUCTION, orderbook_tier_, tick_info_);
    return &tick_info_;
}

// 从委托成交合并后的 parquet 文件中读取的指令
Tick_info *Szot2t::put_instruction(const order_raw_sz &order)
{
    SZOrderType order_type;
    int order_price;
    int64_t order_qty;
    int64_t buy_no = 0;
    int64_t sell_no = 0;

    if (order.execType == 52) // 撤单
    {
        order_type = SZOrderType::Cancel;
        order_price = static_cast<int>(std::roundl(order.lastPx * 1000.0)); // 要以厘为单位，实际中应该是 0
        order_qty = order.lastQty;
        if (order.bidApplSeqNum > 0)
        {
            buy_no = order.bidApplSeqNum;
        }
        else if (order.offerApplSeqNum > 0)
        {
            sell_no = order.offerApplSeqNum;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("撤单消息<{}:{}>没有对应的委托单号", order.transactionTime, order.applSeqNum));
        }
        // DEBUG_LOG(std::format("<{}>撤单", order.transactionTime));
    }
    else if (order.execType == 70) // 成交
    {
        order_type = SZOrderType::Transaction;
        order_price = std::round(order.lastPx * 1000.0);
        order_qty = order.lastQty;
        buy_no = order.bidApplSeqNum;
        sell_no = order.offerApplSeqNum;
        // DEBUG_LOG(std::format("<{}>成交", order.transactionTime));
    }
    else if (order.orderType == 49) // 市价单
    {
        order_type = SZOrderType::Market;
        order_price = std::round(order.price * 1000.0);
        order_qty = order.orderQty;
        if (order.side == 49)
        {
            buy_no = order.applSeqNum;
        }
        else if (order.side == 50)
        {
            sell_no = order.applSeqNum;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("市价单消息<{}:{}>买卖方向错误, side={}", order.transactionTime, order.applSeqNum, order.side));
        }
    }
    else if (order.orderType == 50) // 限价单
    {
        order_type = SZOrderType::Limit;
        order_price = std::round(order.price * 1000.0);
        order_qty = order.orderQty;
        // DEBUG_LOG(std::format("<{}>限价单", order.transactionTime));
        if (order.side == 49)
        {
            buy_no = order.applSeqNum;
        }
        else if (order.side == 50)
        {
            sell_no = order.applSeqNum;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("限价单消息<{}:{}>买卖方向错误, side={}", order.transactionTime, order.applSeqNum, order.side));
        }
    }
    else if (order.orderType == 85) // 本方最优
    {
        order_type = SZOrderType::Best;
        order_price = std::round(order.price * 1000.0);
        order_qty = order.orderQty;
        if (order.side == 49)
        {
            buy_no = order.applSeqNum;
        }
        else if (order.side == 50)
        {
            sell_no = order.applSeqNum;
        }
        else
        {
            bad_thing(__func__, __LINE__, std::format("限价单消息<{}:{}>买卖方向错误, side={}", order.transactionTime, order.applSeqNum, order.side));
        }
    }
    else
    {
        order_type = SZOrderType::UNKOWN;
        order_price = std::round(order.lastPx * 1000.0);
        order_qty = order.lastQty;
    }
    return put_instruction(HHMMSSmmm2uint32_t(order.transactionTime.c_str()), order_type, order_price, buy_no, sell_no, order_qty, 0, 0);
}

void Szot2t::log(LOG_LEVEL level, std::string msg)
{
    if (level >= log_level_)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cout << std::format("[{}][{}] {}", std::chrono::current_zone()->to_local(tp_utc), LOG_LEVEL2Str(level), msg) << std::endl;
    }
}

void Szot2t::log(LOG_LEVEL sys_level, LOG_LEVEL level, std::string msg)
{
    if (level >= sys_level)
    {
        const auto tp_utc{std::chrono::system_clock::now()};

        std::cout << std::format("[{}][{}] {}", std::chrono::current_zone()->to_local(tp_utc), LOG_LEVEL2Str(level), msg) << std::endl;
    }
}

void Szot2t::export_tick_info_(const char *t, uint32_t exchtime, int8_t tick_type, int8_t tick_direc, std::map<int, int> &tier, Tick_info &tick_info)
{
    std::strncpy(tick_info.time, t, sizeof(tick_info.time) - 1); // 当前只用时间部分
    tick_info.time[sizeof(tick_info.time) - 1] = 0;
    tick_info.exchtime = exchtime;
    tick_info.tick_type = tick_type;
    tick_info.tick_direc = tick_direc;
    tick_info.orderbook_tier = tier;

    int i = 0;
    auto it = it_bid1_;
    while (i < 20 && it != orderbook_.end()) // 20档买价
    {
        // 1. 从 orderbook_ 中取出 20 个价位
        // 2. 将这些价位的订单量、订单笔数、大单订单量等信息填入 tick_info 中
        tick_info.bid20prices[i] = it->first;
        tick_info.bid20qty[i] = it->second.total_qty[0];
        tick_info.bid20num[i] = it->second.orders[0].size();
        auto itlg = orderbook_vol_lg_.find(it->first);
        if (itlg == orderbook_vol_lg_.end())
        {
            tick_info.bid20lgqty[i] = 0;
        }
        else
        {
            tick_info.bid20lgqty[i] = itlg->second[0];
        }
        // tick_info.bid20lgqty[i] = orderbook_vol_lg_[it->first][0];
        it--;
        i++;
    }
    while (i < 20) // 当前不够20档，剩下的用 0 填充
    {
        tick_info.bid20prices[i] = 0;
        tick_info.bid20qty[i] = 0;
        tick_info.bid20num[i] = 0;
        tick_info.bid20lgqty[i] = 0;
        i++;
    }
    i = 0;
    it = it_ask1_;
    while (i < 20 && it != orderbook_.end()) // 20档卖价
    {
        tick_info.ask20prices[i] = it->first;
        tick_info.ask20qty[i] = it->second.total_qty[1];
        tick_info.ask20num[i] = it->second.orders[1].size();
        // if (tick_type == TICK_TYPE_TRANSACTION && tick_direc == TICK_DIRECT_AUCTION)
        //     std::cout << "xxx4.5" << std::endl;
        auto itlg = orderbook_vol_lg_.find(it->first);
        if (itlg == orderbook_vol_lg_.end())
        {
            tick_info.ask20lgqty[i] = 0;
        }
        else
        {
            // std::cout << "xxx4.7" << std::endl;
            tick_info.ask20lgqty[i] = itlg->second[1];
        }
        // tick_info.ask20lgqty[i] = orderbook_vol_lg_[it->first][1];
        it++;
        i++;
    }
    while (i < 20) // 当前不够20档，剩下的用 0 填充
    {
        tick_info.ask20prices[i] = 0;
        tick_info.ask20qty[i] = 0;
        tick_info.ask20num[i] = 0;
        tick_info.ask20lgqty[i] = 0;
        i++;
    }
    tick_info.total_buy_qty = total_buy_qty_;
    tick_info.total_sell_qty = total_sell_qty_;
    tick_info.prev_close = prev_close_;
    tick_info.latest = latest_;
    tick_info.mid_price = (latest_buy_price_ + latest_sell_price_) / 2;
    tick_info.spread = ask1_ - bid1_;
    tick_info.sm_order_cum_vol = lround(sm_order_cum_vol_);
    tick_info.mid_order_cum_vol = lround(mid_order_cum_vol_);
    tick_info.lg_order_cum_vol = lround(lg_order_cum_vol_);
    tick_info.slg_order_cum_vol = lround(slg_order_cum_vol_);
    tick_info.buy_order_index = buy_order_index_;
    tick_info.sell_order_index = sell_order_index_;
    tick_info.trade_money = trade_money_;
    tick_info.trade_qty = trade_qty_;
    tick_info.trade_num = trade_num_;
}

void Szot2t::calculate_bid1_ask1_(void)
{
    bid1_ = 0; // 0 表示当前无买单
    it_bid1_ = orderbook_.end();
    ask1_ = 0; // 0 表示当前无卖单
    it_ask1_ = orderbook_.end();
    if (latest_ > 0) // 集合竞价期间有成交
    {
        auto it = orderbook_.lower_bound(latest_); // 指向大于等于成交价的最低非空价位
        if (it != orderbook_.end())                // 大于等于成交价的非空价位是存在的，大于此价位上只存在卖单。如果此价位就是成交价本身，那么它上面要么是剩余买单，要么是剩余卖单
        {
            DEBUG_LOG(std::format("大于等于集合竞价成交价的价位是有买单或卖单的, 成交价为 {}, 大于等于该价的最低非空价位为 {}", latest_, it->first));
            if (!it->second.orders[0].empty()) // 该价位上有买单，it 所指向的价位肯定就是成交价本身
            {
                DEBUG_LOG(std::format("大于等于成交价最低非空价位上有买单，因此买一价就是此价位 {}，申买量为 {}，买单数量：{}", it->first, it->second.total_qty[0], it->second.orders[0].size()));
                bid1_ = it->first;
                it_bid1_ = it;
                it++;                       // 如果还有卖单，一定在更高价位上
                if (it != orderbook_.end()) // 更高价位上有卖单
                {
                    DEBUG_LOG(std::format("更高价位上有卖单，因此卖一价就是此价位 {}，申卖量为 {}，卖单数量：{}", it->first, it->second.total_qty[1], it->second.orders[1].size()));
                    ask1_ = it->first;
                    it_ask1_ = it;
                }
                else
                {
                    DEBUG_LOG(std::format("更高价位上无卖单，卖一价 {}，应该保持为 0", ask1_));
                }
            }
            else // 该价位（可能是成交价，也可能是高于成交价的最低邻近价位）没有买单，那么只可能是有卖单
            {
                ask1_ = it->first; // 本价位肯定有卖单
                it_ask1_ = it;
                DEBUG_LOG(std::format("大于等于成交价最低非空价位上有卖单，因此卖一价就是此价位 {}，申卖量为 {}，卖单数量：{}", it->first, it->second.total_qty[1], it->second.orders[1].size()));
                it--;                       // 如果还有买单，一定在更低价位上
                if (it != orderbook_.end()) // 更低价位上有买单
                {
                    bid1_ = it->first;
                    it_bid1_ = it;
                    DEBUG_LOG(std::format("更低价位上有买单，因此买一价就是此价位 {}，申买量为 {}，买单数量：{}", it->first, it->second.total_qty[0], it->second.orders[0].size()));
                }
                else
                {
                    DEBUG_LOG(std::format("更低价位上无买单，买一价 {}，应该保持为 0", bid1_));
                }
            }
        }
    }
    else // 集合竞价期间没有成交
    {
        if (total_buy_qty_ > 0) // 有买单
        {
            DEBUG_LOG(std::format("集合竞价期间没有成交，但有买单"));
            auto it = orderbook_.begin();
            while (!it->second.orders[0].empty() && it != orderbook_.end()) // 从最低价开始找，找到第一个没有买单的价位
            {
                it++;
            }
            if (it != orderbook_.end()) // 有卖单
            {
                ask1_ = it->first;
                it_ask1_ = it;
                DEBUG_LOG(std::format("也有卖单，卖一价为 {}", it->first));
                it--; // 回退一个价位是最高买价
                bid1_ = it->first;
                it_bid1_ = it;
                DEBUG_LOG(std::format("买一价为 {}", it->first));
            }
            else // 全是买单，没有卖单
            {
                it_bid1_ = std::prev(orderbook_.end());
                bid1_ = it_bid1_->first; // ask1_ 保持 0
                DEBUG_LOG(std::format("买一价为 {}，没有卖单，卖一价为 0", bid1_));
            }
        }
        else if (total_sell_qty_ > 0) // 没有买单，但有卖单
        {
            ask1_ = orderbook_.begin()->first;
            it_ask1_ = orderbook_.begin();
            DEBUG_LOG(std::format("集合竞价期间没有成交，没有买单，但有卖单，卖一价为 {}", ask1_));
        } // 完全无买单和卖单，应极为罕见
        else
        {
            DEBUG_LOG(std::format("集合竞价期间没有成交，也没有买单和卖单，应极为罕见"));
        }
    }
}

// 新增限价委托订单，不进行盘口计算
void Szot2t::insert_limit_order_(int order_price, int64_t order_no, int64_t order_qty, TradeType direction)
{
    int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标

    // 订单池以订单编号为索引，值为[价格(厘), 数量]
    orderpool_[order_no][0] = order_price;
    orderpool_[order_no][1] = order_qty;

    // 将本订单按大小分类加入对应的池子中
    if (order_qty <= mid_threshold_)
    {
        orderpool_sm_.insert(order_no);
    }
    else if (order_qty <= lg_threshold_)
    {
        orderpool_mid_.insert(order_no);
    }
    else if (order_qty <= slg_threshold_)
    {
        orderpool_lg_.insert(order_no);
    }
    else
    {
        orderpool_slg_.insert(order_no);
    }

    // 以价格为索引的30万以上大单当前有效总量统计，不区分大单和超大单了
    if (order_qty > lg_threshold_)
    {
        if (orderbook_vol_lg_.contains(order_price))
        {
            orderbook_vol_lg_[order_price][direct_index] += order_qty;
        }
        else
        {
            orderbook_vol_lg_[order_price][direct_index] = order_qty;
        }
    }

    // 订单薄以价格为索引，值为[买单, 卖单]，买单和卖单都是以订单编号为索引，值为数量
    orderbook_[order_price].orders[direct_index][order_no] = order_qty;
    orderbook_[order_price].total_qty[direct_index] += order_qty;

    // 当前总申买量和总申卖量
    if (direction == TradeType::BUY)
    {
        total_buy_qty_ += order_qty;
    }
    else
    {
        total_sell_qty_ += order_qty;
    }

    // 价格档位
    if (tier_dict_.contains(order_price))
    {
        int tier = tier_dict_[order_price];
        orderbook_tier_[tier] += order_qty;
    }
    else
    {
        BAD_THING_(std::format("新增委托订单: 在涨跌千分点字典中未找到订单对应价位, order_price = {}, 不在涨跌停价之间？", order_price));
    }
}

// 撤销委托订单
void Szot2t::cancel_order_(int64_t order_no, int64_t cancel_qty, TradeType direction, bool do_bid1_ask1)
{
    int direct_index = (direction == TradeType::BUY) ? 0 : 1; // 用于买卖数组下标

    if (!orderpool_.contains(order_no)) // 在订单池中找不到该订单号
    {
        BAD_THING_(std::format("要撤销的订单在 orderpool_ 中不存在, order_no = {}", order_no));
        return;
    }

    int cancel_price = orderpool_[order_no][0];
    if (cancel_qty != orderpool_[order_no][1])
    {
        BAD_THING_(std::format("要撤销的订单在 orderpool_ 中的数量与撤销数量不一致, order_no = {}, cancel_qty = {}, orderpool_[order_no][1] = {}", order_no, cancel_qty, orderpool_[order_no][1]));
        return;
    }
    (void)orderpool_.erase(order_no); // 从订单池中删除订单

    if (!orderbook_.contains(cancel_price)) // 主订单薄中该价位不存在
    {
        BAD_THING_(std::format("要撤销的订单对应价位在 orderbook_ 中不存在, cancel_price = {}", cancel_price));
        return;
    }
    if (!orderbook_[cancel_price].orders[direct_index].contains(order_no)) // 在主订单薄中该价位上不存在该订单号
    {
        BAD_THING_(std::format("要撤销的订单在对应价位的 orderbook_ 中不存在, order_no = {}, cancel_price = {}", order_no, cancel_price));
        return;
    }
    if (orderbook_[cancel_price].orders[direct_index][order_no] != cancel_qty) // 在主订单薄中该价位上该订单号的数量与撤销数量不一致
    {
        BAD_THING_(std::format("要撤销的订单在对应价位的 orderbook_ 中的数量与撤销数量不一致, order_no = {}, cancel_qty = {}, orderbook_[cancel_price].orders[direct_index][order_no] = {}", order_no, cancel_qty, orderbook_[cancel_price].orders[direct_index][order_no]));
        return;
    }
    (void)orderbook_[cancel_price].orders[direct_index].erase(order_no); // 从主订单薄中该价位上删除该订单号

    if (direction == TradeType::BUY)
    {
        total_buy_qty_ -= cancel_qty;
        if (total_buy_qty_ < 0)
        {
            BAD_THING_(std::format("撤销买单({} 股)后总买量变为负数, total_buy_qty_ = {}", cancel_qty, total_buy_qty_));
            return;
        }
    }
    else
    {
        total_sell_qty_ -= cancel_qty;
        if (total_sell_qty_ < 0)
        {
            BAD_THING_(std::format("撤销卖单({} 股)后总卖量变为负数, total_sell_qty_ = {}", cancel_qty, total_sell_qty_));
            return;
        }
    }

    if (do_bid1_ask1)                                                    // 处理盘口状态，连续竞价期间需要
    {
        if (cancel_price == bid1_) // 撤单价为买一价，说明被撤的订单是一个买单
        {
            if (direction != TradeType::BUY)
            {
                BAD_THING_(std::format("撤销订单: 撤单价为买一价，但被撤的订单是一个卖单"));
                return;
            }
            buy_order_index_ = order_no;
            sell_order_index_ = 0;
            if (orderbook_[cancel_price].orders[0].empty()) // 该价位的买单都已删除
            {
                if (total_buy_qty_ == 0) // 甚至所有买单都已删除
                {
                    bid1_ = 0;
                    it_bid1_ = orderbook_.end();
                }
                else // 仍有买单，但已删除买一价
                {
                    it_bid1_--; // 新买一价为更低的相邻价位
                    bid1_ = it_bid1_->first;
                }
            }
        }
        else if (cancel_price == ask1_) // 撤单价为卖一价，说明被撤的订单是一个卖单
        {
            if (direction != TradeType::SELL)
            {
                BAD_THING_(std::format("撤销订单: 撤单价为卖一价，但被撤的订单是一个买单"));
                return;
            }
            buy_order_index_ = 0;
            sell_order_index_ = order_no;
            if (orderbook_[cancel_price].orders[1].empty()) // 该价位的卖单都已删除
            {
                if (total_sell_qty_ == 0) // 所有卖单都已删除
                {
                    ask1_ = 0;
                    it_ask1_ = orderbook_.end();
                }
                else // 仍有卖单，但已删除卖一价
                {
                    it_ask1_++; // 新卖一价为更高的相邻价位
                    ask1_ = it_ask1_->first;
                }
            }
        }
    }
    if (orderbook_[cancel_price].orders[0].empty() && orderbook_[cancel_price].orders[1].empty()) // 该价位的买、卖订单都已删除
    {
        orderbook_.erase(cancel_price);
    }
    else
    {
        orderbook_[cancel_price].total_qty[direct_index] -= cancel_qty; // 调减主订单薄中该价位该方向上的订单总量
        if (orderbook_[cancel_price].total_qty[direct_index] < 0)
        {
            BAD_THING_(std::format("撤销订单后主订单薄中该价位该方向上的订单总量变为负数, cancel_price = {}, direct_index = {}, orderbook_[cancel_price].total_qty[direct_index] = {}", cancel_price, direct_index, orderbook_[cancel_price].total_qty[direct_index]));
            return;
        }
    }

    bool was_lg = false;
    if (orderpool_sm_.contains(order_no)) // 属于小单
    {
        orderpool_sm_.erase(order_no);
    }
    else if (orderpool_mid_.contains(order_no)) // 属于中单
    {
        orderpool_mid_.erase(order_no);
    }
    else if (orderpool_lg_.contains(order_no)) // 属于大单
    {
        was_lg = true;
        orderpool_lg_.erase(order_no);
    }
    else if (orderpool_slg_.contains(order_no)) // 属于超大单
    {
        was_lg = true;
        orderpool_slg_.erase(order_no);
    }
    else
    {
        BAD_THING_(std::format("在4种订单池子中都未找到要撤销的订单, order_no = {}", order_no));
        return;
    }

    if (was_lg)
    {
        if (!orderbook_vol_lg_.contains(cancel_price)) // 以价格为索引的大单量总量统计，该价位未曾有大单或超大单
        {
            BAD_THING_(std::format("在大单量总量统计中未找到要减量的价位, cancel_price = {}, direct_index = {}, cancel_qty = {}", cancel_price, direct_index, cancel_qty));
            return;
        }
        else if (orderbook_vol_lg_[cancel_price][direct_index] < cancel_qty) // 该价位的大单量小于要删除的数量
        {
            BAD_THING_(std::format("在大单量总量统计中当前量小于要删除的数量, cancel_price = {}, direct_index = {}, cancel_qty = {}, vol = {}", cancel_price, direct_index, cancel_qty, orderbook_vol_lg_[cancel_price][direct_index]));
            return;
        }
        orderbook_vol_lg_[cancel_price][direct_index] -= cancel_qty;
        if (orderbook_vol_lg_[cancel_price][0] == 0 && orderbook_vol_lg_[cancel_price][1] == 0) // 该价位的大单都已删除
        {
            orderbook_vol_lg_.erase(cancel_price);
        }
    }

    if (!tier_dict_.contains(cancel_price))
    {
        BAD_THING_(std::format("撤销订单: 在涨跌千分点字典中未找到订单对应价位, cancel_price = {}, 不在涨跌停价之间？", cancel_price));
        return;
    }
    int tier = tier_dict_[cancel_price];
    if (orderbook_tier_[tier] < cancel_qty)
    {
        BAD_THING_(std::format("撤销订单: 在涨跌千分点挂单量小于撤销数量, tier_qty = {}, cancel_qty = {}", orderbook_tier_[tier], cancel_qty));
        return;
    }
    orderbook_tier_[tier] -= cancel_qty;
}

// 处理集合竞价期间的成交，成交双方的委托订单都在订单池中
// 实际上集合竞价成交的订单只有申报价格等于成交价的某一方向有可能部分成交，其它的都是完全成交，所以可能可以优化一下
void Szot2t::auction_transaction_(int64_t buy_order, int64_t sell_order, int64_t trade_qty, int64_t trade_price)
{
    if (!orderpool_.contains(buy_order)) // 买单不在订单池中
    {
        BAD_THING_(std::format("集合竞价期间在订单池中未找到成交买单, buy_order = {}, sell_order = {}, trade_qty = {}, trade_price = {}", buy_order, sell_order, trade_qty, trade_price));
        return;
    }
    if (!orderpool_.contains(sell_order)) // 卖单不在订单池中
    {
        BAD_THING_(std::format("集合竞价期间在订单池中未找到成交卖单, sell_order = {}, buy_order = {}, trade_qty = {}, trade_price = {}", sell_order, buy_order, trade_qty, trade_price));
        return;
    }

    // 成交量、成交笔数、成交金额，单位厘
    trade_qty_ += trade_qty;
    trade_num_++;
    trade_money_ += trade_qty * trade_price;
    
    // 集合竞价期间，原始订单的申报价与实际成交价一般是不相等的
    int buy_order_price = orderpool_[buy_order][0];   // 原始买单报价，单位厘
    int sell_order_price = orderpool_[sell_order][0]; // 原始卖单报价，单位厘

    if (!orderbook_.contains(buy_order_price)) // 买单价位不在主订单薄中
    {
        BAD_THING_(std::format("集合竞价期间在主订单薄中未找到成交买单的原始价位, buy_order_price = {}", buy_order_price));
        return;
    }
    if (!orderbook_[buy_order_price].orders[0].contains(buy_order)) // 买单不在该价位的买单表中
    {
        BAD_THING_(std::format("集合竞价期间在主订单薄中未找到成交买单, buy_order = {}", buy_order));
        return;
    }
    if (!orderbook_.contains(sell_order_price)) // 卖单价位不在主订单薄中
    {
        BAD_THING_(std::format("集合竞价期间在主订单薄中未找到成交卖单的原始价位, sell_order_price = {}", sell_order_price));
        return;
    }
    if (!orderbook_[sell_order_price].orders[1].contains(sell_order)) // 卖单不在该价位的卖单表中
    {
        BAD_THING_(std::format("集合竞价期间在主订单薄中未找到成交卖单, sell_order = {}", sell_order));
        return;
    }

    bool buy_fininshed = false;                                    // 买单是否完全成交
    bool sell_fininshed = false;                                   // 卖单是否完全成交
    latest_ = trade_price;                                         // 以厘为单位的成交价格
    orderpool_[buy_order][1] -= trade_qty;                         // 修改订单池中成交买单的数量，减掉成交部分
    orderbook_[buy_order_price].orders[0][buy_order] -= trade_qty; // 修改主订单薄中成交买单的数量，减掉成交部分
    orderbook_[buy_order_price].total_qty[0] -= trade_qty;
    if (orderpool_[buy_order][1] == 0)                             // 买单已完全成交
    {
        orderpool_.erase(buy_order);
        orderbook_[buy_order_price].orders[0].erase(buy_order);
        if (orderbook_[buy_order_price].orders[0].empty() && orderbook_[buy_order_price].orders[1].empty()) // 主订单薄中该价位的买卖订单都已删除
        {
            orderbook_.erase(buy_order_price);
        }
        buy_fininshed = true;
    }
    orderpool_[sell_order][1] -= trade_qty;                          // 修改订单池中成交卖单的数量，减掉成交部分
    orderbook_[sell_order_price].orders[1][sell_order] -= trade_qty; // 修改主订单薄中成交卖单的数量，减掉成交部分
    orderbook_[sell_order_price].total_qty[1] -= trade_qty;
    if (orderpool_[sell_order][1] == 0)                              // 卖单已完全成交
    {
        orderpool_.erase(sell_order);
        orderbook_[sell_order_price].orders[1].erase(sell_order);
        if (orderbook_[sell_order_price].orders[0].empty() && orderbook_[sell_order_price].orders[1].empty()) // 主订单薄中该价位的买卖订单都已删除
        {
            orderbook_.erase(sell_order_price);
        }
        sell_fininshed = true;
    }

    total_buy_qty_ -= trade_qty;
    if (total_buy_qty_ < 0)
    {
        BAD_THING_(std::format("集合竞价成交后总买量变为负数, total_buy_qty_ = {}", total_buy_qty_));
        return;
    }
    total_sell_qty_ -= trade_qty;
    if (total_sell_qty_ < 0)
    {
        BAD_THING_(std::format("集合竞价成交后总卖量变为负数, total_sell_qty_ = {}", total_sell_qty_));
        return;
    }

    bool was_lg_buy = false;
    if (orderpool_sm_.contains(buy_order)) // 买单属于小单
    {
        sm_order_cum_vol_ += trade_qty * trade_price / 1000.0;
        if (buy_fininshed)
        {
            orderpool_sm_.erase(buy_order);
        }
    }
    else if (orderpool_mid_.contains(buy_order)) // 买单属于中单
    {
        mid_order_cum_vol_ += trade_qty * trade_price / 1000.0;
        if (buy_fininshed)
        {
            orderpool_mid_.erase(buy_order);
        }
    }
    else if (orderpool_lg_.contains(buy_order)) // 买单属于大单
    {
        lg_order_cum_vol_ += trade_qty * trade_price / 1000.0;
        was_lg_buy = true;
        if (buy_fininshed)
        {
            orderpool_lg_.erase(buy_order);
        }
    }
    else if (orderpool_slg_.contains(buy_order)) // 买单属于超大单
    {
        slg_order_cum_vol_ += trade_qty * trade_price / 1000.0;
        was_lg_buy = true;
        if (buy_fininshed)
        {
            orderpool_slg_.erase(buy_order);
        }
    }
    else
    {
        BAD_THING_(std::format("集合竞价成交买单: 在4种订单池子中都未找到买单, buy_order = {}", buy_order));
        return;
    }
    if (was_lg_buy)
    {
        if (!orderbook_vol_lg_.contains(buy_order_price)) // 买单对应价位未曾有大单
        {
            BAD_THING_(std::format("集合竞价成交买单: 在大单量总量统计中未找到买单对应价位, buy_order_price = {}", buy_order_price));
            return;
        }
        else if (orderbook_vol_lg_[buy_order_price][0] < trade_qty) // 买单对应价位的大单量小于要删除的数量
        {
            BAD_THING_(std::format("集合竞价成交买单: 在大单量总量统计中当前量小于本次成交的数量, buy_order_price = {}, trade_qty = {}, vol = {}", buy_order_price, trade_qty, orderbook_vol_lg_[buy_order_price][0]));
            return;
        }
        orderbook_vol_lg_[buy_order_price][0] -= trade_qty;                                           // 调整大单统计薄中买单对应价位的总申买量
        if (orderbook_vol_lg_[buy_order_price][0] == 0 && orderbook_vol_lg_[buy_order_price][1] == 0) // 买单对应价位的大单都已删除
        {
            orderbook_vol_lg_.erase(buy_order_price);
        }
    }

    bool was_lg_sell = false;
    if (orderpool_sm_.contains(sell_order)) // 卖单属于小单
    {
        sm_order_cum_vol_ -= trade_qty * trade_price / 1000.0;
        if (sell_fininshed)
        {
            orderpool_sm_.erase(sell_order);
        }
    }
    else if (orderpool_mid_.contains(sell_order)) // 卖单属于中单
    {
        mid_order_cum_vol_ -= trade_qty * trade_price / 1000.0;
        if (sell_fininshed)
        {
            orderpool_mid_.erase(sell_order);
        }
    }
    else if (orderpool_lg_.contains(sell_order)) // 卖单属于大单
    {
        lg_order_cum_vol_ -= trade_qty * trade_price / 1000.0;
        was_lg_sell = true;
        if (sell_fininshed)
        {
            orderpool_lg_.erase(sell_order);
        }
    }
    else if (orderpool_slg_.contains(sell_order)) // 卖单属于超大单
    {
        slg_order_cum_vol_ -= trade_qty * trade_price / 1000.0;
        was_lg_sell = true;
        if (sell_fininshed)
        {
            orderpool_slg_.erase(sell_order);
        }
    }
    else
    {
        BAD_THING_(std::format("集合竞价成交卖单: 在4种订单池子中都未找到卖单, sell_order = {}", sell_order));
        return;
    }
    if (was_lg_sell)
    {
        if (!orderbook_vol_lg_.contains(sell_order_price)) // 卖单对应价位未曾有大单
        {
            BAD_THING_(std::format("集合竞价成交卖单: 在大单量总量统计中未找到卖单对应价位, sell_order_price = {}", sell_order_price));
            return;
        }
        else if (orderbook_vol_lg_[sell_order_price][1] < trade_qty) // 卖单对应价位的大单量小于要删除的数量
        {
            BAD_THING_(std::format("集合竞价成交卖单: 在大单量总量统计中当前量小于本次成交的数量, sell_order_price = {}, trade_qty = {}, vol = {}", sell_order_price, trade_qty, orderbook_vol_lg_[sell_order_price][1]));
            return;
        }
        orderbook_vol_lg_[sell_order_price][1] -= trade_qty;                                            // 调整大单统计薄中卖单对应价位的总申卖量
        if (orderbook_vol_lg_[sell_order_price][0] == 0 && orderbook_vol_lg_[sell_order_price][1] == 0) // 卖单对应价位的大单都已删除
        {
            orderbook_vol_lg_.erase(sell_order_price);
        }
    }

    if (!tier_dict_.contains(buy_order_price)) // 买单对应价位不在涨跌停价内
    {
        BAD_THING_(std::format("集合竞价成交: 在涨跌千分点字典中未找到买单对应价位, buy_order_price = {}, 不在涨跌停价内？", buy_order_price));
        return;
    }
    int tier = tier_dict_[buy_order_price];
    if (orderbook_tier_[tier] < trade_qty)
    {
        BAD_THING_(std::format("集合竞价成交: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
        return;
    }
    orderbook_tier_[tier] -= trade_qty;
    if (!tier_dict_.contains(sell_order_price)) // 买单对应价位不在涨跌停价内
    {
        BAD_THING_(std::format("集合竞价成交: 在涨跌千分点字典中未找到卖单对应价位, sell_order_price = {}, 不在涨跌停价内？", sell_order_price));
        return;
    }
    tier = tier_dict_[sell_order_price];
    if (orderbook_tier_[tier] < trade_qty)
    {
        BAD_THING_(std::format("集合竞价成交: 在涨跌千分点挂单量小于成交数量, tier_qty = {}, trade_qty = {}", orderbook_tier_[tier], trade_qty));
        return;
    }
    orderbook_tier_[tier] -= trade_qty;
}

// 终结当前主动订单，进行统计，如还有剩余部分，则转为被动订单插入订单池和主订单簿，并更新买卖一
// 各类消息都可能触发
void Szot2t::close_pending_order_()
{
    int64_t unfilled_qty = pending_qty_apply_ - pending_qty_; // 主动订单的未成交数量
    if (unfilled_qty > 0) // 主动订单还有剩余
    {
        int new_price = pending_is_market_ ? pending_latest_price_ : pending_price_; // 次生订单报价，对于限价单保持不变，对于市价单为最近一次成交价

        if (orderbook_.contains(pending_latest_price_)) // 此时主动订单应该已经把对手方最优价吃干净了，否则它应该继续成交才对
        {
            BAD_THING_(std::format("主动订单有剩余，但它没有把对手方最优价吃完就开始终结, pending_latest_price_ = {}", pending_latest_price_));
            return;
        }

        // 把主动订单剩余部分（次生订单）插入订单池和主订单簿
        orderpool_[pending_order_][0] = new_price;
        orderpool_[pending_order_][1] = unfilled_qty;
        int direct_index = (pending_direction_ == TradeType::BUY) ? 0 : 1;
        orderbook_[new_price].orders[direct_index][pending_order_] = unfilled_qty;
        orderbook_[new_price].total_qty[direct_index] = unfilled_qty; // 目前它是该价位上的唯一订单

        // 以价格为索引的30万以上大单（以原始申报量为标准）当前有效总量统计，不区分大单与超大单了
        if (pending_qty_apply_ > lg_threshold_)
        {
            if (orderbook_vol_lg_.contains(new_price))
            {
                BAD_THING_(std::format("当前大单统计中被吃光的价位上还有其它大单, new_price = {}", new_price));
            }
            orderbook_vol_lg_[new_price][direct_index] = unfilled_qty;
        }

        // 更新本方最优价、总申买量或总申卖量
        if (pending_direction_ == TradeType::BUY) // 本次生订单成为新买一中的唯一订单
        {
            if (bid1_ == 0) // 原来无买单
            {
                it_bid1_ = orderbook_.begin();
            }
            else // 原来有买单
            {
                it_bid1_++;
            }
            bid1_ = new_price;
            total_buy_qty_ += unfilled_qty;
        }
        else // 本次生订单成为新卖一中的唯一订单
        {
            if (ask1_ == 0) // 原来无卖单
            {
                it_ask1_ = orderbook_.lower_bound(new_price);
            }
            else // 原来有卖单
            {
                it_ask1_--;
            }
            ask1_ = new_price;
            total_sell_qty_ += unfilled_qty;
        }

        // 将次生订单按大小分类加入对应的池子中，以原始申报量为标准
        if (pending_qty_apply_ <= mid_threshold_)
        {
            orderpool_sm_.insert(pending_order_);
        }
        else if (pending_qty_apply_ <= lg_threshold_)
        {
            orderpool_mid_.insert(pending_order_);
        }
        else if (pending_qty_apply_ <= slg_threshold_)
        {
            orderpool_lg_.insert(pending_order_);
        }
        else
        {
            orderpool_slg_.insert(pending_order_);
        }

        // 价格档位
        if (tier_dict_.contains(new_price))
        {
            int tier = tier_dict_[new_price];
            orderbook_tier_[tier] += unfilled_qty;
        }
        else
        {
            BAD_THING_(std::format("新增委托订单: 在涨跌千分点字典中未找到订单对应价位, new_price = {}, 不在涨跌停价之间？", new_price));
        }
    }

    // 重置当前未完结主动订单相关变量
    pending_order_ = 0;
    pending_qty_apply_ = 0;
    pending_qty_ = 0;
    pending_avail_qty_ = 0;
    pending_is_market_ = false;
    pending_direction_ = TradeType::BUY;
    pending_price_ = 0;
    pending_latest_price_ = 0;
    pending_price_num_ = 0;
    pending_should_tick_after_ate1_ = false;
}
