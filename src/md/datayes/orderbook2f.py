#!/usr/bin/python3
# -*- coding: utf-8 -*-
# 每 100 条逐笔 tick 数据为一个 bar，生成各特征数据，以作为后续机器学习算法的输入数据

'''
每100个tick构成一个bar

- 当前bar跨越的时间长度 (+1)

- tick时间戳间隔 max/mean/std/skew/kurt (+5)

- bar收益率 (+1) 

- tick收益率 min/max/mean/std/skew/kurt (+6)

- 高/开/低/收(最新成交价)/平均价 (+5)

- bar成交量&成交金额 (+2)

- tick成交量 max/mean/std/skew/kurt (+5)

- (中间价 / 最新价) max/mean/std/skew/kurt (+5)

- 前(1/2/3/5/10)档挂单量累计加权价 / 最新价 min/max/mean/std/skew/kurt (+30)

- 前(1/2/3/5/10)tier挂单量累计加权价 / 最新价 min/max/mean/std/skew/kurt (+30)

- 总成交/委托/撤单笔数 (+3)

- 总成交/委托/撤单量 (+3)

- 总成交/委托/撤单金额 (+3)

- bar内成交/委托/撤单买卖方向比例 (+3)

- bid(1/2/3/5/10)档 累计挂单量 min/max/mean/std/skew/kurt (+30)

- ask(1/2/3/5/10)档 累计挂单量 min/max/mean/std/skew/kurt (+30)

- bid(1/2/3/5/10)档 大单累计挂单量 mean (+5)

- ask(1/2/3/5/10)档 大单累计挂单量 mean (+5)

- bid(1/2/3/5/10)档 挂单个数 min/max/mean/std/skew/kurt (+30)

- ask(1/2/3/5/10)档 挂单个数 min/max/mean/std/skew/kurt (+30)

- bid(1/2/3/5/10)档 大单累计挂单个数 mean (+5)

- ask(1/2/3/5/10)档 大单累计挂单个数 mean (+5)

- spread (A4) max/mean/std/skew/kurt (+5)

- order_aggr max/mean/std/skew/kurt (+5)

- bid1(2/3/5/10) 不同挂单量委托个数 (+5)

- ask1(2/3/5/10) 不同挂单量委托个数 (+5)

- 小/中/大/超大单流向 (+4)

- 20档价位 挂单量 分布 skew/kurt (+2)

- 20tier 挂单量 分布 skew/kurt (+2)

- (bid1挂单量 - ask1挂单量) / (bid1挂单量 + ask1挂单量) max/mean/std/skew/kurt (+5)

- (bid1-5挂单量 - ask1-5挂单量) / (bid1-5挂单量 + ask1-5挂单量) max/mean/std/skew/kurt (+5)

- (bid1-10挂单量 - ask1-10挂单量) / (bid1-10挂单量 + ask1-10挂单量) max/mean/std/skew/kurt (+5)

- (bid1大单挂单量 - ask1大单挂单量) / (bid1大单挂单量 + ask1大单挂单量) max/mean/std/skew/kurt (+5)

- (bid1-5大单挂单量 - ask1-5大单挂单量) / (bid1-5大单挂单量 + ask1-5大单挂单量) max/mean/std/skew/kurt (+5)

- (bid1-10大单挂单量 - ask1-10大单挂单量) / (bid1-10大单挂单量 + ask1-10大单挂单量) max/mean/std/skew/kurt (+5)

- (tier1大单挂单量 - tier1大单挂单量) / (tier1大单挂单量 + tier1大单挂单量) max/mean/std/skew/kurt (+5)

- (tier1-2大单挂单量 - tier1-2大单挂单量) / (tier1-2大单挂单量 + tier1-2大单挂单量) max/mean/std/skew/kurt (+5)

- (tier1-3大单挂单量 - tier1-3大单挂单量) / (tier1-3大单挂单量 + tier1-3大单挂单量) max/mean/std/skew/kurt (+5)

- (tier1-5大单挂单量 - tier1-5大单挂单量) / (tier1-5大单挂单量 + tier1-5大单挂单量) max/mean/std/skew/kurt (+5)

- (tier1-10大单挂单量 - tier1-10大单挂单量) / (tier1-10大单挂单量 + tier1-10大单挂单量) max/mean/std/skew/kurt (+5)

- 总委买 / 总委卖 mean (+1)

'''
import os
import psutil
# from nb_common import *
import pandas as pd
import numpy as np
import statistics
from collections.abc import Iterable, Sequence
from tqdm import trange
from datetime import datetime
# from tqdm.notebook import trange, tqdm
# import matplotlib.pyplot as plt
pd.options.display.max_info_columns=400
pd.options.display.max_columns=400
pd.options.display.max_rows=400
pd.options.display.width=500
# plt.rcParams["figure.figsize"] = [24, 10]

barn = 100 # number of ticks per bar
day = '2025-12-19'
# sec = '600900'
sec = '300850'
df = pd.read_parquet(f"/A/md/orderbook/processed/full/{day}/{sec}.parquet")

start_time = datetime.now()

increment = 10
prev_close = df.iloc[0].prev_close
lower_limit = int(round((prev_close * 0.9)/1000, 2) * 1000)
upper_limit = int(round((prev_close * 1.1)/1000, 2) * 1000)
if prev_close > 10000:
    disc_coef = 0.001
elif prev_close > 5000 and prev_close <= 10000:
    disc_coef = 0.002
elif prev_close > 2000 and prev_close <= 5000:
    disc_coef = 0.005
else:
    disc_coef = 0.01
tier_dict = {} # 某一价位对应的 tier 数，tier_dict = {price: tier}
for p in range(lower_limit, upper_limit+increment, increment):
    tier_dict[p] = int(round(round((p/prev_close-1)/disc_coef)*disc_coef*1000))
# print(f"{prev_close} {lower_limit} {upper_limit} {tier_dict}")
price_tier = {} # 某一个 tier 对应的平均价格
for t in tier_dict.values(): # 所有可能的 tiers，如对应 10 元以上股票，tiers = {-100, -99, ..., 100}
    price_tier[t] = sum([p for p in tier_dict if tier_dict[p] == t]) / len([p for p in tier_dict if tier_dict[p] == t])
# print(price_tier)
tier_max = int(max(tier_dict.values())) # 最大 tier 数
tier_min = int(min(tier_dict.values())) # 最小 tier 数
df['t0'] = [tier_dict[p] for p in df.latest] # 以当前最新价为基准，当前最新价对应的 tier 数
df['ask1_5_qty'] = df['ask1_qty'] + df['ask2_qty'] + df['ask3_qty'] + df['ask4_qty'] + df['ask5_qty']
df['ask1_10_qty'] = df['ask1_qty'] + df['ask2_qty'] + df['ask3_qty'] + df['ask4_qty'] + df['ask5_qty'] + df['ask6_qty'] + df['ask7_qty'] + df['ask8_qty'] + df['ask9_qty'] + df['ask10_qty']
df['bid1_5_qty'] = df['bid1_qty'] + df['bid2_qty'] + df['bid3_qty'] + df['bid4_qty'] + df['bid5_qty']
df['bid1_10_qty'] = df['bid1_qty'] + df['bid2_qty'] + df['bid3_qty'] + df['bid4_qty'] + df['bid5_qty'] + df['bid6_qty'] + df['bid7_qty'] + df['bid8_qty'] + df['bid9_qty'] + df['bid10_qty']
# print(f"type of df.iloc[0][t0] {df.iloc[0]['t0']} is {type(df.iloc[0]['t0'])}")

# 给定一个 tier 数，及上下档位数，返回 tier 数列表
def get_tiers(tier, num):
    global tier_min, tier_max
    tmin = np.maximum(tier_min, tier - num).astype(int)
    tmax = np.minimum(tier_max, tier + num).astype(int)
    return list(range(tmin, tmax + 1))
    
# 给定一个 tier 数，及上下档位数，返回 tier 字段名称列表
def get_tiers_f(tier, num):
    global tier_min, tier_max
    tmin = np.maximum(tier_min, tier - num).astype(int)
    tmax = np.minimum(tier_max, tier + num).astype(int)
    if not isinstance(tier, Iterable):
        return [f'price_tier{t}' if t >= 0 else f'price_tier_{-t}' for t in range(tmin, tmax + 1)]
    elif isinstance(tier, pd.Series):
        r = [[f'price_tier{t}' if t >= 0 else f'price_tier_{-t}' for t in range(tmin.iloc[i], tmax.iloc[i] + 1)] for i in range(len(tier))]
        print(f"type of tier is {type(tier)}, will return")
        print(r)
        return r

# 小、中、大、超大单资金净流入
sm_order_cum_amt = df.iloc[0]['sm_order_cum_amt']
mid_order_cum_amt = df.iloc[0]['mid_order_cum_amt']
lg_order_cum_amt = df.iloc[0]['lg_order_cum_amt']
slg_order_cum_amt = df.iloc[0]['slg_order_cum_amt']
oa = [] # output array
for i in trange(len(df) // barn):
# for i in range(10):
    if i == (len(df) // barn) - 1:
        # print(f"Processing last bar, from {i * barn} to {len(df)}")
        dbar = df.iloc[i * barn:]
    else:
        dbar = df.iloc[i * barn:i*barn + barn]
    # print(f"Processing bar {dbar}")
    o = {}
    o['bar_secs'] = (dbar.index[-1] - dbar.index[0]).total_seconds() # bar 跨越的时间长度，单位秒

    # barn 个 tick 之间的时间间隔统计特征
    d1 = pd.Series(dbar.index)
    d2 = d1.shift().reset_index(drop=True)
    diff = (d1 - d2).dt.total_seconds()
    des = diff.describe()
    o['tick_intv_max'] = des['max']
    o['tick_intv_mean'] = des['mean']
    o['tick_intv_std'] = des['std']
    o['tick_intv_skew'] = diff.skew()
    o['tick_intv_kurt'] = diff.kurt()

    o['bar_gain'] = (dbar.latest.iloc[-1] - dbar.latest.iloc[0]) / dbar.latest.iloc[0] * 100 # bar 收益率，百分数
    # barn 个 tick 之间的收益率统计特征
    gain_a = (dbar.latest - dbar.shift().latest) / dbar.latest * 100
    des = gain_a.describe()
    o['tick_gain_min'] = des['min']
    o['tick_gain_max'] = des['max']
    o['tick_gain_mean'] = des['mean']
    o['tick_gain_std'] = des['std']
    o['tick_gain_skew'] = gain_a.skew()
    o['tick_gain_kurt'] = gain_a.kurt()

    # bar 的 OHLC
    o['bar_open'] = dbar.latest.iloc[0]
    o['bar_close'] = dbar.latest.iloc[-1]
    o['bar_high'] = dbar.latest.max()
    o['bar_low'] = dbar.latest.min()
    # 计算加权平均价
    vol_sum = dbar[dbar['tick_type'] == 2]['tick_vol'].sum()
    vol_money = dbar[dbar['tick_type'] == 2]['tick_money'].sum()
    if vol_sum > 0:
        o['bar_wap'] = vol_money / vol_sum * 1000
    else:
        o['bar_wap'] = 0

    # bar 的成交量与成交金额
    o['bar_vol'] = vol_sum
    o['bar_money'] = vol_money * 1000 # 金额与价格一致都是放大千倍

    # 各 tick 的成交量统计特征
    des = dbar[dbar['tick_type'] == 2]['tick_vol'].describe()
    # o['tick_vol_min'] = des['min']
    o['tick_vol_max'] = des['max']
    o['tick_vol_mean'] = des['mean']
    o['tick_vol_std'] = des['std']
    o['tick_vol_skew'] = dbar[dbar['tick_type'] == 2]['tick_vol'].skew()
    o['tick_vol_kurt'] = dbar[dbar['tick_type'] == 2]['tick_vol'].kurt()

    # (中间价 / 最新价) max/mean/std/skew/kurt (+5)
    des = ((dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['bid1_price'] + dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['ask1_price']) / 2).describe()
    # o['midprice_min'] = des['min']
    o['midprice_max'] = des['max']
    o['midprice_mean'] = des['mean']
    o['midprice_std'] = des['std']
    o['midprice_skew'] = ((dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['bid1_price'] + dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['ask1_price']) / 2).skew()
    o['midprice_kurt'] = ((dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['bid1_price'] + dbar[(dbar.bid1_price > 0) & (dbar.ask1_price > 0)]['ask1_price']) / 2).kurt()

    # 前(1/2/3/5/10)档挂单量累计加权价 / 最新价 min/max/mean/std/skew/kurt (+30)
    # 前 1 档
    vol_sum = dbar['bid1_qty'] + dbar['ask1_qty']
    money_sum = dbar['bid1_qty'] * dbar['bid1_price'] + dbar['ask1_qty'] * dbar['ask1_price']
    des = (money_sum / vol_sum).describe()
    o['tick_wap1_min'] = des['min']
    o['tick_wap1_max'] = des['max']
    o['tick_wap1_mean'] = des['mean']
    o['tick_wap1_std'] = des['std']
    o['tick_wap1_skew'] = (money_sum / vol_sum).skew()
    o['tick_wap1_kurt'] = (money_sum / vol_sum).kurt()
    # 前 2 档
    vol_sum += dbar['bid2_qty'] + dbar['ask2_qty']
    money_sum += dbar['bid2_qty'] * dbar['bid2_price'] + dbar['ask2_qty'] * dbar['ask2_price']
    des = (money_sum / vol_sum).describe()
    o['tick_wap2_min'] = des['min']
    o['tick_wap2_max'] = des['max']
    o['tick_wap2_mean'] = des['mean']
    o['tick_wap2_std'] = des['std']
    o['tick_wap2_skew'] = (money_sum / vol_sum).skew()
    o['tick_wap2_kurt'] = (money_sum / vol_sum).kurt()
    # 前 3 档
    vol_sum += dbar['bid3_qty'] + dbar['ask3_qty']
    money_sum += dbar['bid3_qty'] * dbar['bid3_price'] + dbar['ask3_qty'] * dbar['ask3_price']
    des = (money_sum / vol_sum).describe()
    o['tick_wap3_min'] = des['min']
    o['tick_wap3_max'] = des['max']
    o['tick_wap3_mean'] = des['mean']
    o['tick_wap3_std'] = des['std']
    o['tick_wap3_skew'] = (money_sum / vol_sum).skew()
    o['tick_wap3_kurt'] = (money_sum / vol_sum).kurt()
    # 前 5 档
    # vol_sum += dbar['bid4_qty'] + dbar['ask4_qty'] + dbar['bid5_qty'] + dbar['ask5_qty']
    vol_sum = dbar['bid1_5_qty'] + dbar['ask1_5_qty']
    money_sum += dbar['bid4_qty'] * dbar['bid4_price'] + dbar['ask4_qty'] * dbar['ask4_price'] + dbar['bid5_qty'] * dbar['bid5_price'] + dbar['ask5_qty'] * dbar['ask5_price']
    des = (money_sum / vol_sum).describe()
    o['tick_wap5_min'] = des['min']
    o['tick_wap5_max'] = des['max']
    o['tick_wap5_mean'] = des['mean']
    o['tick_wap5_std'] = des['std']
    o['tick_wap5_skew'] = (money_sum / vol_sum).skew()
    o['tick_wap5_kurt'] = (money_sum / vol_sum).kurt()
    # 前 10 档
    # vol_sum += dbar['bid6_qty'] + dbar['ask6_qty'] + dbar['bid7_qty'] + dbar['ask7_qty'] + dbar['bid8_qty'] + dbar['ask8_qty'] + dbar['bid9_qty'] + dbar['ask9_qty'] + dbar['bid10_qty'] + dbar['ask10_qty']
    vol_sum = dbar['bid1_10_qty'] + dbar['ask1_10_qty']
    money_sum += dbar['bid6_qty'] * dbar['bid6_price'] + dbar['ask6_qty'] * dbar['ask6_price'] + dbar['bid7_qty'] * dbar['bid7_price'] + dbar['ask7_qty'] * dbar['ask7_price'] + dbar['bid8_qty'] * dbar['bid8_price'] + dbar['ask8_qty'] * dbar['ask8_price'] + dbar['bid9_qty'] * dbar['bid9_price'] + dbar['ask9_qty'] * dbar['ask9_price'] + dbar['bid10_qty'] * dbar['bid10_price'] + dbar['ask10_qty'] * dbar['ask10_price']
    des = (money_sum / vol_sum).describe()
    o['tick_wap10_min'] = des['min']
    o['tick_wap10_max'] = des['max']
    o['tick_wap10_mean'] = des['mean']
    o['tick_wap10_std'] = des['std']
    o['tick_wap10_skew'] = (money_sum / vol_sum).skew()
    o['tick_wap10_kurt'] = (money_sum / vol_sum).kurt()

    # 前(1/2/3/5/10)tier挂单量累计加权价 / 最新价 min/max/mean/std/skew/kurt (+30)，以当前最新价为基准
    tier_prices = {i: [] for i in (1, 2, 3, 5, 10)}
    for _, r in dbar.iterrows():
        t0 = r.t0 # 当前最新价所在的 tier 数
        for i in tier_prices:
            tn = get_tiers(t0, i) # tier 数的 list
            tf = get_tiers_f(t0, i) # tier 字段名的 list
            sum_v = sum([r[f] for f in tf]) # 挂单量
            sum_m = sum([r[tf[t]] * price_tier[tn[t]] for t in range(len(tn))]) # 挂单金额估计
            tier_prices[i].append(sum_m / sum_v) if sum_v > 0 else tier_prices[i].append(0)
    for i in tier_prices:
        sr = pd.Series(tier_prices[i])
        des = sr.describe()
        o[f'tier_wap{i}_min'] = des['min']
        o[f'tier_wap{i}_max'] = des['max']
        o[f'tier_wap{i}_mean'] = des['mean']
        o[f'tier_wap{i}_std'] = des['std']
        o[f'tier_wap{i}_skew'] = sr.skew()
        o[f'tier_wap{i}_kurt'] = sr.kurt()

    # 总成交/委托/撤单笔数 (+3)
    vc = dbar['tick_type'].value_counts()
    o['bar_transaction_num'] = vc[2] if 2 in vc.index else 0
    o['bar_order_num'] = vc[0] if 0 in vc.index else 0
    o['bar_cancel_num'] = vc[1] if 1 in vc.index else 0

    # 总成交/委托/撤单量 (+3)
    o['bar_transaction_vol'] = dbar[dbar['tick_type'] == 2]['tick_vol'].sum() # 总成交量
    o['bar_order_vol'] = dbar[dbar['tick_type'] == 0]['tick_vol'].sum() # 总委托量
    o['bar_cancel_vol'] = dbar[dbar['tick_type'] == 1]['tick_vol'].sum() # 总撤单量

    # 总成交/委托/撤单金额 (+3)
    o['bar_transaction_money'] = dbar[dbar['tick_type'] == 2]['tick_money'].sum() * 1000 # 总成交金额
    o['bar_order_money'] = dbar[dbar['tick_type'] == 0]['tick_money'].sum() * 1000# 总委托金额
    o['bar_cancel_money'] = dbar[dbar['tick_type'] == 1]['tick_money'].sum() * 1000 # 总撤单金额

    # bar内成交/委托/撤单买卖方向比例 (+3)
    vc = dbar[dbar['tick_type'] == 2]['tick_direc'].value_counts() # 成交的买卖方向统计
    vc1 = vc[1] if 1 in vc.index else 0
    vc2 = vc[2] if 2 in vc.index else 0
    o['bar_transaction_buy_ratio'] = vc1 / (vc1 + vc2) if (vc1 + vc2) > 0 else 0.5
    vc = dbar[dbar['tick_type'] == 0]['tick_direc'].value_counts() # 委托的买卖方向统计
    vc1 = vc[1] if 1 in vc.index else 0
    vc2 = vc[2] if 2 in vc.index else 0
    o['bar_order_buy_ratio'] = vc1 / (vc1 + vc2) if (vc1 + vc2) > 0 else 0.5
    vc = dbar[dbar['tick_type'] == 1]['tick_direc'].value_counts() # 撤单的买卖方向统计
    vc1 = vc[1] if 1 in vc.index else 0
    vc2 = vc[2] if 2 in vc.index else 0
    o['bar_cancel_buy_ratio'] = vc1 / (vc1 + vc2) if (vc1 + vc2) > 0 else 0.5

    # bid(1/2/3/5/10)档 累计挂单量 min/max/mean/std/skew/kurt (+30)
    des = dbar['bid1_qty'].describe() # 1 档
    o['bid1_qty_min'] = des['min']
    o['bid1_qty_max'] = des['max']
    o['bid1_qty_mean'] = des['mean']
    o['bid1_qty_std'] = des['std']
    o['bid1_qty_skew'] = dbar['bid1_qty'].skew()
    o['bid1_qty_kurt'] = dbar['bid1_qty'].kurt()
    des = (dbar['bid1_qty'] + dbar['bid2_qty']).describe() # 2 档
    o['bid2_qty_min'] = des['min']
    o['bid2_qty_max'] = des['max']
    o['bid2_qty_mean'] = des['mean']
    o['bid2_qty_std'] = des['std']
    o['bid2_qty_skew'] = (dbar['bid1_qty'] + dbar['bid2_qty']).skew()
    o['bid2_qty_kurt'] = (dbar['bid1_qty'] + dbar['bid2_qty']).kurt()
    des = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty']).describe() # 3 档
    o['bid3_qty_min'] = des['min']
    o['bid3_qty_max'] = des['max']
    o['bid3_qty_mean'] = des['mean']
    o['bid3_qty_std'] = des['std']
    o['bid3_qty_skew'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty']).skew()
    o['bid3_qty_kurt'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty']).kurt()
    # des = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']).describe() # 5 档
    des = dbar['bid1_5_qty'].describe()
    o['bid5_qty_min'] = des['min']
    o['bid5_qty_max'] = des['max']
    o['bid5_qty_mean'] = des['mean']
    o['bid5_qty_std'] = des['std']
    # o['bid5_qty_skew'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']).skew()
    # o['bid5_qty_kurt'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']).kurt()
    o['bid5_qty_skew'] = dbar['bid1_5_qty'].skew()
    o['bid5_qty_kurt'] = dbar['bid1_5_qty'].kurt()
    # des = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']).describe() # 10 档
    des = dbar['bid1_10_qty'].describe()
    o['bid10_qty_min'] = des['min']
    o['bid10_qty_max'] = des['max']
    o['bid10_qty_mean'] = des['mean']
    o['bid10_qty_std'] = des['std']
    # o['bid10_qty_skew'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']).skew()
    # o['bid10_qty_kurt'] = (dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']).kurt()
    o['bid10_qty_skew'] = dbar['bid1_10_qty'].skew()
    o['bid10_qty_kurt'] = dbar['bid1_10_qty'].kurt()

    # ask(1/2/3/5/10)档 累计挂单量 min/max/mean/std/skew/kurt (+30)
    des = dbar['ask1_qty'].describe() # 1 档
    o['ask1_qty_min'] = des['min']
    o['ask1_qty_max'] = des['max']
    o['ask1_qty_mean'] = des['mean']
    o['ask1_qty_std'] = des['std']
    o['ask1_qty_skew'] = dbar['ask1_qty'].skew()
    o['ask1_qty_kurt'] = dbar['ask1_qty'].kurt()
    des = (dbar['ask1_qty'] + dbar['ask2_qty']).describe() # 2 档
    o['ask2_qty_min'] = des['min']
    o['ask2_qty_max'] = des['max']
    o['ask2_qty_mean'] = des['mean']
    o['ask2_qty_std'] = des['std']
    o['ask2_qty_skew'] = (dbar['ask1_qty'] + dbar['ask2_qty']).skew()
    o['ask2_qty_kurt'] = (dbar['ask1_qty'] + dbar['ask2_qty']).kurt()
    des = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty']).describe() # 3 档
    o['ask3_qty_min'] = des['min']
    o['ask3_qty_max'] = des['max']
    o['ask3_qty_mean'] = des['mean']
    o['ask3_qty_std'] = des['std']
    o['ask3_qty_skew'] = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty']).skew()
    o['ask3_qty_kurt'] = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty']).kurt()
    # des = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty']).describe() # 5 档
    des = dbar['ask1_5_qty'].describe()
    o['ask5_qty_min'] = des['min']
    o['ask5_qty_max'] = des['max']
    o['ask5_qty_mean'] = des['mean']
    o['ask5_qty_std'] = des['std']
    o['ask5_qty_skew'] = dbar['ask1_5_qty'].skew()
    o['ask5_qty_kurt'] = dbar['ask1_5_qty'].kurt()
    # des = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']).describe() # 10 档
    des = dbar['ask1_10_qty'].describe()
    o['ask10_qty_min'] = des['min']
    o['ask10_qty_max'] = des['max']
    o['ask10_qty_mean'] = des['mean']
    o['ask10_qty_std'] = des['std']
    # o['ask10_qty_skew'] = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']).skew()
    # o['ask10_qty_kurt'] = (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']).kurt()
    o['ask10_qty_skew'] = dbar['ask1_10_qty'].skew()
    o['ask10_qty_kurt'] = dbar['ask1_10_qty'].kurt()

    # bid(1/2/3/5/10)档 大单累计挂单量 mean (+5)
    o['bid1_lg_qty_mean'] = dbar['bid1_lg_qty'].mean() # 1 档
    o['bid2_lg_qty_mean'] = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty']).mean() # 2 档
    o['bid3_lg_qty_mean'] = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty']).mean() # 3 档
    o['bid5_lg_qty_mean'] = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty']).mean() # 5 档
    o['bid10_lg_qty_mean'] = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['bid6_lg_qty'] + dbar['bid7_lg_qty'] + dbar['bid8_lg_qty'] + dbar['bid9_lg_qty'] + dbar['bid10_lg_qty']).mean() # 10 档

    # ask(1/2/3/5/10)档 大单累计挂单量 mean (+5)
    o['ask1_lg_qty_mean'] = dbar['ask1_lg_qty'].mean() # 1 档
    o['ask2_lg_qty_mean'] = (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty']).mean() # 2 档
    o['ask3_lg_qty_mean'] = (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty']).mean() # 3 档
    o['ask5_lg_qty_mean'] = (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty']).mean() # 5 档
    o['ask10_lg_qty_mean'] = (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'] + dbar['ask6_lg_qty'] + dbar['ask7_lg_qty'] + dbar['ask8_lg_qty'] + dbar['ask9_lg_qty'] + dbar['ask10_lg_qty']).mean() # 10 档

    # bid(1/2/3/5/10)档 挂单个数 min/max/mean/std/skew/kurt (+30)
    des = dbar['bid1_order_num'].describe() # 1 档
    o['bid1_order_num_min'] = des['min']
    o['bid1_order_num_max'] = des['max']
    o['bid1_order_num_mean'] = des['mean']
    o['bid1_order_num_std'] = des['std']
    o['bid1_order_num_skew'] = dbar['bid1_order_num'].skew()
    o['bid1_order_num_kurt'] = dbar['bid1_order_num'].kurt()
    des = (dbar['bid1_order_num'] + dbar['bid2_order_num']).describe() # 2 档
    o['bid2_order_num_min'] = des['min']
    o['bid2_order_num_max'] = des['max']
    o['bid2_order_num_mean'] = des['mean']
    o['bid2_order_num_std'] = des['std']
    o['bid2_order_num_skew'] = (dbar['bid1_order_num'] + dbar['bid2_order_num']).skew()
    o['bid2_order_num_kurt'] = (dbar['bid1_order_num'] + dbar['bid2_order_num']).kurt()
    des = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num']).describe() # 3 档
    o['bid3_order_num_min'] = des['min']
    o['bid3_order_num_max'] = des['max']
    o['bid3_order_num_mean'] = des['mean']
    o['bid3_order_num_std'] = des['std']
    o['bid3_order_num_skew'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num']).skew()
    o['bid3_order_num_kurt'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num']).kurt()
    des = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num']).describe() # 5 档
    o['bid5_order_num_min'] = des['min']
    o['bid5_order_num_max'] = des['max']
    o['bid5_order_num_mean'] = des['mean']
    o['bid5_order_num_std'] = des['std']
    o['bid5_order_num_skew'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num']).skew()
    o['bid5_order_num_kurt'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num']).kurt()
    des = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num'] + dbar['bid6_order_num'] + dbar['bid7_order_num'] + dbar['bid8_order_num'] + dbar['bid9_order_num'] + dbar['bid10_order_num']).describe() # 10 档
    o['bid10_order_num_min'] = des['min']
    o['bid10_order_num_max'] = des['max']
    o['bid10_order_num_mean'] = des['mean']
    o['bid10_order_num_std'] = des['std']
    o['bid10_order_num_skew'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num'] + dbar['bid6_order_num'] + dbar['bid7_order_num'] + dbar['bid8_order_num'] + dbar['bid9_order_num'] + dbar['bid10_order_num']).skew()
    o['bid10_order_num_kurt'] = (dbar['bid1_order_num'] + dbar['bid2_order_num'] + dbar['bid3_order_num'] + dbar['bid4_order_num'] + dbar['bid5_order_num'] + dbar['bid6_order_num'] + dbar['bid7_order_num'] + dbar['bid8_order_num'] + dbar['bid9_order_num'] + dbar['bid10_order_num']).kurt()

    # ask(1/2/3/5/10)档 挂单个数 min/max/mean/std/skew/kurt (+30)
    des = dbar['ask1_order_num'].describe() # 1 档
    o['ask1_order_num_min'] = des['min']
    o['ask1_order_num_max'] = des['max']
    o['ask1_order_num_mean'] = des['mean']
    o['ask1_order_num_std'] = des['std']
    o['ask1_order_num_skew'] = dbar['ask1_order_num'].skew()
    o['ask1_order_num_kurt'] = dbar['ask1_order_num'].kurt()
    des = (dbar['ask1_order_num'] + dbar['ask2_order_num']).describe() # 2 档
    o['ask2_order_num_min'] = des['min']
    o['ask2_order_num_max'] = des['max']
    o['ask2_order_num_mean'] = des['mean']
    o['ask2_order_num_std'] = des['std']
    o['ask2_order_num_skew'] = (dbar['ask1_order_num'] + dbar['ask2_order_num']).skew()
    o['ask2_order_num_kurt'] = (dbar['ask1_order_num'] + dbar['ask2_order_num']).kurt()
    des = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num']).describe() # 3 档
    o['ask3_order_num_min'] = des['min']
    o['ask3_order_num_max'] = des['max']
    o['ask3_order_num_mean'] = des['mean']
    o['ask3_order_num_std'] = des['std']
    o['ask3_order_num_skew'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num']).skew()
    o['ask3_order_num_kurt'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num']).kurt()
    des = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num']).describe() # 5 档
    o['ask5_order_num_min'] = des['min']
    o['ask5_order_num_max'] = des['max']
    o['ask5_order_num_mean'] = des['mean']
    o['ask5_order_num_std'] = des['std']
    o['ask5_order_num_skew'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num']).skew()
    o['ask5_order_num_kurt'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num']).kurt()
    des = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num'] + dbar['ask6_order_num'] + dbar['ask7_order_num'] + dbar['ask8_order_num'] + dbar['ask9_order_num'] + dbar['ask10_order_num']).describe() # 10 档
    o['ask10_order_num_min'] = des['min']
    o['ask10_order_num_max'] = des['max']
    o['ask10_order_num_mean'] = des['mean']
    o['ask10_order_num_std'] = des['std']
    o['ask10_order_num_skew'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num'] + dbar['ask6_order_num'] + dbar['ask7_order_num'] + dbar['ask8_order_num'] + dbar['ask9_order_num'] + dbar['ask10_order_num']).skew()
    o['ask10_order_num_kurt'] = (dbar['ask1_order_num'] + dbar['ask2_order_num'] + dbar['ask3_order_num'] + dbar['ask4_order_num'] + dbar['ask5_order_num'] + dbar['ask6_order_num'] + dbar['ask7_order_num'] + dbar['ask8_order_num'] + dbar['ask9_order_num'] + dbar['ask10_order_num']).kurt()

    # bid(1/2/3/5/10)档 大单累计挂单个数 mean (+5) XXX 似乎输出的结果中不包含这部分信息
    # o['bid1_big_order_num_mean'] = dbar['bid1_big_order_num'].mean()

    # spread (A4) max/mean/std/skew/kurt (+5)
    des = dbar['spread'].describe()
    o['spread_max'] = des['max']
    o['spread_mean'] = des['mean']
    o['spread_std'] = des['std']
    o['spread_skew'] = dbar['spread'].skew()
    o['spread_kurt'] = dbar['spread'].kurt()

    # order_aggr max/mean/std/skew/kurt (+5)
    des = dbar['order_aggr'].describe()
    o['order_aggr_max'] = des['max']
    o['order_aggr_mean'] = des['mean']
    o['order_aggr_std'] = des['std']
    o['order_aggr_skew'] = dbar['order_aggr'].skew()
    o['order_aggr_kurt'] = dbar['order_aggr'].kurt()

    # bid1(2/3/5/10) 不同挂单量委托个数 (+5)
    # ask1(2/3/5/10) 不同挂单量委托个数 (+5)
    # 背景信息：有些机构的大交易会被他们自己的交易程序分割成等量的多个委托，这里统计的就是某一个或多个档位下有多少种不同申买申卖量
    # 目前还未提供这些信息，略过

    # 小/中/大/超大单流向 (+4)
    o['sm_order_cum_amt'] = dbar.iloc[-1]['sm_order_cum_amt'] - sm_order_cum_amt
    o['mid_order_cum_amt'] = dbar.iloc[-1]['mid_order_cum_amt'] - mid_order_cum_amt
    o['lg_order_cum_amt'] = dbar.iloc[-1]['lg_order_cum_amt'] - lg_order_cum_amt
    o['slg_order_cum_amt'] = dbar.iloc[-1]['slg_order_cum_amt'] - slg_order_cum_amt
    sm_order_cum_amt = dbar.iloc[-1]['sm_order_cum_amt']
    mid_order_cum_amt = dbar.iloc[-1]['mid_order_cum_amt']
    lg_order_cum_amt = dbar.iloc[-1]['lg_order_cum_amt']
    slg_order_cum_amt = dbar.iloc[-1]['slg_order_cum_amt']

    # 20档价位 挂单量 分布 skew/kurt (+2)
    o['price_qty_skew'] = dbar[[f'bid{i}_qty' for i in range(1, 10+1)] + [f'ask{i}_qty' for i in range(1, 10+1)]].skew(axis=1).mean()
    o['price_qty_kurt'] = dbar[[f'bid{i}_qty' for i in range(1, 10+1)] + [f'ask{i}_qty' for i in range(1, 10+1)]].kurt(axis=1).mean()

    # 20tier 挂单量 分布 skew/kurt (+2)
    # o['tier_qty_skew'] = dbar[[get_tiers_f(dbar.t0, 10)]].skew(axis=1).mean()
    skews = []
    kurts = []
    for j in range(len(dbar)):
        skews.append(dbar.iloc[j][get_tiers_f(dbar.iloc[j].t0, 10)].skew())
        kurts.append(dbar.iloc[j][get_tiers_f(dbar.iloc[j].t0, 10)].kurt())
    o['tier_qty_skew'] = np.mean(skews)
    o['tier_qty_kurt'] = np.mean(kurts)

    # (bid1挂单量 - ask1挂单量) / (bid1挂单量 + ask1挂单量) max/mean/std/skew/kurt (+5)
    des = ((dbar['bid1_qty'] - dbar['ask1_qty']) / (dbar['bid1_qty'] + dbar['ask1_qty'])).describe()
    o['bias_qty1_max'] = des['max']
    o['bias_qty1_mean'] = des['mean']
    o['bias_qty1_std'] = des['std']
    o['bias_qty1_skew'] = ((dbar['bid1_qty'] - dbar['ask1_qty']) / (dbar['bid1_qty'] + dbar['ask1_qty'])).skew()
    o['bias_qty1_kurt'] = ((dbar['bid1_qty'] - dbar['ask1_qty']) / (dbar['bid1_qty'] + dbar['ask1_qty'])).kurt()
    # (bid1-5挂单量 - ask1-5挂单量) / (bid1-5挂单量 + ask1-5挂单量) max/mean/std/skew/kurt (+5)
    # des = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty']))).describe()
    des = (dbar['bid1_5_qty'] - dbar['ask1_5_qty']) / (dbar['bid1_5_qty'] + dbar['ask1_5_qty']).describe()
    o['bias_qty1_5_max'] = des['max']
    o['bias_qty1_5_mean'] = des['mean']
    o['bias_qty1_5_std'] = des['std']
    # o['bias_qty1_5_skew'] = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty']))).skew()
    # o['bias_qty1_5_kurt'] = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty']))).kurt()
    o['bias_qty1_5_skew'] = ((dbar['bid1_5_qty'] - dbar['ask1_5_qty']) / (dbar['bid1_5_qty'] + dbar['ask1_5_qty'])).skew()
    o['bias_qty1_5_kurt'] = ((dbar['bid1_5_qty'] - dbar['ask1_5_qty']) / (dbar['bid1_5_qty'] + dbar['ask1_5_qty'])).kurt()
    # (bid1-10挂单量 - ask1-10挂单量) / (bid1-10挂单量 + ask1-10挂单量) max/mean/std/skew/kurt (+5)
    # des = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']))).describe()
    des = (dbar['bid1_10_qty'] - dbar['ask1_10_qty']) / (dbar['bid1_10_qty'] + dbar['ask1_10_qty']).describe()
    o['bias_qty1_10_max'] = des['max']
    o['bias_qty1_10_mean'] = des['mean']
    o['bias_qty1_10_std'] = des['std']
    # o['bias_qty1_10_skew'] = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']))).skew()
    # o['bias_qty1_10_kurt'] = (((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) - (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty'])) / ((dbar['bid1_qty'] + dbar['bid2_qty'] + dbar['bid3_qty'] + dbar['bid4_qty'] + dbar['bid5_qty'] + dbar['bid6_qty'] + dbar['bid7_qty'] + dbar['bid8_qty'] + dbar['bid9_qty'] + dbar['bid10_qty']) + (dbar['ask1_qty'] + dbar['ask2_qty'] + dbar['ask3_qty'] + dbar['ask4_qty'] + dbar['ask5_qty'] + dbar['ask6_qty'] + dbar['ask7_qty'] + dbar['ask8_qty'] + dbar['ask9_qty'] + dbar['ask10_qty']))).kurt()
    o['bias_qty1_10_skew'] = ((dbar['bid1_10_qty'] - dbar['ask1_10_qty']) / (dbar['bid1_10_qty'] + dbar['ask1_10_qty'])).skew()
    o['bias_qty1_10_kurt'] = ((dbar['bid1_10_qty'] - dbar['ask1_10_qty']) / (dbar['bid1_10_qty'] + dbar['ask1_10_qty'])).kurt()

    # (bid1大单挂单量 - ask1大单挂单量) / (bid1大单挂单量 + ask1大单挂单量) max/mean/std/skew/kurt (+5)
    s = (dbar['bid1_lg_qty'] + dbar['ask1_lg_qty'])
    s = s.where(s > 0, 1)
    des = ((dbar['bid1_lg_qty'] - dbar['ask1_lg_qty']) / s).describe()
    o['bias_lg_qty1_max'] = des['max']
    o['bias_lg_qty1_mean'] = des['mean']
    o['bias_lg_qty1_std'] = des['std']
    o['bias_lg_qty1_skew'] = ((dbar['bid1_lg_qty'] - dbar['ask1_lg_qty']) / s).skew()
    o['bias_lg_qty1_kurt'] = ((dbar['bid1_lg_qty'] - dbar['ask1_lg_qty']) / s).kurt()
    # (bid1-5大单挂单量 - ask1-5大单挂单量) / (bid1-5大单挂单量 + ask1-5大单挂单量) max/mean/std/skew/kurt (+5)
    s = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'])
    s = s.where(s > 0, 1)
    des = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'])) / s).describe()
    o['bias_lg_qty1_5_max'] = des['max']
    o['bias_lg_qty1_5_mean'] = des['mean']
    o['bias_lg_qty1_5_std'] = des['std']
    o['bias_lg_qty1_5_skew'] = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'])) / s).skew()
    o['bias_lg_qty1_5_kurt'] = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'])) / s).kurt()
    # (bid1-10大单挂单量 - ask1-10大单挂单量) / (bid1-10大单挂单量 + ask1-10大单挂单量) max/mean/std/skew/kurt (+5)
    s = (dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['bid6_lg_qty'] + dbar['bid7_lg_qty'] + dbar['bid8_lg_qty'] + dbar['bid9_lg_qty'] + dbar['bid10_lg_qty'] + dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'] + dbar['ask6_lg_qty'] + dbar['ask7_lg_qty'] + dbar['ask8_lg_qty'] + dbar['ask9_lg_qty'] + dbar['ask10_lg_qty'])
    s = s.where(s > 0, 1)
    des = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['bid6_lg_qty'] + dbar['bid7_lg_qty'] + dbar['bid8_lg_qty'] + dbar['bid9_lg_qty'] + dbar['bid10_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'] + dbar['ask6_lg_qty'] + dbar['ask7_lg_qty'] + dbar['ask8_lg_qty'] + dbar['ask9_lg_qty'] + dbar['ask10_lg_qty'])) / s).describe()
    o['bias_lg_qty1_10_max'] = des['max']
    o['bias_lg_qty1_10_mean'] = des['mean']
    o['bias_lg_qty1_10_std'] = des['std']
    o['bias_lg_qty1_10_skew'] = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['bid6_lg_qty'] + dbar['bid7_lg_qty'] + dbar['bid8_lg_qty'] + dbar['bid9_lg_qty'] + dbar['bid10_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'] + dbar['ask6_lg_qty'] + dbar['ask7_lg_qty'] + dbar['ask8_lg_qty'] + dbar['ask9_lg_qty'] + dbar['ask10_lg_qty'])) / s).skew()
    o['bias_lg_qty1_10_kurt'] = (((dbar['bid1_lg_qty'] + dbar['bid2_lg_qty'] + dbar['bid3_lg_qty'] + dbar['bid4_lg_qty'] + dbar['bid5_lg_qty'] + dbar['bid6_lg_qty'] + dbar['bid7_lg_qty'] + dbar['bid8_lg_qty'] + dbar['bid9_lg_qty'] + dbar['bid10_lg_qty']) - (dbar['ask1_lg_qty'] + dbar['ask2_lg_qty'] + dbar['ask3_lg_qty'] + dbar['ask4_lg_qty'] + dbar['ask5_lg_qty'] + dbar['ask6_lg_qty'] + dbar['ask7_lg_qty'] + dbar['ask8_lg_qty'] + dbar['ask9_lg_qty'] + dbar['ask10_lg_qty'])) / s).kurt()

    # 这些暂无原始数据
    # (tier1大单挂单量 - tier1大单挂单量) / (tier1大单挂单量 + tier1大单挂单量) max/mean/std/skew/kurt (+5)
    # (tier1-2大单挂单量 - tier1-2大单挂单量) / (tier1-2大单挂单量 + tier1-2大单挂单量) max/mean/std/skew/kurt (+5)
    # (tier1-3大单挂单量 - tier1-3大单挂单量) / (tier1-3大单挂单量 + tier1-3大单挂单量) max/mean/std/skew/kurt (+5)
    # (tier1-5大单挂单量 - tier1-5大单挂单量) / (tier1-5大单挂单量 + tier1-5大单挂单量) max/mean/std/skew/kurt (+5)
    # (tier1-10大单挂单量 - tier1-10大单挂单量) / (tier1-10大单挂单量 + tier1-10大单挂单量) max/mean/std/skew/kurt (+5)

    # 总委买 / 总委卖 mean (+2)
    o['total_buy_qty_mean'] = dbar['total_buy_qty'].mean()
    o['total_sell_qty_mean'] = dbar['total_sell_qty'].mean()
    oa.append(o)

# for o in oa:
#     print(o)

print(f"Time used: {datetime.now() - start_time}")
df = pd.DataFrame(oa)
# df.to_parquet(f"/A/md/orderbook/processed/bar/{day}/{sec}.parquet")
df.to_parquet(f"fs_{sec}.parquet")
# print(f"len of result: {len(df)}")