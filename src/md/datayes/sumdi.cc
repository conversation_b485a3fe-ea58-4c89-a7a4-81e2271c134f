// 超级指数行情 super market data：指数行情消息择优发布程序
// 从不同行情源订阅指数行情数据，择优发布到 ZeroMQ
//   - 支持 L1、L2 行情

#include <getopt.h>
#include <iostream>
#include <fstream>
#include <format>
#include <string>
#include <unordered_map>
#include <vector>
#include <zmq.h>

#include "fkYAML/node.hpp"
#include "lyproto.quota.pb.h"

#include "sumd.h"

constexpr std::string VER_SUMD = "0.0.2";  // 程序版本
constexpr std::string DEFAULT_CONFIG_FILE = "sumdi.yaml";  // 缺省配置文件名
constexpr std::string DEFAULT_OUTPUT_HOST = "127.0.0.1";  // 缺省行情输出服务器地址
constexpr int DEFAULT_OUTPUT_PORT = 30003;    // 缺省行情输出端口
constexpr std::string DEFAULT_SUBSCRIPTION = "I";  // 缺省订阅所有指数行情
constexpr int DEFAULT_HWM = 100;  // 缺省 ZeroMQ socket 的 high water mark，包括输入与输出，高值反而有害，
                                  // 因为处理、发送堆积的过时数据没有意义且浪费资源

// 行情源
struct MdSource
{
    std::string name; // 行情源名称
    std::string host; // 行情源 ZMQ 服务器地址
    int port; // 行情源 ZMQ 服务器端口
};

struct ProgramOptions
{
    std::string conf;   // 配置 YAML 文件路径
    std::vector<MdSource> sources;  // 行情源
    std::vector<std::string> subscriptions; // 订阅的行情主题
    std::string output_host;   // 输出行情的 ZeroMQ XSUB 服务器地址
    int output_port;   // 输出行情的 ZeroMQ XSUB 服务器端口
    int hwm;            // ZeroMQ socket 的 high water mark，包括输入与输出
    bool help;
    bool background;    // Added background option
    bool background_cmd;  // 是否已经在命令行指定过要在后台运行
};

static bool LoadConfigFromYaml(const char *config_file, ProgramOptions *options)
{
    if (config_file == nullptr)
    {
        return false;
    }
    std::ifstream ifs(config_file);
    if (!ifs.good())
    {
        std::cout << std::format("Error opening configuration file: {}", config_file) << std::endl;
        return false;
    }
    fkyaml::node cfg;
    try
    {
        cfg = fkyaml::node::deserialize(ifs);
    }
    catch (const fkyaml::exception &e)
    {
        std::cout << "!!!Config file parse error: " << e.what() << std::endl;
        return false;
    }

    try
    {
        const auto &config = cfg.as_map();
        for (const auto &c : config)
        {
            auto kt = c.first.get_type();
            if (kt == fkyaml::node_type::STRING)
            { // key must be a string
                auto k = c.first.get_value<std::string>();
                if (k == "sources")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            MdSource source;
                            for (const auto &sc : s.as_map())
                            {
                                auto sk = sc.first.get_value<std::string>();
                                if (sk == "name")
                                {
                                    source.name = sc.second.get_value<std::string>();
                                }
                                else if (sk == "host")
                                {
                                    source.host = sc.second.get_value<std::string>();
                                }
                                else if (sk == "port")
                                {
                                    source.port = sc.second.get_value<int>();
                                }
                                else
                                {
                                    std::cout << "unkown config: " << sk << std::endl;
                                }
                            }
                            options->sources.push_back(source);
                        }
                    }
                    else
                    {
                        std::cout << "Config error: sources is not a sequence" << std::endl;
                    }
                }
                else if (k == "subscriptions")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::SEQUENCE)
                    {
                        for (const auto &s : c.second.as_seq())
                        {
                            options->subscriptions.push_back(s.get_value<std::string>());
                        }
                    }
                    else
                    {
                        std::cout << "Config error: subscriptions is not a sequence" << std::endl;
                    }
                }
                else if (k == "high_water_mark")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::INTEGER)
                    {
                        options->hwm = c.second.get_value<int>();
                    }
                    else
                    {
                        std::cout << "Config error: high_water_mark is not an int" << std::endl;
                    }
                }
                else if (k == "output")
                {
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::MAPPING)
                    {
                        for (const auto &o : c.second.as_map())
                        {
                            auto ok = o.first.get_value<std::string>();
                            if (ok == "host")
                            {
                                options->output_host = o.second.get_value<std::string>();
                            }
                            else if (ok == "port")
                            {
                                options->output_port = o.second.get_value<int>();
                            }
                            else
                            {
                                std::cout << "unkown config: " << ok << std::endl;
                            }
                        }
                    }
                    else
                    {
                        std::cout << "Config error: output is not a map" << std::endl;
                    }
                }
                else if (k == "background")
                {
                    if (options->background_cmd) // 命令行优先
                    {
                        continue;
                    }
                    auto vt = c.second.get_type();
                    if (vt == fkyaml::node_type::BOOLEAN)
                    {
                        options->background = c.second.get_value<bool>();
                    }
                    else
                    {
                        std::cout << "Config error: background is not a bool" << std::endl;
                    }
                }
                else
                {
                    std::cout << "unkown config: " << k << std::endl;
                }
            }
        }
        DEBUG_LOG(std::format("Loaded configuration from {}", config_file));
    }
    catch (const fkyaml::exception &e)
    {
        std::cout << "!!!Config file parse error: " << e.what() << std::endl;
        return false;
    }
    return true;
}

static void PrintUsage(const char *progname)
{
    std::cout << std::format("超级行情 sumdi 程序 Version: {}", VER_SUMD) << std::endl;
    std::cout << std::format("Usage: {} [OPTIONS]", progname) << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << std::format("  -c, --conf <file>   Configuration file path (default: {})", DEFAULT_CONFIG_FILE) << std::endl;
    std::cout << "  -b, --background    Run in background" << std::endl;
    std::cout << "  -h, --help          Display this help and exit" << std::endl;
}

static bool ParseCommandLine(int argc, char *argv[], ProgramOptions &options)
{
    static struct option long_options[] = {
        {"conf", required_argument, NULL, 'c'},
        {"background", no_argument, NULL, 'b'},
        {"help", no_argument, NULL, 'h'},
        {NULL, 0, NULL, 0}};

    int c;
    while ((c = getopt_long(argc, argv, "c:bh", long_options, NULL)) != -1)
    {
        switch (c)
        {
        case 'c':
            options.conf = optarg;
            break;
        case 'b':
            options.background = true;
            options.background_cmd = true;
            break;
        case 'h':
            options.help = true;
            break;
        default:
            return false;
        }
    }
    return true;
}

int main(int argc, char *argv[])
{
    ProgramOptions options = {
        DEFAULT_CONFIG_FILE,
        {},
        {},
        DEFAULT_OUTPUT_HOST,
        DEFAULT_OUTPUT_PORT,
        DEFAULT_HWM,
        false,
        false,
        false  // 还没在命令行指定过要在后台运行
    };

    if (!ParseCommandLine(argc, argv, options))
    {
        PrintUsage(argv[0]);
        BAD_THING("解析命令行参数错误");
    }
    if (options.help)
    {
        PrintUsage(argv[0]);
        return 0;
    }
    if (options.conf.empty())
    {
        options.conf = DEFAULT_CONFIG_FILE;
    }
    if (!LoadConfigFromYaml(options.conf.c_str(), &options))
    {
        BAD_THING("加载配置文件错误");
    }
    if (options.sources.empty())
    {
        BAD_THING("没有配置行情源");
    }
    if (options.subscriptions.empty())
    {
        options.subscriptions.push_back(DEFAULT_SUBSCRIPTION);
    }
    for (const auto &s : options.subscriptions)
    {
        if (s[0] != 'I')
        {
            BAD_THING(std::format("订阅的行情主题必须以字母I开头，但有主题 {} 没有以I开头", s));
        }
    }

    if (options.background)
    {
        // Run as daemon if background option is set
        DEBUG_LOG("Starting in background mode...");

        // Fork the process
        pid_t pid = fork();

        if (pid < 0)
        {
            // Fork failed
            std::cerr << std::format("Failed to fork process: {}", strerror(errno)) << std::endl;
            return 1;
        }

        if (pid > 0)
        {
            // Parent process exits
            DEBUG_LOG(std::format("Daemon started with PID {}", pid));
            return 0;
        }

        // Child process continues

        // Create a new session and process group
        if (setsid() < 0)
        {
            std::cerr << std::format("Failed to create new session: {}", strerror(errno)) << std::endl;
            return 1;
        }
    }

    auto zmq_ctx = zmq_ctx_new();
    auto input_socket = zmq_socket(zmq_ctx, ZMQ_SUB);  // 输入行情
    zmq_setsockopt(input_socket, ZMQ_RCVHWM, &options.hwm, sizeof(options.hwm));
    for (const auto &s : options.sources)
    {
        std::string endpoint = std::format("tcp://{}:{}", s.host, s.port);
        zmq_connect(input_socket, endpoint.c_str());
    }
    for (const auto &s : options.subscriptions)
    {
        zmq_setsockopt(input_socket, ZMQ_SUBSCRIBE, s.c_str(), s.size());
    }
    auto output_socket = zmq_socket(zmq_ctx, ZMQ_PUB);  // 择优的输出行情
    zmq_setsockopt(output_socket, ZMQ_SNDHWM, &options.hwm, sizeof(options.hwm));
    zmq_connect(output_socket, std::format("tcp://{}:{}", options.output_host, options.output_port).c_str());

    char buffer[1024]; // 对于行情来说 1024 足够了
    struct time_volume {
        int64_t volume; // 累计成交量
        int exchtime;   // 交易所时间戳
    };
    std::unordered_map<std::string, time_volume> latest_time_volume; // 用于择优的最新行情
    int more = 0; // 用于接收 ZMQ 消息时检查是否还有下一个 frame
    size_t more_size = sizeof(more);
    while (true)
    {
        LYPROTO::QUOTA::MarketData o;
        auto msg_len = zmq_recv(input_socket, buffer, sizeof(buffer), 0);
        if (msg_len == -1)
        {
            DEBUG_LOG(std::format("!!!接收行情失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (static_cast<size_t>(msg_len) > sizeof(buffer))
        {
            DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 超过缓冲区大小 {}, 忽略", msg_len, sizeof(buffer)));
            continue;
        }
        more_size = sizeof(more);
        auto r = zmq_getsockopt(input_socket, ZMQ_RCVMORE, &more, &more_size);
        if (r != 0)
        {
            DEBUG_LOG(std::format("!!!获取 ZeroMQ 行情 ZMQ_RCVMORE 标志失败 ({}) : {}, 退出", zmq_strerror(errno), errno));
            break;
        }
        if (more != 0) // 应该只有一个 frame
        {
            DEBUG_LOG(std::format("!!!接收行情后，ZMQ_RCVMORE 标志非 0，忽略"));
            continue;
        }
        if (msg_len < 16)
        {
            DEBUG_LOG(std::format("!!!接收行情异常: 数据长度 {} 小于 16， 忽略", msg_len));
            continue;
        }
        if (buffer[0] != 'I')  // 只对指数行情择优
        {
            DEBUG_LOG(std::format("!!!接收行情不是指数行情 {}，忽略，请配置好订阅主题", std::string_view(buffer, 15)));
            continue;
        }
        if (!o.ParseFromArray(buffer + 15, msg_len - 15))
        {
            DEBUG_LOG(std::format("!!!解析行情失败 {}，忽略", std::string_view(buffer, 15)));
            continue;
        }
        std::string stkid = o.stkid();
        int exchtime;
        try {
            exchtime = std::stoi(o.exchtime());
        } catch (const std::exception &e) {
            DEBUG_LOG(std::format("!!!Invalid exchtime: {} from {}, security: {}", o.exchtime(), o.mdsource(), stkid));
            DEBUG_LOG(std::format("!!!Message: \n{}", o.ShortDebugString()));
            continue;
        }
        int64_t volume = o.volume();
        auto latest = latest_time_volume.find(stkid);  // 查表
        if (latest == latest_time_volume.end())  // 此证券还未收录
        {
            latest_time_volume.insert({stkid, {volume, exchtime}});  // 记录到表里
            zmq_send(output_socket, buffer, msg_len, 0);
        }
        else
        {
            if (volume > latest->second.volume)  // 成交量更大意味着时间上更靠后，因为累计成交量是一个随时间单调的量
            {
                zmq_send(output_socket, buffer, msg_len, 0);
                latest->second.volume = volume;
                latest->second.exchtime = exchtime;
            }
            else if (volume == latest->second.volume)
            {
                // 成交量相等，有可能是行情确实是新的但这段时间内没有成交（只有挂单），还有一种可能：此是上交所 L1 行情，与前面的一个上交所 L2 行情是同一条行情
                // XXX 先暂时不考虑成交量不变的情况，因为多数都确实没什么意义。以后考虑记录上次发送时间，如果相同的话，要上次发送时间后 6 秒后才会发送，可能还要考虑相对交易所时间戳的时延
                // if (exchtime > latest->second.exchtime) {
                //     // 对于深交所指数行情，其交易所时间戳粒度为秒，且其秒数都是 3 的倍数，所以只要两条行情的时间戳不等，一定可以确定其时间先后，而且我们可以肯定两条行情之间的
                //     // exchtime 差值一定大于等于 3000（而且可以大到非常大，如 13:59:59.999 与 14:00:00.000 之间只有 1 毫秒，但 exchtime 差值却是 4040001），所以下列不等式
                //     // 对于深交所一定成立
                //     // 对于上交所指数行情，其 L2 行情的交易所时间戳粒度为秒，且其秒数模 3 不一定保持不变，其 L1 行情的交易所时间戳粒度为毫秒，且其秒数与对应的 L2 行情的秒数相等
                //     // (但比较特殊的是国信中畅的上交所指数行情有许多都把其毫秒数都清零了)，所以可能出现重复的现象：L2 行情先到，时间戳精确到秒，然后 L1 行情到，时间戳精确到毫秒，
                //     // 但只大几十毫秒，这实际上是同一条行情，所以我们要检查一下时间差，如果大于 100 毫秒，才认为是新的行情（实际上观察到的一般在 50 毫秒以内）
                //     // 这里我们没有检查前一条行情的毫秒数部分是 0，因为似乎没必要
                //     if (exchtime - latest->second.exchtime > 100)
                //     {
                //         zmq_send(output_socket, buffer, msg_len, 0);
                //     }
                //     latest->second.exchtime = exchtime;
                // }
            }
        }
    }

    // 应该不会到这里来
    zmq_close(input_socket);
    zmq_close(output_socket);
    zmq_ctx_destroy(zmq_ctx);
    return 0;
}