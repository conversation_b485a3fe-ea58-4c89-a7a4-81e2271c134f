// 从 ZMQ 接收上交所逐笔数据并保存到文件中

#include <iostream>
#include <fstream>
#include <format>
#include <sys/time.h>
#include <zmq.h>

#include "fkYAML/node.hpp"

#include "mdl_api.h"
#include "mdl_shl2_msg.h"

using namespace datayes::mdl;

// Define program options structure
struct ProgramOptions
{
    const char *filep; // data file name prefix
    const char *logfp; // log file name prefix
    int zmq_port;
    const char *conf; // Added config file option
    bool help;
    bool background; // Added background option
};

#define DEFAULT_ZMQ_PORT 30001

std::ofstream ofs;

int main(int argc, char *argv[])
{
    char buffer[1024];
    mdl_shl2_msg::NGTSTick *resp = (mdl_shl2_msg::NGTSTick *)buffer;
    struct timeval tv;
    struct tm tm1;
    struct tm timeinfo;
    void *context = zmq_ctx_new();
    void *socket = zmq_socket(context, ZMQ_SUB);
    zmq_connect(socket, "tcp://localhost:30001");
    zmq_setsockopt(socket, ZMQ_SUBSCRIBE, "", 0);

    ofs.open("sh_transorder_1.csv", std::ios::out | std::ios::trunc);
    ofs << "RecvTime,BizIndex,Channel,SecurityID,TickTime,Type,BuyOrderNO,SellOrderNO,Price,Qty,TradeMoney,TickBSFlag,Delay" << std::endl;

    while (true)
    {
        zmq_recv(socket, buffer, 1024, 0);

        gettimeofday(&tv, NULL);
        localtime_r(&tv.tv_sec, &timeinfo);
        long long recvtime = tv.tv_sec * 1000000 + tv.tv_usec;

        tm1.tm_year = timeinfo.tm_year;
        tm1.tm_mon = timeinfo.tm_mon;
        tm1.tm_mday = timeinfo.tm_mday;
        tm1.tm_hour = resp->TickTime.GetHour();
        tm1.tm_min = resp->TickTime.GetMinute();
        tm1.tm_sec = resp->TickTime.GetSecond();
        tm1.tm_isdst = 0;
        long long ticktime = mktime(&tm1) * 1000000 + resp->TickTime.GetMilliSec() * 1000;
        ofs << std::format("{:04d}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}.{:06d},{},{},{},{:02d}:{:02d}:{:02d}.{:03d},{},{},{},{},{},{},{},{}",
                           timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                           timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec, (int)tv.tv_usec,
                           resp->BizIndex, resp->Channel, resp->SecurityID.std_str().c_str(),
                           resp->TickTime.GetHour(), resp->TickTime.GetMinute(), resp->TickTime.GetSecond(), resp->TickTime.GetMilliSec(),
                           resp->Type.c_str(), resp->BuyOrderNO, resp->SellOrderNO, resp->Price.GetFloat(), resp->Qty, resp->TradeMoney.GetDouble(), resp->TickBSFlag.c_str(), recvtime - ticktime)
            << std::endl;
    }

    zmq_close(socket);
    zmq_ctx_destroy(context);
    return 0;
}