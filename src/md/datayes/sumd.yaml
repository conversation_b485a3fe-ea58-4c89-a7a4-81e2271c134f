# 超级行情 sumd 配置文件
# 

# 现阶段每只股票只支持最多一个逐笔全速行情源，当然可有多个逐笔全速行情源为不同证券提供逐笔全速行情
sources:
  - name: DYL2     # 通联 L2 行情
    host: ***********
    port: 9869
  - name: DYOT     # 通联逐笔全速行情，包含上交所与深交所
    host: ***********
    # host: 127.0.0.1
    port: 30002
  - name: HTL1     # 华泰 L1 行情
    host: ************
    port: 9869
  - name: GXSZ     # 国信中畅 L1 行情，东莞 VPN
    host: ***********
    port: 9869
  - name: GXSH_BIG     # 国信中畅 L1 行情，上海 VPN
    host: ************
    port: 9869
  - name: GXSH_SMALL   # 国信中畅 L1 行情，上海 VPN
    host: ************
    port: 9870

# 行情消息中的 MdSource 字段与行情源类型映射表，合法的行情源类型为 L1、L2、OT
md_source_type_dict:
  DYL2: L2
  DYSHOT: OT
  DYSZOT: OT
  HTL1: L1
  GXSZ: L1
  GXSH: L1

subscriptions:     # 订阅的行情主题，必须以字符 'S' 开头，因为指数编码与股票、基金编码有重合，指数编码以 'I' 开头
  # - S              # 全部股票、基金行情
  # - S159             # 深交所 ETF
  # 2025.11.11 白工给出的 ETF 列表
  - S159633
  - S159845
  - S159901
  - S159902
  - S159915
  - S159919
  - S159922
  - S159934
  - S159937
  - S159949
  - S159952
  - S159992
  - S159995
  - S510050
  - S510100
  - S510180
  - S510210
  - S510300
  - S510310
  - S510330
  - S510500
  - S510880
  - S511010
  - S511880
  - S511990
  - S512000
  - S512010
  - S512050
  - S512070
  - S512100
  - S512760
  - S512880
  - S515050
  - S518800
  - S518880
  - S588000
  - S588050
  - S588080
  - S588200
  - S588220
  # 一些有趣的深交所股票
  - S003816
  - S002520
  - S002657
  - S002594
  - S000657
  - S300850
  - S300815
  # 一些有趣的上交所股票
  - S600519
  - S600900
  - S601985
  - S601006
  - S600585 

high_water_mark: 100  # ZeroMQ socket 的 high water mark，包括输入与输出，高值没有意义，可能反而有害，堆积的数据都是过时的数据

output:
  host: 127.0.0.1  # 输出 PB 行情的 ZeroMQ XSUB 服务器地址，缺省为空，表示不输出 PB 行情，如果 host_ut 也为空，则缺省为 127.0.0.1
  port: 30003      # 输出 PB 行情的 ZeroMQ XSUB 服务器端口，缺省 30003
  intv_min: 350    # 最小行情输出间隔，毫秒，缺省 100
  intv_max: 500    # 最大行情输出间隔，毫秒，必须是 1000 的因数，缺省 500
  intv_step: 50    # 时间驱动的行情输出间隔步进，毫秒，必须是 1000 的因数，且小于 intv_max，缺省 50
  host_ut: 127.0.0.1  # 输出极简行情的 ZeroMQ XSUB 服务器地址，缺省为空，表示不输出极简行情
  port_ut: 30005      # 输出极简行情的 ZeroMQ XSUB 服务器端口，缺省 30005

background: false   # 是否在后台运行
