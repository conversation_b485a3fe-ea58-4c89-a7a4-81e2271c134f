# Makefile for building dytk and dyot executables

CXX = g++
CXXFLAGS = -g -O2 -std=c++20 -Wall
LDFLAGS = -Wl,-rpath=/usr/local/lib

INCS = -I./mdl_sdk_2_13_231/include

# Library directories for linking
LIBDIR = -L./mdl_sdk_2_13_231/libs/linux

# Libraries to link for compilation
LIBS = -lmdl_api -ldl -lpthread -lrt -lzmq -lprotobuf -lm
# 逐笔接收程序
LIBS_OT = -lmdl_api -ldl -pthread -lrt -lzmq -lm
# 逐笔快照程序
LIBS_SNAP = -lmdl_api -ldl -pthread -lrt -lzmq -lprotobuf -lm -lparquet -larrow

# Static linked lib
SLIBS = -l:libhiredis.a

# Source and object files
SRC = dymd.cc lyproto.quota.pb.cc dymd.h lyproto.quota.pb.h dytk.cc dyot.cc dyszot2z.cc dyshot2z.cc dyshotz2f.cc dyszoz2f.cc dysztz2f.cc dyshot2za.cc dyshot2t.cc dyshotsnap.cc
OBJ_DYTK = dymd.o lyproto.quota.pb.o dytk.o
OBJ_DYOT = dymd.o lyproto.quota.pb.o dyot.o
OBJ_DYSZOT2Z = dyszot2z.o
OBJ_DYSHOT2Z = dyshot2z.o
OBJ_DYSHOTZ2F = dyshotz2f.o
OBJ_DYSZOZ2F = dyszoz2f.o
OBJ_DYSZTZ2F = dysztz2f.o
OBJ_DYSHOT2ZA = dyshot2za.o
OBJ_DYSHOT2T = dyshot2t.o
OBJ_DYSHOTSNAP = lyproto.quota.pb.o dyshotsnap.o dyshot2t.o
DYTK = dytk
DYOT = dyot

# 深交所逐笔ZMQ发布
DYSZOT2Z = dyszot2z
# 上交所逐笔ZMQ发布，裸数据，按 Channel 分多ZMQ端口
DYSHOT2Z = dyshot2z
# 上交所逐笔ZMQ发布，multipart 消息，所有 Channel 发布到同一个端口
DYSHOT2ZA = dyshot2za
# 上交所逐笔存盘
DYSHOTZ2F = dyshotz2f
# 深交所委托存盘
DYSZOZ2F = dyszoz2f
# 深交所成交存盘
DYSZTZ2F = dysztz2f
# 上交所逐笔快照
DYSHOTSNAP = dyshotsnap

TARGET = $(DYTK) $(DYOT) $(DYSZOT2Z) $(DYSHOT2Z) $(DYSHOTZ2F) $(DYSZOZ2F) $(DYSZTZ2F) $(DYSHOT2ZA) $(DYSHOTSNAP)

# Local libs and targets for installation
MDL_LIB = ./mdl_sdk_2_13_231/libs/linux/libmdl_api.so
TARGET_MDL_LIB = /usr/local/lib/libmdl_api.so

all: $(TARGET)

%.o: %.cc
	 $(CXX) $(CXXFLAGS) $(INCS) -c $< -o $@

$(DYTK): $(OBJ_DYTK)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYTK) $(LIBDIR) $(LIBS) $(SLIBS)

$(DYOT): $(OBJ_DYOT)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYOT) $(LIBDIR) $(LIBS) $(SLIBS)

$(DYSZOT2Z): $(OBJ_DYSZOT2Z)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSZOT2Z) $(LIBDIR) $(LIBS_OT)

$(DYSHOT2Z): $(OBJ_DYSHOT2Z)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSHOT2Z) $(LIBDIR) $(LIBS_OT)

$(DYSHOT2ZA): $(OBJ_DYSHOT2ZA)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSHOT2ZA) $(LIBDIR) $(LIBS_OT)

$(DYSHOTZ2F): $(OBJ_DYSHOTZ2F)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSHOTZ2F) $(LIBDIR) $(LIBS_OT)

$(DYSZOZ2F): $(OBJ_DYSZOZ2F)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSZOZ2F) $(LIBDIR) $(LIBS_OT)

$(DYSZTZ2F): $(OBJ_DYSZTZ2F)
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSZTZ2F) $(LIBDIR) $(LIBS_OT)

$(DYSHOTSNAP): $(OBJ_DYSHOTSNAP) dyshot2t.h
	 $(CXX) $(LDFLAGS) -o $@ $(OBJ_DYSHOTSNAP) $(LIBDIR) $(LIBS_SNAP) $(SLIBS)

$(TARGET_MDL_LIB): $(MDL_LIB)
	 install -m 755 $(MDL_LIB) /usr/local/lib/


install: $(TARGET) $(TARGET_MDL_LIB)
	 install -d /usr/local/sbin
	 install -d /usr/local/lib
	 install -m 755 $(TARGET) /usr/local/sbin/

uninstall:
	rm -f /usr/local/sbin/$(DYTK)
	rm -f /usr/local/sbin/$(DYOT)
	rm -f /usr/local/lib/libmdl_api.so
	ldconfig

clean:
	rm -f $(TARGET) $(OBJ_DYTK) $(OBJ_DYOT)

.PHONY: all clean install uninstall
