// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_gfex_msg {

static const uint16_t MDLVID_MDL_GFEX = 101;

enum MDL_GFEXMessageID {
	MDLMID_MDL_GFEX_Future = 1,
	MDLMID_MDL_GFEX_Option = 2,
	MDLMID_MDL_GFEX_FuturePlus = 3,
	MDLMID_MDL_GFEX_CmbTypeInfo = 4
};

#pragma pack(1)

struct Future {
	enum {
		ServiceID = MDLSID_MDL_GFEX,
		ServiceVer = MDLVID_MDL_GFEX,
		MessageID = MDLMID_MDL_GFEX_Future
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t LastVolume;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreOpenInt;
	int64_t OpenIntChg;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> PreCloPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct Option {
	enum {
		ServiceID = MDLSID_MDL_GFEX,
		ServiceVer = MDLVID_MDL_GFEX,
		MessageID = MDLMID_MDL_GFEX_Option
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t LastVolume;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreOpenInt;
	int64_t OpenIntChg;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> PreCloPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct FuturePlus {
	enum {
		ServiceID = MDLSID_MDL_GFEX,
		ServiceVer = MDLVID_MDL_GFEX,
		MessageID = MDLMID_MDL_GFEX_FuturePlus
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t LastVolume;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreOpenInt;
	int64_t OpenIntChg;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> PreCloPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
		int32_t DerVolume;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct CmbTypeInfo {
	enum {
		ServiceID = MDLSID_MDL_GFEX,
		ServiceVer = MDLVID_MDL_GFEX,
		MessageID = MDLMID_MDL_GFEX_CmbTypeInfo
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString CmbtypeId;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> LastTradeVolume;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LifeLowPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	struct BidBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
	};
	MDLListT<AskBookItem> AskBook;
};

#pragma pack()

} // namespace mdl_gfex_msg
} // namespace mdl
} // namespace datayes
