// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_cni_msg {

static const uint16_t MDLVID_MDL_CNI = 101;

enum MDL_CNIMessageID {
	MDLMID_MDL_CNI_Snapshot360001 = 1
};

#pragma pack(1)

struct Snapshot360001 {
	enum {
		ServiceID = MDLSID_MDL_CNI,
		ServiceVer = MDLVID_MDL_CNI,
		MessageID = MDLMID_MDL_CNI_Snapshot360001
	};
	MDLTime UpdateTime;
	MDLAnsiString IndexType;
	MDLAnsiString SecurityID;
	MDLUTF8String Symbol;
	MDLDoubleT<2> TotalVolumeTrade;
	MDLDoubleT<2> TotalValueTrade;
	MDLAnsiString Currency;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<5> LastPrice;
	MDLDoubleT<5> PreCloPrice;
	MDLDoubleT<5> OpenPrice;
	MDLDoubleT<5> HighPrice;
	MDLDoubleT<5> LowPrice;
	MDLDoubleT<5> ClosePrice;
	MDLDoubleT<5> AsiaCloPrice;
	MDLDoubleT<5> GlobalCloPrice;
};

#pragma pack()

} // namespace mdl_cni_msg
} // namespace mdl
} // namespace datayes
