// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_bar_msg {

static const uint16_t MDLVID_MDL_BAR = 101;

enum MDL_BARMessageID {
	MDLMID_MDL_BAR_XSHGStockMinuteBar = 1,
	MDLMID_MDL_BAR_XSHEStockMinuteBar = 2,
	MDLMID_MDL_BAR_HKEXStockMinuteBar = 3,
	MDLMID_MDL_BAR_DCEFutureMinuteBar = 4,
	MDLMID_MDL_BAR_SHFEFutureMinuteBar = 5,
	MDLMID_MDL_BAR_CZCEFutureMinuteBar = 6,
	MDLMID_MDL_BAR_CFFEXFutureMinuteBar = 7,
	MDLMID_MDL_BAR_XSHGOptionMinuteBar = 8,
	MDLMID_MDL_BAR_XSHGIndexMinuteBar = 9,
	MDLMID_MDL_BAR_XSHEIndexMinuteBar = 10,
	<PERSON><PERSON><PERSON>_MDL_BAR_XSHGCapitalFlow = 11,
	MDLMID_MDL_BAR_XSHECapitalFlow = 12,
	MDLMID_MDL_BAR_IndustryCapitalFlow = 13,
	MDLMID_MDL_BAR_XSHGMoneyFlow = 14,
	MDLMID_MDL_BAR_XSHEMoneyFlow = 15,
	MDLMID_MDL_BAR_XSHGStockMultiMinuteBar = 16,
	MDLMID_MDL_BAR_XSHEStockMultiMinuteBar = 17,
	MDLMID_MDL_BAR_HKEXStockMultiMinuteBar = 18,
	MDLMID_MDL_BAR_DCEFutureMultiMinuteBar = 19,
	MDLMID_MDL_BAR_SHFEFutureMultiMinuteBar = 20,
	MDLMID_MDL_BAR_CZCEFutureMultiMinuteBar = 21,
	MDLMID_MDL_BAR_CFFEXFutureMultiMinuteBar = 22,
	MDLMID_MDL_BAR_XSHGOptionMultiMinuteBar = 23,
	MDLMID_MDL_BAR_XSHGIndexMultiMinuteBar = 24,
	MDLMID_MDL_BAR_XSHEIndexMultiMinuteBar = 25,
	MDLMID_MDL_BAR_IndustryMoneyFlow = 26,
	MDLMID_MDL_BAR_HangSengIndexMinuteBar = 27,
	MDLMID_MDL_BAR_DeltaTick = 29,
	MDLMID_MDL_BAR_NeeqMinuteBar = 30,
	MDLMID_MDL_BAR_NeeqMultiMinuteBar = 31,
	MDLMID_MDL_BAR_DCEFutureBar = 36,
	MDLMID_MDL_BAR_SHFEFutureBar = 37,
	MDLMID_MDL_BAR_CZCEFutureBar = 38,
	MDLMID_MDL_BAR_CFFEXFutureBar = 39,
	MDLMID_MDL_BAR_CZCEOptionMinuteBar = 40,
	MDLMID_MDL_BAR_CZCEOptionMultiMinuteBar = 41,
	MDLMID_MDL_BAR_DCEOptionMinuteBar = 42,
	MDLMID_MDL_BAR_DCEOptionMultiMinuteBar = 43,
	MDLMID_MDL_BAR_CZCEOptionBar = 44,
	MDLMID_MDL_BAR_DCEOptionBar = 45,
	MDLMID_MDL_BAR_NanHuaFutureMinuteBar = 46,
	MDLMID_MDL_BAR_FXCMMinuteBar_V2 = 48,
	MDLMID_MDL_BAR_DYFutureIndex = 52,
	MDLMID_MDL_BAR_DYThemeIndex = 53,
	MDLMID_MDL_BAR_SHSZAccuMainMoneyFlow = 54,
	MDLMID_MDL_BAR_SWIndustry = 55,
	MDLMID_MDL_BAR_DeltaTheme = 56,
	MDLMID_MDL_BAR_CrudeFutureMinuteBar = 57,
	MDLMID_MDL_BAR_CrudeFutureSecondBar = 58,
	MDLMID_MDL_BAR_ThemeMinuteBar = 59,
	MDLMID_MDL_BAR_SHSZAccuMoneyFlow = 60,
	MDLMID_MDL_BAR_SHFEOptionMinuteBar = 61,
	MDLMID_MDL_BAR_SHFEOptionMultiMinuteBar = 62,
	MDLMID_MDL_BAR_SHFEOptionBar = 63,
	MDLMID_MDL_BAR_IndexStatistics = 64,
	MDLMID_MDL_BAR_SwgIndexMinuteBar = 65,
	MDLMID_MDL_BAR_DYThemeIndexAA = 66,
	MDLMID_MDL_BAR_DYThemeMoneyFlow = 67,
	MDLMID_MDL_BAR_CustomTheme = 68,
	MDLMID_MDL_BAR_SHSZDeltaPrice = 69,
	MDLMID_MDL_BAR_AHComp = 70,
	MDLMID_MDL_BAR_SHSZAccuMoneyFlow2 = 71,
	MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Ext = 72,
	MDLMID_MDL_BAR_HKEXIndexMinuteBar = 73,
	MDLMID_MDL_BAR_MarketMoneyFlow = 75,
	MDLMID_MDL_BAR_PreMktLimitChange = 76,
	MDLMID_MDL_BAR_SHSZDynamicMinuteBar = 77,
	MDLMID_MDL_BAR_CFFEXOptionBar = 78,
	MDLMID_MDL_BAR_XSHEOptionBar = 79,
	MDLMID_MDL_BAR_CniIndexMinuteBar = 80,
	MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Extra = 81,
	MDLMID_MDL_BAR_SHSZMoneyFlow2PriceSpread = 82,
	MDLMID_MDL_BAR_DYThemeIndexExtra = 83,
	MDLMID_MDL_BAR_RegionMoneyFlow = 84,
	MDLMID_MDL_BAR_CsiIndexMinuteBar = 85,
	MDLMID_MDL_BAR_DeltaIndex = 86,
	MDLMID_MDL_BAR_OrderDetailStat = 87,
	MDLMID_MDL_BAR_SHSZLimitStatus = 88,
	MDLMID_MDL_BAR_IndexDynamicBar = 89,
	MDLMID_MDL_BAR_MarketMetrics = 90,
	MDLMID_MDL_BAR_DeltaPTFPTick = 91,
	MDLMID_MDL_BAR_IndexMoneyFlow2 = 92,
	MDLMID_MDL_BAR_FundsValuation = 93,
	MDLMID_MDL_BAR_CrudeOptionMinuteBar = 94,
	MDLMID_MDL_BAR_SWIndustry2 = 95,
	MDLMID_MDL_BAR_XSHEBondMinuteBar = 96,
	MDLMID_MDL_BAR_XSHGBondMinuteBar = 97,
	MDLMID_MDL_BAR_DYThemeIndex2 = 98,
	MDLMID_MDL_BAR_GFEXFutureMinuteBar = 99,
	MDLMID_MDL_BAR_GFEXOptionMinuteBar = 100,
	MDLMID_MDL_BAR_ThemeMinuteBar2 = 101,
	MDLMID_MDL_BAR_SHSZDeltaIndex = 102,
	MDLMID_MDL_BAR_DYThemeIndex3 = 103,
	MDLMID_MDL_BAR_ThemeMinuteBar3 = 104,
	MDLMID_MDL_BAR_BJBondBar = 105,
	MDLMID_MDL_BAR_DYThemeMoneyFlow3 = 106,
	MDLMID_MDL_BAR_TickerChgPct = 107,
	MDLMID_MDL_BAR_BJStockMinuteBar = 108,
	MDLMID_MDL_BAR_ETFMoneyFlow = 109,
	MDLMID_MDL_BAR_XSHGStockBar = 110,
	MDLMID_MDL_BAR_XSHEStockBar = 111,
	MDLMID_MDL_BAR_XSHGIndexBar = 112,
	MDLMID_MDL_BAR_XSHEIndexBar = 113,
	MDLMID_MDL_BAR_BJIndexBar = 114,
	MDLMID_MDL_BAR_SHSZDyn1MinBar = 115,
	MDLMID_MDL_BAR_StockSelectMainFundingMetrics = 116,
	MDLMID_MDL_BAR_StockSelectMainFlowMetrics = 117,
	MDLMID_MDL_BAR_SSQuanMetrics = 118,
	MDLMID_MDL_BAR_SHSZAccuMoneyFlowMinute = 119
};

#pragma pack(1)

struct XSHGStockMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGStockMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct XSHEStockMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEStockMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct HKEXStockMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_HKEXStockMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct DCEFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct SHFEFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CZCEFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CFFEXFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CFFEXFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct XSHGOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t TotalLongPosition;
};

struct XSHGIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct XSHEIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct XSHGCapitalFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGCapitalFlow
	};
	MDLAnsiString SecurityID;
	MDLTime TradeTime;
	MDLDoubleT<3> Price;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int32_t BSFlag;
	MDLDoubleT<3> CapitalInFlow;
	MDLDoubleT<3> CapitalOutFlow;
	MDLDoubleT<3> NetCapitalInflow;
};

struct XSHECapitalFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHECapitalFlow
	};
	MDLAnsiString SecurityID;
	MDLTime TradeTime;
	MDLDoubleT<3> Price;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int32_t BSFlag;
	MDLDoubleT<3> CapitalInFlow;
	MDLDoubleT<3> CapitalOutFlow;
	MDLDoubleT<3> NetCapitalInflow;
};

struct IndustryCapitalFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_IndustryCapitalFlow
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLTime TradeTime;
	MDLDoubleT<3> CapitalInFlow;
	MDLDoubleT<3> CapitalOutFlow;
	MDLDoubleT<3> NetCapitalInflow;
};

struct XSHGMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGMoneyFlow
	};
	MDLAnsiString SecurityID;
	MDLTime TradeTime;
	MDLDoubleT<3> MoneyInFlow;
	int32_t MoneyInFlowType;
	MDLDoubleT<3> MoneyOutFlow;
	int32_t MoneyOutFlowType;
	MDLDoubleT<3> MoneyNetInFlow;
	int32_t MoneyNetInFlowType;
};

struct XSHEMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEMoneyFlow
	};
	MDLAnsiString SecurityID;
	MDLTime TradeTime;
	MDLDoubleT<3> MoneyInFlow;
	int32_t MoneyInFlowType;
	MDLDoubleT<3> MoneyOutFlow;
	int32_t MoneyOutFlowType;
	MDLDoubleT<3> MoneyNetInFlow;
	int32_t MoneyNetInFlowType;
};

struct XSHGStockMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGStockMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct XSHEStockMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEStockMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct HKEXStockMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_HKEXStockMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct DCEFutureMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEFutureMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct SHFEFutureMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEFutureMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CZCEFutureMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEFutureMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CFFEXFutureMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CFFEXFutureMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct XSHGOptionMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGOptionMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t TotalLongPosition;
	int32_t Unit;
};

struct XSHGIndexMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGIndexMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct XSHEIndexMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEIndexMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct IndustryMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_IndustryMoneyFlow
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TypeSymbol;
	MDLTime TradeTime;
	MDLDoubleT<3> InFlow_s;
	MDLDoubleT<3> InFlow_m;
	MDLDoubleT<3> InFlow_l;
	MDLDoubleT<3> InFlow_xl;
	MDLDoubleT<3> OutFlow_s;
	MDLDoubleT<3> OutFlow_m;
	MDLDoubleT<3> OutFlow_l;
	MDLDoubleT<3> OutFlow_xl;
	MDLDoubleT<3> NetInFlow_s;
	MDLDoubleT<3> NetInFlow_m;
	MDLDoubleT<3> NetInFlow_l;
	MDLDoubleT<3> NetInFlow_xl;
	MDLDoubleT<3> netflowS;
	MDLDoubleT<3> netflowSpct;
	MDLDoubleT<3> netflowM;
	MDLDoubleT<3> netflowMpct;
	MDLDoubleT<3> netflowL;
	MDLDoubleT<3> netflowLpct;
	MDLDoubleT<3> netflowXL;
	MDLDoubleT<3> netflowXLpct;
	MDLDoubleT<3> netflowT;
	MDLDoubleT<3> netflowTpct;
	MDLDoubleT<3> netflowNS;
	MDLDoubleT<3> netflowNSpct;
};

struct HangSengIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_HangSengIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLAnsiString IndexCode;
};

struct DeltaTick {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DeltaTick
	};
	uint32_t ExchangeCD;
	uint32_t MsgID;
	MDLAnsiString SecurityID;
	MDLTime UpdateTime;
	int64_t DeltaVolume;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> DeltaPrice;
	MDLAnsiString Side;
};

struct NeeqMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_NeeqMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
};

struct NeeqMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_NeeqMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct DCEFutureBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEFutureBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct SHFEFutureBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEFutureBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct CZCEFutureBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEFutureBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct CFFEXFutureBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CFFEXFutureBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct CZCEOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CZCEOptionMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEOptionMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct DCEOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct DCEOptionMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEOptionMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CZCEOptionBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CZCEOptionBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct DCEOptionBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DCEOptionBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct NanHuaFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_NanHuaFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
};

struct FXCMMinuteBar_V2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_FXCMMinuteBar_V2
	};
	MDLDate DataDate;
	MDLAnsiString CurrencyPair;
	MDLTime BarTime;
	MDLDoubleT<6> OpenBid;
	MDLDoubleT<6> CloseBid;
	MDLDoubleT<6> HighBid;
	MDLDoubleT<6> LowBid;
	MDLDoubleT<6> OpenAsk;
	MDLDoubleT<6> CloseAsk;
	MDLDoubleT<6> HighAsk;
	MDLDoubleT<6> LowAsk;
	int32_t TotalTicks;
};

struct DYFutureIndex {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYFutureIndex
	};
	MDLDate DataDate;
	MDLTime DateTime;
	uint32_t IndexType;
	MDLAnsiString IndexID;
	MDLAnsiString IndexName;
	MDLUTF8String IndexCNName;
	MDLDoubleT<5> OpenPrice;
	MDLDoubleT<5> ClosePrice;
	MDLDoubleT<5> HighPrice;
	MDLDoubleT<5> LowPrice;
	MDLDoubleT<5> LastPrice;
	MDLDoubleT<3> OpenIntrest;
	MDLDoubleT<3> Turnover;
	int64_t Volume;
	MDLDate ClearingDay;
};

struct DYThemeIndex {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeIndex
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<4> ChgPct;
	MDLAnsiString Ticker;
	int64_t UpNum;
	int64_t DownNum;
	int64_t UpLimitNum;
	int64_t DownLimitNum;
	MDLDoubleT<4> ChgPct5;
	MDLDoubleT<4> ChgPct10;
	MDLDoubleT<4> ChgPct20;
	MDLDoubleT<4> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
};

struct SHSZAccuMainMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMainMoneyFlow
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLDoubleT<3> NetMainInflow;
};

struct SWIndustry {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SWIndustry
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString IndustryID;
	MDLUTF8String IndustryName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<3> ChgPct;
	MDLAnsiString Ticker;
	uint32_t UpNum;
	uint32_t DownNum;
	uint32_t EqualNum;
	uint32_t UpLimitNum;
	uint32_t DownLimitNum;
	MDLDoubleT<3> ChgPct5;
	MDLDoubleT<3> ChgPct10;
	MDLDoubleT<3> ChgPct20;
	MDLDoubleT<3> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
};

struct DeltaTheme {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DeltaTheme
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> LastPrice;
	int64_t DeltaVolume;
};

struct CrudeFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CrudeFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct CrudeFutureSecondBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CrudeFutureSecondBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct ThemeMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_ThemeMinuteBar
	};
	MDLAnsiString ThemeID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct SHSZAccuMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMoneyFlow
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString Exchangecd;
	MDLDoubleT<3> inflowS;
	MDLDoubleT<3> inflowM;
	MDLDoubleT<3> inflowL;
	MDLDoubleT<3> inflowXL;
	MDLDoubleT<3> outflowS;
	MDLDoubleT<3> outflowM;
	MDLDoubleT<3> outflowL;
	MDLDoubleT<3> outflowXL;
	MDLDoubleT<3> netflowS;
	MDLDoubleT<3> netflowSpct;
	MDLDoubleT<3> netflowM;
	MDLDoubleT<3> netflowMpct;
	MDLDoubleT<3> netflowL;
	MDLDoubleT<3> netflowLpct;
	MDLDoubleT<3> netflowXL;
	MDLDoubleT<3> netflowXLpct;
	MDLDoubleT<3> netflowT;
	MDLDoubleT<3> netflowTpct;
	MDLDoubleT<3> netflowNS;
	MDLDoubleT<3> netflowNSpct;
};

struct SHFEOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct SHFEOptionMultiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEOptionMultiMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct SHFEOptionBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHFEOptionBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDate TradeDate;
	MDLDate ClearingDate;
};

struct IndexStatistics {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_IndexStatistics
	};
	MDLAnsiString code;
	MDLUTF8String secName;
	MDLDoubleT<3> MarketValue;
	MDLDoubleT<3> PETTM;
	MDLDoubleT<3> TurnoverRate;
	uint32_t UpNum;
	uint32_t DownNum;
	uint32_t EqualNum;
	uint64_t NegMktValue;
	MDLAnsiString ExchangeCD;
	MDLAnsiString LeadTicker;
	uint32_t UpLimitNum;
	uint32_t DownLimitNum;
	MDLDoubleT<3> UnWtTurnoverRate;
};

struct SwgIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SwgIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct DYThemeIndexAA {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeIndexAA
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<3> ChgPct;
	MDLAnsiString Ticker;
	int64_t UpNum;
	int64_t DownNum;
	int64_t UpLimitNum;
	int64_t DownLimitNum;
	MDLDoubleT<3> ChgPct5;
	MDLDoubleT<3> ChgPct10;
	MDLDoubleT<3> ChgPct20;
	MDLDoubleT<3> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
};

struct DYThemeMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeMoneyFlow
	};
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<3> InFlow_s;
	MDLDoubleT<3> InFlow_m;
	MDLDoubleT<3> InFlow_l;
	MDLDoubleT<3> InFlow_xl;
	MDLDoubleT<3> OutFlow_s;
	MDLDoubleT<3> OutFlow_m;
	MDLDoubleT<3> OutFlow_l;
	MDLDoubleT<3> OutFlow_xl;
	MDLDoubleT<3> netflowS;
	MDLDoubleT<3> netflowSpct;
	MDLDoubleT<3> netflowM;
	MDLDoubleT<3> netflowMpct;
	MDLDoubleT<3> netflowL;
	MDLDoubleT<3> netflowLpct;
	MDLDoubleT<3> netflowXL;
	MDLDoubleT<3> netflowXLpct;
	MDLDoubleT<3> netflowT;
	MDLDoubleT<3> netflowTpct;
	MDLDoubleT<3> netflowNS;
	MDLDoubleT<3> netflowNSpct;
};

struct CustomTheme {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CustomTheme
	};
	MDLAnsiString Tag;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<3> ChgPct;
	MDLAnsiString Ticker;
	int64_t UpNum;
	int64_t DownNum;
	int64_t UpLimitNum;
	int64_t DownLimitNum;
	MDLDoubleT<3> TurnoverRate;
	struct ExtraFieldsItem {
		MDLDoubleT<3> ExValue;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct SHSZDeltaPrice {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZDeltaPrice
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString Exchange;
	MDLUTF8String ShortNM;
	int64_t DeltaVolume;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> DeltaPrice;
	MDLAnsiString Side;
};

struct AHComp {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_AHComp
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString PartyID;
	MDLAnsiString TickerA;
	MDLAnsiString TickerH;
	MDLFloatT<3> LastPriceA;
	MDLFloatT<3> LastPriceH;
	MDLFloatT<3> LastPriceHA;
	MDLFloatT<4> ChgPctA;
	MDLFloatT<4> ChgPctH;
	MDLFloatT<4> AHRatio;
	MDLFloatT<4> HARatio;
	int64_t VolumeA;
	int64_t VolumeH;
};

struct SHSZAccuMoneyFlow2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMoneyFlow2
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> NetflowS;
	MDLFloatT<3> NetflowSpct;
	MDLDoubleT<3> NetflowM;
	MDLFloatT<3> NetflowMpct;
	MDLDoubleT<3> NetflowL;
	MDLFloatT<3> NetflowLpct;
	MDLDoubleT<3> NetflowXL;
	MDLFloatT<3> NetflowXLpct;
	MDLDoubleT<3> NetflowT;
	MDLFloatT<3> NetflowTpct;
};

struct SHSZAccuMoneyFlow2Ext {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Ext
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	uint64_t InVolumeS;
	uint64_t InVolumeM;
	uint64_t InVolumeL;
	uint64_t InVolumeXL;
	uint64_t OutVolumeS;
	uint64_t OutVolumeM;
	uint64_t OutVolumeL;
	uint64_t OutVolumeXL;
	uint32_t InOrdersS;
	uint32_t InOrdersM;
	uint32_t InOrdersL;
	uint32_t InOrdersXL;
	uint32_t OutOrdersS;
	uint32_t OutOrdersM;
	uint32_t OutOrdersL;
	uint32_t OutOrdersXL;
};

struct HKEXIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_HKEXIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct MarketMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_MarketMoneyFlow
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Market;
	int32_t Sector;
	MDLUTF8String SectorName;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> NetflowS;
	MDLFloatT<4> NetflowSpct;
	MDLDoubleT<3> NetflowM;
	MDLFloatT<4> NetflowMpct;
	MDLDoubleT<3> NetflowL;
	MDLFloatT<4> NetflowLpct;
	MDLDoubleT<3> NetflowXL;
	MDLFloatT<4> NetflowXLpct;
	MDLDoubleT<3> NetflowT;
	MDLFloatT<4> NetflowTpct;
};

struct PreMktLimitChange {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_PreMktLimitChange
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLFloatT<5> PrevLimitChg;
};

struct SHSZDynamicMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZDynamicMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLAnsiString ExchangeCD;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLDoubleT<6> MinChg;
	MDLDoubleT<6> MinTurnover;
	MDLTime UpdateTime;
	MDLDoubleT<4> VWap;
	uint64_t TotalTradNum;
	MDLTime BarTime2;
};

struct CFFEXOptionBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CFFEXOptionBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct XSHEOptionBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEOptionBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<4> TotalValue;
	int64_t TotalLongPosition;
	int32_t Unit;
};

struct CniIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CniIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<5> OpenPrice;
	MDLDoubleT<5> ClosePrice;
	MDLDoubleT<5> HighPrice;
	MDLDoubleT<5> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<2> TotalValue;
	int32_t Unit;
};

struct SHSZAccuMoneyFlow2Extra {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Extra
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLDoubleT<3> NetflowAuction;
	MDLDoubleT<3> DDX;
	MDLDoubleT<3> DDY;
	MDLDoubleT<3> DDZ;
	struct ExtraFieldsItem {
		int64_t ExValue;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct SHSZMoneyFlow2PriceSpread {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZMoneyFlow2PriceSpread
	};
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLFloatT<2> Price;
	MDLDoubleT<3> Inflow;
	MDLDoubleT<3> Outflow;
	int64_t VolumeB;
	int64_t VolumeS;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct DYThemeIndexExtra {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeIndexExtra
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLDoubleT<3> Chg;
	int64_t EqualCount;
	int64_t MtkValue;
	int64_t NegMktValue;
	MDLDoubleT<3> PETTM;
};

struct RegionMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_RegionMoneyFlow
	};
	MDLTime DataTime;
	MDLAnsiString Region;
	MDLUTF8String RegionName;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> NetflowS;
	MDLFloatT<4> NetflowSpct;
	MDLDoubleT<3> NetflowM;
	MDLFloatT<4> NetflowMpct;
	MDLDoubleT<3> NetflowL;
	MDLFloatT<4> NetflowLpct;
	MDLDoubleT<3> NetflowXL;
	MDLFloatT<4> NetflowXLpct;
	MDLDoubleT<3> NetflowT;
	MDLFloatT<4> NetflowTpct;
	MDLDoubleT<3> NetflowMain;
	MDLFloatT<4> NetflowMainpct;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct CsiIndexMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CsiIndexMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<5> TotalValue;
	int32_t Unit;
	uint32_t Currency;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct DeltaIndex {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DeltaIndex
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString SecurityID;
	MDLUTF8String Symbol;
	MDLDoubleT<3> LastPrice;
	int64_t DeltaVolume;
	MDLDoubleT<3> DeltaPrice;
};

struct OrderDetailStat {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_OrderDetailStat
	};
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString Exchange;
	MDLDoubleT<2> AvgVolBuy;
	MDLDoubleT<2> AvgVolSell;
	struct BuyPricesItem {
		MDLFloatT<2> Price;
		int64_t VolPL;
		int32_t NumOrdPL;
		MDLDoubleT<2> AvgVolPL;
	};
	MDLListT<BuyPricesItem> BuyPrices;
	struct SellPricesItem {
		MDLFloatT<2> Price;
		int64_t VolPL;
		int32_t NumOrdPL;
		MDLDoubleT<2> AvgVolPL;
	};
	MDLListT<SellPricesItem> SellPrices;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct SHSZLimitStatus {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZLimitStatus
	};
	MDLAnsiString SecurityID;
	MDLAnsiString Exchange;
	MDLTime DataTime;
	uint32_t UpLimitCount;
	int32_t LimitStatus;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct IndexDynamicBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_IndexDynamicBar
	};
	MDLAnsiString SecurityID;
	MDLAnsiString ExchangeCD;
	MDLUTF8String ShortNM;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLAnsiString Currency;
	int32_t Source;
};

struct MarketMetrics {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_MarketMetrics
	};
	MDLAnsiString Name;
	MDLTime UpdateTime;
	int32_t UpNum;
	int32_t EqualNum;
	int32_t DownNum;
	int32_t UpLimitNum;
	int32_t PrevUpLimitNum;
	MDLFloatT<4> UpLimitRatio;
	int32_t UpLimitSide;
	int32_t DownLimitNum;
	int32_t PrevDownLimitNum;
	MDLFloatT<4> DownLimitRatio;
	int32_t DownLimitSide;
	MDLFloatT<4> LimitSuccessRatio;
	MDLFloatT<4> PrevLimitConRatio;
	MDLFloatT<4> LimitFailRatio;
	MDLDoubleT<3> TurnoverSH;
	MDLDoubleT<3> TurnoverSZ;
	MDLDoubleT<3> TurnoverSHSZ;
	MDLDoubleT<3> TurnoverChange;
	int32_t VolLabel;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
	MDLDoubleT<3> TurnoverBJ;
};

struct DeltaPTFPTick {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DeltaPTFPTick
	};
	MDLAnsiString SecurityID;
	MDLAnsiString ExchangeCD;
	MDLTime DataTime;
	uint32_t DeltaNum;
	int64_t DeltaVolume;
	MDLDoubleT<3> DeltaValue;
	MDLDoubleT<3> LastPrice;
	MDLAnsiString Side;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct IndexMoneyFlow2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_IndexMoneyFlow2
	};
	MDLTime DataTime;
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> NetflowS;
	MDLFloatT<3> NetflowSpct;
	MDLDoubleT<3> NetflowM;
	MDLFloatT<3> NetflowMpct;
	MDLDoubleT<3> NetflowL;
	MDLFloatT<3> NetflowLpct;
	MDLDoubleT<3> NetflowXL;
	MDLFloatT<3> NetflowXLpct;
	MDLDoubleT<3> NetflowT;
	MDLFloatT<3> NetflowTpct;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct FundsValuation {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_FundsValuation
	};
	MDLAnsiString Ticker;
	MDLUTF8String Symbol;
	MDLTime DataTime;
	MDLFloatT<5> AlReturn;
	MDLFloatT<4> NAV;
	MDLFloatT<5> ChgPct;
	int32_t ChgFlag;
	MDLTime CreateTime;
	MDLFloatT<4> Chg;
};

struct CrudeOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_CrudeOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct SWIndustry2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SWIndustry2
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString IndustryID;
	MDLUTF8String IndustryName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<3> ChgPct;
	MDLAnsiString Ticker;
	uint32_t UpNum;
	uint32_t DownNum;
	uint32_t EqualNum;
	uint32_t UpLimitNum;
	uint32_t DownLimitNum;
	MDLDoubleT<3> ChgPct5;
	MDLDoubleT<3> ChgPct10;
	MDLDoubleT<3> ChgPct20;
	MDLDoubleT<3> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
};

struct XSHEBondMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEBondMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<4> TotalValue;
	int32_t Unit;
};

struct XSHGBondMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGBondMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<4> TotalValue;
	int32_t Unit;
};

struct DYThemeIndex2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeIndex2
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<4> ChgPct;
	MDLDoubleT<3> Chg;
	MDLAnsiString Ticker;
	int64_t UpNum;
	int64_t DownNum;
	int64_t EqualCount;
	int64_t UpLimitNum;
	int64_t DownLimitNum;
	MDLDoubleT<4> ChgPct5;
	MDLDoubleT<4> ChgPct10;
	MDLDoubleT<4> ChgPct20;
	MDLDoubleT<4> ChgPct60;
	MDLDoubleT<4> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
	int64_t MtkValue;
	int64_t NegMktValue;
	MDLDoubleT<3> PETTM;
	int32_t Status;
	int32_t ThemeType;
};

struct GFEXFutureMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_GFEXFutureMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct GFEXOptionMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_GFEXOptionMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t OpenInterest;
	int32_t Unit;
	MDLDate TradDay;
	MDLDate ActionDay;
};

struct ThemeMinuteBar2 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_ThemeMinuteBar2
	};
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
	int32_t Unit;
};

struct SHSZDeltaIndex {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZDeltaIndex
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLUTF8String ShortNM;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> DeltaPrice;
	int64_t DeltaVolume;
	MDLDoubleT<3> DeltaValue;
};

struct DYThemeIndex3 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeIndex3
	};
	MDLDate DataDate;
	MDLTime DataTime;
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	int64_t Volume;
	MDLDoubleT<3> Value;
	MDLDoubleT<4> ChgPct;
	MDLDoubleT<3> Chg;
	MDLAnsiString Ticker;
	int64_t UpNum;
	int64_t DownNum;
	int64_t EqualCount;
	int64_t UpLimitNum;
	int64_t DownLimitNum;
	MDLDoubleT<4> ChgPct5;
	MDLDoubleT<4> ChgPct10;
	MDLDoubleT<4> ChgPct20;
	MDLDoubleT<4> ChgPctFY;
	MDLDoubleT<3> TurnoverRate;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
};

struct ThemeMinuteBar3 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_ThemeMinuteBar3
	};
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	MDLDoubleT<3> NetInflow;
	MDLDoubleT<3> NetMainInflow;
	int32_t Unit;
};

struct BJBondBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_BJBondBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct DYThemeMoneyFlow3 {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_DYThemeMoneyFlow3
	};
	MDLAnsiString ThemeID;
	MDLUTF8String ThemeName;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<3> InFlowS;
	MDLDoubleT<3> InFlowM;
	MDLDoubleT<3> InFlowL;
	MDLDoubleT<3> InFlowXL;
	MDLDoubleT<3> OutFlowS;
	MDLDoubleT<3> OutFlowM;
	MDLDoubleT<3> OutFlowL;
	MDLDoubleT<3> OutFlowXL;
	MDLDoubleT<3> NetflowS;
	MDLDoubleT<3> NetflowSpct;
	MDLDoubleT<3> NetflowM;
	MDLDoubleT<3> NetflowMpct;
	MDLDoubleT<3> NetflowL;
	MDLDoubleT<3> NetflowLpct;
	MDLDoubleT<3> NetflowXL;
	MDLDoubleT<3> NetflowXLpct;
	MDLDoubleT<3> NetflowT;
	MDLDoubleT<3> NetflowTpct;
	MDLDoubleT<3> NetflowNS;
	MDLDoubleT<3> NetflowNSpct;
};

struct TickerChgPct {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_TickerChgPct
	};
	MDLAnsiString Ticker;
	MDLTime UpdateTime;
	MDLAnsiString Excd;
	MDLDoubleT<3> ChgPct;
	MDLDoubleT<3> ChgPct1W;
	MDLDoubleT<3> ChgPctFW;
	MDLDoubleT<3> ChgPct1M;
	MDLDoubleT<3> ChgPctFM;
	MDLDoubleT<3> ChgPct3M;
	MDLDoubleT<3> ChgPctTM;
	MDLDoubleT<3> ChgPct6M;
	MDLDoubleT<3> ChgPctSM;
	MDLDoubleT<3> ChgPct1Y;
	MDLDoubleT<3> ChgPctFY;
	MDLDoubleT<3> ChgPctFL;
	MDLDoubleT<3> ChgPctAdj;
	MDLDoubleT<3> ChgPctAdj1W;
	MDLDoubleT<3> ChgPctAdjFW;
	MDLDoubleT<3> ChgPctAdj1M;
	MDLDoubleT<3> ChgPctAdjFM;
	MDLDoubleT<3> ChgPctAdj3M;
	MDLDoubleT<3> ChgPctAdjTM;
	MDLDoubleT<3> ChgPctAdj6M;
	MDLDoubleT<3> ChgPctAdjSM;
	MDLDoubleT<3> ChgPctAdj1Y;
	MDLDoubleT<3> ChgPctAdjFY;
	MDLDoubleT<3> ChgPctAdjFL;
	MDLUTF8String SecurityName;
};

struct BJStockMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_BJStockMinuteBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct ETFMoneyFlow {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_ETFMoneyFlow
	};
	MDLTime DataTime;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> InflowT;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> OutflowT;
	MDLDoubleT<3> NetflowS;
	MDLDoubleT<3> NetflowM;
	MDLDoubleT<3> NetflowL;
	MDLDoubleT<3> NetflowXL;
	MDLDoubleT<3> NetflowT;
	uint64_t InVolumeS;
	uint64_t InVolumeM;
	uint64_t InVolumeL;
	uint64_t InVolumeXL;
	uint64_t OutVolumeS;
	uint64_t OutVolumeM;
	uint64_t OutVolumeL;
	uint64_t OutVolumeXL;
	uint32_t InOrdersS;
	uint32_t InOrdersM;
	uint32_t InOrdersL;
	uint32_t InOrdersXL;
	uint32_t OutOrdersS;
	uint32_t OutOrdersM;
	uint32_t OutOrdersL;
	uint32_t OutOrdersXL;
};

struct XSHGStockBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGStockBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	uint64_t TotalTradNum;
	int32_t Unit;
	MDLDoubleT<3> VWap;
};

struct XSHEStockBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEStockBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	uint64_t TotalTradNum;
	int32_t Unit;
	MDLDoubleT<3> VWap;
};

struct XSHGIndexBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHGIndexBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLDoubleT<4> VWap;
};

struct XSHEIndexBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_XSHEIndexBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLDoubleT<4> VWap;
};

struct BJIndexBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_BJIndexBar
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
};

struct SHSZDyn1MinBar {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZDyn1MinBar
	};
	MDLAnsiString SecurityID;
	MDLAnsiString ExchangeCD;
	MDLTime BarTime;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int32_t Unit;
	MDLDoubleT<6> MinChg;
	MDLDoubleT<6> MinTurnover;
	MDLTime UpdateTime;
	MDLDoubleT<4> VWap;
	uint64_t TotalTradNum;
	MDLTime BarTime2;
};

struct StockSelectMainFundingMetrics {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_StockSelectMainFundingMetrics
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<2> BuyOrderMoneyL;
	MDLDoubleT<2> SellOrderMoneyL;
	MDLDoubleT<2> BuyTransMoneyL;
	MDLDoubleT<2> SellTransMoneyL;
	MDLDoubleT<4> FlowStrengthL;
	MDLDoubleT<4> FlowStrengthSumL;
	MDLDoubleT<2> ActBuyMoneyL;
	MDLDoubleT<2> PasBuyMoneyL;
	MDLDoubleT<2> ActSellMoneyL;
	MDLDoubleT<2> PasSellMoneyL;
	MDLDoubleT<2> NetflowL;
	MDLDoubleT<2> TradeMoney;
	MDLDoubleT<4> FlowSpctL;
	MDLDoubleT<2> NegMarketValue;
	int64_t Timestamp;
};

struct StockSelectMainFlowMetrics {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_StockSelectMainFlowMetrics
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<4> FlowStrength;
	MDLDoubleT<4> FlowStrengthSum;
	MDLDoubleT<2> InFlow;
	MDLDoubleT<2> OutFlow;
	MDLDoubleT<2> NetFlow;
	MDLDoubleT<2> TradeMoney;
	MDLDoubleT<4> FlowSpct;
	MDLDoubleT<2> MarketVal;
};

struct SSQuanMetrics {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SSQuanMetrics
	};
	MDLAnsiString SecurityID;
	MDLTime BarTime;
	MDLDoubleT<2> QuanBuyOrderMoney;
	MDLDoubleT<2> QuanSellOrderMoney;
	MDLDoubleT<2> QuanBuyTransMoney;
	MDLDoubleT<2> QuanSellTransMoney;
	MDLDoubleT<4> QuanFlowStrength;
	MDLDoubleT<4> QuanFlowStrengthSum;
	MDLDoubleT<2> QuanActBuyMoney;
	MDLDoubleT<2> QuanPasBuyMoney;
	MDLDoubleT<2> QuanActSellMoney;
	MDLDoubleT<2> QuanPasSellMoney;
	MDLDoubleT<2> QuanNetflow;
	MDLDoubleT<2> TradeMoney;
	MDLDoubleT<4> QuanFlowSpct;
	MDLDoubleT<2> NegMarketValue;
	int64_t Timestamp;
};

struct SHSZAccuMoneyFlowMinute {
	enum {
		ServiceID = MDLSID_MDL_BAR,
		ServiceVer = MDLVID_MDL_BAR,
		MessageID = MDLMID_MDL_BAR_SHSZAccuMoneyFlowMinute
	};
	MDLTime BarTime;
	uint32_t Unit;
	MDLAnsiString Ticker;
	MDLAnsiString ExchangeCD;
	MDLDoubleT<3> InflowS;
	MDLDoubleT<3> InflowM;
	MDLDoubleT<3> InflowL;
	MDLDoubleT<3> InflowXL;
	MDLDoubleT<3> OutflowS;
	MDLDoubleT<3> OutflowM;
	MDLDoubleT<3> OutflowL;
	MDLDoubleT<3> OutflowXL;
	MDLDoubleT<3> NetflowS;
	MDLDoubleT<3> NetflowM;
	MDLDoubleT<3> NetflowL;
	MDLDoubleT<3> NetflowXL;
	MDLDoubleT<3> NetflowT;
	uint64_t BuyVolumeS;
	uint64_t BuyVolumeM;
	uint64_t BuyVolumeL;
	uint64_t BuyVolumeXL;
	uint64_t SellVolumeS;
	uint64_t SellVolumeM;
	uint64_t SellVolumeL;
	uint64_t SellVolumeXL;
	uint32_t BuyOrdersS;
	uint32_t BuyOrdersM;
	uint32_t BuyOrdersL;
	uint32_t BuyOrdersXL;
	uint32_t SellOrdersS;
	uint32_t SellOrdersM;
	uint32_t SellOrdersL;
	uint32_t SellOrdersXL;
};

#pragma pack()

} // namespace mdl_bar_msg
} // namespace mdl
} // namespace datayes
