// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_hkex_msg {

static const uint16_t MDLVID_MDL_HKEX = 101;

enum MDL_HKEXMessageID {
	MDLMID_MDL_HKEX_OMDMarketData = 2,
	MDLMID_MDL_HKEX_HangSengIndex = 3,
	MDLMID_MDL_HKEX_OMDClosingPrice = 4,
	MDLMID_MDL_HKEX_SCMarketTurnover = 5,
	MDLMID_MDL_HKEX_SCDailyQuotaBalance = 6,
	MDLMID_MDL_HKEX_OMDIndexData = 7,
	MDLMID_MDL_HKEX_OMDSecurityDefinition = 8,
	MDLMID_MDL_HKEX_OMDTradeTicker = 9,
	MDLMID_MDL_HKEX_BrokerQueue = 10,
	MDLMID_MDL_HKEX_OMDOrderBook = 11
};

#pragma pack(1)

struct OMDMarketData {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDMarketData
	};
	MDLAnsiString TickerSymbol;
	MDLTime TradTime;
	int32_t TradStatus;
	int32_t TradType;
	MDLFloatT<3> PreClosePrice;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> NominalPrice;
	MDLFloatT<3> VWAP;
	MDLFloatT<3> Change;
	MDLFloatT<5> ChangePct;
	uint64_t Quantity;
	MDLDoubleT<3> Turnover;
	uint32_t NumTrade;
	MDLAnsiString CurrencyCode;
	MDLFloatT<3> Yield;
	MDLFloatT<3> IEP;
	uint64_t AggQty;
	uint32_t TradSessionID;
	uint32_t TradSessionSubID;
	uint32_t TradSessionStatus;
	struct BidBookItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct HangSengIndex {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_HangSengIndex
	};
	uint32_t IndexType;
	uint32_t SecNo;
	uint32_t Status;
	MDLDoubleT<2> CurrentIndex;
	MDLDoubleT<2> DailyIndexHigh;
	MDLDoubleT<2> DailyIndexLow;
	MDLDoubleT<2> EstamitedAveragePrice;
	uint64_t IndexTurnover;
	MDLDoubleT<2> IndexPointChange;
	MDLDoubleT<2> IndexPointChangePercent;
	MDLTime UpdateTime;
	MDLAnsiString IndexCode;
};

struct OMDClosingPrice {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDClosingPrice
	};
	MDLAnsiString TickerSymbol;
	MDLTime TradTime;
	MDLFloatT<3> ClosingPrice;
	uint32_t NumberOfTrades;
};

struct SCMarketTurnover {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_SCMarketTurnover
	};
	MDLTime DataTime;
	MDLAnsiString Market;
	MDLAnsiString Direction;
	int64_t Buy;
	int64_t Sell;
	int64_t Total;
};

struct SCDailyQuotaBalance {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_SCDailyQuotaBalance
	};
	MDLTime DataTime;
	MDLAnsiString Market;
	MDLAnsiString Direction;
	int64_t DailyQuotaBalance;
};

struct OMDIndexData {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDIndexData
	};
	MDLAnsiString IndexCode;
	MDLAnsiString IndexSource;
	MDLAnsiString CurrencyCode;
	MDLAnsiString IndexStatus;
	MDLDate IndexDate;
	MDLTime IndexTime;
	MDLDoubleT<4> IndexValue;
	MDLDoubleT<4> NetChgPrevDay;
	MDLDoubleT<4> HighValue;
	MDLDoubleT<4> LowValue;
	MDLDoubleT<2> EASValue;
	MDLDoubleT<4> IndexTurnover;
	MDLDoubleT<4> OpeningValue;
	MDLDoubleT<4> ClosingValue;
	MDLDoubleT<4> PreviousSesClose;
	int64_t IndexVolume;
	MDLFloatT<4> NetChgPrevDayPct;
	MDLTime DataTime;
};

struct OMDSecurityDefinition {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDSecurityDefinition
	};
	MDLAnsiString SecurityCode;
	MDLAnsiString MarketCode;
	MDLAnsiString ISINCode;
	MDLAnsiString InstrumentType;
	uint32_t ProductType;
	MDLAnsiString SpreadTableCode;
	MDLAnsiString SecShortName;
	MDLAnsiString CurrencyCode;
	MDLUTF8String SecNameGCCS;
	MDLUTF8String SecNameGB;
	uint32_t LotSize;
	MDLFloatT<3> PreClosePrice;
	MDLAnsiString VCMFlag;
	MDLAnsiString ShortSellFlag;
	MDLAnsiString CASFlag;
	MDLAnsiString CCASSFlag;
	MDLAnsiString DummySecFlag;
	MDLAnsiString StampDutyFlag;
	MDLDate ListingDate;
	MDLDate DelistingDate;
	MDLAnsiString FreeText;
	MDLAnsiString POSFlag;
	MDLFloatT<3> POSUpperLimit;
	MDLFloatT<3> POSLowerLimit;
	MDLAnsiString EFNFlag;
	MDLFloatT<3> AccruedInterest;
	MDLFloatT<3> CouponRate;
	MDLFloatT<3> ConversionRatio;
	MDLFloatT<3> StrikePrice1;
	MDLFloatT<3> StrikePrice2;
	MDLDate MaturityDate;
	MDLAnsiString CallPutFlag;
	MDLAnsiString Style;
	MDLAnsiString WarrantType;
	MDLFloatT<3> CallPrice;
	MDLFloatT<3> Entitlement;
	uint32_t WarrantsPerEntl;
	uint32_t UnderlySecNum;
	struct UnderlySecuritiesItem {
		MDLAnsiString UnderlySecuritiesCode;
	};
	MDLListT<UnderlySecuritiesItem> UnderlySecurities;
	MDLTime SendTime;
	uint32_t DomainStmtSecurityCode;
};

struct OMDTradeTicker {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDTradeTicker
	};
	uint32_t SecurityCode;
	uint32_t TickerID;
	MDLFloatT<3> Price;
	uint64_t Quantity;
	MDLTime TradeTime;
	int32_t TrdType;
	MDLAnsiString TrdCancelFlag;
	MDLTime SendTime;
};

struct BrokerQueue {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_BrokerQueue
	};
	uint32_t SecurityCode;
	int32_t Side;
	int32_t More;
	MDLTime SendTime;
	struct ItemTypesItem {
		int32_t Item;
		int32_t Type;
	};
	MDLListT<ItemTypesItem> ItemTypes;
};

struct OMDOrderBook {
	enum {
		ServiceID = MDLSID_MDL_HKEX,
		ServiceVer = MDLVID_MDL_HKEX,
		MessageID = MDLMID_MDL_HKEX_OMDOrderBook
	};
	uint32_t SecurityCode;
	struct BooksItem {
		uint64_t Quantity;
		MDLFloatT<3> Price;
		uint32_t NumberOfOrders;
		uint32_t Side;
		uint32_t PriceLevel;
		uint32_t UpdateAction;
	};
	MDLListT<BooksItem> Books;
	MDLTime SendTime;
};

#pragma pack()

} // namespace mdl_hkex_msg
} // namespace mdl
} // namespace datayes
