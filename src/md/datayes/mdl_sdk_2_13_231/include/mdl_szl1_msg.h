// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_szl1_msg {

static const uint16_t MDLVID_MDL_SZL1 = 101;

enum MDL_SZL1MessageID {
	MDLMID_MDL_SZL1_SZL1Index = 1,
	MDLMID_MDL_SZL1_SZL1Stock = 2,
	MDLMID_MDL_SZL1_Level1Plus = 3,
	MDLMID_MDL_SZL1_SZL1Option = 4,
	MDLMID_MDL_SZL1_SZL1Option2 = 5,
	MDLMID_MDL_SZL1_Bonds = 6,
	MDLMID_MDL_SZL1_Order300392 = 7,
	MDLMID_MDL_SZL1_Transaction300391 = 8,
	MDLMID_MDL_SZL1_BondDistribution = 9,
	MDLMID_MDL_SZL1_ATP = 10,
	MDLMID_MDL_SZL1_Index2 = 11,
	MDLM<PERSON>_MDL_SZL1_Snapshot309211 = 12
};

#pragma pack(1)

struct SZL1Index {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_SZL1Index
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLDoubleT<3> PreCloseIndex;
	MDLDoubleT<3> OpenIndex;
	MDLDoubleT<3> HighIndex;
	MDLDoubleT<3> LowIndex;
	MDLDoubleT<3> LastIndex;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<3> Turnover;
	MDLTime UpdateTime;
};

struct SZL1Stock {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_SZL1Stock
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLDoubleT<3> Turnover;
	uint32_t TurnNum;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> PE1;
	MDLFloatT<3> PE2;
	MDLFloatT<3> AskPrice5;
	uint64_t AskVolume5;
	MDLFloatT<3> AskPrice4;
	uint64_t AskVolume4;
	MDLFloatT<3> AskPrice3;
	uint64_t AskVolume3;
	MDLFloatT<3> AskPrice2;
	uint64_t AskVolume2;
	MDLFloatT<3> AskPrice1;
	uint64_t AskVolume1;
	MDLFloatT<3> BidPrice1;
	uint64_t BidVolume1;
	MDLFloatT<3> BidPrice2;
	uint64_t BidVolume2;
	MDLFloatT<3> BidPrice3;
	uint64_t BidVolume3;
	MDLFloatT<3> BidPrice4;
	uint64_t BidVolume4;
	MDLFloatT<3> BidPrice5;
	uint64_t BidVolume5;
	MDLTime UpdateTime;
	MDLFloatT<3> DifPrice1;
	MDLFloatT<3> DifPrice2;
	uint64_t OpenInt;
	uint64_t Volume;
	uint32_t DeletionIndicator;
};

struct Level1Plus {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Level1Plus
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString TradingPhaseCode;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct SZL1Option {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_SZL1Option
	};
	MDLAnsiString SecurityID;
	MDLUTF8String InstrumentID;
	MDLTime UpdateTime;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	int64_t TotalOfferQty;
	MDLDoubleT<6> WeightedAvgOfferPx;
	int64_t TotalBidQty;
	MDLDoubleT<6> WeightedAvgBidPx;
	MDLDoubleT<4> HighLimitPrice;
	MDLDoubleT<4> LowLimitPrice;
	int64_t OpenInt;
	MDLFloatT<4> AskPrice5;
	uint64_t AskVolume5;
	MDLFloatT<4> AskPrice4;
	uint64_t AskVolume4;
	MDLFloatT<4> AskPrice3;
	uint64_t AskVolume3;
	MDLFloatT<4> AskPrice2;
	uint64_t AskVolume2;
	MDLFloatT<4> AskPrice1;
	uint64_t AskVolume1;
	MDLFloatT<4> BidPrice1;
	uint64_t BidVolume1;
	MDLFloatT<4> BidPrice2;
	uint64_t BidVolume2;
	MDLFloatT<4> BidPrice3;
	uint64_t BidVolume3;
	MDLFloatT<4> BidPrice4;
	uint64_t BidVolume4;
	MDLFloatT<4> BidPrice5;
	uint64_t BidVolume5;
	MDLFloatT<4> PrevClearingPrice;
	MDLFloatT<4> ClearingPrice;
	MDLFloatT<4> ClosePx;
};

struct SZL1Option2 {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_SZL1Option2
	};
	MDLAnsiString SecurityID;
	MDLUTF8String InstrumentID;
	MDLTime UpdateTime;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	int64_t TotalOfferQty;
	MDLDoubleT<6> WeightedAvgOfferPx;
	int64_t TotalBidQty;
	MDLDoubleT<6> WeightedAvgBidPx;
	MDLDoubleT<4> HighLimitPrice;
	MDLDoubleT<4> LowLimitPrice;
	int64_t OpenInt;
	MDLFloatT<4> AskPrice5;
	uint64_t AskVolume5;
	MDLFloatT<4> AskPrice4;
	uint64_t AskVolume4;
	MDLFloatT<4> AskPrice3;
	uint64_t AskVolume3;
	MDLFloatT<4> AskPrice2;
	uint64_t AskVolume2;
	MDLFloatT<4> AskPrice1;
	uint64_t AskVolume1;
	MDLFloatT<4> BidPrice1;
	uint64_t BidVolume1;
	MDLFloatT<4> BidPrice2;
	uint64_t BidVolume2;
	MDLFloatT<4> BidPrice3;
	uint64_t BidVolume3;
	MDLFloatT<4> BidPrice4;
	uint64_t BidVolume4;
	MDLFloatT<4> BidPrice5;
	uint64_t BidVolume5;
	MDLFloatT<4> PrevClearingPrice;
	MDLFloatT<4> ClearingPrice;
	MDLFloatT<4> ClosePx;
	MDLFloatT<4> ReferPrice;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct Bonds {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Bonds
	};
	MDLTime UpdateTime;
	MDLUTF8String SecurityName;
	MDLAnsiString SecurityID;
	MDLAnsiString MDStreamID;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	uint32_t TradeType;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	MDLDoubleT<5> WAvgPx;
	MDLDoubleT<5> PreWAvgPx;
	int64_t WAvgPxChgBP;
	MDLDoubleT<4> AuctionPrice;
	int64_t AuctionVolume;
	MDLDoubleT<4> AuctionValue;
	struct BidLevelsItem {
		MDLDoubleT<4> Price;
		int64_t Volume;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct AskLevelsItem {
		MDLDoubleT<4> Price;
		int64_t Volume;
	};
	MDLListT<AskLevelsItem> AskLevels;
	struct SubTrdPhaseCodeItem {
		MDLAnsiString SubTradPhase;
	};
	MDLListT<SubTrdPhaseCodeItem> SubTrdPhaseCode;
};

struct Order300392 {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Order300392
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> Price;
	int64_t OrderQty;
	int32_t Side;
	MDLTime TransactTime;
	MDLAnsiString QuoteID;
	MDLAnsiString MemberID;
	MDLAnsiString InvestorType;
	MDLAnsiString InvestorID;
	MDLUTF8String InvestorName;
	MDLAnsiString TraderCode;
	uint32_t SettlPeriod;
	uint32_t SettlType;
	MDLUTF8String Memo;
};

struct Transaction300391 {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Transaction300391
	};
	uint32_t ChannelNo;
	int64_t ApplSeqNum;
	MDLAnsiString MDStreamID;
	int64_t BidApplSeqNum;
	int64_t OfferApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLDoubleT<4> LastPx;
	int64_t LastQty;
	int32_t ExecType;
	MDLTime TransactTime;
	uint32_t SettlPeriod;
	uint32_t SettlType;
};

struct BondDistribution {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_BondDistribution
	};
	MDLTime UpdateTime;
	MDLUTF8String SecurityName;
	MDLAnsiString SecurityID;
	MDLAnsiString MDStreamID;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<4> PreCloPrice;
	int64_t TurnNum;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> DifPrice1;
	MDLDoubleT<4> DifPrice2;
	struct BidLevelsItem {
		MDLDoubleT<4> Price;
		int64_t Volume;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct AskLevelsItem {
		MDLDoubleT<4> Price;
		int64_t Volume;
	};
	MDLListT<AskLevelsItem> AskLevels;
};

struct ATP {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_ATP
	};
	MDLAnsiString SecurityID;
	MDLAnsiString MDStreamID;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	int64_t Volume;
	MDLDoubleT<4> Turnover;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> ClosePrice;
	int64_t AskVolume1;
	int64_t BidVolume1;
	int64_t TurnNum;
};

struct Index2 {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Index2
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLDoubleT<4> PreCloseIndex;
	MDLDoubleT<4> OpenIndex;
	MDLDoubleT<4> HighIndex;
	MDLDoubleT<4> LowIndex;
	MDLDoubleT<4> LastIndex;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<3> Turnover;
	MDLTime UpdateTime;
};

struct Snapshot309211 {
	enum {
		ServiceID = MDLSID_MDL_SZL1,
		ServiceVer = MDLVID_MDL_SZL1,
		MessageID = MDLMID_MDL_SZL1_Snapshot309211
	};
	MDLTime UpdateTime;
	uint32_t ChannelNo;
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLAnsiString SecurityIDSource;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<6> IOPV;
};

#pragma pack()

} // namespace mdl_szl1_msg
} // namespace mdl
} // namespace datayes
