// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_neeq_msg {

static const uint16_t MDLVID_MDL_NEEQ = 101;

enum MDL_NEEQMessageID {
	MDLMID_MDL_NEEQ_Index = 1,
	MDLMID_MDL_NEEQ_Stock = 2,
	MDLMID_MDL_NEEQ_MatchedBargainOrder = 3,
	MDLMID_MDL_NEEQ_SecurityStatus = 4,
	MDLMID_MDL_NEEQ_NanHuaFuture = 5,
	MDLMID_MDL_NEEQ_FXCMForex = 7,
	MDLMID_MDL_NEEQ_BJStock = 8,
	MDLMID_MDL_NEEQ_BJBondSnapshot = 9,
	MDLMID_MDL_NEEQ_BJBondOrder011 = 10,
	MDLMID_MDL_NEEQ_BJBondTrans01X = 11,
	MDLMID_MDL_NEEQ_BJBondSnapshotL1 = 12,
	MDLMID_MDL_NEEQ_BJIndex = 13
};

#pragma pack(1)

struct Index {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_Index
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLFloatT<3> PreCloIndex;
	MDLFloatT<3> OpenIndex;
	MDLFloatT<3> LastIndex;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLFloatT<3> HighIndex;
	MDLFloatT<3> LowIndex;
	uint32_t StatusCode;
	MDLTime UpdateTime;
};

struct Stock {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_Stock
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> LastPrice;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	uint32_t TurnNum;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<4> PE1;
	MDLFloatT<4> PE2;
	MDLFloatT<3> DifPrice1;
	MDLFloatT<3> DifPrice2;
	uint64_t OpenInt;
	uint32_t StatusCode;
	MDLTime UpdateTime;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct MatchedBargainOrder {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_MatchedBargainOrder
	};
	MDLUTF8String SecurityID;
	MDLAnsiString TranscationUnit;
	MDLAnsiString TranscationType;
	uint64_t Volume;
	MDLFloatT<3> Price;
	uint32_t TranscationNo;
	MDLTime OrderTime;
	uint32_t RecordStatus;
	uint32_t ReservedFlag;
	MDLTime UpdateTime;
};

struct SecurityStatus {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_SecurityStatus
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString XXYWJC;
	MDLAnsiString XXJCZQ;
	MDLAnsiString XXISIN;
	int32_t XXZRDW;
	MDLAnsiString XXHYZL;
	MDLAnsiString XXHBZL;
	MDLFloatT<2> XXMGMZ;
	uint64_t XXZGB;
	uint64_t XXFXSGB;
	MDLFloatT<4> XXSNSY;
	MDLFloatT<4> XXBNSY;
	MDLFloatT<6> XXJSFL;
	MDLFloatT<6> XXYHSL;
	MDLFloatT<6> XXGHFL;
	MDLDate XXGPRQ;
	MDLDate XXZQQXR;
	MDLDate XXDQR;
	uint64_t XXMBXL;
	uint32_t XXBLDW;
	uint32_t XXSLDW;
	uint32_t XXZXSBSL;
	MDLFloatT<3> XXJGDW;
	MDLFloatT<3> XXSBCS;
	MDLFloatT<3> XXHXCS;
	uint32_t XXXJXZ;
	MDLFloatT<3> XXZTJG;
	MDLFloatT<3> XXDTJG;
	MDLFloatT<3> XXDZZTJG;
	MDLFloatT<3> XXDZDTJG;
	MDLAnsiString XXCFGBZ;
	MDLFloatT<2> XXZHBL;
	MDLAnsiString XXZRZT;
	MDLAnsiString XXZQJB;
	MDLAnsiString XXZRLX;
	uint32_t XXZSSSL;
	MDLAnsiString XXTPBZ;
	MDLAnsiString XXCQCX;
	MDLAnsiString XXWLTP;
	MDLAnsiString XXQTYW;
	MDLTime UpdateTime;
	MDLAnsiString XXJYS;
	MDLAnsiString XXFCBZ;
	MDLAnsiString XXRZBD;
	MDLAnsiString XXRQBD;
	MDLAnsiString XXDRRZ;
	MDLAnsiString XXDRRQ;
};

struct NanHuaFuture {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_NanHuaFuture
	};
	MDLAnsiString SecurityID;
	MDLAnsiString ExchangeID;
	MDLAnsiString ExchangeSecID;
	int32_t SecurityStatus;
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	int32_t UpdateSequence;
	MDLDate ClearingDate;
	MDLDoubleT<6> ChangePrice;
	MDLDoubleT<6> ChangeMarkup;
	MDLDoubleT<6> ChangeSwing;
	MDLDoubleT<3> LastVolume;
	MDLDoubleT<3> Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> AveragePrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> PreClosePrice;
	MDLDoubleT<6> ClosePrice;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<6> PreDelta;
	MDLDoubleT<6> CurrDelta;
	MDLDoubleT<6> PreSetPrice;
	MDLDoubleT<6> SetPrice;
	MDLDoubleT<6> ULimitPrice;
	MDLDoubleT<6> LLimitPrice;
	MDLDoubleT<3> BestBidVolume;
	MDLDoubleT<6> BestBidPrice;
	MDLDoubleT<3> BestAskVolume;
	MDLDoubleT<6> BestAskPrice;
	struct BidPriceLevelItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		MDLDoubleT<3> Volume;
		MDLDoubleT<6> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
	MDLDoubleT<6> OpenAmount;
};

struct FXCMForex {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_FXCMForex
	};
	MDLAnsiString OfferID;
	MDLAnsiString Instrument;
	MDLAnsiString QuoteID;
	MDLDoubleT<6> Bid;
	MDLDoubleT<6> Ask;
	MDLDoubleT<6> Low;
	MDLDoubleT<6> High;
	int32_t Volume;
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString BidTradable;
	MDLAnsiString AskTradable;
	MDLDoubleT<6> SellInterest;
	MDLDoubleT<6> BuyInterest;
	MDLAnsiString ContractCurrency;
	int32_t Digits;
	MDLDoubleT<6> PointSize;
	MDLAnsiString SubscriptionStatus;
	int32_t InstrumentType;
	MDLDoubleT<6> ContractMultiplier;
	MDLAnsiString TradingStatus;
	MDLDate ValueDate;
	MDLAnsiString BidID;
	MDLAnsiString AskID;
	MDLDate BidExpireDate;
	MDLDate AskExpireDate;
	int32_t UpdateType;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct BJStock {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJStock
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> LastPrice;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	uint32_t TurnNum;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<4> PE1;
	MDLFloatT<4> PE2;
	MDLFloatT<3> DifPrice1;
	MDLFloatT<3> DifPrice2;
	uint64_t OpenInt;
	uint32_t StatusCode;
	MDLTime UpdateTime;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLFloatT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct BJBondSnapshot {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJBondSnapshot
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLAnsiString SecIDSrc;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<5> PreClosePx;
	int64_t NumTrades;
	int64_t TotalVolumeTrade;
	MDLDoubleT<3> TotalValueTrade;
	MDLDoubleT<5> LastPrice;
	int32_t TradeType;
	MDLDoubleT<5> OpenPrice;
	MDLDoubleT<5> ClosePrice;
	MDLDoubleT<5> HighPrice;
	MDLDoubleT<5> LowPrice;
	MDLDoubleT<5> WAvgPx;
	MDLDoubleT<5> DifPrice1;
	MDLDoubleT<5> DifPrice2;
	MDLDoubleT<5> AltWAvgBidPx;
	int64_t TotalBidQty;
	MDLDoubleT<5> AltWAvgOfferPx;
	int64_t TotalOfferQty;
	MDLDoubleT<5> AuctionPrice;
	struct Bid1OrderQtyItem {
		int64_t Qty;
	};
	MDLListT<Bid1OrderQtyItem> Bid1OrderQty;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<5> Price;
		int64_t NumberOfOrders;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct Ask1OrderQtyItem {
		int64_t Qty;
	};
	MDLListT<Ask1OrderQtyItem> Ask1OrderQty;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<5> Price;
		int64_t NumberOfOrders;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	MDLAnsiString SubTradPhase1;
	MDLAnsiString SubTradPhase2;
	MDLAnsiString SubTradPhase3;
	MDLAnsiString SubTradPhase4;
	MDLAnsiString SubTradPhase5;
	int64_t AuctionVolumeTrade;
	MDLDoubleT<3> AuctionValueTrade;
	MDLTime TradeTime;
	MDLAnsiString MDStreamID;
};

struct BJBondOrder011 {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJBondOrder011
	};
	MDLTime TransactTime;
	int32_t Channel;
	int64_t ApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecIDSrc;
	MDLDoubleT<5> Price;
	int64_t OrderQty;
	int32_t Side;
	int32_t OrdType;
	int32_t SettlType;
	int32_t SettlPeriod;
	MDLAnsiString MDStreamID;
};

struct BJBondTrans01X {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJBondTrans01X
	};
	MDLAnsiString MDStreamID;
	MDLTime TransactTime;
	int32_t Channel;
	int64_t ApplSeqNum;
	MDLAnsiString SecurityID;
	MDLAnsiString SecIDSrc;
	int64_t BidApplSeqNum;
	int64_t AskApplSeqNum;
	MDLDoubleT<5> TradePrice;
	int64_t TradeQty;
	MDLDoubleT<3> TradeMoney;
	int32_t ExecType;
	int32_t SettlType;
	int32_t SettlPeriod;
};

struct BJBondSnapshotL1 {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJBondSnapshotL1
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLAnsiString SecIDSrc;
	MDLAnsiString TradingPhaseCode;
	MDLDoubleT<5> PreClosePx;
	int64_t NumTrades;
	int64_t TotalVolumeTrade;
	MDLDoubleT<3> TotalValueTrade;
	MDLDoubleT<5> LastPrice;
	int32_t TradeType;
	MDLDoubleT<5> OpenPrice;
	MDLDoubleT<5> ClosePrice;
	MDLDoubleT<5> HighPrice;
	MDLDoubleT<5> LowPrice;
	MDLDoubleT<5> WAvgPx;
	MDLDoubleT<5> DifPrice1;
	MDLDoubleT<5> DifPrice2;
	MDLDoubleT<5> AuctionPrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<5> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<5> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	MDLAnsiString SubTradPhase1;
	MDLAnsiString SubTradPhase2;
	MDLAnsiString SubTradPhase3;
	MDLAnsiString SubTradPhase4;
	MDLAnsiString SubTradPhase5;
	int64_t AuctionVolumeTrade;
	MDLDoubleT<3> AuctionValueTrade;
	MDLTime TradeTime;
	MDLAnsiString MDStreamID;
};

struct BJIndex {
	enum {
		ServiceID = MDLSID_MDL_NEEQ,
		ServiceVer = MDLVID_MDL_NEEQ,
		MessageID = MDLMID_MDL_NEEQ_BJIndex
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLFloatT<3> PreCloIndex;
	MDLFloatT<3> OpenIndex;
	MDLFloatT<3> LastIndex;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLFloatT<3> HighIndex;
	MDLFloatT<3> LowIndex;
	uint32_t StatusCode;
	MDLTime UpdateTime;
};

#pragma pack()

} // namespace mdl_neeq_msg
} // namespace mdl
} // namespace datayes
