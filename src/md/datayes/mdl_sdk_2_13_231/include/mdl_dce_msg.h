// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_dce_msg {

static const uint16_t MDLVID_MDL_DCE = 101;

enum MDL_DCEMessageID {
	MDLMID_MDL_DCE_CTPFuture = 1,
	MDLMID_MDL_DCE_Future = 2,
	MDLMID_MDL_DCE_FuturePlus = 5,
	MDLMID_MDL_DCE_CTPOption = 6,
	MDLMID_MDL_DCE_Indexes = 7,
	MDLMID_MDL_DCE_Index2 = 8
};

#pragma pack(1)

struct CTPFuture {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_CTPFuture
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDoubleT<3> BidPrice2;
	int32_t BidVolume2;
	MDLDoubleT<3> AskPrice2;
	int32_t AskVolume2;
	MDLDoubleT<3> BidPrice3;
	int32_t BidVolume3;
	MDLDoubleT<3> AskPrice3;
	int32_t AskVolume3;
	MDLDoubleT<3> BidPrice4;
	int32_t BidVolume4;
	MDLDoubleT<3> AskPrice4;
	int32_t AskVolume4;
	MDLDoubleT<3> BidPrice5;
	int32_t BidVolume5;
	MDLDoubleT<3> AskPrice5;
	int32_t AskVolume5;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
};

struct Future {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_Future
	};
	MDLAnsiString InstruID;
	MDLDate TradDay;
	MDLDate ActionDay;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> SetPrice;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> OpenInt;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
};

struct FuturePlus {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_FuturePlus
	};
	MDLAnsiString SecurityID;
	MDLDate UpdateDate;
	MDLDate ClearingDate;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct CTPOption {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_CTPOption
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> BidPrice1;
	int32_t BidVolume1;
	MDLDoubleT<3> AskPrice1;
	int32_t AskVolume1;
	MDLDoubleT<3> BidPrice2;
	int32_t BidVolume2;
	MDLDoubleT<3> AskPrice2;
	int32_t AskVolume2;
	MDLDoubleT<3> BidPrice3;
	int32_t BidVolume3;
	MDLDoubleT<3> AskPrice3;
	int32_t AskVolume3;
	MDLDoubleT<3> BidPrice4;
	int32_t BidVolume4;
	MDLDoubleT<3> AskPrice4;
	int32_t AskVolume4;
	MDLDoubleT<3> BidPrice5;
	int32_t BidVolume5;
	MDLDoubleT<3> AskPrice5;
	int32_t AskVolume5;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
};

struct Indexes {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_Indexes
	};
	MDLDate TradDay;
	MDLDate ActionDay;
	MDLTime UpdateTime;
	uint32_t IndexNo;
	MDLAnsiString IndexCode;
	MDLUTF8String IndexName;
	MDLDoubleT<3> LastClearPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ChangePrice;
	MDLDoubleT<3> ChangeRate;
	uint32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> LifeLow;
	MDLDoubleT<3> LifeHigh;
	MDLDoubleT<3> LastClose;
	MDLDoubleT<3> AvgPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> ClearPrice;
	uint32_t IndexQuotSeq;
};

struct Index2 {
	enum {
		ServiceID = MDLSID_MDL_DCE,
		ServiceVer = MDLVID_MDL_DCE,
		MessageID = MDLMID_MDL_DCE_Index2
	};
	MDLDate TradDay;
	MDLDate ActionDay;
	MDLTime UpdateTime;
	uint32_t IndexNo;
	MDLAnsiString IndexCode;
	MDLUTF8String IndexName;
	MDLDoubleT<4> LastClearPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> ChangePrice;
	MDLDoubleT<4> ChangeRate;
	MDLDoubleT<4> LifeLow;
	MDLDoubleT<4> LifeHigh;
	MDLDoubleT<4> LastClose;
	MDLDoubleT<4> AvgPrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> ClearPrice;
	uint32_t IndexQuotSeq;
};

#pragma pack()

} // namespace mdl_dce_msg
} // namespace mdl
} // namespace datayes
