// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_sge_msg {

static const uint16_t MDLVID_MDL_SGE = 101;

enum MDL_SGEMessageID {
	MDLMID_MDL_SGE_GoldPrice = 1,
	MDLMID_MDL_SGE_GoldDelPrice = 2,
	MDLMID_MDL_SGE_MarketInfo = 3,
	MDLMID_MDL_SGE_MarketStatusChange = 4,
	MDLMID_MDL_SGE_InstrumentInfo = 5,
	MDLMID_MDL_SGE_InstrumentStatusChange = 6,
	MD<PERSON>ID_MDL_SGE_ExchangeStatus = 7,
	MDLMID_MDL_SGE_DelInstruInfo = 8,
	MDLMID_MDL_SGE_CompensationInfo = 9,
	MDLMID_MDL_SGE_Announcement = 10,
	MDLMID_MDL_SGE_GoldNotice = 11,
	MDLMID_MDL_SGE_SilverNotice = 12,
	MDLMID_MDL_SGE_GoldFpvNotice = 13,
	MDLMID_MDL_SGE_SilverFpvNotice = 14,
	MDLMID_MDL_SGE_OptionsImvolNotice = 15
};

#pragma pack(1)

struct GoldPrice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_GoldPrice
	};
	MDLAnsiString MarketID;
	MDLAnsiString InstruID;
	MDLUTF8String InstruName;
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLDoubleT<6> PreSetPrice;
	MDLDoubleT<6> PreCloPrice;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> ClosePrice;
	MDLDoubleT<6> SettlePrice;
	int64_t TradeVolume;
	MDLDoubleT<6> TradeWeight;
	MDLDoubleT<6> ULimitPrice;
	MDLDoubleT<6> LLimitPrice;
	MDLDoubleT<6> UpDown;
	MDLDoubleT<6> UpDownRate;
	int64_t OpenInt;
	int64_t TurnOver;
	MDLDoubleT<6> AvgPrice;
	struct BidLevelsItem {
		MDLDoubleT<6> Price;
		int64_t Volume;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct AskLevelsItem {
		MDLDoubleT<6> Price;
		int64_t Volume;
	};
	MDLListT<AskLevelsItem> AskLevels;
};

struct GoldDelPrice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_GoldDelPrice
	};
	MDLAnsiString InstruID;
	int64_t BidLot;
	int64_t AskLot;
	int64_t MidBidLot;
	int64_t MidAskLot;
};

struct MarketInfo {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_MarketInfo
	};
	MDLAnsiString MarketID;
	MDLUTF8String MarketName;
	MDLAnsiString MarketType;
	MDLAnsiString MarketOpenFlag;
	MDLAnsiString MarketState;
};

struct MarketStatusChange {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_MarketStatusChange
	};
	MDLAnsiString MarketID;
	MDLAnsiString MarketState;
};

struct InstrumentInfo {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_InstrumentInfo
	};
	MDLAnsiString InstruID;
	MDLUTF8String InstruName;
	MDLAnsiString MarketID;
	MDLAnsiString VarietyType;
	MDLAnsiString VarietyID;
	int64_t InstUnit;
	MDLDoubleT<6> Tick;
	int64_t MaxHand;
	int64_t MinHand;
	MDLDoubleT<6> UpperLimit;
	MDLDoubleT<6> LowerLimit;
	MDLAnsiString InstOpenFlag;
	MDLAnsiString InstState;
	MDLDoubleT<6> RefPrice;
	MDLDoubleT<6> RecvRate;
	MDLDate DueDate;
	int64_t DeliveryDay;
	MDLAnsiString InstCode;
};

struct InstrumentStatusChange {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_InstrumentStatusChange
	};
	MDLAnsiString InstruID;
	MDLAnsiString InstState;
};

struct ExchangeStatus {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_ExchangeStatus
	};
	MDLAnsiString ExchangeID;
	MDLDate TradDay;
	MDLAnsiString ExchangeState;
	MDLTime AnnounceTime;
};

struct DelInstruInfo {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_DelInstruInfo
	};
	MDLAnsiString VarietyID;
	MDLUTF8String VarietyName;
	MDLUTF8String VarietyAbbr;
	MDLAnsiString VarietyType;
	MDLDoubleT<6> MinPickup;
	MDLDoubleT<6> DefaultStdWeight;
	MDLDoubleT<6> PickupBase;
	MDLAnsiString WeightUnit;
	MDLAnsiString DestroyFlag;
};

struct CompensationInfo {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_CompensationInfo
	};
	MDLDate TradDay;
	MDLAnsiString InstruID;
	MDLAnsiString PayDirection;
	MDLDoubleT<6> DeferFeeRate;
};

struct Announcement {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_Announcement
	};
	MDLDate AnnounceDate;
	MDLTime AnnounceTime;
	MDLUTF8String Title;
	MDLUTF8String Content;
};

struct GoldNotice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_GoldNotice
	};
	MDLTime MarketBeginTime;
	MDLDate TradDay;
	MDLAnsiString InstruID;
	MDLUTF8String InstruName;
	MDLAnsiString InstCode;
	MDLAnsiString SessionID;
	MDLUTF8String SessionName;
	MDLDoubleT<4> InitPrice;
	MDLTime BeginTime;
	int64_t TradeVolume;
	MDLDoubleT<3> TradeWeight;
	int64_t BidLot;
	MDLDoubleT<3> BidWeight;
	int64_t AskLot;
	MDLDoubleT<3> AskWeight;
	MDLDoubleT<4> MkiFpPrice;
	MDLAnsiString IsSettlePrice;
	int64_t Rounds;
	MDLTime ApplyTime;
	MDLDoubleT<4> MonthAvgPrice;
	uint64_t MonthAmount;
	MDLDoubleT<6> AvgPrice;
};

struct SilverNotice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_SilverNotice
	};
	MDLDate TradDay;
	MDLAnsiString InstruID;
	MDLAnsiString InstCode;
	MDLAnsiString SessionID;
	MDLUTF8String SessionName;
	MDLDoubleT<4> InitPrice;
	MDLTime BeginTime;
	int64_t TradeVolume;
	int64_t BidLot;
	int64_t BidWeight;
	int64_t AskLot;
	int64_t AskWeight;
	MDLDoubleT<4> MkiFpPrice;
	MDLAnsiString IsSettlePrice;
	int64_t Rounds;
	MDLTime ApplyTime;
	MDLDoubleT<4> MonthAvgPrice;
	uint64_t MonthAmount;
	uint64_t AvgPrice;
};

struct GoldFpvNotice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_GoldFpvNotice
	};
	MDLDate TradDay;
	MDLAnsiString CurveID;
	MDLAnsiString InstruID;
	MDLAnsiString InstCode;
	MDLUTF8String InstruName;
	int64_t InstruOrder;
	int64_t TenorID;
	MDLUTF8String TenorName;
	int64_t TenorOrder;
	MDLDoubleT<6> Price;
};

struct SilverFpvNotice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_SilverFpvNotice
	};
	MDLDate TradDay;
	MDLAnsiString CurveID;
	MDLAnsiString InstruID;
	MDLAnsiString InstCode;
	MDLUTF8String InstruName;
	int64_t InstruOrder;
	int64_t TenorID;
	MDLUTF8String TenorName;
	int64_t TenorOrder;
	MDLDoubleT<6> Price;
};

struct OptionsImvolNotice {
	enum {
		ServiceID = MDLSID_MDL_SGE,
		ServiceVer = MDLVID_MDL_SGE,
		MessageID = MDLMID_MDL_SGE_OptionsImvolNotice
	};
	MDLDate TradDay;
	MDLAnsiString CurveID;
	MDLAnsiString InstruID;
	MDLAnsiString InstCode;
	MDLUTF8String InstruName;
	int64_t InstruOrder;
	int64_t TenorID;
	MDLUTF8String TenorName;
	int64_t TenorOrder;
	MDLAnsiString Target;
	MDLDoubleT<6> Price;
};

#pragma pack()

} // namespace mdl_sge_msg
} // namespace mdl
} // namespace datayes
