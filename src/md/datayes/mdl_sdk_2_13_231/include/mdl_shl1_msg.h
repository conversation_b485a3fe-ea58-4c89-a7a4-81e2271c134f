// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_shl1_msg {

static const uint16_t MDLVID_MDL_SHL1 = 101;

enum MDL_SHL1MessageID {
	MDLMID_MDL_SHL1_SHL1Index = 1,
	MDLMID_MDL_SHL1_SHL1Stock = 2,
	MDLMID_MDL_SHL1_Indexes = 3,
	MDLMID_MDL_SHL1_Equity = 4,
	MDLMID_MDL_SHL1_Bonds = 5,
	MDLMID_MDL_SHL1_Funds = 6,
	MDLMID_MDL_SHL1_Level1Plus = 7,
	MDLMID_MDL_SHL1_Equity2 = 8,
	MDLMID_MDL_SHL1_Bond2 = 9,
	MDLMID_MDL_SHL1_Fund2 = 10,
	MDLMID_MDL_SHL1_Comp = 11,
	MDLMID_MDL_SHL1_BondDist = 12,
	MDLMID_MDL_SHL1_ATP = 13
};

#pragma pack(1)

struct SHL1Index {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_SHL1Index
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLDoubleT<3> PreCloseIndex;
	MDLDoubleT<3> OpenIndex;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> HighIndex;
	MDLDoubleT<3> LowIndex;
	MDLDoubleT<3> LastIndex;
	int64_t TradVolume;
	MDLTime UpdateTime;
};

struct SHL1Stock {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_SHL1Stock
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLDoubleT<3> Turnover;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	uint64_t Volume;
	MDLFloatT<3> PE;
	uint64_t BidVolume1;
	MDLFloatT<3> BidPrice1;
	uint64_t BidVolume2;
	MDLFloatT<3> BidPrice2;
	uint64_t BidVolume3;
	MDLFloatT<3> BidPrice3;
	uint64_t BidVolume4;
	MDLFloatT<3> BidPrice4;
	uint64_t BidVolume5;
	MDLFloatT<3> BidPrice5;
	uint64_t AskVolume1;
	MDLFloatT<3> AskPrice1;
	uint64_t AskVolume2;
	MDLFloatT<3> AskPrice2;
	uint64_t AskVolume3;
	MDLFloatT<3> AskPrice3;
	uint64_t AskVolume4;
	MDLFloatT<3> AskPrice4;
	uint64_t AskVolume5;
	MDLFloatT<3> AskPrice5;
	MDLTime UpdateTime;
	uint32_t DeletionIndicator;
};

struct Indexes {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Indexes
	};
	MDLAnsiString IndexID;
	MDLUTF8String IndexName;
	MDLDoubleT<4> PreCloseIndex;
	MDLDoubleT<4> OpenIndex;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<4> HighIndex;
	MDLDoubleT<4> LowIndex;
	MDLDoubleT<4> LastIndex;
	uint64_t TradVolume;
	MDLTime UpdateTime;
	MDLDoubleT<4> CloseIndex;
	MDLAnsiString TradingPhaseCode;
};

struct Equity {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Equity
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Bonds {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Bonds
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Funds {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Funds
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> IOPV;
	MDLDoubleT<3> PreCloseIOPV;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
};

struct Level1Plus {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Level1Plus
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString TradingPhaseCode;
	uint64_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct ExtendFieldsItem {
		uint32_t Type;
		int64_t Value;
	};
	MDLListT<ExtendFieldsItem> ExtendFields;
};

struct Equity2 {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Equity2
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	uint64_t NumTrades;
};

struct Bond2 {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Bond2
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	uint64_t NumTrades;
};

struct Fund2 {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Fund2
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<5> IOPV;
	MDLDoubleT<5> PreCloseIOPV;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	uint64_t NumTrades;
};

struct Comp {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_Comp
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	uint64_t NumTrades;
	MDLAnsiString MDStreamID;
};

struct BondDist {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_BondDist
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	uint64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> ClosePrice;
	struct BidPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		uint64_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	uint64_t NumTrades;
};

struct ATP {
	enum {
		ServiceID = MDLSID_MDL_SHL1,
		ServiceVer = MDLVID_MDL_SHL1,
		MessageID = MDLMID_MDL_SHL1_ATP
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString TradingPhaseCode;
	MDLTime UpdateTime;
	int64_t Volume;
	MDLDoubleT<2> Turnover;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> ClosePrice;
	int64_t AskVolume1;
	int64_t BidVolume1;
	int32_t NumTrades;
};

#pragma pack()

} // namespace mdl_shl1_msg
} // namespace mdl
} // namespace datayes
