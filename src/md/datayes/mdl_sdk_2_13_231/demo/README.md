MDL C++ SDK DEMO 使用指南
=========================

此文件夹包含MDL C++ SDK的使用示例。

1. mdl_api_demo.cpp (38%): 演示了**手动订阅**的模式，及c++ sdk的基础用法，包含创建IOManager，Subscriber，订阅消息号，连接等步骤。
2. mdl_auto_subscribe_demo.cpp (47%): 演示了**自动订阅**的模式，该模式只需指定自动订阅服务器地址和用户token，即可根据用户权限订阅所有具备权限的消息。（新手友好）
3. mdl_subscribe_demo.cpp (5%): 演示了在手动订阅模式下，更细粒度的订阅控制，例如只想订阅一个消息号中的几只证券。
4. mdl_latency_output.cpp (10%): 演示了如何获取消息延迟。用户可参考这部分代码进行数据延迟测试。

括号内的百分比表示每个文件你可能使用到的概率。对于新接入sdk的用户来说，基本上只会用到前2个文件，推荐使用自动订阅。

使用方法
--------

`demo`文件夹下包含makefile和vc工程文件，Linux平台直接在此目录下执行make命令机可，Windows平台打开相应的工程文件进行编译。

> Windows动态库的vc版本是vc143，即VisualStudio 2022，工程文件可能用VS2019产出。打开会有工程文件升级的提示，按提示升级即可。

编译后会在`bin`目录产生可执行文件。每个可执行文件直接运行都会打印用法，例如Linux平台下，

```bash
cd bin
./mdl_api_demo
usage: mdl_api_demo server_hostname:port
```

因此，只需要传入行情服务器的地址和端口，即可运行（前提是代码里已经替换token，代码里需要替换用户token的地方已经用注释标注）。


实践建议
--------

Demo中仅展示了基础用法，在实践中通常还需注意以下几点：

- IOManager对象全局仅需创建一个，但Subscriber可以创建多个，每个Subscriber会创建一个连接。
- 合理分配哪些Subscriber订阅哪些消息，对于数据量大的消息（如沪深逐笔），推荐拆分连接订阅，将他们放到单独的连接中去，甚至单独开一个连接订阅这个消息。
- 发布的行情类型以端口号区分，不同端口号的行情不同(每个端口号可能有一个或多个服务器地址)，非自动订阅时，不同端口需要创建独立的Subscriber订阅行情。
- 不要在不同的Subscriber中订阅相同的消息号。

Troubleshooting
---------------

### mdl_latency_output.cpp编译报错

```
undefined reference to `Json::Value::asString[abi:cxx11]() const'
```

可能的原因：

1. demo中用到的json库使用g++-4.8编译，高版本编译器可能有abi不兼容。
2. 系统中装了同名的库，链接到错误的动态库。

解决办法：

1. 如果用不到这个文件，不编译这个文件即可，或者直接删掉它。（推荐）
2. 如果用到了，下面两条路选择其一，
    1. 换成系统中自带的库，不要链接我们提供的json库，具体怎么换请自行百度。升版本可能导致json库的API不兼容，具体参考json库的官方文档自行解决。
    2. 使用g++-4.8编译。