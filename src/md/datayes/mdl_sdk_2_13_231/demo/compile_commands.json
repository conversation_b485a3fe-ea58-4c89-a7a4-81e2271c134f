[{"arguments": ["g++-4.8", "-c", "-I../include", "-I../3rd", "-g", "-O2", "-std=c++11", "-o", "../bin/mdl_api_demo.o", "mdl_api_demo.cpp"], "directory": "/home/<USER>/code/mdl_sdk/demo", "file": "mdl_api_demo.cpp"}, {"arguments": ["g++-4.8", "-c", "-I../include", "-I../3rd", "-g", "-O2", "-std=c++11", "-o", "../bin/mdl_latency_output.o", "mdl_latency_output.cpp"], "directory": "/home/<USER>/code/mdl_sdk/demo", "file": "mdl_latency_output.cpp"}, {"arguments": ["g++-4.8", "-c", "-I../include", "-I../3rd", "-g", "-O2", "-std=c++11", "-o", "../bin/mdl_subscribe_demo.o", "mdl_subscribe_demo.cpp"], "directory": "/home/<USER>/code/mdl_sdk/demo", "file": "mdl_subscribe_demo.cpp"}, {"arguments": ["g++-4.8", "-c", "-I../include", "-I../3rd", "-g", "-O2", "-std=c++11", "-o", "../bin/mdl_auto_subscribe_demo.o", "mdl_auto_subscribe_demo.cpp"], "directory": "/home/<USER>/code/mdl_sdk/demo", "file": "mdl_auto_subscribe_demo.cpp"}]